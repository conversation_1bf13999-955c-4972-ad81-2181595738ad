﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using RequisitionServices.MMISServices;
using RequisitionServices.DomainServices;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class ConfigurationServiceTests
    {
        [TestMethod]
        [TestCategory("IntegrationTests")]
        public void TestUseStatusFromIIBResourceGroupNotSet()
        {
            var smartCoidService = new SmartCOIDService();
            var configurationService = new ConfigurationService(smartCoidService);
            var result = configurationService.UseStatusFromIIB("08752");
            Assert.AreEqual(false, result, "The resource group is not set to send REQ/SPR status");
        }
    }
}
