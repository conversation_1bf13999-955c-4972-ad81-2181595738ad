﻿using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.MMISServices.DTO
{
    public class DepartmentRecordModel
    {
        public int DepartmentCoid { get; set; }

        public int DepartmentNumber { get; set; }

        public string Description { get; set; }

        public bool AutoIssueFlag { get; set; }

        public bool IsChargeableFlag { get; set; }

        public int ExpenseAccount { get; set; }

        public int CorporateDepartment { get; set; }

        public int AssociateDepartment { get; set; }

        public bool IsActive { get; set; }

        public bool GalenFlag { get; set; }

        public int ShipToCode { get; set; }

        public Department MapToDepartment()
        {
            return new Department()
            {
                IsActive = this.IsActive,
                Id = this.DepartmentNumber,
                Description = this.Description,
                COID = this.DepartmentCoid.ToString()
            };
        }
    }
}
