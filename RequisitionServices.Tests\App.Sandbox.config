<?xml version="1.0" encoding="utf-8" ?>

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <appSettings>

    <add key="APIURL" value="https://sbx-api-requisitions.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="ProcessMessagesFromIIB" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    <add key="UseStatusFromIIB" value="true" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />

    <add key="HomeAPIUrl" value="http://dev-api.healthtrustpg.com/v1/api/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SmartServicesAPIUrl" value="https://sbx-api-smartservices.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SecurityAPIUrl" value="http://dev-api.nsa.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SecurtiyAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="ProfileAPIUrl" value="http://dev-api-profile.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="EProcurementUrl" value="https://sbx-smart.healthtrustpg.com/Procurement" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>

  <system.serviceModel>
    <client>
      <endpoint address="http://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx"
          binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRushApprovalWorkflow"
          contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
      <endpoint address="http://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx"
          binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_INonRushApprovalWorkflow"
          contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </client>
  </system.serviceModel>
</configuration>