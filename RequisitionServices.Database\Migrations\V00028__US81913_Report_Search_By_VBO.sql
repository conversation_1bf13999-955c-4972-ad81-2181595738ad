﻿USE [eProcurementQA]
GO

IF TYPE_ID(N'VendorAffiliationList') IS NULL
	CREATE TYPE [dbo].[VendorAffiliationList] AS TABLE(
		[VendorId] [int] NOT NULL
	)
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : <PERSON>zo
Created     : 03-28-2022
Usage       : Vendor Requisitions Report

***************************************************************************
*/

CREATE PROCEDURE [dbo].[usp_VBORequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@vboFirst bit,
	@vendorAffiliations [dbo].[VendorAffiliationList] READONLY,
	@userIsVendor bit
AS

BEGIN

	DECLARE @minBackMonths INT = -13

	SELECT
		[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
		[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
		[DistinctRequisitions].[Comments] AS [Comments],
		[DistinctRequisitions].[CreateDate] AS [CreateDate],
		[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
		[DistinctRequisitions].[FirstName] AS [FirstName],
		[DistinctRequisitions].[LastName] AS [LastName],
		[DistinctRequisitions].[IsMobile] AS [IsMobile],
		[DistinctRequisitions].[IsVendor] AS [IsVendor],
		[AllReqItems].[Id] AS [RequisitionItemId],
		[AllReqItems].[ItemId] AS [RequisitionItemNumber],
		[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
		[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
		[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSsytemId],
		[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
		[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
		[AllReqItems].[Discount] AS [Discount],
		[AllReqItems].[VendorId] AS [VendorId],
		[AllReqItems].[UnitCost] AS [UnitCost],
		[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
		[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
		[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
		[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
		[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
		[DistinctRequisitions].[CreatedBy] AS [CreatedById],
		[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
	FROM
	(
		SELECT
			ROW_NUMBER() OVER 
			(	ORDER BY 
				(
					CASE @statusSorting WHEN 1 THEN 
						CASE [RequisitionStatusTypeId] 
							WHEN 7 THEN 1 
							WHEN 6 THEN 2
							WHEN 12 THEN 3
							WHEN 1 THEN 4
							WHEN 2 THEN 5
							WHEN 4 THEN 6
							ELSE 7
						END 
					END
				), [ReqTypeGroupingOrder], 
				CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
				CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
				CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
			) as rowNumber,
			[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
			[Req].[RequisitionId] AS [RequisitionId],
			[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[Req].[LocationIdentifier] AS [LocationIdentifier],
			[Req].[Comments] AS [Comments],
			[Req].[CreatedBy] AS [CreatedBy],
			[Req].[CreateDate] AS [CreateDate],
			[Req].[RequisitionTypeId] AS [RequisitionTypeId],
			[Req].[IsMobile] AS [IsMobile],
			[Req].[IsVendor] AS [IsVendor],
			[User].[FirstName] AS [FirstName],
			[User].[LastName] AS [LastName],
			(
				CASE @statusSorting 
					WHEN 1 THEN 
						CASE [RequisitionStatusTypeId]
							WHEN 7 THEN 1 
							WHEN 6 THEN 2
							WHEN 12 THEN 3
							WHEN 1 THEN 4
							WHEN 2 THEN 5
							WHEN 4 THEN 6
							ELSE 7
						END 
				END
			) AS [ConditionalStatusSorting],
			dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
			+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
			- 1 AS [TotalReqCount]
		FROM
		(
			SELECT
				(CASE
					WHEN @mobileReqs = 1
					THEN 
						CASE
							WHEN [Requisition].[IsMobile] = @mobileReqs
							THEN 1
							ELSE 2
						END
					ELSE
						CASE
							WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
							THEN 1
							ELSE 2 
						END
				END) AS [ReqTypeGroupingOrder],
				[Requisition].[RequisitionId] AS [RequisitionId],
				[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
				[Requisition].[LocationIdentifier] AS [LocationIdentifier],
				[Requisition].[Comments] AS [Comments],
				[Requisition].[CreatedBy] AS [CreatedBy],
				[Requisition].[CreateDate] AS [CreateDate],
				[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
				[Requisition].[IsMobile] AS [IsMobile],
				[Requisition].[IsVendor] AS [IsVendor]
			FROM
				[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
			WHERE
				[Requisition].IsVendor = 1 
				AND [Requisition].CreateDate >= DATEADD(MONTH, @minBackMonths, GETDATE())
				AND 1 <> [Requisition].[RequisitionStatusTypeId]
				AND 5 <> [Requisition].[RequisitionStatusTypeId]
				AND 8 <> [Requisition].[RequisitionStatusTypeId]
				AND 12 <> [Requisition].[RequisitionStatusTypeId]
				AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid  
				AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
				AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate

		) AS [Req]
			LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
			LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
			LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
			LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
		WHERE
			((@userIsVendor = 1 AND [ReqItem].VendorId IN (SELECT VendorId FROM @vendorAffiliations)) OR @userIsVendor = 0)
			AND
			(
				@filterText IS NULL OR
				(
					[Req].[RequisitionId] LIKE '%' + @filterText + '%'
					OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
					OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
					OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
					OR [Req].[Comments] LIKE '%' + @filterText + '%'
					OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
					OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
					OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
				)
			)
		GROUP BY
			[Req].[ReqTypeGroupingOrder],
			[Req].[RequisitionId],
			[Req].[RequisitionStatusTypeId],
			[Req].[LocationIdentifier],
			[Req].[Comments],
			[Req].[CreatedBy],
			[Req].[CreateDate],
			[Req].[RequisitionTypeId],
			[Req].[IsMobile],
			[Req].[IsVendor],
			[User].[FirstName],
			[User].[LastName]
		ORDER BY
			[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
			CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
			CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
			CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
		
	
		OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS [DistinctRequisitions]
		LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
		LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
	ORDER BY 
		[DistinctRequisitions].rowNumber
	OPTION (RECOMPILE)

END

GO

/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportGet
Purpose     : Returns list of VBO requisitions for the Requisition Report export.
Used By     : SMART Procurement team
Author      : Julio Pozo
Created     : 03-29-2022
Usage       : Vendor Requisitions Report Export

***************************************************************************
*/

CREATE PROCEDURE [dbo].[usp_VBORequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@maxExportedRequisitionCount int = 1000,
	@vboFirst bit,
	@vendorAffiliations [dbo].[VendorAffiliationList] READONLY,
	@userIsVendor bit

AS
BEGIN

DECLARE @minBackMonths INT = -13

SELECT  
	[Req].[RequisitionId]				AS [RequisitionId],
	[Req].[CreatedBy]					AS [RequisitionerId],
	[Req].[RequisitionTypeId]			AS [RequisitionTypeId],
	[Req].[CreateDate]					AS [RequisitionCreateDate],
	[Req].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
	[Req].[IsMobile]					AS [RequisitionIsMobile],
	[User].[FirstName]					AS [RequisitionerFirstName],
	[User].[LastName]					AS [RequisitionerLastName],
	[ReqItem].[Id]						AS [RequisitionItemId],
	[ReqItem].[ItemId]					AS [RequisitionItemNumber],
	[ReqItem].[UOMCode]					AS [RequisitionItemUomCode],
	[ReqItem].[PONumber]				AS [RequisitionItemPONumber],
	[ReqItem].[UnitCost]				AS [RequisitionItemUnitCost],
	[ReqItem].[VendorId]				AS [RequisitionItemVendorId],
	[ReqItem].[VendorName]				AS [RequisitionItemVendorName],
	[ReqItem].[ItemDescription]			AS [RequisitionItemDescription],
	[ReqItem].[ParIdentifier]			AS [RequisitionItemParIdentifier],
	[ReqItem].[ReOrder]					AS [RequisitionItemReorderNumber],
	[ReqItem].[ParentSystemId]			AS [RequisitionItemParentSystemId],
	[ReqItem].[OriginalParentSystemId]	AS [RequisitionItemOriginalParentSystemId],
	[ReqItem].[QuantityToOrder]			AS [RequisitionItemQuantityOrdered],
	[ReqItem].[SmartItemNumber]			AS [RequisitionItemSmartItemNumber],
	[ReqItem].[GeneralLedgerCode]		AS [RequisitionItemGeneralLedgerCode],
	[ReqItem].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
	[ReqItem].[Discount]				AS [Discount],
	[ReqItem].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
	[SprDetails].[UOMCode]				AS [SprDetailsUomCode],
	[SprDetails].[VendorId]				AS [SprDetailsVendorId],
	[SprDetails].[VendorName]			AS [SprDetailsVendorName],
	[SprDetails].[PartNumber]			AS [SprDetailsPartNumber],
	[SprDetails].[ItemDescription]		AS [SprDetailsDescription],
	[SprDetails].[TradeInValue]			AS [SprDetailsTradeInValue],
	[SprDetails].[BudgetNumber]			AS [SprDetailsBudgetNumber],
	[SprDetails].[GeneralLedgerCode]	AS [SprDetailsGeneralLedgerCode],
	[SprDetails].[EstimatedPrice]		AS [SprDetailsEstimatedUnitPrice],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
FROM
(
	SELECT TOP (@maxExportedRequisitionCount)
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
	END)										AS [ReqTypeGroupingOrder],
	[Requisition].[RequisitionId]				AS [RequisitionId],
	[Requisition].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Requisition].[LocationIdentifier]			AS [LocationIdentifier],
	[Requisition].[IsMobile]					AS [IsMobile],
	[Requisition].[Comments]					AS [Comments],
	[Requisition].[CreatedBy]					AS [CreatedBy],
	[Requisition].[CreateDate]					AS [CreateDate],
	[Requisition].[RequisitionTypeId]			AS [RequisitionTypeId]
	FROM 
		[dbo].[Requisitions]					AS [Requisition] WITH (NOLOCK)
	WHERE
		[Requisition].IsVendor = 1 
		AND [Requisition].CreateDate >= DATEADD(MONTH, @minBackMonths, GETDATE())
		AND 1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]
		AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
		AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
		AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate

	ORDER BY

		CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
		END,

		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Requisition].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Requisition].[CreateDate] END DESC
		

) AS [Req]

LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
WHERE
	((@userIsVendor = 1 AND [ReqItem].VendorId IN (SELECT VendorId FROM @vendorAffiliations)) OR @userIsVendor = 0)
	AND
	(
		@filterText IS NULL OR
		(
			[Req].[RequisitionId] LIKE '%' + @filterText + '%'
			OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
			OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
			OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
			OR [Req].[Comments] LIKE '%' + @filterText + '%'
			OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
			OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
			OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
		)
	)
ORDER BY
	[ConditionalStatusSorting] 

OPTION (RECOMPILE)

END

GO