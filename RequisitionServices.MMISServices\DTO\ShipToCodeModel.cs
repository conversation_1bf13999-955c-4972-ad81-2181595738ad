﻿using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.MMISServices.DTO
{
    public class ShipToCodeModel
    {
        public int Coid { get; set; }

        public int ShipNumber { get; set; }

        public string ShipName { get; set; }

        public string Address { get; set; }

        public string City { get; set; }

        public string County { get; set; }

        public string State { get; set; }

        public string ZipCode { get; set; }

        public string Phone { get; set; }

        public bool CRDO { get; set; }

        public string HIN { get; set; }

        public bool IsInCity { get; set; }

        public Address MapToAddress()
        {
            return new Address()
            {
                AddressName = ShipName,
                ExternalSystemId = ShipNumber,
                Address1 = Address,
                City = City,
                State = State,
                Zip = ZipCode
            };
        }
    }
}
