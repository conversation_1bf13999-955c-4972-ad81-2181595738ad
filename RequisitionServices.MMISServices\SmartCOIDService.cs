﻿using log4net;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using RequisitionServices.MMISServices.Utilities;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    public class SmartCOIDService : ISmartCOIDService
    {
        private ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private const string getUserCoidsMethod = "Coids/GetAllCoids/";
        private const string getCOIDList = "Coids/GetCOIDsList";
        private const string getCOIDMethod = "Coids/GetCoid/";
        private const string getGLAccountsMethod = "GLAccounts/GetAllGLAccounts/";
        private const string getGLAccountMethod = "GLAccounts/GetGLAccountById/";
        private const string getCostCodesMethod = "GLAccounts/GetAllCostCodes/";
        
        private const string getAddressMethod = "ShipToCodes/GetShipToCodeById/";
        private const string getAllAddressesMethod = "ShipToCodes/GetAllShipToCodes/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public Location GetCOID(string userName, string coid)
        {
            var COIDRecord = new CoidRecordModel();
            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                COIDRecord = ApiUtility.ExecuteApiGetTo<CoidRecordModel>(endpoint, getCOIDMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetCOID method", ex);
                return null;
            }

            return COIDRecord.MapToLocation();
        }

        public IEnumerable<Location> GetUserCOIDs(string userName)
        {
            var coidRecordsModel = new CoidRecordsModel();
            var locations = new List<Location>();
            try
            {
                SmartInputValidator.CheckUserName(ref userName);

                coidRecordsModel = ApiUtility.ExecuteApiGetTo<CoidRecordsModel>(endpoint, getUserCoidsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName }
                                                                            });

                
                if (coidRecordsModel != null && coidRecordsModel.CoidData != null)
                {
                    foreach (var record in coidRecordsModel.CoidData)
                    {
                        locations.Add(record.MapToLocation());
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetUserCOIDs method", ex);
                throw;
            }

            return locations;
        }

        public List<int> GetAllCOIDs(string userName)
        {
            var COIDList = new List<int>();
            
            try
            {
                SmartInputValidator.CheckUserName(ref userName);

                COIDList = ApiUtility.ExecuteApiGetTo<List<int>>(endpoint, getCOIDList, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName }
                                                                            });
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetAllCOIDs method", ex);
                throw;
            }

            return COIDList;
        }

        public IEnumerable<int> GetAllCostCodes(string userName, string coid)
        {
            var costCodesModel = new CostCodesModel();
            var costCodes = new List<int>();            

            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                costCodesModel = ApiUtility.ExecuteApiGetTo<CostCodesModel>(endpoint, getCostCodesMethod, new Dictionary<string, string>()
                                                                        {
                                                                            { "userId", userName },
                                                                            { "coid", coid }
                                                                        });

                if (costCodesModel != null && costCodesModel.CostCodes != null)
                {
                    costCodes = costCodesModel.CostCodes;
                }
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetAllCostCodes method", ex);
                throw;
            }

            return costCodes;
        }

         public IEnumerable<GLAccount> GetAllGLAccounts(string userName, string coid, string accountStringPartial)
         {            
            GLAccountsModel glAccountsModel;
            var glAccounts = new List<GLAccount>();

            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                glAccountsModel = ApiUtility.ExecuteApiGetTo<GLAccountsModel>(endpoint, getGLAccountsMethod, new Dictionary<string, string>()
                                                                         {
                                                                             { "userId", userName },
                                                                             { "coid", coid },
                                                                             { "accountStringPartial", accountStringPartial }
                                                                         });


                if (glAccountsModel != null && glAccountsModel.Accounts != null)
                {
                    foreach (var gLAccountModel in glAccountsModel.Accounts)
                    {
                        glAccounts.Add(gLAccountModel.MapToGLAccount());
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetAllGLAccounts method", ex);
                throw;
            }

            return glAccounts;
         } 

        public GLAccount GetGLAccount(string userName, string coid, long accountNumber)
        {
            var glAccountModel = new GLAccountModel();

            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                glAccountModel = ApiUtility.ExecuteApiGetTo<GLAccountModel>(endpoint, getGLAccountMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "GLAccountNumber", accountNumber.ToString() }
                                                                            });
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetGLAccount method", ex);
                throw;
            }

            return glAccountModel.MapToGLAccount(); 
        }

        public Address GetAddress(string userName, string coid, int shipNumber)
        {
            var shipToCodeModel = new ShipToCodeModel();

            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                shipToCodeModel = ApiUtility.ExecuteApiGetTo<ShipToCodeModel>(endpoint, getAddressMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "shipNumber", shipNumber.ToString() }
                                                                            });
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetAddress method", ex);
                throw;
            }

            return shipToCodeModel.MapToAddress();
        }

        public IEnumerable<Address> GetAddresses(string userName, string coid)
        {
            var addressModels = new ShipToCodeRecordsModel();
            var addresses = new List<Address>();

            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                addressModels = ApiUtility.ExecuteApiGetTo<ShipToCodeRecordsModel>(endpoint, getAllAddressesMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });
                
                if (addressModels != null && addressModels.ShipToCodes != null)
                {
                    foreach (var shipToCode in addressModels.ShipToCodes)
                    {
                        addresses.Add(shipToCode.MapToAddress());
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error("Exception calling GetAddresses method", ex);
                throw;
            }

            return addresses;
        }
    }
}
