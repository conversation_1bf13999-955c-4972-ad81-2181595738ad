﻿using System.ComponentModel;

namespace RequisitionServices.DomainModel.Enum
{
    public enum RequisitionStatusTypeEnum
    {
        [Description("Unknown")]
        Unknown = 0,
        [Description("Draft")]
        Draft = 1,
        [Description("Pending Approval")]
        PendingApproval = 2,
        [Description("Approved")]
        Approved = 3,
        [Description("Submitted")]
        Submitted = 4,
        [Description("Deleted")]
        Deleted = 5,
        [Description("Denied")]
        Denied = 6,
        [Description("Submission Error")]
        SubmissionError = 7,
        [Description("Template")]
        Template = 8,
        [Description("Adhoc Review Requested")]
        AdhocReviewRequested = 9,
        [Description("Adhoc Review Provided")]
        AdhocReviewProvided = 10,
        [Description("Workflow Changed")]
        WorkflowChanged = 11,
        [Description("Recalled")]
        Recalled = 12,
        [Description("Legacy")]
        Legacy = 13,
        [Description("On Hold")]
        OnHold = 14
    }
}
