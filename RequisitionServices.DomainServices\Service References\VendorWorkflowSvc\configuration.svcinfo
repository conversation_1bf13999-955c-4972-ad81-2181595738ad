﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_IVendorApprovalWorkflow&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_IVendorApprovalWorkflow" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IVendorApprovalWorkflow&quot; contract=&quot;VendorWorkflowSvc.IVendorApprovalWorkflow&quot; name=&quot;BasicHttpBinding_IVendorApprovalWorkflow&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IVendorApprovalWorkflow&quot; contract=&quot;VendorWorkflowSvc.IVendorApprovalWorkflow&quot; name=&quot;BasicHttpBinding_IVendorApprovalWorkflow&quot; /&gt;" contractName="VendorWorkflowSvc.IVendorApprovalWorkflow" name="BasicHttpBinding_IVendorApprovalWorkflow" />
  </endpoints>
</configurationSnapshot>