﻿using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class RequisitionDetailModel
    {
        public int Id { get; set; }
        public int CoidNumber { get; set; }
        public int ReqNumber { get; set; }
        public string ItemStatus { get; set; }
        public int ItemNumber { get; set; }
        public int ItemQuantity { get; set; }
        public int IssuedQuantity { get; set; }
        public DateTime PurchaseOrderCreatedDate { get; set; }
        public DateTime PurchaseOrderItemReceivedDate { get; set; }
        public DateTime DateOfService { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public int PurchaseOrderLine { get; set; }
        public string ReqUserId { get; set; }
        public int AddedPreviousReqNumber { get; set; }
        public DateTime ReqScheduledDate { get; set; }
        public string ReqType { get; set; }
        public int ActualReqOrderQuantity { get; set; }
        public bool StatPOFlag { get; set; }
        public string ItemDistributionPoint { get; set; }
        public int DistributionCoid { get; set; }
        public bool ConfirmReq { get; set; }

        //ReqServices added property
        public bool IsStatusChange { get; set; } //TODO: [DEPRECATED] Remove
        public int? OriginalReqId { get; set; }
        public string SubItemFlag { get; set; }
    }
}
