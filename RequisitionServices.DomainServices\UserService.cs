﻿using log4net;
using RequisitionServices.DomainModel.Constants;
using RequisitionServices.DomainModel.Email;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Utility.Domain;
using RequisitionServices.Utility.Model;
using RequisitionServices.Utility.Web;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Web;

namespace RequisitionServices.DomainServices
{
    public class UserService : IUserService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private string appKey = ConfigurationManager.AppSettings.Get("SecurtiyAPIKey");
        private string securityEndpoint = ConfigurationManager.AppSettings.Get("SecurityAPIUrl");
        readonly string newSecurityEndpoint = ConfigurationManager.AppSettings.Get("SecurityAPINewUrl");
        private string profileEndpoint = ConfigurationManager.AppSettings.Get("ProfileAPIUrl");
        private string homeAPIEndpoint = ConfigurationManager.AppSettings.Get("HomeAPIUrl");
        private string eproEndpoint = ConfigurationManager.AppSettings.Get("EProcurementUrl");
        private bool ffRetrieveDelegateUsers = Convert.ToBoolean(ConfigurationManager.AppSettings.Get("FeatureFlags:RetrieveDelegateUsersDE16834"));
        private int delegateMaxDegreeOfParallelism = Convert.ToInt32(ConfigurationManager.AppSettings.Get("DelegateMaxDegreeOfParallelism"));

        private const string getUsersWithPartAndSoCMethod = "User/GetUsersWithPartAndSpanOfControl/";
        private const string getUserInfoMethod = "User/GetUserInfo/";
        private const string getUserPartsMethod = "User/GetUserParts/";
        private const string getProfileMethod = "Profile/";
        private const string getChildCoidsMethod = "Company/ChildCoids/";
        private const string getFacilityMethod = "Facility/Get/";
        private const string getUsersWithRoleAndSocMethod = "User/GetUsersWithRoleAndSpanOfControl";
        private const string PostUserDetails = "details";

        private ISmartCOIDService smartCOIDService;
        private ISmartDepartmentService smartDepartmentService;
        private IParService parService;
        private IUserRepository userRepository;
        private IWorkflowRepository workflowRepository;
        private IRequisitionRepository requisitionRepository;
        private IRequisitionStatusRepository requisitionStatusRepository;
        private IWorkflowService workflowService;
        private ITypeRepository typeRepository;
        private IEmailService emailService;
        private IVendorService vendorSvc;
        private IAuditService auditService;
        private IConfigurationService configurationService;
        private IFacilityWorkflowService _facilityWorkflowService;

        readonly List<SpanOfControl> _testCoidSpanOfControls = !string.IsNullOrWhiteSpace(ConfigurationManager.AppSettings.Get("coidsForQATesting")) ? ConfigurationManager.AppSettings.Get("coidsForQATesting").Split(',')
            .Select(x => new SpanOfControl { Hierarchy = OrganizationalLevelHierarchyType.Facility, ObjectId = x }).ToList()
            : new List<SpanOfControl>();

        public static Dictionary<string, string> ProcurementRoles = new Dictionary<string, string>
        {
            {  "ProcureApprover", "593A0F8C-ABC6-4A41-9BA6-CA330CCBC397" },
            {  "ProcureAdmin", "7666CBA0-B84E-4E9B-9401-15B5A9C44942" },
            {  "ProcureRequisitioner", "EE8B2FB1-D7ED-4FA4-9F62-3F8F5154658E" },
            {  "ProcureSPR", "90114BA0-ACA8-43CC-B22A-2B7FFCA8BBE9" },
            {  "ProcureReviewer", "ACCD2C46-1DB8-452F-8BC8-DD55B11E626C" },
            {  "ProcureVBO", "0B6EDD25-4C04-4D31-B28C-20D3B141DAC5" }
        };
        public UserService(ISmartCOIDService smartCOIDSvc, ISmartDepartmentService smartDeptSvc, IParService parSvc, IUserRepository userRepo, IWorkflowRepository workflowRepo, IRequisitionRepository requisitionRepo, IRequisitionStatusRepository requisitionStatusRepo, IWorkflowService workflowSvc, IEmailService emailSvc, ITypeRepository typeRepo, IVendorService vendSvc, IAuditService auditSvc, IConfigurationService configurationSvc, IFacilityWorkflowService facilityWorkflowService)
        {
            smartCOIDService = smartCOIDSvc;
            smartDepartmentService = smartDeptSvc;
            parService = parSvc;
            userRepository = userRepo;
            workflowRepository = workflowRepo;
            requisitionRepository = requisitionRepo;
            requisitionStatusRepository = requisitionStatusRepo;
            workflowService = workflowSvc;
            emailService = emailSvc;
            typeRepository = typeRepo;
            this.vendorSvc = vendSvc;
            auditService = auditSvc;
            configurationService = configurationSvc;
            _facilityWorkflowService = facilityWorkflowService;
        }

        public Profile GetProfile(string domainSlashUserName)
        {
            //Get email from Security profile
            if (!String.IsNullOrWhiteSpace(domainSlashUserName) && domainSlashUserName.IndexOf('/') != -1)
            {
                return this.GetSecurityProfile(domainSlashUserName.Split('/')[0], domainSlashUserName.Split('/')[1]);
            }
            return null;
        }

        public Profile GetSecurityProfile(string domain, string userName)
        {
            try
            {
                var parameters = new Dictionary<string, object>
                {
                    { "domain", domain },
                    { "userName", userName },
                    { "appId", appKey },
                    { "withDetails", true }
                };
                var user = ApiUtility.ExecuteSecurityApiPostTo<Profile>(newSecurityEndpoint + PostUserDetails,
                    parameters, GetSecurityToken());
                return user;
            }
            catch (Exception ex)
            {
                log.Error(
                    $"Error retrieving Security profile for specified user: Domain {domain} , UserName {userName}", ex);
                return null;
            }
        }

        public Approver GetApproverByUserNameAndCOID(string accountName, string COID)
        {
            return userRepository.GetApproverByUserNameAndCOID(accountName, COID);
        }

        public Approver GetApproverByUserId(int userId, string coid)
        {
            return userRepository.GetApproverByUserId(userId, coid);
        }

        public async Task<IEnumerable<ActiveApproversDto>> GetActiveApproversAsync()
        {
            return await userRepository.GetActiveApproversAsync();
        }

        public async Task<IEnumerable<ApproverWorkflowDto>> GetApproverWorkflowsAsync(int approverUserId)
        {
            return await userRepository.GetApproverWorkflowsAsync(approverUserId);
        }

        public async Task<ActiveApproversDto> GetActiveApproversForBulkExchangeAsync(string searchAccountName, int selectedApproverUserId)
        {
            ActiveApproversDto approver = null;
            User user = userRepository.GetUser(searchAccountName);
            if (user != null)
            {
                approver = new ActiveApproversDto
                {
                    Id = user.Id,
                    AccountName = user.AccountName,
                    FirstName = user.FirstName,
                    LastName = user.LastName
                };

                var userApprover = await userRepository.GetUserAndDelegateAsync(selectedApproverUserId);
                approver.IsDelegate = userApprover != null && userApprover.DelegatedUserId == approver.Id;
            }

            return approver;
        }

        public Approver GetApproverByUserIdCOIDAndDelegateId(int? userId, string COID, int delegateId)
        {
            return userRepository.GetApproverByUserIdCOIDAndDelegateId(userId, COID, delegateId);
        }

        private List<UserRoleNew> GetUserRoles(string userId)
        {
            userId = userId.Contains('/') ? userId.Split('/')[1] : userId;
            var generatedUrl = newSecurityEndpoint + userId + "/applications/" + appKey + "/roles";
            var roleList = ApiUtility.ExecuteSecurityTokenApiGetTo<List<UserRoleNew>>(generatedUrl, null, GetSecurityToken());
            return roleList;
        }
        public IEnumerable<AppPart> GetUserParts(string domainSlashUserName)
        {
            if (String.IsNullOrWhiteSpace(domainSlashUserName) || domainSlashUserName.IndexOf('/') == 0)
            {
                return null;
            }

            var nameParts = domainSlashUserName.Split('/');

            if (nameParts == null || nameParts.Count() < 2)
            {
                return null;
            }

            return this.GetUserParts(nameParts[0], nameParts[1]);
        }

        public IEnumerable<AppPart> GetUserParts(string domain, string userName)
        {
            var newRoles = GetUserRoles(userName);
            var userParts = GenerateAppParts(newRoles);

            return userParts;
        }

        public IEnumerable<Location> GetLocations(string userName)
        {
            return smartCOIDService.GetUserCOIDs(userName);
        }

        public Location GetLocation(string userName, string COID)
        {
            return smartCOIDService.GetCOID(userName, COID);
        }
        private void AddSecurityToken()
        {
            var securityService = new TokenGeneratorService();
            var token = securityService.GetSmartClientAuthToken<dynamic>();
            HttpContext.Current.Request.Headers["Authorization"] = "Bearer " + token.access_token;
        }
        
        private static string GetSecurityToken()
        {
            var securityService = new TokenGeneratorService();
            var token = securityService.GetSmartClientAuthToken<dynamic>();
            string bearerToken = "Bearer " + token.access_token;
            if (HttpContext.Current != null)
                HttpContext.Current.Request.Headers["Authorization"] = bearerToken;
            return bearerToken;
        }
        public IEnumerable<Department> GetAllDepartments(string userName, string COID, bool userIsVendor = false)
        {
            var departments = smartDepartmentService.GetDepartments(userName, COID);

            if (userIsVendor)
            {
                var parsForFacility = parService.GetParsByUserLocation(userName, COID);

                var departmentsWithParsForVendorUser = parsForFacility.Where(x => (x.ParType == (int)ParType.BillOnly || x.ParType == (int)ParType.Capitated) && x.ParId.ToUpperInvariant().StartsWith("B")).Select(y => y.Department.Id).Distinct();
                return departments.Where(x => departmentsWithParsForVendorUser.Contains(x.Id));
            }

            return departments;
        }

        public string GetFavoriteFacilityId(string userName)
        {
            return userRepository.GetFavoriteFacilityId(userName);
        }

        public PersonalizationDTO SetFavoriteFacility(PersonalizationDTO personalizationDTO)
        {
            return userRepository.SetFavoriteFacility(personalizationDTO);
        }

        public void DeleteFavoriteFacility(string userName)
        {
            userRepository.DeleteFavoriteFacility(userName);
        }

        public int GetFavoriteDepartmentId(string userName, string COID)
        {
            return userRepository.GetFavoriteDepartmentId(userName, COID);
        }

        public Department GetFavoriteDepartment(string userName, string cOID)
        {
            var favoriteDepartmentId = this.GetFavoriteDepartmentId(userName, cOID);

            return (favoriteDepartmentId != 0)
               ? this.GetDepartment(userName, cOID, favoriteDepartmentId)
               : null;
        }

        public PersonalizationDTO SetFavoriteDepartment(PersonalizationDTO personalizationDTO)
        {
            return userRepository.SetFavoriteDepartment(personalizationDTO);
        }

        public void DeleteFavoriteDepartment(PersonalizationDTO personalizationDTO)
        {
            userRepository.DeleteFavoriteDepartment(personalizationDTO);
        }

        public string GetFavoriteParId(string userName, string COID, int departmentId)
        {
            return userRepository.GetFavoriteParId(userName, COID, departmentId);
        }

        public Par GetFavoritePar(string userName, string COID, int departmentId)
        {
            var favoriteParClass = userRepository.GetFavoriteParId(userName, COID, departmentId);

            return (favoriteParClass != null)
                ? parService.GetParById(userName, COID, departmentId, favoriteParClass)
                : null;
        }

        public PersonalizationDTO SetFavoritePar(PersonalizationDTO personalizationDTO)
        {
            return userRepository.SetFavoritePar(personalizationDTO);
        }

        public void DeleteFavoritePar(PersonalizationDTO personalizationDTO)
        {
            userRepository.DeleteFavoritePar(personalizationDTO);
        }

        public IEnumerable<Department> GetAllDepartmentsForCache(string userName, string COID)
        {
            var departments = smartDepartmentService.GetDepartmentsForCache(userName, COID).ToList();

            return departments;
        }

        public Department GetDepartment(string userName, string COID, int departmentId)
        {
            return smartDepartmentService.GetDepartment(userName, COID, departmentId);
        }

        private List<AppPart> GenerateAppParts(List<UserRoleNew> UserRoles)
        {
            List<AppPart> AppParts = new List<AppPart>();
            foreach (var role in UserRoles)
            {
                switch (role.Name)
                {
                    //FIXME: These hard-coded values need to be cleaned up.........
                    case UserProfileRoleNames.EProcApproverRoleName:
                        var partPA = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":3,\"ApplicationSystemId\":0,\"Name\":\"MyApprovals\",\"AppPartType\":2},{\"Id\":4,\"ApplicationSystemId\":0,\"Name\":\"MyItemRequests\",\"AppPartType\":0},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":33,\"ApplicationSystemId\":0,\"Name\":\"AdHocReviewer\",\"AppPartType\":15}]");
                        AppParts.AddRange(partPA);
                        break;
                    case UserProfileRoleNames.EProcAdministratorRoleName:
                        var partPAd = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":22,\"ApplicationSystemId\":0,\"Name\":\"AdminScreen\",\"AppPartType\":9},{\"Id\":23,\"ApplicationSystemId\":0,\"Name\":\"ManageWorkflowNotifications\",\"AppPartType\":10},{\"Id\":24,\"ApplicationSystemId\":0,\"Name\":\"UserWorkflowEdit\",\"AppPartType\":11},{\"Id\":25,\"ApplicationSystemId\":0,\"Name\":\"ApproverEdit\",\"AppPartType\":12}]");
                        AppParts.AddRange(partPAd);
                        break;
                    case UserProfileRoleNames.EProcRequisitionerRoleName:
                        var partReq = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":2,\"ApplicationSystemId\":0,\"Name\":\"MyRequisitions\",\"AppPartType\":1},{\"Id\":4,\"ApplicationSystemId\":0,\"Name\":\"MyItemRequests\",\"AppPartType\":0},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":19,\"ApplicationSystemId\":0,\"Name\":\"SearchItems\",\"AppPartType\":6}]");
                        AppParts.AddRange(partReq);
                        break;
                    case UserProfileRoleNames.EProcSPRRequisitionerRoleName:
                        var partspr = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":2,\"ApplicationSystemId\":0,\"Name\":\"MyRequisitions\",\"AppPartType\":1},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4},{\"Id\":19,\"ApplicationSystemId\":0,\"Name\":\"SearchItems\",\"AppPartType\":6},{\"Id\":20,\"ApplicationSystemId\":0,\"Name\":\"CreateSPR\",\"AppPartType\":7},{\"Id\":21,\"ApplicationSystemId\":0,\"Name\":\"CreateCapitalReq\",\"AppPartType\":8},{\"Id\":26,\"ApplicationSystemId\":0,\"Name\":\"PunchOutReq\",\"AppPartType\":13},{\"Id\":29,\"ApplicationSystemId\":0,\"Name\":\"SPRPARItems\",\"AppPartType\":14},{\"Id\":34,\"ApplicationSystemId\":0,\"Name\":\"CreateRushReq\",\"AppPartType\":16}]");
                        AppParts.AddRange(partspr);
                        break;
                    case UserProfileRoleNames.EProcReviewerRoleName:
                        var partPR = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":5,\"ApplicationSystemId\":0,\"Name\":\"MyReports\",\"AppPartType\":5},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"PriceViewer\",\"AppPartType\":4}]");
                        AppParts.AddRange(partPR);
                        break;
                    case UserProfileRoleNames.EProcVendorRoleName:
                        var partVB = Newtonsoft.Json.JsonConvert.DeserializeObject<List<AppPart>>("[{\"Id\":17,\"ApplicationSystemId\":0,\"Name\":\"VendorBillOnly\"},{\"Id\":6,\"ApplicationSystemId\":0,\"Name\":\"SearchItems\"}]");
                        AppParts.AddRange(partVB);
                        break;
                    default:
                        break;
                }
            }
            return AppParts.DistinctBy(d => d.Id).ToList();
        }

        public IEnumerable<User> RetrieveUsers(string userName, string COID, IEnumerable<NamesDTO> usersNames)
        {
            var accountNames = usersNames.Select(x => x.AccountName);
            var existingUsers = userRepository.GetUsers(accountNames).ToList();
            var newUsers = new List<User>();
            foreach (var missingAccount in usersNames.Where(x => !existingUsers.Where(y => !String.IsNullOrWhiteSpace(y.AccountName)).Select(z => z.AccountName.ToLower()).Contains(x.AccountName.ToLower())))
            {
                newUsers.Add(userRepository.InsertUser(new User()
                {
                    AccountName = missingAccount.AccountName,
                    CreatedBy = userName,
                    CreateDate = DateTime.Now,
                    FirstName = missingAccount.FirstName,
                    LastName = missingAccount.LastName
                }));
            }

            if (newUsers.Count() > 0)
            {
                existingUsers.AddRange(newUsers);
            }

            foreach(var user in existingUsers)
            {
                user.UserSetupWorkflows = this.GetUserSetupWorkflows(user.AccountName, COID);
            }
            
            return existingUsers;
        }

        private IEnumerable<Approver> RetrieveApprovers(string userName, string COID, IEnumerable<NamesDTO> usersNames)
        {
            var accountNames = usersNames.Select(x => x.AccountName);
            //Get/Create user accounts
            var existingUsers = this.RetrieveUsers(userName, COID, usersNames);


            //Get/Create/Reactivate approver accounts
            var existingApprovers = userRepository.GetApprovers(accountNames, COID).ToList();


            foreach (var inactiveApprover in existingApprovers.Where(x => !x.IsActive))
            {
                inactiveApprover.IsActive = true;
                userRepository.UpdateApprover(inactiveApprover, null, userName);
            }

            var newApprovers = new List<Approver>();

            foreach (var missingApprover in accountNames.Where(x => !existingApprovers.Where(y => !String.IsNullOrWhiteSpace(y.User.AccountName)).Select(z => z.User.AccountName.ToLower()).Contains(x.ToLower())))
            {
                var currentUser = existingUsers.Where(x => x.AccountName.ToLower() == missingApprover.ToLower()).FirstOrDefault();
                if (currentUser != null)
                {
                    currentUser.UserSetupWorkflows = this.GetUserSetupWorkflows(currentUser.AccountName, COID); //MON: not sure if needed

                    newApprovers.Add(userRepository.InsertApprover(new Approver()
                    {
                        User = currentUser,
                        UserId = currentUser.Id,
                        MaxApprovalAmount = 0,
                        CapitalMaxApprovalAmount = 0,
                        IsActive = true,
                        CreatedBy = userName,
                        CreateDate = DateTime.Now,
                        COID = COID
                    }, null));

                }
            }

            foreach (var missingApprover in newApprovers)
            {
                var approverWithDelegate = userRepository.GetApproversByUserId(missingApprover.UserId).FirstOrDefault(x => x.Delegate != null);
                if (approverWithDelegate != null)
                {
                    var delegateOtherApprover = userRepository.GetApprover((int)approverWithDelegate.Delegate);
                    var delegateApprover = userRepository.GetApproverByUserId(delegateOtherApprover.UserId, COID);
                    missingApprover.Delegate = delegateApprover.Id;
                    userRepository.UpdateApprover(missingApprover, "", missingApprover.User.AccountName);
                }

            }

            if (newApprovers.Count() > 0)
            {
                existingApprovers.AddRange(newApprovers);
            }

            return existingApprovers;
        }

        public void DeleteDelegatesForApprover(string userName, string delegateEmail)
        {
            if (userRepository.IsApproverByUserName(userName))
            {
                var user = userRepository.GetUser(userName);
                workflowRepository.DeleteDelegatedSteps(user.Id);
                _facilityWorkflowService.DeleteDelegatedSteps(user.Id);

                var approvers = userRepository.GetApproversByUserId(user.Id);

                var delegateId = approvers.Where(x => x.Delegate != null).Select(x => x.Delegate.Value).FirstOrDefault();
                var delegateUserID = userRepository.GetApprover(delegateId).UserId;
                //Create Approver Change Log
                auditService.UpdateApproverChangeLogList(approvers, (int)AuditEventTypeEnum.DelegateDeleted, delegateUserID);

                foreach (var approver in approvers.ToList())
                {
                    approver.Delegate = null;
                    userRepository.UpdateApprover(approver, "Delegate is unassigned", userName);
                }

                UserProfile approverProfile = this.GetUserProfile(userName);
                var userInfo = approverProfile.FirstName + " " + approverProfile.LastName;

                //Email for removed delegate approver
                if (!String.IsNullOrEmpty(delegateEmail)) //check added as delegateEmail is made an optional in the controller
                {
                    var emailRequest = new EmailRequest()
                    {
                        EmailType = EmailType.UnassignedDelegatedApprover,
                        Emails = new string[1] { delegateEmail },
                        SenderName = userInfo,
                        ActionDate = DateTime.Now
                    };
                    emailService.SendEmail(emailRequest);
                }
            }
        }

        public IEnumerable<Approver> UpdateApprovers(string userName, IEnumerable<Approver> approvers, string COID)
        {
            if (approvers != null)
            {
                var originalDbApprovers = userRepository.GetApprovers(approvers.Where(x => x.User != null).Select(x => x.User.AccountName.ToLower()), COID.ToLower()).ToList();

                List<Approver> originalApprovers = new List<Approver>();
                var userid = this.GetUserByAccountName(userName).Id;

                foreach (var approver in originalDbApprovers)
                {
                    originalApprovers.Add(approver.CreateDeepCopy(approver));
                }

                List<Approver> ApproverAmountChangeStandard = new List<Approver>();
                List<Approver> ApproverAmountChangeCapital = new List<Approver>();

                foreach (var approver in approvers)
                {
                    //Detect if changes are made
                    var originalApprover = originalApprovers.Where(x => x.Id == approver.Id).FirstOrDefault();
                    if (originalApprover != null)
                    {
                        //Check if delegateApproverId is different.  If so, keep delegateApproverId in db as is.
                        if (originalApprover.Delegate != approver.Delegate)
                        {
                            approver.Delegate = originalApprover.Delegate;
                        }

                        if (originalApprover.MaxApprovalAmount != approver.MaxApprovalAmount || originalApprover.CapitalMaxApprovalAmount != approver.CapitalMaxApprovalAmount || originalApprover.IsCERReviewer != approver.IsCERReviewer)
                        {
                            userRepository.UpdateApprover(approver, null, userName);
                        }

                        bool changeCapitals = false;
                        bool changeOthers = false;

                        //reorder all workflows required to change
                        if (originalApprover.MaxApprovalAmount != approver.MaxApprovalAmount)
                        {
                            changeOthers = true;
                        }
                        if (originalApprover.CapitalMaxApprovalAmount != approver.CapitalMaxApprovalAmount)
                        {
                            changeCapitals = true;
                        }
                        this.ReorderMultipleWorkflowSteps(userName, approver.User.AccountName, changeCapitals, changeOthers, COID); //pass userName for reqHistory
                    }

                    if (originalApprover.MaxApprovalAmount != approver.MaxApprovalAmount)
                    {
                        ApproverAmountChangeStandard.Add(approver);
                    }
                    if (originalApprover.CapitalMaxApprovalAmount != approver.CapitalMaxApprovalAmount)
                    {
                        ApproverAmountChangeCapital.Add(approver);
                    }
                }

                //Create Approver Change Log
                if (ApproverAmountChangeStandard != null)
                {
                    auditService.UpdateApproverChangeLogList(ApproverAmountChangeStandard, (int)AuditEventTypeEnum.StandardApprovalAmountChanged, userid);
                }
                if (ApproverAmountChangeCapital != null)
                {
                    auditService.UpdateApproverChangeLogList(ApproverAmountChangeCapital, (int)AuditEventTypeEnum.CapitalApprovalAmountChanged, userid);
                }
            }

            return approvers;
        }

        public async Task<IEnumerable<Approver>> UpdateApproversAsync(string userName, IEnumerable<Approver> approvers,
            string coid)
        {
            var requisitionStatusHistories = new List<RequisitionStatusHistory>();
            var updateApproversAsync = approvers.ToList();

            var originalDbApprovers = await userRepository.GetApproversAsync(
                updateApproversAsync.Where(x => x.User != null).Select(x => x.User.AccountName.ToLower()),
                coid.ToLower());

            var userid = this.GetUserByAccountName(userName).Id;

            var originalApprovers = originalDbApprovers.AsParallel()
                .Select(approver => approver.CreateDeepCopy(approver)).ToList();

            var approverAmountChangeStandard = new List<Approver>();
            var approverAmountChangeCapital = new List<Approver>();

            updateApproversAsync.AsParallel().ForAll(async (approver) =>
            {
                //Detect if changes are made
                var originalApprover = originalApprovers.FirstOrDefault(x => x.Id == approver.Id);
                if (originalApprover != null)
                {
                    //Check if delegateApproverId is different.  If so, keep delegateApproverId in db as is.
                    if (originalApprover.Delegate != approver.Delegate)
                    {
                        approver.Delegate = originalApprover.Delegate;
                    }

                    if (originalApprover.MaxApprovalAmount != approver.MaxApprovalAmount ||
                        originalApprover.CapitalMaxApprovalAmount != approver.CapitalMaxApprovalAmount ||
                        originalApprover.IsCERReviewer != approver.IsCERReviewer)
                    {
                        userRepository.UpdateApprover(approver, null, userName);
                    }

                    var changeOthers = originalApprover.MaxApprovalAmount != approver.MaxApprovalAmount;

                    // reorder all work-flows required to change
                    var changeCapitals = originalApprover.CapitalMaxApprovalAmount !=
                                         approver.CapitalMaxApprovalAmount;

                    requisitionStatusHistories.AddRange(await ReorderMultipleWorkflowStepsAsync(userName,
                        approver.User.AccountName,
                        changeCapitals, changeOthers, coid));
                }

                if (originalApprover != null && originalApprover.MaxApprovalAmount != approver.MaxApprovalAmount)
                {
                    approverAmountChangeStandard.Add(approver);
                }

                if (originalApprover != null &&
                    originalApprover.CapitalMaxApprovalAmount != approver.CapitalMaxApprovalAmount)
                {
                    approverAmountChangeCapital.Add(approver);
                }

                try
                {
                    requisitionRepository.BulkInsertRequisitionStatusHistory(requisitionStatusHistories);
                }
                catch (Exception e)
                {
                    //ToDo: Handle Exception 
                    Console.Write(e.Message);
                }
            });

            //Create Approver Change Log
            auditService.UpdateApproverChangeLogList(approverAmountChangeStandard,
                (int)AuditEventTypeEnum.StandardApprovalAmountChanged, userid);
            auditService.UpdateApproverChangeLogList(approverAmountChangeCapital,
                (int)AuditEventTypeEnum.CapitalApprovalAmountChanged, userid);

            return updateApproversAsync;
        }

        public void UpdateUserName(string accountName, string firstName, string lastName)
        {
            var user = userRepository.GetUser(accountName);
            if (user != null && (firstName != user.FirstName || lastName != user.LastName))
            {
                user.FirstName = firstName;
                user.LastName = lastName;
                userRepository.UpdateUser(user, null, null);
            }
            else if (user == null)
            {
                user = new User();
                user.FirstName = firstName;
                user.LastName = lastName;
                user.CreatedBy = "System";
                user.AccountName = accountName;
                user.CreateDate = DateTime.Now;
                userRepository.InsertUser(user);
            }
        }

        public ValidationOfUserWorkflowsDTO GetValidationOfUserWorkflows(ValidateUserWorkflowsRequestDTO validateUserWorkflowsRequestDTO)
        {
            var returnDTO = new ValidationOfUserWorkflowsDTO();
            var validations = new List<WorkflowValidationDTO>();
            List<Tuple<OrganizationalLevelHierarchyType, string, IEnumerable<Facility>>> hierarchyObjectFacilities = null;

            foreach (var workflow in validateUserWorkflowsRequestDTO.workflows)
            {
                bool isEmptyOnSetup = false;
                if (workflow.UserWorkflowSteps == null || !workflow.UserWorkflowSteps.Any())
                {
                    isEmptyOnSetup = true;
                }
                var validation = ValidateUserWorkflow(workflow.UserWorkflowSteps, workflow.UserName, workflow.WorkflowType, workflow.COID, ref hierarchyObjectFacilities, workflow.RequisitionTotal, isEmptyOnSetup);
                validation.WorkflowTypeId = workflow.WorkflowType.ToInt();
                validations.Add(validation);
            }

            returnDTO.validations = validations;

            return returnDTO;
        }

        private WorkflowValidationDTO ValidateUserWorkflow(IEnumerable<UserWorkflowStep> userSteps, string username, WorkflowTypeEnum workflowType, string COID, ref List<Tuple<OrganizationalLevelHierarchyType, string, IEnumerable<Facility>>> hierarchyObjectFacilities, decimal? requisitionTotal = null, bool isEmptyOnSetup = false)
        {
            var isValid = true;
            var warningMessages = new List<string>();

            if (isEmptyOnSetup)
            {
                isValid = true;
                return new WorkflowValidationDTO() { IsValid = isValid, WarningMessages = warningMessages.Distinct().ToList() };
            }

            if (userSteps == null || !userSteps.Any())
            {
                isValid = false;
                warningMessages.Add("No User Workflow setup.");
                return new WorkflowValidationDTO() { IsValid = isValid, WarningMessages = warningMessages };
            }

            //Check RequisitionTotal - if NULL then validating workflow from RequisitionSubmit... need to get UserProfiles
            if (requisitionTotal != null)
            {
                foreach (UserWorkflowStep userStep in userSteps)
                {
                    userStep.Approver.User.UserProfile = this.GetUserProfile(userStep.Approver.User.AccountName);
                }
            }

            IEnumerable<UserWorkflowStep> workflowStepWhereApproverIsNull = userSteps.Where(uw => uw.Approver == null);

            if (workflowStepWhereApproverIsNull.Any())
            {
                IEnumerable<Approver> approvers = userRepository.GetApprovers(workflowStepWhereApproverIsNull.Select(a => a.ApproverId));
                foreach (var approver in approvers)
                {
                    //userSteps.First(ap => ap.Approver.Id == approver.Id).Approver = approver;
                    userSteps.Where(ap => ap.ApproverId == approver.Id).ToList().ForEach(uws => uws.Approver = approver);
                }

                IEnumerable<UserWorkflowStep> RemainingNullApprovers = userSteps.Where(uw => uw.Approver == null);

                if (RemainingNullApprovers.Any())
                {
                    isValid = false;
                    warningMessages.Add("An approver does not exist yet.");
                }
            }


            IEnumerable<int> workflowApproversUsersThatAreNull = userSteps.Where(uw => uw.Approver != null && uw.Approver.User == null).Select(au => au.Approver.UserId);

            IEnumerable<User> users = userRepository.GetUsers(workflowApproversUsersThatAreNull);

            if (users.Any())
            {
                foreach (var u in users)
                {
                    userSteps.Select(ap => ap.Approver).First(us => us.User.Id == u.Id).User = u;
                }

                IEnumerable<User> usersStillNull = userSteps.Where(uw => uw.Approver != null).Select(au => au.Approver.User).Where(u => u == null);


                if (usersStillNull.Any())
                {
                    isValid = false;
                    warningMessages.Add("An approver's user account no longer exists.");
                }
            }

            IEnumerable<User> workflowApproversUsersProfilesThatAreNull = userSteps.Where(uw => uw.Approver != null).Select(au => au.Approver.User).Where(u => u != null && u.UserProfile == null);
            if (workflowApproversUsersProfilesThatAreNull.Any())
            {
                foreach (var user in workflowApproversUsersProfilesThatAreNull)
                {
                    if (!String.IsNullOrWhiteSpace(user.AccountName))
                    {
                        var userProfile = this.GetUserProfile(user.AccountName);
                        if (userProfile != null)
                        {
                            userSteps.Select(ap => ap.Approver).First(us => us.User.Id == user.Id).User.UserProfile = userProfile;
                        }
                        else
                        {
                            isValid = false;
                            if (!String.IsNullOrWhiteSpace(user.AccountName))
                            {
                                warningMessages.Add(user.AccountName.Split('/')[1] + " user account no longer exists.");
                            }
                        }
                    }
                }
            }

            IEnumerable<UserProfile> workflowApproverNoLongerExist = userSteps.Where(x => x.Approver != null && x.Approver.User != null).Select(x => x.Approver.User.UserProfile);
            if (workflowApproverNoLongerExist.Any())
            {
                var NoLongerApproverRoles = workflowApproverNoLongerExist.Where(x => x != null).Select(x => x.Roles);
                foreach (var userRoles in NoLongerApproverRoles)
                {
                    var roleNames = userRoles.Select(x => x.RoleName.ToLower());
                    var checkForApproverRoles = true;
                    foreach (var role in roleNames)
                    {
                        if (role.Contains("approver"))
                        {
                            checkForApproverRoles = false;
                        }
                    }

                    if (checkForApproverRoles)
                    {
                        isValid = false;
                        warningMessages.Add("An approver no longer has access to approve.");
                        break;
                    }
                }
            }

            IEnumerable<UserProfile> workflowApproversUsersProfilesThatAreNotNull = userSteps.Where(uw => uw.Approver != null).Where(au => au.Approver.User != null).Select(p => p.Approver.User.UserProfile).Where(pr => pr != null);
            if (workflowApproversUsersProfilesThatAreNotNull.Any())
            {
                if (workflowApproversUsersProfilesThatAreNotNull.Where(p => p.IsDeactivated).Any())
                {
                    isValid = false;
                    warningMessages.Add("An approver's user account has been deactivated.");
                }

                if (workflowApproversUsersProfilesThatAreNotNull.Where(p => p.SpanOfControl == null || !p.SpanOfControl.Any()).Any())
                {
                    isValid = false;
                    warningMessages.Add("An approver has no Span of Control.");
                }
                else
                {
                    if (hierarchyObjectFacilities == null)
                    {
                        hierarchyObjectFacilities = new List<Tuple<OrganizationalLevelHierarchyType, string, IEnumerable<Facility>>>();
                    }

                    foreach (var userProfile in workflowApproversUsersProfilesThatAreNotNull)
                    {
                        if (!String.IsNullOrWhiteSpace(COID))
                        {
                            //Get facilities for SoC
                            var facilities = new HashSet<string>();
                            foreach (var spanOfControl in userProfile.SpanOfControl)
                            {
                                var hierarchyObjectFacility = hierarchyObjectFacilities.FirstOrDefault(x => x.Item1 == spanOfControl.Hierarchy && x.Item2 == spanOfControl.ObjectId);
                                if (hierarchyObjectFacility == null)
                                {
                                    var socFacilities = GetFacilitiesFromSpanOfControl(spanOfControl.Hierarchy, spanOfControl.ObjectId, username);
                                    hierarchyObjectFacility = new Tuple<OrganizationalLevelHierarchyType, string, IEnumerable<Facility>>(spanOfControl.Hierarchy, spanOfControl.ObjectId, socFacilities);
                                    hierarchyObjectFacilities.Add(hierarchyObjectFacility);
                                }

                                facilities.UnionWith(hierarchyObjectFacility.Item3.Where(f => f != null).Select(f => f.COID));
                            }

                            //Check it
                            //TODO: Change PadLeft function for UK
                            if (facilities.All(x => x != COID.PadLeft(5, '0')))
                            {
                                isValid = false;
                                warningMessages.Add("An approver does not have Span of Control for the facility on this requisition.");
                            }
                        }
                    }
                }
            }

            //Verify has final approver
            if (!userSteps.Where(x => x.IsFinalStep).Any())
            {
                isValid = false;
                warningMessages.Add("No Final Approver.");
            }

            //Verify if workflowType is rush, then has final rush
            if (workflowType == WorkflowTypeEnum.Rush)
            {
                if (!userSteps.Where(x => x.DelegatedByUserId == null && x.IsFinalRushStep).Any())
                {
                    isValid = false;
                    warningMessages.Add("No Final Rush Approver assigned.");
                }
            }

            if (requisitionTotal != null)
            {
                var requesitionerParts = this.GetUserParts(username);
                Approver approver = null;
                if (requesitionerParts.Any(x => x.AppPartType == AppPartType.MyApprovals))
                {
                    approver = this.GetApproverByUserNameAndCOID(username, COID);
                }

                if (workflowType == WorkflowTypeEnum.Capital)
                {
                    if (!this.RequisitionerCapitalMaxApprovalAmountIsGreaterOrEqual(approver, requisitionTotal))
                    {
                        if (!userSteps.Where(x => !x.IsFinalRushStep && x.Approver.CapitalMaxApprovalAmount >= requisitionTotal).Any())
                        {
                            isValid = false;
                            warningMessages.Add("No approvers in user's workflow meet the spend limit.");
                            if (approver != null)
                            {
                                warningMessages.Add("Requisitioner's approval amount does not meet the spend limit.");
                            }
                        }
                    }
                }
                else
                {
                    if (!this.RequisitionerMaxApprovalAmountIsGreaterOrEqual(approver, requisitionTotal))
                    {
                        if (!userSteps.Where(x => !x.IsFinalRushStep && x.Approver.MaxApprovalAmount >= requisitionTotal).Any())
                        {
                            isValid = false;
                            warningMessages.Add("No approvers in user's workflow meet the spend limit.");
                            if (approver != null)
                            {
                                warningMessages.Add("Requisitioner's approval amount does not meet the spend limit.");
                            }
                        }
                    }
                }

            }

            return new WorkflowValidationDTO() { IsValid = isValid, WarningMessages = warningMessages.Distinct().ToList() };
        }

        public WorkflowValidationDTO ValidateUserWorkflow(IEnumerable<UserWorkflowStep> userSteps, string username, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal = null, bool isEmptyOnSetup = false)
        {
            List<Tuple<OrganizationalLevelHierarchyType, string, IEnumerable<Facility>>> hierarchyObjectFacilities = null;
            return this.ValidateUserWorkflow(userSteps, username, workflowType, COID, ref hierarchyObjectFacilities, requisitionTotal, isEmptyOnSetup);
        }

        public WorkflowValidationDTO ValidateUserWorkflow(string userName, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal = null)
        {
            //Get user's steps
            var userSteps = this.GetUserWorkflowSteps(userName, COID, (int)workflowType).ToList();

            //TODO: Get User Profiles?
            List<Tuple<OrganizationalLevelHierarchyType, string, IEnumerable<Facility>>> hierarchyObjectFacilities = null;
            return this.ValidateUserWorkflow(userSteps, userName, workflowType, COID, ref hierarchyObjectFacilities, requisitionTotal, isEmptyOnSetup: false);
        }

        public IEnumerable<WorkflowType> GetAllWorkflowTypes()
        {
            return typeRepository.GetWorkflowTypes();
        }

        public IEnumerable<WorkflowType> GetAllUserWorkflowTypes()
        {
            var allWorkflowTypes = GetAllWorkflowTypes();

            return allWorkflowTypes.Where(x => x.Id != (int)WorkflowTypeEnum.Vendor);
        }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, int? workflowTypeId = null)
        {
            var workflowSteps = new List<UserWorkflowStep>();

            if (workflowTypeId != null)
            {
                workflowSteps = workflowRepository.GetUserWorkflowSteps(userName, COID, (int)workflowTypeId).ToList();
            }
            else
            {
                workflowSteps = workflowRepository.GetUserWorkflowSteps(userName, COID).ToList();
            }

            return workflowSteps;
        }

        public UserSetupWorkflows GetUserSetupWorkflows(string userName, string COID)
        {
            var workflowSteps = workflowRepository.GetUserWorkflowSteps(userName, COID).ToList();

            var user = new UserSetupWorkflows();

            user.HasStdWorkflow = workflowSteps.Any(x => x.WorkflowTypeId == (int)WorkflowTypeEnum.SPR && x.User.AccountName.ToLower() == userName.ToLower());
            user.HasRushWorkflow = workflowSteps.Any(x => x.WorkflowTypeId == (int)WorkflowTypeEnum.Rush && x.User.AccountName.ToLower() == userName.ToLower());
            user.HasCapitalWorkflow = workflowSteps.Any(x => x.WorkflowTypeId == (int)WorkflowTypeEnum.Capital && x.User.AccountName.ToLower() == userName.ToLower());
            user.HasPunchoutWorkflow = workflowSteps.Any(x => x.WorkflowTypeId == (int)WorkflowTypeEnum.PunchOut && x.User.AccountName.ToLower() == userName.ToLower());

            return user;
        }

        public IEnumerable<WorkflowExportDTO> GetUserWorkflowsWithoutDelegates(IEnumerable<string> userNames, string COID, string smartCountryCode)
        {
            var dtoList = new List<WorkflowExportDTO>();
            var workflowSteps = workflowRepository.GetMultipleUsersWorkflowStepsWithoutDelegates(userNames, COID).ToList();
            foreach (var userName in userNames)
            {
                var workflowStringList = new List<string>();
                var userWorkFlowExportDTO = new WorkflowExportDTO() { UserName = userName };
                foreach (WorkflowTypeEnum wfType in Enum.GetValues(typeof(WorkflowTypeEnum)))
                {
                    if (wfType != WorkflowTypeEnum.NotApplicable)
                    {
                        var workflowString = "";
                        var steps = workflowSteps.Where(x => x.WorkflowTypeId == (int)wfType && x.User.AccountName.ToLower() == userName.ToLower()).OrderBy(y => y.Step).ToList();
                        foreach (var step in steps)
                        {
                            string newWorkflowString;
                            if (step.Approver.IsCERReviewer)
                            {
                                newWorkflowString = step.Approver.User.FirstName + " " + step.Approver.User.LastName + ", " + step.Approver.User.AccountName.Split('/')[1] + ", HVS Approver]";
                            }
                            else
                            {
                                if (wfType == WorkflowTypeEnum.Capital)
                                {
                                    newWorkflowString = step.Approver.User.FirstName + " " + step.Approver.User.LastName + ", " + step.Approver.User.AccountName.Split('/')[1] + ", " + step.Approver.CapitalMaxApprovalAmount.ToString("C", LocationMapper.GetCultureForFormattingUsingSmartCountryCode(smartCountryCode)) + "]";
                                }
                                else
                                {
                                    newWorkflowString = step.Approver.User.FirstName + " " + step.Approver.User.LastName + ", " + step.Approver.User.AccountName.Split('/')[1] + ", " + step.Approver.MaxApprovalAmount.ToString("C", LocationMapper.GetCultureForFormattingUsingSmartCountryCode(smartCountryCode)) + "]";
                                }
                            }

                            if (!step.Equals(steps.Last()))
                            {
                                newWorkflowString += ",";
                            }

                            if (step.IsFinalStep)
                            {
                                newWorkflowString = "[Final, " + newWorkflowString;
                            }
                            else if (step.IsFinalRushStep)
                            {
                                newWorkflowString = "[Final Rush, " + newWorkflowString;
                            }
                            else
                            {
                                newWorkflowString = "[Step " + step.Step + ", " + newWorkflowString;
                            }

                            if (!step.Equals(steps.First()))
                            {
                                newWorkflowString = " " + newWorkflowString;
                            }

                            workflowString += newWorkflowString;
                        }
                        if ((int)wfType >= workflowStringList.Count())
                        {
                            workflowStringList.Add(workflowString);
                        }
                        else
                        {
                            workflowStringList.Insert((int)wfType, workflowString);
                        }
                    }
                }
                userWorkFlowExportDTO.UserName = userName;
                userWorkFlowExportDTO.userWorkflowStrings = workflowStringList;
                dtoList.Add(userWorkFlowExportDTO);
            }
            return dtoList;
        }

        public void SaveUserEditInfo(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO)
        {
            var approverList = new List<Approver>();
            approverList.Add(saveUserEditInfoDTO.approver);
            var saveWorkflowsDTO = new SaveWorkflowsDTO() { workflows = saveUserEditInfoDTO.workflows };

            this.SaveWorkflows(userName, saveWorkflowsDTO);
            this.UpdateApprovers(userName, approverList, saveUserEditInfoDTO.COID);
        }

        public async Task<bool> SaveUserEditInfoAsync(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO)
        {
            var approverList = new List<Approver>();
            approverList.Add(saveUserEditInfoDTO.approver);
            var saveWorkflowsDTO = new SaveWorkflowsDTO() { workflows = saveUserEditInfoDTO.workflows };

            this.SaveWorkflows(userName, saveWorkflowsDTO);
            await this.UpdateApproversAsync(userName, approverList, saveUserEditInfoDTO.COID);
            return true;
        }

        public IEnumerable<UserWorkflowDTO> SaveWorkflows(string updater, SaveWorkflowsDTO saveWorkflowsDTO)
        {
            var returnList = saveWorkflowsDTO.workflows;
            List<Requisition> pendingRequisitionsForUser = null;
            foreach (var workflow in returnList)
            {
                workflow.UserworkflowSteps = this.SaveUserWorkflowSteps(updater, workflow.UserName, workflow.WorkflowTypeId, workflow.COID, workflow.UserworkflowSteps, ref pendingRequisitionsForUser);
            }
            pendingRequisitionsForUser = null;
            return returnList;
        }

        // TODO Danger, danger! This method is an infinite loop!
        public IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string updater, string userName, int workflowTypeId, string COID, IEnumerable<UserWorkflowStep> userworkflowSteps)
        {
            return this.SaveUserWorkflowSteps(updater, userName, workflowTypeId, COID, null);
        }

        private IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string updater, string userName, int workflowTypeId, string COID, IEnumerable<UserWorkflowStep> userworkflowSteps, ref List<Requisition> pendingRequisitionsForUser)
        {
            if (userworkflowSteps != null)
            {
                userworkflowSteps.ToList().ForEach(x => x.Approver = userRepository.GetApprover(x.ApproverId));
            }

            if (pendingRequisitionsForUser == null)
            {
                pendingRequisitionsForUser = requisitionRepository.GetPendingRequisitionsForUserCOID(userName, COID);
            }

            //Get original Workflow and requisitions in that workflow
            var originalWorkflowSteps = this.GetUserWorkflowSteps(userName, COID, workflowTypeId);
            IEnumerable<Requisition> requisitionsByWorkflow = pendingRequisitionsForUser.Where(x => (int)x.ApplicableWorkflowType == workflowTypeId).Distinct();

            if (userworkflowSteps.Any())
            {
                userworkflowSteps = this.CheckDelegateStepsAreUpToDate(userName, userworkflowSteps);
            }

            var vendors = new List<VendorHeaderInfo>();

            //Adjust steps in requisitions before 
            foreach (var req in requisitionsByWorkflow)
            {
                foreach (var ri in req.RequisitionItems)
                {
                    if (ri.SPRDetail != null && (ri.SPRDetail.Vendor == null || string.IsNullOrEmpty(ri.SPRDetail.Vendor.Name) || ri.SPRDetail.Vendor.Name == "VENDOR NOT FOUND"))
                    {
                        if (!vendors.Any())
                        {
                            vendors = vendorSvc.GetAllVendorsForCoid(COID);
                        }
                        var vendor = vendors.Where(x => x.Id == ri.SPRDetail.VendorId).FirstOrDefault();
                        if (vendor == null)
                        {
                            log.Error(string.Format("Vendor not found for Coid: {0}, VendorId: {1}", COID, ri.SPRDetail.VendorId));
                            ri.SPRDetail.Vendor = new Vendor { Id = (int)ri.SPRDetail.VendorId, Name = "VENDOR NOT FOUND" };
                        }
                        else
                        {
                            ri.SPRDetail.Vendor = new Vendor { Id = vendor.Id, Name = vendor.Name };
                        }
                    }
                }

                this.AdjustRequisitionStepsOnWorkflowSave(updater, req, originalWorkflowSteps, userworkflowSteps);
            }

            workflowRepository.SaveUserWorkflowSteps(userName, workflowTypeId, COID, userworkflowSteps).ToList();

            foreach (var req in requisitionsByWorkflow.Where(x => x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval))
            {
                workflowService.ReInitializeWorkflowByRequisition(req, userName);
                requisitionRepository.UpdateRequisitionOnReorderWorkflowAndAddHistory(req, updater);
            }

            return userworkflowSteps;
        }

        public async Task<List<RequisitionStatusHistory>> ReorderMultipleWorkflowStepsAsync(string updater, string approverUserName, bool changeCapitals, bool changeOthers, string coid)
        {
            var requisitionStatusHistories = new List<RequisitionStatusHistory>();

            if (!changeCapitals && !changeOthers) return requisitionStatusHistories;

            var workflowSteps = workflowRepository.GetApproverWorkflowStepsList(approverUserName, coid).ToList();

            var distinctWorkflow = workflowSteps.Select(x => new { x.User.AccountName, x.UserId, x.WorkflowTypeId, COID = coid }).Distinct();

            if (changeCapitals && !changeOthers)
            {
                distinctWorkflow = distinctWorkflow.Where(x => x.WorkflowTypeId == (int)WorkflowTypeEnum.Capital);
            }
            else if (!changeCapitals && changeOthers)
            {
                distinctWorkflow = distinctWorkflow.Where(x => x.WorkflowTypeId != (int)WorkflowTypeEnum.Capital);
            }

            foreach (var distinct in distinctWorkflow)
            {
                var distinctWorkflowSteps = workflowRepository.GetDistinctWorkflowSteps(distinct.UserId, distinct.WorkflowTypeId, distinct.COID);
                var statusHistories = ReorderWorkflowStepsReturnRequisitionStatusHistory(updater, approverUserName, distinct.AccountName, distinct.WorkflowTypeId, coid, distinctWorkflowSteps);

                requisitionStatusHistories.AddRange(statusHistories);
            };

            return await Task.FromResult(requisitionStatusHistories);
        }

        public void ReorderMultipleWorkflowSteps(string updater, string approverUserName, bool changeCapitals, bool changeOthers, string COID)
        {
            if (changeCapitals || changeOthers)
            {
                List<UserWorkflowStep> workflowSteps = workflowRepository.GetApproverWorkflowSteps(approverUserName, COID).ToList();

                var distinctWorkflow = workflowSteps.Select(x => new { x.User.AccountName, x.UserId, x.WorkflowTypeId, COID }).Distinct();

                if (changeCapitals && !changeOthers)
                {
                    distinctWorkflow = distinctWorkflow.Where(x => x.WorkflowTypeId == (int)WorkflowTypeEnum.Capital);
                }
                else if (!changeCapitals && changeOthers)
                {
                    distinctWorkflow = distinctWorkflow.Where(x => x.WorkflowTypeId != (int)WorkflowTypeEnum.Capital);
                }

                foreach (var distinct in distinctWorkflow)
                {
                    var distinctWorkflowSteps = workflowRepository.GetDistinctWorkflowSteps(distinct.UserId, distinct.WorkflowTypeId, distinct.COID);
                    this.ReorderWorkflowSteps(updater, approverUserName, distinct.AccountName, distinct.WorkflowTypeId, COID, distinctWorkflowSteps);
                }
            }
        }

        public void SaveFacilityWorkflowSteps(SaveFacilityWorkflowDTO request)
        {
            if (request.Workflow.Steps != null)
            {
                request.Workflow.Steps.ToList().ForEach(x => x.Approver = userRepository.GetApprover(x.ApproverId));
            }

            var pendingVendorRequisitions = requisitionRepository.GetPendingRequisitionsByFacilityWorkflow(request.Workflow.Coid);
            var originalWorkflowSteps = _facilityWorkflowService.Get(request.Workflow.Coid, WorkflowTypeEnum.Vendor);

            if (request.Workflow.Steps.Any())
            {
                request.Workflow.Steps = this.CheckDelegateStepsAreUpToDate(request.Username, request.Workflow.Steps);
            }

            var vendors = new List<VendorHeaderInfo>();

            //Adjust steps in requisitions before 
            foreach (var req in pendingVendorRequisitions)
            {
                this.AdjustRequisitionStepsOnFacilityWorkflowSave(request.Username, req, originalWorkflowSteps.Steps, request.Workflow.Steps);
            }

            _facilityWorkflowService.Save(request);
        }

        private IEnumerable<RequisitionStatusHistory> ReorderWorkflowStepsReturnRequisitionStatusHistory(string updater, string approverUsername, string userName, int workflowTypeId, string coid, IReadOnlyCollection<UserWorkflowStep> workflowSteps)
        {
            var requisitionStatusHistories = new List<RequisitionStatusHistory>();
            if (workflowSteps == null || !workflowSteps.Any()) return requisitionStatusHistories;

            var originalWorkflowSteps = workflowSteps.Select(workflow => workflow.CreateDeepCopy(workflow)).ToList();
            var steps = workflowSteps.Where(x => x.IsFinalStep == false && x.IsFinalRushStep == false && x.Approver != null && x.DelegatedByUserId == null);
            var delegatedWorkflowSteps = workflowSteps.Where(x => x.DelegatedByUserId != null && x.Approver != null).ToList();

            if (workflowTypeId == (int)WorkflowTypeEnum.Capital)
            {
                steps = steps.OrderBy(x => x.Approver.CapitalMaxApprovalAmount).ThenBy(x => x.Step).ToList();
                delegatedWorkflowSteps = delegatedWorkflowSteps.OrderBy(x => x.Approver.CapitalMaxApprovalAmount).ThenBy(x => x.Step).ToList();
            }
            else
            {
                steps = steps.OrderBy(x => x.Approver.MaxApprovalAmount).ThenBy(x => x.Step).ToList();
                delegatedWorkflowSteps = delegatedWorkflowSteps.OrderBy(x => x.Approver.MaxApprovalAmount).ThenBy(x => x.Step).ToList();
            }

            var i = 1;
            foreach (var step in steps)
            {
                step.Step = i++;
                var delegateWorkflowStep = delegatedWorkflowSteps.FirstOrDefault(x => x.DelegatedByUserId == step.Approver.UserId);
                if (delegateWorkflowStep != null)
                {
                    delegateWorkflowStep.Step = step.Step;
                }

            }

            var finalSteps = workflowSteps.Where(x => x.IsFinalStep);
            var finalRushSteps = workflowSteps.Where(x => x.IsFinalRushStep);

            var userWorkflowSteps = finalSteps.ToList();
            foreach (var step in userWorkflowSteps)
            {
                step.Step = i;
                var delegateWorkflowStep = delegatedWorkflowSteps.FirstOrDefault(x => x.DelegatedByUserId == step.Approver.UserId && x.IsFinalStep);
                if (delegateWorkflowStep != null)
                {
                    delegateWorkflowStep.Step = step.Step;
                }
            }

            if (workflowTypeId == 1)
            {
                i++;
                foreach (var step in finalRushSteps)
                {
                    step.Step = i;
                    var delegateWorkflowStep = delegatedWorkflowSteps.FirstOrDefault(x => x.DelegatedByUserId == step.Approver.UserId && x.IsFinalRushStep);
                    if (delegateWorkflowStep != null)
                    {
                        delegateWorkflowStep.Step = step.Step;
                    }
                }
            }

            workflowRepository.SaveUserWorkflowSteps(userName, workflowTypeId, coid, workflowSteps);

            var stepsCanAffect = workflowRepository.GetApproverWorkflowSteps(approverUsername, coid);
            var requisitions = requisitionRepository.GetRequisitionsByWorkflowOwners(userName, stepsCanAffect, coid);
            var requisitionsByWorkflow = requisitions.Where(x => (int)x.ApplicableWorkflowType == workflowTypeId).Distinct();

            foreach (var requisition in requisitionsByWorkflow)
            {
                var maximumAmount = workflowSteps.Select(x => x.Approver.MaxApprovalAmount).Max();
                if (workflowTypeId == (int)WorkflowTypeEnum.Capital)
                {
                    maximumAmount = workflowSteps.Select(x => x.Approver.CapitalMaxApprovalAmount).Max();
                }

                var requiredTotal = this.GetRequisitionTotal(requisition);
                if (requiredTotal > maximumAmount &&
                    requisition.ApprovalStep < userWorkflowSteps.Select(x => x.Step).FirstOrDefault() &&
                    requisition.ApprovedAmount < requiredTotal)
                {
                    this.DenyRequisition(requisition,
                        "No approver exists in workflow that meets requisition total amount.", updater);
                }
                else
                {
                    var workflowStep =
                        originalWorkflowSteps.FirstOrDefault(x => x.Step == requisition.ApprovalStep);
                    var changedWorkflowStep = workflowSteps.FirstOrDefault(x => x.Step == 1);
                    if (workflowStep != null) // Checks for bad date, if there is, then reset to first step.
                    {
                        changedWorkflowStep = workflowSteps.FirstOrDefault(x => x.Id == workflowStep.Id);
                    }

                    var originalStep = requisition.ApprovalStep;
                    requisition.ApprovalStep = changedWorkflowStep?.Step;

                    if (changedWorkflowStep != null &&
                        (changedWorkflowStep.WorkflowTypeId == (int)WorkflowTypeEnum.Rush &&
                         originalStep != changedWorkflowStep.Step && requisition.WorkflowInstanceId != null))
                    {
                        workflowService.ReInitializeWorkflowByRequisition(requisition.RequisitionId, userName);
                    }

                    requisition.DenyDelegateByApproverId = null;

                    requisitionStatusHistories.Add(requisitionRepository.BuildRequisitionStatusHistory(requisition,
                        updater, (int)RequisitionStatusTypeEnum.WorkflowChanged));
                    requisitionStatusHistories.Add(requisitionRepository.BuildRequisitionStatusHistory(requisition,
                        "System", requisition.RequisitionStatusTypeId));
                }
            }
            return requisitionStatusHistories;
        }


        private void ReorderWorkflowSteps(string updater, string approverUsername, string userName, int workflowTypeId, string COID, List<UserWorkflowStep> workflowSteps)
        {
            if (workflowSteps != null && workflowSteps.Any())
            {
                List<UserWorkflowStep> originalWorkflowSteps = new List<UserWorkflowStep>();
                foreach (var workflow in workflowSteps)
                {
                    originalWorkflowSteps.Add(workflow.CreateDeepCopy(workflow));
                }

                var steps = workflowSteps.Where(x => x.IsFinalStep == false && x.IsFinalRushStep == false && x.Approver != null && x.DelegatedByUserId == null);
                var delegatedWorkflowSteps = workflowSteps.Where(x => x.DelegatedByUserId != null && x.Approver != null);

                if (workflowTypeId == (int)WorkflowTypeEnum.Capital)
                {
                    steps = steps.OrderBy(x => x.Approver.CapitalMaxApprovalAmount).ThenBy(x => x.Step).ToList();
                    delegatedWorkflowSteps = delegatedWorkflowSteps.OrderBy(x => x.Approver.CapitalMaxApprovalAmount).ThenBy(x => x.Step).ToList();
                }
                else
                {
                    steps = steps.OrderBy(x => x.Approver.MaxApprovalAmount).ThenBy(x => x.Step).ToList();
                    delegatedWorkflowSteps = delegatedWorkflowSteps.OrderBy(x => x.Approver.MaxApprovalAmount).ThenBy(x => x.Step).ToList();
                }

                int i = 1;
                foreach (var step in steps)
                {
                    step.Step = i++;
                    var delegateWorkflowStep = delegatedWorkflowSteps.FirstOrDefault(x => x.DelegatedByUserId == step.Approver.UserId);
                    if (delegateWorkflowStep != null)
                    {
                        delegateWorkflowStep.Step = step.Step;
                    }

                }

                var finalSteps = workflowSteps.Where(x => x.IsFinalStep);
                var finalRushSteps = workflowSteps.Where(x => x.IsFinalRushStep);

                foreach (var step in finalSteps)
                {
                    step.Step = i;
                    var delegateWorkflowStep = delegatedWorkflowSteps.FirstOrDefault(x => x.DelegatedByUserId == step.Approver.UserId && x.IsFinalStep);
                    if (delegateWorkflowStep != null)
                    {
                        delegateWorkflowStep.Step = step.Step;
                    }
                }

                if (workflowTypeId == 1)
                {
                    i++;
                    foreach (var step in finalRushSteps)
                    {
                        step.Step = i;
                        var delegateWorkflowStep = delegatedWorkflowSteps.FirstOrDefault(x => x.DelegatedByUserId == step.Approver.UserId && x.IsFinalRushStep);
                        if (delegateWorkflowStep != null)
                        {
                            delegateWorkflowStep.Step = step.Step;
                        }
                    }
                }

                workflowRepository.SaveUserWorkflowSteps(userName, workflowTypeId, COID, workflowSteps);

                var stepsCanAffect = workflowRepository.GetApproverWorkflowSteps(approverUsername, COID);
                var requisitions = requisitionRepository.GetRequisitionsByWorkflowOwners(userName, stepsCanAffect, COID);
                var requisitionsByWorkflow = requisitions.Where(x => (int)x.ApplicableWorkflowType == workflowTypeId).Distinct();

                foreach (var requisition in requisitionsByWorkflow)
                {
                    var maximumAmount = workflowSteps.Select(x => x.Approver.MaxApprovalAmount).Max();
                    if (workflowTypeId == (int)WorkflowTypeEnum.Capital)
                    {
                        maximumAmount = workflowSteps.Select(x => x.Approver.CapitalMaxApprovalAmount).Max();
                    }
                    var requiredTotal = this.GetRequisitionTotal(requisition);
                    if (requiredTotal > maximumAmount && requisition.ApprovalStep < finalSteps.Select(x => x.Step).FirstOrDefault() && requisition.ApprovedAmount < requiredTotal)
                    {
                        this.DenyRequisition(requisition, "No approver exists in workflow that meets requisition total amount.", updater);
                    }
                    else
                    {
                        var workflowStep = originalWorkflowSteps.FirstOrDefault(x => x.Step == requisition.ApprovalStep);
                        var changedWorkflowStep = workflowSteps.FirstOrDefault(x => x.Step == 1);
                        if (workflowStep != null) // Checks for bad data, if there is, then reset to first step.
                        {
                            changedWorkflowStep = workflowSteps.FirstOrDefault(x => x.Id == workflowStep.Id);
                        }
                        var originalStep = requisition.ApprovalStep;
                        requisition.ApprovalStep = changedWorkflowStep?.Step;

                        //If it's rush, then reset the approval workflow (Uncomment for sandbox)
                        if (changedWorkflowStep != null && (changedWorkflowStep.WorkflowTypeId == (int)WorkflowTypeEnum.Rush && originalStep != changedWorkflowStep.Step && requisition.WorkflowInstanceId != null))
                        {
                            workflowService.ReInitializeWorkflowByRequisition(requisition.RequisitionId, userName);
                        }

                        requisitionRepository.UpdateRequisitionOnReorderWorkflowAndAddHistory(requisition, updater);
                    }
                }
            }
        }

        private StatusUpdateDTO DenyRequisition(Requisition requisition, string comment, string denierName)
        {
            var denierUser = userRepository.GetUser(denierName);
            if (denierUser == null)
            {
                throw new ArgumentException("No user in database with this accountName.");
            }

            workflowService.DenyRequisition(requisition, denierUser.Id);

            if (String.IsNullOrWhiteSpace(requisition.Comments))
            {
                requisition.Comments = comment;
            }
            else
            {
                requisition.Comments = requisition.Comments + System.Environment.NewLine + comment;
            }

            //Save new "Denied" status            
            requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.Denied;
            requisitionRepository.UpdateRequisition(requisition, "System", true, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);

            var profile = this.GetProfile(requisition.CreatedBy);
            if (profile != null)
            {
                var emailRequest = new EmailRequest()
                {
                    EmailType = EmailType.RequisitionDenied,
                    Emails = new string[1] { profile.Email },
                    ViewLink = this.GetRequisitionUrl(requisition.RequisitionId),
                    SenderName = profile.FullName,
                    Requisition = requisition,
                    Comments = requisition.Comments,
                    ActionDate = requisition.CreateDate
                };
                emailService.SendEmail(emailRequest);

            }

            return new StatusUpdateDTO() { IsStatusUpdated = true };
        }

        private void AdjustRequisitionStepsOnWorkflowSave(string updater, Requisition requisition, IEnumerable<UserWorkflowStep> originalWorkflowSteps, IEnumerable<UserWorkflowStep> changedWorkflowSteps)
        {
            var currentStep = requisition.ApprovalStep;
            var sendEmail = true;
            var denied = false;

            if (!originalWorkflowSteps.Any()) // Current step should be 1 because an empty workflow denies requisition
            {
                requisition.ApprovalStep = 1;
            }
            else if (!changedWorkflowSteps.Any())
            {
                denied = this.DenyRequisition(requisition, "Workflow has been cleared. Please set up a new workflow.", updater).IsStatusUpdated;
                sendEmail = false;
            }
            else
            {
                var lastApproverStep = 0;
                var lastApprover = changedWorkflowSteps.Where(x => !x.IsFinalStep && !x.IsFinalRushStep).OrderBy(x => x.Step).LastOrDefault();
                if (lastApprover != null)
                {
                    lastApproverStep = lastApprover.Step;
                }

                var originalApproverWorkflowStep = originalWorkflowSteps.Where(x => x.Step == currentStep).FirstOrDefault();
                if (originalApproverWorkflowStep == null)
                {
                    //only should get here if BAD DATA and current step of requisition does not exist in Original Workflow
                    requisition.ApprovalStep = 1;
                }
                else
                {
                    var setWorkFlowStep = new UserWorkflowStep();
                    var originalApproverInChangedWorkflowStep = changedWorkflowSteps.Where(x => x.ApproverId == originalApproverWorkflowStep.ApproverId).FirstOrDefault();
                    var requisitionTotal = this.GetRequisitionTotal(requisition);

                    bool reqTotalCanBeMet = true;

                    //check the respective approval amounts in the workflow
                    if (requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital)
                    {
                        if (changedWorkflowSteps.Where(x => x.Approver.CapitalMaxApprovalAmount >= requisitionTotal).FirstOrDefault() == null)
                        {
                            reqTotalCanBeMet = false;
                        }
                    }
                    else
                    {
                        if (changedWorkflowSteps.Where(x => x.Approver.MaxApprovalAmount >= requisitionTotal).FirstOrDefault() == null)
                        {
                            reqTotalCanBeMet = false;
                        }
                    }

                    //Check if Approver at currentStep in OriginalWorkflow is Final/FinalRush
                    if (originalApproverWorkflowStep.IsFinalStep || originalApproverWorkflowStep.IsFinalRushStep)
                    {
                        //Find final/final rush in changed and keep at step
                        if (originalApproverWorkflowStep.IsFinalStep)
                        {
                            setWorkFlowStep = changedWorkflowSteps.Where(x => x.IsFinalStep).FirstOrDefault();
                        }
                        else
                        {
                            setWorkFlowStep = changedWorkflowSteps.Where(x => x.IsFinalRushStep).FirstOrDefault();
                        }

                        requisition.ApprovalStep = setWorkFlowStep.Step;
                    }
                    // going to check if capital or not and then check approvers' appropriate amounts to see if denial is needed
                    else if (!reqTotalCanBeMet)
                    {
                        denied = this.DenyRequisition(requisition, "No approver exists in workflow that meets requisition total amount.", updater).IsStatusUpdated;
                        sendEmail = false;
                    }
                    //Check if Approver at currentStep exists in ChangedWorkflow AND is not in FinalStep or FinalRushStep within ChangedWorkflow
                    else if (originalApproverInChangedWorkflowStep != null && !originalApproverInChangedWorkflowStep.IsFinalStep && !originalApproverInChangedWorkflowStep.IsFinalRushStep)
                    {
                        //Follow that Approver
                        requisition.ApprovalStep = originalApproverInChangedWorkflowStep.Step;
                        sendEmail = false;
                    }
                    else //If approver does not exist keep at step OR if current step exceeds number of Approver Steps move to an approver that meets Requisition Approval amt
                    {
                        if (currentStep > lastApproverStep)
                        {
                            setWorkFlowStep = changedWorkflowSteps.Where(x => x.Approver.MaxApprovalAmount >= requisitionTotal).OrderBy(x => x.Approver.MaxApprovalAmount).FirstOrDefault(); //Will first be the highest step (lowest max approval amt of approvers)
                            if (setWorkFlowStep != null)
                            {
                                requisition.ApprovalStep = setWorkFlowStep.Step;
                            }
                        }
                    }
                }
            }

            var changedPeople = changedWorkflowSteps.Where(x => x.Step == requisition.ApprovalStep);
            var originalPeople = originalWorkflowSteps.Where(y => y.Step == requisition.ApprovalStep);
            var newlyAddedPeople = changedPeople.Where(x => !originalPeople.Any(y => y.ApproverId == x.ApproverId));
            var emailAddresses = new List<string>();
            foreach (var newlyAddedPerson in newlyAddedPeople)
            {
                if (newlyAddedPerson.Approver.User == null)
                {
                    newlyAddedPerson.Approver.User = userRepository.GetUser(newlyAddedPerson.Approver.UserId);
                }
                emailAddresses.AddRange(GetApproverEmails(newlyAddedPerson?.Approver?.User?.AccountName));

            }
            SendEmail(sendEmail, requisition, emailAddresses);
        }

        private void AdjustRequisitionStepsOnFacilityWorkflowSave(string updater, Requisition requisition, IEnumerable<FacilityWorkflowStep> originalWorkflowSteps, IEnumerable<FacilityWorkflowStep> changedWorkflowSteps)
        {
            var currentStep = requisition.ApprovalStep;
            var sendEmail = true;
            var denied = false;

            if (!changedWorkflowSteps.Any())
            {
                denied = this.DenyRequisition(requisition, "Workflow has been cleared. Please set up a new workflow.", updater).IsStatusUpdated;
                sendEmail = false;
            }

            var newlyAddedPeople = changedWorkflowSteps.Where(x => !originalWorkflowSteps.Any(y => y.ApproverId == x.ApproverId));
            var emailAddresses = new List<string>();
            foreach (var newlyAddedPerson in newlyAddedPeople)
            {
                if (newlyAddedPerson.Approver.User == null)
                {
                    newlyAddedPerson.Approver.User = userRepository.GetUser(newlyAddedPerson.Approver.UserId);
                }
                emailAddresses.AddRange(GetApproverEmails(newlyAddedPerson?.Approver?.User?.AccountName));
            }
            SendEmail(sendEmail, requisition, emailAddresses);
        }

        private void SendEmail(bool sendEmail, Requisition requisition, IEnumerable<string> emailAddresses)
        {
            if (sendEmail)
            {
                var summaryComments = requisition.Comments;
                string vendors = GetVendorsForEmailTemplate(requisition);
                var emailRequest = new EmailRequest()
                {
                    EmailType = EmailType.PendingApproval,
                    Emails = emailAddresses.ToArray(),
                    ViewLink = this.GetApprovalUrl(requisition.RequisitionId),
                    SenderName = this.GetUserFullName(requisition.CreatedBy),
                    Requisition = requisition,
                    Vendors = vendors,
                    Comments = summaryComments,
                    ActionDate = requisition.CreateDate
                };

                if (requisition.IsRush)
                {
                    emailRequest.EmailType = EmailType.PendingApprovalRush;
                }
                if (requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.OnHold)
                {
                    emailRequest.EmailType = EmailType.PendingApprovalOnHold;
                }

                emailService.SendEmail(emailRequest);
            }
        }

        public string GetVendorsForEmailTemplate(Requisition requisition)
        {
            string vendors = "";
            foreach (var reqItem in requisition.RequisitionItems)
            {
                if (reqItem != null)
                {
                    var vendorInfo = "";
                    if(reqItem.SPRDetail != null&&reqItem.SPRDetail.Vendor != null)
                    {
                      vendorInfo=reqItem.SPRDetail.Vendor.Name.Trim() + " (" + reqItem.SPRDetail.Vendor.Id.ToString() + ")<br />";
                    }
                    else if (reqItem.VendorName != null)
                    {
                      vendorInfo = reqItem.VendorName.Trim() + " (" + reqItem.VendorId.ToString() + ")<br />";
                    }
                    else
                    {
                        string vendorID = "";
                        if (reqItem.SPRDetail != null)
                            {
                              vendorID = reqItem.SPRDetail.Vendor.Id.ToString();
                            }
                        else
                            {
                              vendorID = reqItem.VendorId.ToString();
                            }
                        vendorInfo = "VENDOR NOT FOUND"+" (" + vendorID + ")<br />";
                        log.Error(string.Format("Vendor not found for Coid: {0}, VendorId:{1}", LocationMapper.GetCOID(requisition.LocationIdentifier), vendorID));
                    }

                    if (!vendors.Contains(vendorInfo))
                      vendors += vendorInfo;
                }
            }

            return vendors;
        }

        public string GetUserFullName(string createdBy)
        {
            var user = userRepository.GetUser(createdBy);
            if (user != null)
            {
                return user.FirstName + " " + user.LastName;
            }
            else
            {
                var profile = this.GetProfile(createdBy);
                if (profile != null)
                {
                    return profile.FullName;
                }
                else
                {
                    return createdBy;
                }
            }
        }

        private string[] GetApproverEmails(string userName)
        {
            var emails = new List<String>();
            if (userName != null && userName.IndexOf('/') != -1)
            {
                var approverUserProfile = this.GetProfile(userName);
                if (approverUserProfile != null)
                {
                    emails.Add(approverUserProfile.Email);
                }
            }

            if (emails.Any())
            {
                emails = emails.Distinct().ToList();
            }

            return emails.ToArray();
        }

        private string GetRequisitionUrl(int requisitionId)
        {
            return eproEndpoint + "MyRequisition/" + requisitionId.ToString();
        }

        private string GetApprovalUrl(int requisitionId)
        {
            return eproEndpoint + "MyApproval/" + requisitionId.ToString();
        }

        private UserProfile GetUserProfile(string domainSlashUserName)
        {
            if (String.IsNullOrWhiteSpace(domainSlashUserName) || domainSlashUserName.IndexOf('/') == 0)
            {
                return null;
            }

            var nameParts = domainSlashUserName.Split('/');

            if (nameParts == null || nameParts.Count() < 2)
            {
                throw new ArgumentException("Invalid domainSlashUserName passed, no slash found, PARAMETER = " + domainSlashUserName);
            }

            return this.GetUserProfile(nameParts[0], nameParts[1]);
        }

        private UserProfile GetUserProfile(string domain, string userName)
        {
            try
            {
                var url = newSecurityEndpoint + userName + "/applications/" + appKey;
                var userInfo = new UserProfile();
                userInfo = ApiUtility.ExecuteSecurityTokenApiGetTo<UserProfile>(url, null, GetSecurityToken());
                
                //Ensure SoC is properly established
                if (userInfo.SpanOfControl == null)
                {
                    userInfo.SpanOfControl = new List<SpanOfControl>();
                }

                if (userInfo.SpanOfControl.Any())
                {
                    userInfo.SpanOfControl.AddRange(_testCoidSpanOfControls);
                }

                if(userInfo.UserType.ToLower() == "vendor")
                {
                    if (userInfo.UserType.ToLower() == "vendor")
                    {
                        //parses out email domain after @ and assigns to user.Domain
                        userInfo.Domain = userInfo.Email.Split('@')[1];
                    }
                }

                //Check for primary facility, add in if not already there
                if (!String.IsNullOrWhiteSpace(userInfo.PrimaryFacility) && !userInfo.SpanOfControl.Where(x => x.Hierarchy == OrganizationalLevelHierarchyType.Facility && x.ObjectId.ToLower() == userInfo.PrimaryFacility.ToLower()).Any())
                {
                    userInfo.SpanOfControl.Add(new SpanOfControl() { Hierarchy = OrganizationalLevelHierarchyType.Facility, ObjectId = userInfo.PrimaryFacility });
                }

                return userInfo;
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error retrieving user profile for {0}", userName), ex);
                return null;
            }
        }

        private IEnumerable<Facility> GetFacilitiesFromSpanOfControl(OrganizationalLevelHierarchyType hierarchy, string organizationId, string username)
        {
            //If facility already, just need details
            if (hierarchy == OrganizationalLevelHierarchyType.Facility)
            {
                return new List<Facility>() { this.GetFacility(username, organizationId) };
            }

            var facilities = ApiUtility.ExecuteApiGetTo<IEnumerable<Facility>>(homeAPIEndpoint, getChildCoidsMethod, new Dictionary<string, string>()
                                                                                                            {
                                                                                                                { "Hierarchy", ((int)hierarchy).ToString() },
                                                                                                                { "parent", organizationId }
                                                                                                            });

            return facilities;
        }

        private Facility GetFacility(string username, string coid)
        {
            if (String.IsNullOrWhiteSpace(coid))
            {
                return null;
            }

            if (coid.Length < 5)
            {
                coid = coid.PadLeft(5, '0');
            }

            if (_testCoidSpanOfControls.Select(x => x.ObjectId.ToLowerInvariant()).Contains(coid.ToLowerInvariant()))
            {
                return new Facility(smartCOIDService.GetCOID(username, coid));
            }

            return ApiUtility.ExecuteApiGetTo<Facility>(homeAPIEndpoint, getFacilityMethod, new Dictionary<string, string>() { { "cOID", coid } });
        }

        public void AssignDelegateForApprover(int delegateUserId, string delegateEmail, string userName)
        {
            if (!userRepository.IsApproverByUserName(userName))
            {
                throw new ArgumentException("The userName passed in is not a valid approver");
            }

            var user = userRepository.GetUser(userName);

            var delegatedSteps = workflowService.GetUserWorkflowStepsDelegatedByUserId(user.Id);

            if (delegatedSteps != null && delegatedSteps.Any())
            {
                workflowRepository.DeleteDelegatedSteps(user.Id);
            }

            _facilityWorkflowService.DeleteDelegatedSteps(user.Id);

            var delegateUser = userRepository.GetUser(delegateUserId);
            delegateUser.UserProfile = this.GetUserProfile(delegateUser.AccountName);
            if (delegateUser.FirstName != delegateUser.UserProfile.FirstName || delegateUser.LastName != delegateUser.UserProfile.LastName)
            {
                this.UpdateUserName(delegateUser.AccountName, delegateUser.UserProfile.FirstName, delegateUser.UserProfile.LastName);
            }

            var approvers = userRepository.GetApproversByUserId(user.Id);
            foreach (var approver in approvers.ToList())
            {
                var delegateApprover = userRepository.GetApproverByUserId(delegateUserId, approver.COID) ?? userRepository.InsertApprover(new Approver()
                {
                    User = delegateUser,
                    UserId = delegateUser.Id,
                    MaxApprovalAmount = 0,
                    CapitalMaxApprovalAmount = 0,
                    IsActive = true,
                    CreatedBy = userName,
                    CreateDate = DateTime.Now,
                    COID = approver.COID
                }, null);

                approver.Delegate = delegateApprover.Id;
                userRepository.UpdateApprover(approver, "Assign delegate", userName);
            }

            var sendEmail = false;

            //Look up steps user approver in (that aren't delegated)
            var steps = workflowRepository.GetApproverWorkflowSteps(userName).Where(x => x.DelegatedByUserId == null).ToList();

            //Add delegate to steps approver is currently in
            if (steps.Any())
            {
                sendEmail = true;
                foreach (var step in steps)
                {
                    //Add new step for delegate (if they are not already there)
                    if (!workflowRepository.GetUserWorkflowSteps(step.User.AccountName, step.COID).Where(x =>
                         x.Step == step.Step && x.Approver.UserId == delegateUserId && x.WorkflowTypeId == step.WorkflowTypeId && x.DelegatedByUserId == user.Id).Any())
                    {
                        var accountNameDTOList = new List<NamesDTO>();
                        accountNameDTOList.Add(new NamesDTO()
                        {
                            AccountName = delegateUser.AccountName,
                            FirstName = delegateUser.FirstName,
                            LastName = delegateUser.LastName
                        });

                        var delegateApprover = this.RetrieveApprovers(delegateUser.UserProfile.UserName, step.COID, accountNameDTOList).FirstOrDefault();
                        workflowRepository.InsertWorkflowStep(new UserWorkflowStep()
                        {
                            ApproverId = delegateApprover.Id,
                            UserId = step.UserId,
                            Step = step.Step,
                            DelegatedByUserId = user.Id,
                            WorkflowTypeId = step.WorkflowTypeId,
                            CreatedBy = userName,
                            CreateDate = DateTime.Now,
                            COID = step.COID,
                            IsFinalRushStep = step.IsFinalRushStep,
                            IsFinalStep = step.IsFinalStep
                        });
                    }
                }
            }

            sendEmail = _facilityWorkflowService.AssignDelegateForApprover(delegateUserId, userName, user, delegateUser) || sendEmail;

            //Email for delegate assignment
            if (sendEmail && delegateEmail != "")
            {
                UserProfile approverProfile = this.GetUserProfile(userName);
                var userInfo = approverProfile.FirstName + " " + approverProfile.LastName;
                var emailRequest = new EmailRequest()
                {
                    EmailType = EmailType.AssignedDelegatedApprover,
                    Emails = new string[1] { delegateEmail },
                    SenderName = userInfo,
                    ActionDate = DateTime.Now
                };

                emailService.SendEmail(emailRequest);
            }

            //Create Approver Change Log
            auditService.UpdateApproverChangeLogList(approvers, (int)AuditEventTypeEnum.DelegateAdded, delegateUserId);
        }

        public IEnumerable<UserReportInfoDTO> GetUserReportInfo(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID)
        {
            var result = RetrieveUserReportAndEditInfo(userName, usersNames, COID);

            return result.Select(x => new UserReportInfoDTO()
            {
                UserName = x.User.AccountName,
                StandardApprovalAmount = x.Approver == null ? 0m : x.Approver.MaxApprovalAmount,
                CapitalApprovalAmount = x.Approver == null ? 0m : x.Approver.CapitalMaxApprovalAmount,
                IsCERReviewer = x.Approver == null ? false : x.Approver.IsCERReviewer,
                UserSetupWorkflows = x.UserSetupWorkflows
            });
        }

        public UserEditDTO GetUserEditUsers(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID)
        {
            var result = RetrieveUserReportAndEditInfo(userName, usersNames, COID);

            List<Approver> approvers = new List<Approver>();
            List<User> users = new List<User>();

            foreach (var user in result)
            {
                if (user.Approver != null && user.Approver.Id != 0)
                {
                    user.Approver.User.UserSetupWorkflows = user.UserSetupWorkflows;
                    approvers.Add(user.Approver);
                }
                else
                {
                    user.User.UserSetupWorkflows = user.UserSetupWorkflows;
                    users.Add(user.User);
                }
            }

            return new UserEditDTO()
            {
                Approvers = approvers,
                NonApprovers = users
            };

        }


        public User GetUserByAccountName(string accountName)
        {
            return userRepository.GetUser(accountName);
        }
        public User GetUserByAccountNameWithoutDomain(string accountName)
        {
            return userRepository.GetUserWithoutDomain(accountName);
        }

        public Approver GetFirstApproverByUserName(string accountName)
        {
            var user = userRepository.GetUser(accountName);
            return userRepository.GetApproversByUserId(user.Id).FirstOrDefault();
        }

        public User GetDelegateUserByApproverId(int approverId)
        {
            var userId = userRepository.GetApprover(approverId).UserId;
            return userRepository.GetUser(userId);
        }

        public bool RequisitionerMaxApprovalAmountIsGreaterOrEqual(Approver approver, decimal? requisitionTotal = null)
        {
            if (approver != null && requisitionTotal != null)
            {
                if (approver.MaxApprovalAmount >= requisitionTotal)
                {
                    return true;
                }
            }
            return false;
        }

        public bool RequisitionerCapitalMaxApprovalAmountIsGreaterOrEqual(Approver approver, decimal? requisitionTotal = null)
        {
            if (approver != null && requisitionTotal != null)
            {
                if (approver.CapitalMaxApprovalAmount >= requisitionTotal)
                {
                    return true;
                }
            }
            return false;
        }

        //FIXME: Needs peer review that it's truly not being used
        public string BulkAddMissingUsersByCoid(string accountName, string COID)
        {
            UserProfile admin = this.GetUserProfile(accountName);
            var adminRole = UserProfileRoleNames.EProcAdministratorRoleName;
            if (admin.Roles.Any(x => x.RoleName.ToLower() == adminRole.ToLower()))
            {
                IEnumerable<UserProfile> allUsers = GetUserProfilesForCOID(COID);
                IEnumerable<string> accountNames = allUsers.Select(x => x.DomainSlashUserName);
                IEnumerable<User> dbUsers = userRepository.GetUsersWithUserProfiles(allUsers);

                IEnumerable<UserProfile> missingUsers = allUsers.Where(x => !dbUsers.Select(y => y.AccountName.ToLower()).Contains(x.DomainSlashUserName.ToLower()));
                dbUsers.ToList().AddRange(AddUsersToDatabase(missingUsers, accountName));

                //var approverAdGroup = EProcurementADGroups["approver"];
                IEnumerable<User> allApproverUsers = dbUsers.Where(x => x.UserProfile.Roles.Any(y => y.RoleName == UserProfileRoleNames.EProcApproverRoleName));
                IEnumerable<Approver> dbApprovers = userRepository.GetApproversWithUserProfilesInUser(allApproverUsers.Select(x => x.UserProfile), COID);
                IEnumerable<User> missingApproverUsers = allApproverUsers.Where(x => !dbApprovers.Select(y => y.User.AccountName.ToLower()).Contains(x.AccountName.ToLower())).ToList();
                dbApprovers.ToList().AddRange(AddApproversToDatabase(missingApproverUsers, accountName, COID));

                return "Users have been added to the database!";
            }
            else
            {
                return "You do not have administrator access.";
            }

        }

        //FIXME: Needs peer review that it's truly not being used
        private IEnumerable<User> AddUsersToDatabase(IEnumerable<UserProfile> users, string accountName)
        {
            List<User> newUsers = new List<User>();
            foreach (var user in users)
            {
                var newUser = new User()
                {
                    AccountName = user.DomainSlashUserName,
                    CreatedBy = accountName,
                    CreateDate = DateTime.Now,
                    FirstName = user.FirstName,
                    LastName = user.LastName
                };
                newUser = userRepository.InsertUser(newUser);
                if (newUser != null)
                {
                    newUser.UserProfile = user;
                    newUsers.Add(newUser);
                }
            }
            return newUsers;
        }

        //FIXME: Needs peer review that it's truly not being used
        private IEnumerable<Approver> AddApproversToDatabase(IEnumerable<User> users, string accountName, string COID)
        {
            List<Approver> newApprovers = new List<Approver>();
            foreach (var user in users)
            {
                var newApprover = new Approver()
                {
                    UserId = user.Id,
                    MaxApprovalAmount = 0,
                    IsActive = !user.UserProfile.IsDeactivated,
                    CreatedBy = accountName,
                    CreateDate = DateTime.Now,
                    CapitalMaxApprovalAmount = 0,
                    COID = COID,
                    Delegate = null,
                    IsCERReviewer = false
                };
                newApprover = userRepository.InsertApprover(newApprover, null);
            }
            return newApprovers;
        }

        private decimal GetRequisitionTotal(Requisition requisition)
        {
            decimal total = 0;

            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    if (requisitionItem.SPRDetail != null)
                    {
                        if (requisitionItem.SPRDetail.EstimatedPrice != null)
                        {
                            total += ((decimal)requisitionItem.SPRDetail.EstimatedPrice * requisitionItem.QuantityToOrder);
                        }
                    }
                }
            }

            return total;
        }

        private IEnumerable<UserReportAndEditDBInfo> RetrieveUserReportAndEditInfo(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID)
        {

            var users = userRepository.GetUserReportAndEditDBInfos(usersNames, COID);

            users = AddMissingUsersFromUserReportAndEdit(userName, usersNames, COID, users);

            return users;
        }

        private IEnumerable<UserReportAndEditDBInfo> AddMissingUsersFromUserReportAndEdit(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID, IEnumerable<UserReportAndEditDBInfo> infos)
        {
            Approver newApprover = null;
            foreach (var missingUser in usersNames.Where(x => !infos.Where(y => !String.IsNullOrWhiteSpace(y.User.AccountName)).Select(z => z.User.AccountName.ToLower()).Contains(x.AccountName.ToLower())))
            {
                newApprover = null;
                var newUser = userRepository.InsertUser(new User()
                {
                    AccountName = missingUser.AccountName,
                    CreatedBy = userName,
                    CreateDate = DateTime.Now,
                    FirstName = missingUser.FirstName,
                    LastName = missingUser.LastName
                });

                if (missingUser.IsApprover)
                {
                    newApprover = userRepository.InsertApprover(new Approver()
                    {
                        User = newUser,
                        UserId = newUser.Id,
                        MaxApprovalAmount = 0,
                        CapitalMaxApprovalAmount = 0,
                        IsActive = true,
                        CreatedBy = userName,
                        CreateDate = DateTime.Now,
                        COID = COID
                    }, null);
                }
                infos.ToList().Add(new UserReportAndEditDBInfo()
                {
                    User = newUser,
                    Approver = newApprover,
                    UserSetupWorkflows = new UserSetupWorkflows()
                    {
                        HasStdWorkflow = false,
                        HasRushWorkflow = false,
                        HasCapitalWorkflow = false,
                        HasPunchoutWorkflow = false
                    }
                });
            }

            foreach (var missingApproverUser in infos.Where(x => (x.Approver == null || x.Approver.Id == 0) && usersNames.Where(y => !String.IsNullOrWhiteSpace(y.AccountName) && y.IsApprover).Select(z => z.AccountName.ToLower()).Contains(x.User.AccountName.ToLower())))
            {
                newApprover = userRepository.InsertApprover(new Approver()
                {
                    User = missingApproverUser.User,
                    UserId = missingApproverUser.User.Id,
                    MaxApprovalAmount = 0,
                    CapitalMaxApprovalAmount = 0,
                    IsActive = true,
                    CreatedBy = userName,
                    CreateDate = DateTime.Now,
                    COID = COID
                }, null);
                missingApproverUser.Approver = newApprover;
            }

            return infos;

        }

        // CheckDelegateStepsAreUpToDate overload for user workflow steps (facility workflow steps are directly below)
        private IEnumerable<UserWorkflowStep> CheckDelegateStepsAreUpToDate(string userName, IEnumerable<UserWorkflowStep> workflowSteps)
        {
            var groupedSteps = workflowSteps.GroupBy(x => x.Step);
            var addSteps = new List<UserWorkflowStep>();
            var removeSteps = new List<UserWorkflowStep>();

            foreach (var groupStep in groupedSteps)
            {
                foreach (var nonDelegateStep in groupStep.Where(x => x.DelegatedByUserId == null))
                {
                    var delegateStep = groupStep.FirstOrDefault(d => d.DelegatedByUserId == nonDelegateStep.Approver.UserId);
                    if (delegateStep == null && nonDelegateStep.Approver.Delegate != null)
                    {
                        addSteps.Add(new UserWorkflowStep()
                        {
                            User = nonDelegateStep.User,
                            UserId = nonDelegateStep.UserId,
                            COID = nonDelegateStep.COID,
                            Step = nonDelegateStep.Step,
                            Approver = userRepository.GetApprover((int)nonDelegateStep.Approver.Delegate),
                            ApproverId = (int)nonDelegateStep.Approver.Delegate,
                            IsFinalStep = nonDelegateStep.IsFinalStep,
                            IsFinalRushStep = nonDelegateStep.IsFinalRushStep,
                            DelegatedByUserId = nonDelegateStep.Approver.UserId,
                            CreatedBy = userName,
                            CreateDate = DateTime.Now,
                            WorkflowTypeId = nonDelegateStep.WorkflowTypeId
                        });
                    }
                    else if (delegateStep != null && nonDelegateStep.Approver.Delegate == null)
                    {
                        removeSteps.Add(delegateStep);
                    }
                    else if (delegateStep != null && nonDelegateStep.Approver.Delegate != null && delegateStep.ApproverId != nonDelegateStep.Approver.Delegate)
                    {
                        delegateStep.ApproverId = (int)nonDelegateStep.Approver.Delegate;
                        delegateStep.Approver = userRepository.GetApprover((int)nonDelegateStep.Approver.Delegate);
                        delegateStep.Id = 0;
                    }
                }
            }

            List<UserWorkflowStep> returnList = workflowSteps.Where(x => !removeSteps.Contains(x)).ToList();

            returnList.AddRange(addSteps);

            return returnList;
        }

        // CheckDelegateStepsAreUpToDate overload for facility workflow steps (user workflow steps are directly above)
        private IEnumerable<FacilityWorkflowStep> CheckDelegateStepsAreUpToDate(string userName, IEnumerable<FacilityWorkflowStep> workflowSteps)
        {
            var addSteps = new List<FacilityWorkflowStep>();
            var removeSteps = new List<FacilityWorkflowStep>();

            foreach (var nonDelegateStep in workflowSteps.Where(x => x.DelegatedByUserId == null))
            {
                var delegateStep = workflowSteps.FirstOrDefault(d => d.DelegatedByUserId == nonDelegateStep.Approver.UserId);
                if (delegateStep == null && nonDelegateStep.Approver.Delegate != null)
                {
                    addSteps.Add(new FacilityWorkflowStep()
                    {
                        Coid = nonDelegateStep.Coid,
                        Approver = userRepository.GetApprover((int)nonDelegateStep.Approver.Delegate),
                        ApproverId = (int)nonDelegateStep.Approver.Delegate,
                        DelegatedByUserId = nonDelegateStep.Approver.UserId,
                        CreatedBy = userName,
                        CreateDateUtc = DateTime.Now,
                        WorkflowTypeId = nonDelegateStep.WorkflowTypeId,
                        WorkflowType = nonDelegateStep.WorkflowType,
                    });
                }
                else if (delegateStep != null && nonDelegateStep.Approver.Delegate == null)
                {
                    removeSteps.Add(delegateStep);
                }
                else if (delegateStep != null && nonDelegateStep.Approver.Delegate != null && delegateStep.ApproverId != nonDelegateStep.Approver.Delegate)
                {
                    delegateStep.ApproverId = (int)nonDelegateStep.Approver.Delegate;
                    delegateStep.Approver = userRepository.GetApprover((int)nonDelegateStep.Approver.Delegate);
                    delegateStep.Id = 0;
                }
            }

            List<FacilityWorkflowStep> returnList = workflowSteps.Where(x => !removeSteps.Contains(x)).ToList();

            returnList.AddRange(addSteps);

            return returnList;
        }

        //FIXME: Needs peer review that it's truly not being used
        //These are methods that call PASS for bulk adding users into the database
        private IEnumerable<UserProfile> GetUserProfilesForCOID(string COID)
        {
            var userProfiles = new List<UserProfile>();
            var userProfileGroups = new List<List<UserProfile>>();

            foreach (var role in ProcurementRoles.Values)
            {
                userProfileGroups.Add(this.GetUsersWithRoleAndSoC(role, new SpanOfControl { Hierarchy = OrganizationalLevelHierarchyType.Facility, ObjectId = COID }).ToList());
            };

            if (userProfileGroups.Count > 0)
            {
                userProfiles = userProfileGroups[0];
            }
            if (userProfileGroups.Count > 1)
            {
                for (var i = 1; i < userProfileGroups.Count; i++)
                {
                    userProfiles = userProfiles.Union(userProfileGroups[i]).ToList();
                }
            }
            return userProfiles;
        }

        //FIXME: Needs peer review that it's truly not being used
        //This is a copy of the same method in epro API's User Service
        private IEnumerable<UserProfile> GetUsersWithRoleAndSoC(string roleId, SpanOfControl spanOfControl)
        {
            if (string.IsNullOrWhiteSpace(roleId))
            {
                return null;
            }

            if (spanOfControl == null)
            {
                return null;
            }

            try
            {
                AddSecurityToken();
                var generatedUrl = newSecurityEndpoint + "roles/" + roleId + "/objectId/" + spanOfControl.ObjectId + "/hierarchyType/" + spanOfControl.Hierarchy.ToInt().ToString() + "/applicationId/" + appKey + "/withDetails";
                var usrList = ApiUtility.ExecuteSecurityTokenApiGetTo<List<UserProfile>>(generatedUrl, null, GetSecurityToken());
                return usrList;
            }
            catch (Exception ex)
            {
                log.Error("Error retrieving users for given role and Span Of Control", ex);
                return null;
            }
        }

        public void SaveBulkApproverJobDetails(string userName, BulkApproverJobTracker bulkApproverJobTracker)
        {
            bulkApproverJobTracker.UserId = GetUserByAccountName(userName).Id;
            workflowRepository.SaveBulkApproverJobDetails(bulkApproverJobTracker);
        }

        public GetBulkJobDetailsDTO GetBulkApproverJobDetails(string UserName)
        {
            List<BulkApproverJobTracker> jobResults = workflowRepository.GetBulkApproverJobDetails(UserName);
            return new GetBulkJobDetailsDTO(jobResults, jobResults.Count);
        }

        public BulkApproverJobTracker GetBulkApproverJobTracker(Guid bulkApproverId)
        {
            return userRepository.GetBulkApproverJobTracker(bulkApproverId);
        }

        public void UpdateBulkApproverJobStatus(BulkApproverJobStatusDTO bulkApproverJobStatusDto)
        {
            userRepository.UpdateBulkApproverJobTrackerStatus(bulkApproverJobStatusDto);
        }
    }
}
