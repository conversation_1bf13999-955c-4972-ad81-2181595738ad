﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30406.217
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices", "RequisitionServices\RequisitionServices.csproj", "{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.Database", "RequisitionServices.Database\RequisitionServices.Database.csproj", "{75CFCF81-401A-4388-BE1F-ED579C161B41}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.DomainModel", "RequisitionServices.DomainModel\RequisitionServices.DomainModel.csproj", "{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.Repositories", "RequisitionServices.Repositories\RequisitionServices.Repositories.csproj", "{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.DomainServices", "RequisitionServices.DomainServices\RequisitionServices.DomainServices.csproj", "{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.Utility", "RequisitionServices.Utility\RequisitionServices.Utility.csproj", "{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Services.Injection", "Services.Injection\Services.Injection.csproj", "{825495F5-D287-4ABD-ADCD-08BEB4311659}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.MMISServices", "RequisitionServices.MMISServices\RequisitionServices.MMISServices.csproj", "{706343DC-AB56-4A18-A4FC-CB852C6CA64C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "RequisitionServices.Tests", "RequisitionServices.Tests\RequisitionServices.Tests.csproj", "{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{5C1D192B-0527-4195-A417-F09FCDC8A6A0}"
	ProjectSection(SolutionItems) = preProject
		readme.md = readme.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		CI_CD|Any CPU = CI_CD|Any CPU
		Debug|Any CPU = Debug|Any CPU
		Production|Any CPU = Production|Any CPU
		QA|Any CPU = QA|Any CPU
		QASandbox|Any CPU = QASandbox|Any CPU
		Release|Any CPU = Release|Any CPU
		Sandbox|Any CPU = Sandbox|Any CPU
		UKProduction|Any CPU = UKProduction|Any CPU
		UKQA|Any CPU = UKQA|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Production|Any CPU.Build.0 = Production|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.QA|Any CPU.Build.0 = QA|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Release|Any CPU.ActiveCfg = UKQA|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Production|Any CPU.Build.0 = Production|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.QA|Any CPU.Build.0 = QA|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Release|Any CPU.Build.0 = Release|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{75CFCF81-401A-4388-BE1F-ED579C161B41}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Production|Any CPU.Build.0 = Production|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.QA|Any CPU.Build.0 = QA|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Release|Any CPU.Build.0 = Release|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Production|Any CPU.Build.0 = Production|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.QA|Any CPU.Build.0 = QA|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Production|Any CPU.Build.0 = Production|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.QA|Any CPU.Build.0 = QA|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Release|Any CPU.Build.0 = Release|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Production|Any CPU.Build.0 = Production|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.QA|Any CPU.Build.0 = QA|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Release|Any CPU.Build.0 = Release|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{6A4F4B1C-F81D-45FF-8B83-B693A6DBA39D}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Production|Any CPU.Build.0 = Production|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.QA|Any CPU.Build.0 = QA|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Release|Any CPU.Build.0 = Release|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{825495F5-D287-4ABD-ADCD-08BEB4311659}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Production|Any CPU.Build.0 = Production|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.QA|Any CPU.Build.0 = QA|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Release|Any CPU.Build.0 = Release|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{706343DC-AB56-4A18-A4FC-CB852C6CA64C}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.CI_CD|Any CPU.ActiveCfg = CI_CD|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.CI_CD|Any CPU.Build.0 = CI_CD|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Production|Any CPU.ActiveCfg = Production|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Production|Any CPU.Build.0 = Production|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.QA|Any CPU.ActiveCfg = QA|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.QA|Any CPU.Build.0 = QA|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.QASandbox|Any CPU.ActiveCfg = QASandbox|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.QASandbox|Any CPU.Build.0 = QASandbox|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Sandbox|Any CPU.ActiveCfg = Sandbox|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.Sandbox|Any CPU.Build.0 = Sandbox|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.UKProduction|Any CPU.ActiveCfg = UKProduction|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.UKProduction|Any CPU.Build.0 = UKProduction|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.UKQA|Any CPU.ActiveCfg = UKQA|Any CPU
		{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}.UKQA|Any CPU.Build.0 = UKQA|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {96F55F09-5F75-478F-9993-B34DB96866C9}
	EndGlobalSection
EndGlobal
