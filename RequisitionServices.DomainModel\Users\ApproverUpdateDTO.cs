﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Users
{
    public class ApproverUpdateDTO
    {
        // Backing field for UserName to ensure it is always lowercase
        private string _userName;
        
        public string UserName
        {
            get => _userName;
            set => _userName = value.ToLower();
        }

        public string COID { get; set; }

        public IEnumerable<Approver> Approvers { get; set; }
    }
}
