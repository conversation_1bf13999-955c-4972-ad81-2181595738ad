USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_PendingApprovalsGet]    Script Date: 3/19/2024 5:16:41 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_PendingApprovalsGet
Purpose     : Returns a paginated list of requisitions and ad hoc reviews for the MyApprovals page.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 11-14-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/14/2017      Script created
Peter Hurlburt		11/29/2017      Submitted for deployment 24
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		09/14/2020		Rewrote the query to optimize the sproc (stop timeout)
Peter Hurlburt		02/04/2022		Added vendor requisitions as possible
									results, using facility workflow steps
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		06/08/2022		Adding grouping to compensate for req item join
Peter Hurlburt		07/22/2022		Remove references to facility workflow step number
Colin Glasco		10/12/2022		Adding UnitCost from VboHoldItemConversion table
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs
Jeremiah King		02/09/2023		Adding new RequisitionStatusTypeId = 17 for MyApprovals
Jeremiah King		03/05/2024		Added new boolean value for isExcludeVboChecked for Non-Vbo 
									filtering of requisitions when checkbox checked on UI
Jeremiah King		03/21/2024		Removed RequisitionStatusTypeId = 17 and added 
									RequisitionSubmissionTypeId into SELECT 
									and INSERT statements to access from within the 
									requisition object. Added update for filtering on 
									Description from RequisitionSubmissionTypes table

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_PendingApprovalsGet]
	@userName varchar(100),
	@isExcludeVboChecked bit,
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@vboFirst bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @totalPendingActions bigint
DECLARE @pendingActions TABLE (
	RowOrder int,
	SortOrder int,
	RequisitionId int,
	RequisitionStatusTypeId int,
	RequisitionLocationIdentifier VARCHAR(50),
	RequisitionComments VARCHAR(255),
	RequisitionCreateDate DATETIME,
	RequisitionTypeId int,
	PendingReviewsExist bit,
	RequisitionerId VARCHAR(100),
	RequisitionerFirstName VARCHAR(255),
	RequisitionerLastName VARCHAR(255),
	ReviewId int,
	RequesterId VARCHAR(255),
	RequesterFirstName VARCHAR(255),
	RequesterLastName VARCHAR(255),
	RequesterComments VARCHAR(255),
	RequestCreateDate DATETIME,
	CountryCode VARCHAR(3),
	DateForSorting DATETIME,
	IsVendor BIT,
	RequisitionSubmissionTypeId int,
	Description VARCHAR(50)
)

INSERT INTO @pendingActions (
	RowOrder,
	SortOrder,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionCreateDate,
	RequisitionTypeId,
	PendingReviewsExist,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	ReviewId,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	RequestCreateDate,
	CountryCode,
	DateForSorting,
	IsVendor,
	RequisitionSubmissionTypeId,
	Description
)
(
	SELECT 
		ROW_NUMBER() OVER
		(
			ORDER BY SortOrder,
				CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
				CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC,
				CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC
		) AS RowOrder,
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionLocationIdentifier,
		RequisitionComments,
		RequisitionCreateDate,
		RequisitionTypeId,
		PendingReviewsExist,
		RequisitionerId,
		RequisitionerFirstName,
		RequisitionerLastName,
		ReviewId,
		RequesterId,
		RequesterFirstName,
		RequesterLastName,
		RequesterComments,
		RequestCreateDate,
		CountryCode,
		DateForSorting,
		IsVendor,
		RequisitionSubmissionTypeId,
		Description
	FROM 
	(
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 3
					ELSE CASE
						WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup) THEN 1
						ELSE 2
						END
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Approval' AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor,
				Requisition.RequisitionSubmissionTypeId,
				ReqSubmissionTypes.Description
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [UserWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON WorkflowStep.UserId = Requisitioner.Id
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor,
					RequisitionSubmissionTypeId,
					(CASE RequisitionTypeId
						WHEN 5 THEN 2	--Capital
						WHEN 6 THEN 3	--Punchout
						WHEN 7 THEN 1	--Rush
						ELSE 0			--Standard
					END) AS [WorkflowTypeId]
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId = 2 --Pending Approval
				) AS Requisition
				ON Requisitioner.AccountName = Requisition.CreatedBy
				AND WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
				AND WorkflowStep.Step = Requisition.ApprovalStep
				AND WorkflowStep.WorkflowTypeId = Requisition.WorkflowTypeId
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			LEFT OUTER JOIN RequisitionSubmissionTypes AS ReqSubmissionTypes WITH (NOLOCK) 
				ON Requisition.RequisitionSubmissionTypeId = ReqSubmissionTypes.Id
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 0
		)
		UNION
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN CASE
						WHEN Requisition.RequisitionStatusTypeId = 14 THEN 2
						ELSE 3
						END
					ELSE CASE
						WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup) THEN 1
						ELSE 2
						END
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				CASE Requisition.RequisitionStatusTypeId
					WHEN 2 THEN 'Pending Approval'
					ELSE 'On Hold'
					END AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor,
				Requisition.RequisitionSubmissionTypeId,
				ReqSubmissionTypes.Description
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [FacilityWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor,
					RequisitionSubmissionTypeId
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId IN (2, 14) --Pending Approval or On Hold
				) AS Requisition
				ON WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
			LEFT OUTER JOIN Users AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
							LEFT OUTER JOIN [RequisitionSubmissionTypes] AS ReqSubmissionTypes WITH (NOLOCK) 
				ON Requisition.RequisitionSubmissionTypeId = ReqSubmissionTypes.Id
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 1
				AND @isExcludeVboChecked=0
		)
		UNION
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 1
					ELSE 2
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				CAST (0 AS BIT) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				Review.Id AS ReviewId,
				Requester.AccountName AS RequesterId,
				Requester.FirstName AS RequesterFirstName,
				Requester.LastName AS RequesterLastName,
				Review.RequesterComments AS RequesterComments,
				Review.CreateDate AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Review' AS StatusForFilter,
				Review.CreateDate AS DateForSorting,
				Requisition.IsVendor,
				Requisition.RequisitionSubmissionTypeId,
				ReqSubmissionType.Description
			FROM [AdhocReviews] AS Review WITH (NOLOCK)
			INNER JOIN [Users] AS Requester WITH (NOLOCK)
				ON Review.Requester = Requester.AccountName
			INNER JOIN [Requisitions] AS Requisition WITH (NOLOCK)
				ON Review.RequisitionId = Requisition.RequisitionId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN RequisitionSubmissionTypes AS ReqSubmissionType WITH (NOLOCK)
				ON Requisition.RequisitionSubmissionTypeId = ReqSubmissionType.Id
			WHERE Review.Reviewer = @userName
				AND Review.Recommended IS NULL
				AND Requisition.RequisitionStatusTypeId IN (0, 2, 3, 4, 8, 9, 10, 11, 14)
		)
	) AS PendingAction
	WHERE @filterText IS NULL OR
	(PendingAction.RequisitionId LIKE @filterText + '%'
	OR PendingAction.RequisitionerId LIKE '%' + @filterText + '%'
	OR PendingAction.RequisitionerFirstName LIKE @filterText + '%'
	OR PendingAction.RequisitionerLastName LIKE @filterText + '%'
	OR PendingAction.Description  LIKE @filterText + '%'
	OR (PendingAction.RequisitionerFirstName + ' ' + PendingAction.RequisitionerLastName) LIKE @filterText + '%'
	OR PendingAction.RequesterId LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterFirstName LIKE @filterText + '%'
	OR PendingAction.RequesterLastName LIKE @filterText + '%'
	OR (PendingAction.RequesterFirstName + ' ' + PendingAction.RequesterLastName) LIKE @filterText + '%'
	OR PendingAction.RequisitionComments LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterComments LIKE '%' + @filterText + '%'
	OR PendingAction.StatusForFilter LIKE '%' + @filterText + '%'
	OR PendingAction.RequisitionSubmissionTypeId LIKE '%' + @filterText + '%'
	OR RequisitionLocationIdentifier LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND PendingAction.RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
	OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)) 
	GROUP BY
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionLocationIdentifier,
		RequisitionComments,
		RequisitionCreateDate,
		RequisitionTypeId,
		PendingReviewsExist,
		RequisitionerId,
		RequisitionerFirstName,
		RequisitionerLastName,
		ReviewId,
		RequesterId,
		RequesterFirstName,
		RequesterLastName,
		RequesterComments,
		RequestCreateDate,
		CountryCode,
		DateForSorting,
		IsVendor,
		RequisitionSubmissionTypeId,
		Description
)

SELECT @totalPendingActions = COUNT(*) FROM @pendingActions

SELECT 
	PendingAction.RequisitionId AS RequisitionId,
	PendingAction.RequisitionStatusTypeId AS RequisitionStatusTypeId,
	PendingAction.RequisitionLocationIdentifier AS RequisitionLocationIdentifier,
	PendingAction.RequisitionComments AS RequisitionComments,
	PendingAction.RequisitionCreateDate AS RequisitionCreateDate,
	PendingAction.RequisitionTypeId AS RequisitionTypeId,
	PendingAction.PendingReviewsExist AS PendingReviewsExist,
	RequisitionItem.Id AS RequisitionItemId,
	RequisitionItem.ItemId AS RequisitionItemNumber,
	RequisitionItem.RequisitionItemStatusTypeId AS RequisitionItemStatusTypeId,
	RequisitionItem.QuantityToOrder AS RequisitionItemQuantityToOrder,
	RequisitionItem.ParentSystemId AS RequisitionItemParentSystemId,
	RequisitionItem.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
	RequisitionItem.ParentRequisitionItemId AS RequisitionItemParentItemId,
	RequisitionItem.ParIdentifier AS RequisitionItemParIdentifier,
	RequisitionItem.Discount AS Discount,
	RequisitionItem.VendorId AS VendorId,
	RequisitionItem.UnitCost AS UnitCost,
	SPRDetail.VendorId AS SprDetailsVendorId,
	SPRDetail.VendorName AS SprDetailsVendorName,
	SPRDetail.PartNumber AS SprDetailsPartNumber,
	SPRDetail.EstimatedPrice AS SprDetailsEstimatedPrice,
	Attachment.RequisitionItemId AS SprDetailsFileAttachment,
	PendingAction.RequisitionerId AS RequisitionerId,
	PendingAction.RequisitionerFirstName AS RequisitionerFirstName,
	PendingAction.RequisitionerLastName AS RequisitionerLastName,
	PendingAction.ReviewId AS ReviewId,
	PendingAction.RequesterId AS RequesterId,
	PendingAction.RequesterFirstName AS RequesterFirstName,
	PendingAction.RequesterLastName AS RequesterLastName,
	PendingAction.RequesterComments AS RequesterComments,
	PendingAction.RequestCreateDate AS RequestCreateDate,
	@totalPendingActions AS TotalReqCount,
	PendingAction.CountryCode AS CountryCode,
	PendingAction.IsVendor,
	PendingAction.RequisitionSubmissionTypeId,
	VboHoldItemConversion.UnitCost AS VboHoldItemConversionUnitCost
FROM (
	SELECT * FROM @pendingActions
	ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS PendingAction
LEFT OUTER JOIN [RequisitionItems] AS RequisitionItem WITH (NOLOCK)
	ON PendingAction.RequisitionId = RequisitionItem.RequisitionId
LEFT OUTER JOIN [SPRDetails] AS SPRDetail WITH (NOLOCK)
	ON RequisitionItem.Id = SPRDetail.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM [FileAttachments] WITH (NOLOCK)) AS Attachment
	ON RequisitionItem.Id = Attachment.RequisitionItemId
LEFT OUTER JOIN [VboHoldItemConversions] AS VboHoldItemConversion WITH (NOLOCK)
	ON RequisitionItem.Id = VboHoldItemConversion.RequisitionItemId

END
GO


