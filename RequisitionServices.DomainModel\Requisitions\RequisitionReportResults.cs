﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionReportResults
    {
        public RequisitionReportResults(List<Requisition> requisitions, long totalCount)
        {
            this.Requisitions = requisitions;
            this.TotalCount = totalCount;
        }

        public List<Requisition> Requisitions { get; set; }

        public long TotalCount { get; set; }
    }
}
