﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.DTO;
using System.Collections.Generic;
using Microsoft.Web.Http;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IItemService
    {
        Item GetItemByParId(string userName, string COID, string itemId, string parId);
        Item GetItem(string userName, string COID, string itemId);
        IEnumerable<Item> GetItems(string userName, string COID, List<ItemParDTO> itemPars);
        IEnumerable<ItemDetailsDTO> GetItemsWithDetails(string userName, string COID, int departmentId, List<ItemParDTO> itemPars, ApiVersion version = null);
        IEnumerable<DeliveryMethodType> GetDeliveryMethods();
        IEnumerable<UnitOfMeasureModel> GetUomsForItem(string username, string coid, string countryCode, int itemNumber);

        /// <summary>
        /// <Userstory>US119491</Userstory>
        /// <para>Get Item Price  details for the vendor Id or re-order number</para>
        /// </summary>
        /// <param name="username">User Id</param>
        /// <param name="coid"> Facility number</param>
        /// <param name="reordernumber">Item re-order||vendor part number</param>
        /// <param name="vendornumber">Vendor Id</param>
        /// <returns></returns>
        ItemPriceDetails GetItemPrice(string username, string coid, string reordernumber, string vendornumber);
    }
}
