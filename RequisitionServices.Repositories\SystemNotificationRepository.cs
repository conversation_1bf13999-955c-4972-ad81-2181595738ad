﻿using RequisitionServices.Database;
using RequisitionServices.DomainModel.SystemNotifications;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Configuration;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;

namespace RequisitionServices.Repositories
{
    public class SystemNotificationRepository : AbstractRepository, ISystemNotificationRepository
    {
        private IUserRepository userRepository;
        public SystemNotificationRepository(EProcurementContext context, IUserRepository userRepository) : base(context)
        {
            this.userRepository = userRepository;
        }
                
        public bool CheckSystemNotificationAuthorization(string userName)
        {
            return context.SystemNotificationAuthorizedUsers
                    .Include(x => x.User)
                    .Where(x => x.User.AccountName.ToLower() == userName.ToLower()).Any();
        }

        public List<SystemNotificationAdminDTO> GetUsersWithAuthorization()
        {
            return context.SystemNotificationAuthorizedUsers
                    .Include(x => x.User)
                    .Select(x => new SystemNotificationAdminDTO()
                    {
                        AdminId = x.Id,
                        UserId = x.UserId,
                        UserName = x.User.AccountName,
                        FirstName = x.User.FirstName,
                        LastName = x.User.LastName
                    }).ToList();
        }

        public List<SystemNotificationAdminDTO> SearchNewAdminUsingCOID(string searchString)
        {
            if (searchString.IndexOf(" ") > -1)
            {
                List<string> nameSearch = searchString.Split((string[])null, StringSplitOptions.RemoveEmptyEntries).ToList();
                nameSearch.ForEach(x => x = "%" + x + "%");

                return context.Users
                    .Where(x => (x.FirstName.Contains(nameSearch.First()) && x.LastName.Contains(nameSearch.Last())) || (x.FirstName.Contains(nameSearch.Last()) && x.LastName.Contains(nameSearch.First()))).Select(x => new SystemNotificationAdminDTO()
                    {
                        UserId = x.Id,
                        UserName = x.AccountName,
                        FirstName = x.FirstName,
                        LastName = x.LastName
                    }).ToList();
            }
            else
            {
                var search = "%" + searchString + "%";
                return context.Users
                    .Where(x => x.AccountName.Contains(searchString) || x.FirstName.Contains(searchString) || x.LastName.Contains(searchString)).Select(x => new SystemNotificationAdminDTO()
                    {
                        UserId = x.Id,
                        UserName = x.AccountName,
                        FirstName = x.FirstName,
                        LastName = x.LastName
                    }).ToList();
            }
        }

        public List<SystemNotificationAdminDTO> SearchNewAdminUsingThreeFour(string searchUserName)
        {
            //only returns users that are not already admins with authorization

            return context.Users
                .Where(x => x.AccountName.Contains(searchUserName) && !context.SystemNotificationAuthorizedUsers.Select(y => y.UserId).Contains(x.Id)).Select(x => new SystemNotificationAdminDTO()
                {
                    UserId = x.Id,
                    UserName = x.AccountName,
                    FirstName = x.FirstName,
                    LastName = x.LastName
                }).ToList();
        }

        public List<SystemNotificationAdminDTO> UpdateAdminsWithAuthorization(List<SystemNotificationAdminDTO> systemNotificationAdmins)
        {
            var masterUserName = ConfigurationManager.AppSettings.Get("SystemNotificationMasterAdmin");

            var masterUser = context.Users.Where(x => x.AccountName.EndsWith("/" + masterUserName)).FirstOrDefault();            

            var oldAdminUsers = context.SystemNotificationAuthorizedUsers.ToList();

            if (!systemNotificationAdmins.Any(x => x.UserId == masterUser.Id) && !oldAdminUsers.Any(y => y.UserId == masterUser.Id))
            {
                systemNotificationAdmins.Add(new SystemNotificationAdminDTO()
                {
                    UserId = masterUser.Id,
                    UserName = masterUser.AccountName,
                    FirstName = masterUser.FirstName,
                    LastName = masterUser.LastName
                });
            }

            var returnList = new List<SystemNotificationAdminDTO>();
            var deletedAdmins = oldAdminUsers.Where(x => !systemNotificationAdmins.Select(y => y.UserId).Contains(x.UserId) && x.UserId != masterUser.Id).ToList();

            if (deletedAdmins.Any())
            {
                context.SystemNotificationAuthorizedUsers.RemoveRange(deletedAdmins);
            }
                
            var newAdmins = systemNotificationAdmins.Where(x => x.AdminId == 0).Select(y => new SystemNotificationAuthorizedUser() { UserId = y.UserId });

            if (newAdmins != null && newAdmins.Any())
            {
                context.SystemNotificationAuthorizedUsers.AddRange(newAdmins);
            }
            context.SaveChanges();

            return GetUsersWithAuthorization();
        }


        public SystemNotification GetLatestNonExpiredSystemNotification()
        {
            var latestMessage = context.SystemNotifications.Where(x => x.ExpirationDate > DateTime.UtcNow);
            SystemNotification returnMessage = null;
            if (latestMessage != null && latestMessage.Any())
            {
                returnMessage = latestMessage.OrderByDescending(y => y.CreatedDate).First();
            }
            return returnMessage;
        }

        public SystemNotification SaveNewSystemNotification(SystemNotification notification)
        {
            //make sure that the table is empty of old messages before saving new one.
            var allNotifications = context.SystemNotifications.ToList();
            if (allNotifications != null && allNotifications.Any())
            {
                RemoveOldSystemNotifications();
            }

            if (notification != null && notification.Message != null && notification.ExpirationDate != null)
            {
                notification.CreatedDate = DateTime.Now;
                context.SystemNotifications.Add(notification);
                context.SaveChanges();
            }
            return notification;
        }

        public void RemoveOldSystemNotifications()
        {
            var deleteAll = context.SystemNotifications.ToList();
            context.SystemNotifications.RemoveRange(deleteAll);
            context.SaveChanges();
        }
    }
}
