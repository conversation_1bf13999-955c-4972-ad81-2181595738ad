﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.Items
{
    /// <summary>
    /// <Userstory>US119491</Userstory>
    /// <para>This class was introduced to capture the response data of ItemContract/ItemByVendorReorder from legacy connector API</para>
    /// </summary>
    public class ItemPriceDetails
    {
        /// <summary>
        /// Get or Set the Coid
        /// </summary>
        public int Coid { get; set; }
        /// <summary>
        /// Get or Set the Reorder Number
        /// </summary>
        public string ItemReordNo { get; set; }
        /// <summary>
        /// Get or Set the Vendor Number
        /// </summary>
        public int ItemVendor { get; set; }
        /// <summary>
        /// Get or Set the Item Number
        /// </summary>
        public int ItemNbr { get; set; }
        /// <summary>
        /// Get or Set the Item Description
        /// </summary>
        public string ItemDescription { get; set; }
        /// <summary>
        /// Get or Set the Contract Price
        /// </summary>
        public decimal ContractItemPrice { get; set; }
        /// <summary>
        /// Get or Set the Contract UOM
        /// </summary>
        public string ContractPuom { get; set; }
        /// <summary>
        /// Get or Set the Contract Indicator
        /// </summary>
        public bool FromContractIndicator { get; set; }
        /// <summary>
        /// Get or Set the Warning Message
        /// </summary>
        public string Warning { get; set; }
        /// <summary>
        /// Get or Set the LegacyConnector API call result
        /// </summary>
        public bool LegacyConnectorAPICallIsSuccess { get; set; }
        /// <summary>
        /// Get or Set the DataPresent Indicator
        /// </summary>
        public bool IsDataPresent { get; set; }
        /// <summary>
        /// Get or Set the Error
        /// </summary>
        public dynamic errors { get; set; }
        /// <summary>
        /// Get or Set the Fault
        /// </summary>
        public dynamic fault { get; set; }
    }
}
