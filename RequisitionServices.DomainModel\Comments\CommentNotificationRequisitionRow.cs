﻿using System;

namespace RequisitionServices.DomainModel.Comments
{
    public class CommentNotificationRequisitionRow
    {
        public int RequisitionId { get; set; }
        public int RequisitionStatusTypeId { get; set; }
        public int RequisitionTypeId { get; set; }
        public string LocationIdentifier { get; set; }
        public string Comments { get; set; }
        public string RequisitionerFirstName { get; set; }
        public string RequisitionerLastName { get; set; }
        public DateTime CreateDate { get; set; }
        public string CountryCode { get; set; }
        public bool IsMobile { get; set; }
        public bool IsVendor { get; set; }
        public int HasSPRItems { get; set; }
        public int HasFileAttachment { get; set; }
        public int NewCommentCount { get; set; }
        public int TotalReqCount { get; set; }

    }
}
