﻿using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.Interface;

namespace RequisitionServices.MMISServices.Utilities.SmartItemUtilities
{
    public class SmartReqItemUtility : ISmartItemUtility  //Used when getting information from Legacy's RequisitionService
    {
        public List<Requisition> GetRequisitionsFromSmart(string userName, string coid, List<string> reqIds, ref ISmartRequisitionService requisitionService)
        {
            List<int> intReqIds;
            try
            {
                intReqIds = reqIds.Select(int.Parse).ToList();
            }
            catch
            {
                int logInt;
                throw new Exception(string.Format("{0} of {2}: {3}", "GetRequisitionsFromSmart", "SmartReqItemUtility", "A non-numeric externalReqId was passed in\n" + reqIds.Where(x => !Int32.TryParse(x, out logInt)).ToString()));
            }
            return requisitionService.GetRequisitions(userName, coid, intReqIds);
        }

        public List<RequisitionItemWithSubItem> MatchRequisitionItemsToSmartItem(ref IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            /* IMPORTANT: This logic is pretty confounding. We waited about four months just to be able to consume the new "Auto-Sub" service.
                I was not about to wait until they changed it again just to make more sense on our end.
            */            
            List<RequisitionItemWithSubItem> matchesReqItemWithSubItem = new List<RequisitionItemWithSubItem>();
            IEnumerable<RequisitionItem> matchesReqItem;
            /* If SMART gave us a UNIQUEID, we can match on it, meaning we should only get one item in the matches,
             but we should code in case we get multiple items that match, to continue the idea of using lists.
             */
            if (smartItem.Id != 0)
            {
                switch (smartItem.RequisitionItemAutoSubFlag == null ? "" : smartItem.RequisitionItemAutoSubFlag.ToUpper())
                {
                    case "S":
                        matchesReqItem = this.MatchOnPrimaryId(submittedReqItems, smartItem);
                        var subItemList = this.MatchOnParentRequisitionItemId(submittedReqItems, smartItem);
                        matchesReqItemWithSubItem = (from parent in matchesReqItem
                                                    join sub in subItemList on parent.Id equals sub.ParentRequisitionItemId into ps
                                                        from pairs in ps.DefaultIfEmpty()
                                                    select new RequisitionItemWithSubItem() { originalRequisitionItem = parent, substitutedRequisitionItem = pairs })
                                                    .ToList();
                        break;
                    default:  //For "M" and "N", because we know that if this item does have a subitem, it will be updated separately.
                        matchesReqItem = this.MatchOnPrimaryId(submittedReqItems, smartItem);
                        matchesReqItemWithSubItem = matchesReqItem.Select(x => new RequisitionItemWithSubItem()
                        {
                            originalRequisitionItem = x,
                            substitutedRequisitionItem = null
                        }).ToList();
                        break;
                }
            }
            else
            {
                // we can only use UNIQUEID to determine if something is a subitem or not, so we just treat any and all items
                // that come back from Legacy without a UNIQUEID as a regular item and not a subItem
                matchesReqItem = this.MatchOnItemNumber(submittedReqItems, smartItem);
                matchesReqItemWithSubItem = matchesReqItem.Select(x => new RequisitionItemWithSubItem()
                {
                    originalRequisitionItem = x,
                    substitutedRequisitionItem = null
                }).ToList();
            }

            return matchesReqItemWithSubItem;
        }

        public List<RequisitionItemWithSubItem> MatchDiscountRequisitionItemsToSmartItem(ref IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            //From what I could tell that was no way that there will be a discount for Int Legacy req numbers.
            //TODO: CWG TALK TO SUNGHO ABOUT THIS TOMORROW
            return new List<RequisitionItemWithSubItem>();
        }

        public void UpdateRequisitionItemInfoFromSmartItem(ref RequisitionItem reqItem, RequisitionItem smartItem)
        {
                //Map over info needed
                reqItem.RequisitionScheduledDate = smartItem.RequisitionScheduledDate;
                reqItem.QuantityFulfilled = smartItem.QuantityFulfilled;
                reqItem.RequisitionItemStatusTypeId = smartItem.RequisitionItemStatusTypeId;
        }

        public void UpdateDiscountRequisitionItemInfoFromSmartItem(ref RequisitionItem reqItem, RequisitionItem smartItem)
        {
            //With no matching, there will be no mapping.  
            return;
        }

        private List<RequisitionItem> MatchOnPrimaryId(IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            return submittedReqItems.Where(x => (x.ParentSystemId == smartItem.ParentSystemId
                                                    || (smartItem.OriginalParentSystemId != null
                                                        && x.ParentSystemId == smartItem.OriginalParentSystemId))
                                                    && x.Id == smartItem.Id
                                                ).ToList();
        }

        private List<RequisitionItem> MatchOnParentRequisitionItemId(IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            return submittedReqItems.Where(x => (x.ParentSystemId == smartItem.ParentSystemId
                                                    || (smartItem.OriginalParentSystemId != null
                                                        && x.ParentSystemId == smartItem.OriginalParentSystemId))
                                                    && x.ParentRequisitionItemId == smartItem.Id
                                                ).ToList();
        }

        private List<RequisitionItem> MatchOnItemNumber(IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            return submittedReqItems.Where(x => (x.ParentSystemId == smartItem.ParentSystemId
                                                    || (smartItem.OriginalParentSystemId != null
                                                        && x.ParentSystemId == smartItem.OriginalParentSystemId))
                                                    && x.ItemId == smartItem.ItemId
                                                ).ToList();
        }
    }
}
