USE [eProcurementQA]
GO


-- Add new Workflow Type
INSERT INTO [dbo].[WorkflowTypes] ([Id], [Description])
	VALUES (4, 'Vendor')
GO

-- Create new FacilityWorkflowSteps table
CREATE TABLE [dbo].[FacilityWorkflowSteps](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Step] [int] NOT NULL,
	[ApproverId] [int] NOT NULL,
	[CreatedBy] [varchar](100) NOT NULL,
	[CreateDateUtc] [datetime] NOT NULL,
	[WorkflowTypeId] [int] NOT NULL,
	[IsOrStep] [bit] NOT NULL,
	[IsFinalStep] [bit] NOT NULL,
	[Coid] [varchar](10) NOT NULL,
	[DelegatedByUserId] [int] NULL,
 CONSTRAINT [PK_dbo_FacilityWorkflowSteps] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps] ADD  CONSTRAINT [DF_dbo_FacilityWorkflowSteps_WorkflowTypeId]  DEFAULT ((0)) FOR [WorkflowTypeId]
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps] ADD  CONSTRAINT [dbo_FacilityWorkflowSteps_IsOrStep]  DEFAULT ((0)) FOR [IsOrStep]
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps] ADD  CONSTRAINT [dbo_FacilityWorkflowSteps_IsFinalStep]  DEFAULT ((0)) FOR [IsFinalStep]
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps]  WITH CHECK ADD  CONSTRAINT [FK_dbo_FacilityWorkflowSteps_dbo_Approvers_ApproverId] FOREIGN KEY([ApproverId])
REFERENCES [dbo].[Approvers] ([Id])
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps] CHECK CONSTRAINT [FK_dbo_FacilityWorkflowSteps_dbo_Approvers_ApproverId]
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps]  WITH CHECK ADD  CONSTRAINT [FK_dbo_FacilityWorkflowSteps_dbo_WorkflowTypes_WorkflowTypeId] FOREIGN KEY([WorkflowTypeId])
REFERENCES [dbo].[WorkflowTypes] ([Id])
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[FacilityWorkflowSteps] CHECK CONSTRAINT [FK_dbo_FacilityWorkflowSteps_dbo_WorkflowTypes_WorkflowTypeId]
GO
