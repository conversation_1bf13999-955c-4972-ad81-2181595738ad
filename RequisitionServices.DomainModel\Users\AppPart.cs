﻿using RequisitionServices.DomainModel.Enum;
using System;

namespace RequisitionServices.DomainModel.Users
{
    public class AppPart
    {
        public int Id { get; set; }

        public int ApplicationSystemId { get; set; }

        public String Name { get; set; }

        public AppPartType AppPartType
        {
            get
            {
                switch (this.Name)
                {
                    case "MyRequisitions":
                        return AppPartType.MyRequisitions;
                    case "MyApprovals":
                        return AppPartType.MyApprovals;
                    case "MyItemRequests":
                        return AppPartType.MyItemRequests;
                    case "PriceViewer":
                        return AppPartType.PriceViewer;
                    case "MyReports":
                        return AppPartType.MyReports;
                    case "SearchItems":
                        return AppPartType.SearchItems;
                    case "CreateSPR":
                        return AppPartType.CreateSPR;
                    case "CreateCapitalReq":
                        return AppPartType.CreateCapitalReq;
                    case "AdminScreen":
                        return AppPartType.AdminScreen;
                    case "ManageWorkflowNotifications":
                        return AppPartType.ManageWorkflowNotifications;
                    case "UserWorkflowEdit":
                        return AppPartType.UserWorkflowEdit;
                    case "ApproverEdit":
                        return AppPartType.ApproverEdit;
                    case "PunchOutReq":
                        return AppPartType.PunchOutReq;
                    case "CreateRushReq":
                        return AppPartType.CreateRushReq;
                    default:
                        return AppPartType.Unknown;
                }
            }
        }
    }
}
