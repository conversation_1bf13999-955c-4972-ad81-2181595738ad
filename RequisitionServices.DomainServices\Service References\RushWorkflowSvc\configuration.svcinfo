﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_IRushApprovalWorkflow1&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_IRushApprovalWorkflow1" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpsBinding_IRushApprovalWorkflow&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="BasicHttpsBinding_IRushApprovalWorkflow" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IRushApprovalWorkflow1&quot; contract=&quot;RushWorkflowSvc.IRushApprovalWorkflow&quot; name=&quot;BasicHttpBinding_IRushApprovalWorkflow1&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IRushApprovalWorkflow1&quot; contract=&quot;RushWorkflowSvc.IRushApprovalWorkflow&quot; name=&quot;BasicHttpBinding_IRushApprovalWorkflow1&quot; /&gt;" contractName="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow1" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_IRushApprovalWorkflow&quot; contract=&quot;RushWorkflowSvc.IRushApprovalWorkflow&quot; name=&quot;BasicHttpsBinding_IRushApprovalWorkflow&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_IRushApprovalWorkflow&quot; contract=&quot;RushWorkflowSvc.IRushApprovalWorkflow&quot; name=&quot;BasicHttpsBinding_IRushApprovalWorkflow&quot; /&gt;" contractName="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpsBinding_IRushApprovalWorkflow" />
  </endpoints>
</configurationSnapshot>