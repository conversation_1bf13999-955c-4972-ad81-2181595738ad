﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Vendors
{
    public class PunchOutVendor
    {
        public int PunchOutVendorId { get; set; }

        [Required]
        [StringLength(75)]
        public string Name { get; set; }

        public int? VendorId { get; set; }

        [NotMapped]
        public Vendor Vendor { get; set; }

        [Required]
        [StringLength(80)]
        public string FromIdentityType { get; set; }

        [Required]
        [StringLength(80)]
        public string FromIdentityValue { get; set; }

        [Required]
        [StringLength(80)]
        public string ToIdentityType { get; set; }

        [Required]
        [StringLength(80)]
        public string ToIdentityValue { get; set; }

        [Required]
        [StringLength(80)]
        public string IdentityPhrase { get; set; }

        [Required]
        [StringLength(255)]
        public string RequestURL { get; set; }

        [Required]
        public int CompanyId { get; set; }
    }
}
