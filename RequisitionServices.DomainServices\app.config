﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <system.serviceModel>
        <bindings>
            <basicHttpBinding>
                <binding name="BasicHttpBinding_IRushApprovalWorkflow" />
                <binding name="BasicHttpBinding_INonRushApprovalWorkflow" />
                <binding name="BasicHttpBinding_INonRushApprovalWorkflow1" />
                <binding name="BasicHttpsBinding_INonRushApprovalWorkflow">
                    <security mode="Transport" />
                </binding>
                <binding name="BasicHttpBinding_IRushApprovalWorkflow1" />
                <binding name="BasicHttpsBinding_IRushApprovalWorkflow">
                    <security mode="Transport" />
                </binding>
                <binding name="BasicHttpBinding_IVendorApprovalWorkflow" />
            </basicHttpBinding>
        </bindings>
        <client>
            <endpoint address="http://local-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_INonRushApprovalWorkflow" contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow" />
            <endpoint address="http://local-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRushApprovalWorkflow" contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow" />
            <endpoint address="http://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_INonRushApprovalWorkflow1" contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow1" />
            <endpoint address="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_INonRushApprovalWorkflow" contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpsBinding_INonRushApprovalWorkflow" />
            <endpoint address="http://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRushApprovalWorkflow1" contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow1" />
            <endpoint address="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_IRushApprovalWorkflow" contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpsBinding_IRushApprovalWorkflow" />
            <endpoint address="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IVendorApprovalWorkflow" contract="VendorWorkflowSvc.IVendorApprovalWorkflow" name="BasicHttpBinding_IVendorApprovalWorkflow" />
        </client>
    </system.serviceModel>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.8.0" newVersion="3.1.8.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.12.0" newVersion="2.0.12.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" /></startup></configuration>
