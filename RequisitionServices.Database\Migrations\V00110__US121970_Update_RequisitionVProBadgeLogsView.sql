/****** Object:  View [dbo].[RequisitionVProBadgeLogsView]    Script Date: 4/29/2025 09:00:28 AM ******/
USE [eProcurementQA]
GO
DROP VIEW IF EXISTS RequisitionVProBadgeLogsView;

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE VIEW [dbo].[RequisitionVProBadgeLogsView]
AS
     SELECT 
            logs.[Id], 
            logs.[RequisitionId], 
            logs.[BadgeInStatusId], 
            statusTypes.[Status] AS BadgeStatus,
            logs.[CreateDate], 
            logs.[LastModifiedDate]
     FROM [dbo].[RequisitionVProBadgeLogs] logs
     LEFT JOIN [dbo].[RequisitionVProBadgeStatusTypes] statusTypes
     ON logs.[BadgeInStatusId] = statusTypes.[StatusId];
GO