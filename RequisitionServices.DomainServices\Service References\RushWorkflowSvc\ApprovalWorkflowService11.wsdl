<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:i0="http://local-approvalworkflow.healthtrustpg.com/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ApprovalWorkflowService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsp:Policy wsu:Id="BasicHttpsBinding_IRushApprovalWorkflow_policy">
    <wsp:ExactlyOne>
      <wsp:All>
        <sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
          <wsp:Policy>
            <sp:TransportToken>
              <wsp:Policy>
                <sp:HttpsToken RequireClientCertificate="false" />
              </wsp:Policy>
            </sp:TransportToken>
            <sp:AlgorithmSuite>
              <wsp:Policy>
                <sp:Basic256 />
              </wsp:Policy>
            </sp:AlgorithmSuite>
            <sp:Layout>
              <wsp:Policy>
                <sp:Strict />
              </wsp:Policy>
            </sp:Layout>
          </wsp:Policy>
        </sp:TransportBinding>
      </wsp:All>
    </wsp:ExactlyOne>
  </wsp:Policy>
  <wsdl:import namespace="http://local-approvalworkflow.healthtrustpg.com/" location="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?wsdl=wsdl0" />
  <wsdl:types />
  <wsdl:binding name="BasicHttpBinding_IRushApprovalWorkflow" type="i0:IRushApprovalWorkflow">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="StartRushApproval">
      <soap:operation soapAction="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushApproval" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ApproveRushRequisition">
      <soap:operation soapAction="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/ApproveRushRequisition" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:binding name="BasicHttpsBinding_IRushApprovalWorkflow" type="i0:IRushApprovalWorkflow">
    <wsp:PolicyReference URI="#BasicHttpsBinding_IRushApprovalWorkflow_policy" />
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="StartRushApproval">
      <soap:operation soapAction="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushApproval" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ApproveRushRequisition">
      <soap:operation soapAction="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/ApproveRushRequisition" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ApprovalWorkflowService">
    <wsdl:port name="BasicHttpBinding_IRushApprovalWorkflow" binding="tns:BasicHttpBinding_IRushApprovalWorkflow">
      <soap:address location="http://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" />
    </wsdl:port>
    <wsdl:port name="BasicHttpsBinding_IRushApprovalWorkflow" binding="tns:BasicHttpsBinding_IRushApprovalWorkflow">
      <soap:address location="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>