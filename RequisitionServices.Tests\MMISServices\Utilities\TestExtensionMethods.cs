﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using RequisitionServices.MMISServices.Utilities;

namespace RequisitionServices.Tests.MMISServices.Utilities
{
    [TestClass]
    public class TestExtensionMethods
    {
        /// <summary>
        /// Test
        /// </summary>
        [TestMethod]
        public void TestIsValidItemIdForSMART()
        {
            string value = null;

            Assert.IsFalse(value.IsValidItemIdForSMART(), "null is not allowed");

            value = string.Empty;

            Assert.IsFalse(value.IsValidItemIdForSMART(), "empty space is not allowed");

            value = "-0";

            Assert.IsFalse(value.IsValidItemIdForSMART(), "Anything zero or below zero is not allowed");

            value = "0";

            Assert.IsFalse(value.IsValidItemIdForSMART(), "Anything zero or below zero is not allowed");

            value = "-1";

            Assert.IsFalse(value.IsValidItemIdForSMART(), "Anything zero or below zero is not allowed");

            value = "Hello";

            Assert.IsFalse(value.IsValidItemIdForSMART(), "Text is not allowed");

            value = "123";

            Assert.IsTrue(value.IsValidItemIdForSMART(), "Numbers are allowed");
        }
    }
}
