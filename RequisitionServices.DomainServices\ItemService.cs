﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.DTO;
using Microsoft.Web.Http;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.MMISServices.LegacyConnectorInterfaces;

namespace RequisitionServices.DomainServices
{
    public class ItemService : IItemService
    {
        private ISmartItemService smartItemService;
        private ISmartItemParService smartItemParService;
        private ISmartContractService smartContractService;
        private IIINItemService iinItemService;
        private IParService parService;
        private ITypeRepository typeRepository;
        private ILegacyConnectorItemPriceService _legacyConnectorItemPriceService;

        public ItemService(ISmartItemService smartItemSvc, ISmartItemParService smartItemParSvc, ISmartContractService smartContractSvc, IIINItemService iinItemSvc, IParService parSvc, ITypeRepository typeRepo, ILegacyConnectorItemPriceService legacyConnectorItemPriceService)
        {
            smartItemService = smartItemSvc;
            smartItemParService = smartItemParSvc;
            smartContractService = smartContractSvc;
            iinItemService = iinItemSvc;
            parService = parSvc;
            typeRepository = typeRepo;
            this._legacyConnectorItemPriceService = legacyConnectorItemPriceService;
        }

        public IEnumerable<DeliveryMethodType> GetDeliveryMethods()
        {
            return typeRepository.GetDeliveryMethods();
        }

        public Item GetItemByParId(string userName, string COID, string itemId, string parId)
        {
            return this.GetItems(userName, COID, new List<ItemParDTO> { new ItemParDTO { ItemId = itemId, ParId = parId } }).FirstOrDefault();
        }

        public Item GetItem(string userName, string COID, string itemId)
        {
            return smartItemService.GetItemByItemId(userName, COID, itemId);
        }

        public IEnumerable<Item> GetItems(string userName, string COID, List<ItemParDTO> itemPars)
        {
            itemPars.Where(x => x.ParId == null).ToList().ForEach(y => y.ParId = "SPR");
            return smartItemService.GetItems(userName, COID, itemPars);
        }

        public IEnumerable<ItemDetailsDTO> GetItemsWithDetails(string userName, string COID, int departmentId, List<ItemParDTO> itemPars, ApiVersion version = null)
        {
            var itemDetailsList = new List<ItemDetailsDTO>();
            var items = this.GetItems(userName, COID, itemPars).ToList();

            if (items != null && !items.Any(x => x != null && String.IsNullOrWhiteSpace(x.Id)))
            {
                foreach(var item in items)
                {
                    var itemDetails = new ItemDetailsDTO();
                    itemDetails.AvailableParItems = parService.GetParItemsByItem(userName, COID, departmentId, item.Id, item, version);

                    if (itemDetails.AvailableParItems == null || itemDetails.AvailableParItems.Count() == 0)
                    {
                        if (item.IsStock)
                        {
                            item.UOM = item.IUOM;
                            item.Price = item.CostIUOM;
                        }
                        else
                        {
                            item.UOM = item.PUOM;
                            item.Price = (item.ComplianceCode.ToUpperInvariant() == "C") ? item.Price : item.CostPUOM;
                        }
                    }
                    else
                    {
                        foreach (var par in itemDetails.AvailableParItems)
                        {
                            par.Item.QuantityAvailable = item.QuantityAvailable;
                        }
                    }
                    itemDetails.Item = item;
                    itemDetailsList.Add(itemDetails);
                }               
            }
            return itemDetailsList;
        }

        public IEnumerable<UnitOfMeasureModel> GetUomsForItem(string username, string coid, string countryCode, int itemNumber)
        {
            var coidInt = SmartInputValidator.CheckCoid(coid);

            var details = smartItemParService.GetItemParDetails(new GetItemParDetailsRequest
            {
                Coid = coidInt,
                CountryCode = countryCode,
                Items = new List<ItemParModel>
                {
                    new ItemParModel
                    {
                        ItemId = itemNumber
                    }
                },
                UserId = username
            });

            return details?.FirstOrDefault()?.AvailableUoms ?? new List<UnitOfMeasureModel>();
        }

        /// <summary>
        /// Get Item Price  details for the vendor Id or re-order number
        /// </summary>
        /// <param name="username">User Id</param>
        /// <param name="coid"> Facility number</param>
        /// <param name="reordernumber">Item re-order||vendor part number</param>
        /// <param name="vendornumber">Vendor Id</param>
        /// <returns></returns>
        public ItemPriceDetails GetItemPrice(string username, string coid, string reordernumber, string vendornumber)
        {
            return _legacyConnectorItemPriceService.GetItemPrice(username, coid, reordernumber, vendornumber);
        }
    }
}
