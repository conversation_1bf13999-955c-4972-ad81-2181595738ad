﻿using System.Linq;
using System.Web.Mvc;
using log4net;
using log4net.Appender;
using log4net.Repository.Hierarchy;

namespace RequisitionServices.Areas.Home
{
    public class HomeController : Controller
    {
        [System.Web.Http.HttpGet]
        public ActionResult Index()
        {
            return Redirect("/swagger");
        }


        [System.Web.Http.HttpGet]
        public ActionResult GetLog()
        {
            var rootAppender = ((Hierarchy)LogManager.GetRepository())
                                         .Root.Appenders.OfType<FileAppender>()
                                         .FirstOrDefault();


            return Content(rootAppender.File);
        }
    }
}