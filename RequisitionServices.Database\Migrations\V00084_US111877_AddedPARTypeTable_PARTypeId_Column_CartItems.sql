


CREATE TABLE [dbo].[PARType](
	[Id] [int] NOT NULL,
	[Name] [varchar](20) NOT NULL,
 CONSTRAINT [PK_PARType] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO


INSERT INTO [dbo].[PARType]
           ([Id]
           ,[Name])
     VALUES
	       (0
           ,'Default'),
           (1
           ,'Half PAR'),
           (2
           ,'Full PAR')
GO
ALTER TABLE [dbo].[CartItems]
ADD [PARTypeId] [int] NOT NULL DEFAULT 0;

ALTER TABLE [dbo].[CartItems]
ADD CONSTRAINT [FK_CartItems_PARType] FOREIGN KEY ([PARTypeId])
REFERENCES [dbo].[PARType] ([Id]);



