﻿using System.Linq;
using System.Web;

namespace RequisitionServices.Utility.Web
{
    public static class HeaderUtility
    {
        private const string UserFacilityHeaderKey = "UserFacility";

        public static string GetUserFacilityHeaderValue()
        {
            var header = HttpContext.Current.Request.Headers[UserFacilityHeaderKey];
            if(header != null)
            {
                return header.ToString();
            }
            else
            {
                return null;
            }

        }

        public static string GetUserName()
        {
            var authInfo = HttpContext.Current.User.Identity.Name;
            if (!string.IsNullOrEmpty(authInfo))
            {   
               return authInfo.Split('\\').LastOrDefault();
            }

            return null;
        }

        public static string GetUserDomain()
        {
            var authInfo = HttpContext.Current.User.Identity.Name;
            if (!string.IsNullOrEmpty(authInfo))
            {
                return authInfo.Split('\\').FirstOrDefault();
            }

            return null;
        } 
    }
}
