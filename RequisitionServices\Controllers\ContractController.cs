﻿using RequisitionServices.DomainModel.Contract;
using RequisitionServices.DomainModel.Contracts;
using RequisitionServices.DomainServices.Interface;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class ContractController : ApiController
    {
        private IContractService ContractService;
        public ContractController(IContractService ContractSrvc)
        {
            ContractService = ContractSrvc;
        }

        [HttpGet]
        public ContractReportResults GetAllContracts(int rowOffset, int pageSize, string sortOrder, string filterText = null)
        {
            return ContractService.GetAllContracts(rowOffset, pageSize, sortOrder, filterText);
        }

        [HttpGet]
        public ContractReportResults GetContractsById(int rowOffset, int pageSize, string sortOrder, string searchText, string filterText = null)
        {
            return ContractService.GetContractsById(rowOffset, pageSize, sortOrder, filterText, searchText);
        }

        [HttpGet]
        public ContractReportResults GetContractsByVendor(int rowOffset, int pageSize, string sortOrder, string searchText, string filterText = null)
        {
            return ContractService.GetContractsByVendor(rowOffset, pageSize, sortOrder, filterText, searchText);
        }

        [HttpGet]
        public ContractDetails GetContractDetails(string userName, string COID, string contractId, string vendorNumber)
        {
            return ContractService.GetContractDetails(userName, COID, contractId, vendorNumber);
        }

    }
}