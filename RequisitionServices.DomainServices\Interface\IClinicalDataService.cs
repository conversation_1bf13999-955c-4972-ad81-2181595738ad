﻿using RequisitionServices.DomainModel.Clinical;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IClinicalDataService
    {
        IEnumerable<Provider> GetProviders(string userName, string COID);

        IEnumerable<Patient> GetPatients(string userName, string COID);

        Patient GetPatient(string userName, string COID, string patientId);

        IEnumerable<Patient> SearchPatientsByName(string userName, string COID, string patientName);
    }
}
