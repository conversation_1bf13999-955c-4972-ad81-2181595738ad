﻿using RequisitionServices.DomainModel.Vira;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IViraService
    {
        /// <summary>
        /// Gets the Vira item status record by requisition and item IDs.
        /// </summary>
        /// <param name="requisitionId">The requisition ID.</param>
        /// <param name="requisitionItemId">The requisition item ID.</param>
        /// <returns>The Vira item status record.</returns>
        Task<ViraItemStatus> GetViraItemStatusRecordById(int requisitionId, int requisitionItemId);

        /// <summary>
        /// Creates a new Vira item status record.
        /// </summary>
        /// <param name="viraItemStatus">The Vira item status to create.</param>
        /// <returns>The created Vira item status record.</returns>
        Task<ViraItemStatus> CreateViraItemStatusRecord(ViraItemStatus viraItemStatus);

        /// <summary>
        /// Deletes the specific Vira item status record by requisition and item IDs.
        /// </summary>
        /// <param name="requisitionId">The requisition ID.</param>
        /// <param name="requisitionItemId">The requisition item ID.</param>
        /// <returns>A message indicating the result of the deletion.</returns>
        Task<string> DeleteViraItemStatusRecord(int requisitionId, int requisitionItemId);

        /// <summary>
        /// Updates an existing Vira item status record.
        /// </summary>
        /// <param name="viraItemStatus">The Vira item status to update.</param>
        /// <returns>A message indicating the result of the update.</returns>
        Task<string> UpdateViraItemStatusRecord(ViraItemStatus viraItemStatus);
    }
}
