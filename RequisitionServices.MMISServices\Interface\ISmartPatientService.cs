﻿using RequisitionServices.DomainModel.Clinical;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartPatientService
    {
        IEnumerable<Patient> GetPatients(string userName, string coid);

        Patient GetPatient(string userName, string coid, string patientId);

        IEnumerable<Patient> SearchPatientsByName(string userName, string coid, string patientName);
    }
}
