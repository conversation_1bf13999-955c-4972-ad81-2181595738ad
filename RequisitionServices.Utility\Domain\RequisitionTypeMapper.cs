﻿using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.Utility.Domain
{
    public static class RequisitionTypeMapper
    {
        public static int GetRequisitionType(string reqType)
        {
            int requisitionType;
            switch (reqType)
            {
                case "G":
                    requisitionType = (int)RequisitionTypeEnum.Standard;
                    break;
                case "S":
                    requisitionType = (int)RequisitionTypeEnum.Rush;
                    break;
                case "O":
                    requisitionType = (int)RequisitionTypeEnum.BillOnly;
                    break;
                case "B":
                    requisitionType = (int)RequisitionTypeEnum.BillAndReplace;
                    break;
                case "C":
                    requisitionType = (int)RequisitionTypeEnum.CapitatedBillOnly;
                    break;
                case "L":
                    requisitionType = (int)RequisitionTypeEnum.CapitatedBillAndReplace;
                    break;
                default:
                    requisitionType = (int)RequisitionTypeEnum.Standard;
                    break;
            }
            return requisitionType;
        }
    }
}
