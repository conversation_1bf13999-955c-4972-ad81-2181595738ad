﻿using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace RequisitionServices.DomainModel.Requisitions
    {
    /// <summary>
    /// Represents a data transfer object for a tuple of advanced filter lists
    /// </summary>
    public class RequisitionPurchasingAdvancedFiltersDto
        {
        private List<ReqTypeModel> _reqTypes = new List<ReqTypeModel>();
        private List<VendorModel> _vendors = new List<VendorModel>();
        private List<BuyerModel>  _buyers = new List<BuyerModel>();
        /// <summary>
        /// Gets or sets the list of requisition types.
        /// </summary>
        public List<ReqTypeModel> ReqTypes 
        {
            get { return _reqTypes;  }
            set { _reqTypes = value ?? new List<ReqTypeModel>(); } 
        }

        /// <summary>
        /// Gets or sets the list of vendors.
        /// </summary>
        public List<VendorModel> Vendors
        {
            get { return _vendors; }
            set { _vendors = value ?? new List<VendorModel>(); }
        }

        /// <summary>
        /// Gets or sets the list of buyers.
        /// </summary>
        public List<BuyerModel> Buyers
          {
            get { return _buyers; }
            set { _buyers = value ?? new List<BuyerModel>(); }
        }
        /// <summary>
        /// Holds user type values for filtering search results
        /// </summary>
        public string FilterText { get; set; }

        /// <summary>
        /// Attribute allows endpoint to accept Date instead of DateTime
        /// startDate is used as Date for date range of linq query
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? startDate { get; set; } = null;

        /// <summary>
        /// Attribute allows endpoint to accept Date instead of DateTime
        /// endDate is used as Date for date range of linq query
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? endDate { get; set; } = null;

    }
}
