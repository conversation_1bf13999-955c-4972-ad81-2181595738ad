﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface ICommentRepository
    {
        List<Comment> Get(int requisitionId);
        Comment Add(Comment comment);
        List<CommentNotificationRequisitionRow> GetNotifications(string username, int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string filterText);
        void AddNotifications(List<UnreadComment> unreadComments);
        void RemoveNotifications(int requisitionId, int userId);
    }
}