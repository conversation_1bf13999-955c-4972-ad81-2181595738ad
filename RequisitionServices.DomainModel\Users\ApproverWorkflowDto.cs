﻿namespace RequisitionServices.DomainModel.Users
{
    public class ApproverWorkflowDto
    {
        public string AccountName { get; set; }
        public decimal CapitalMaxApprovalAmount { get; set; }
        public string Coid { get; set; }
        public string Description { get; set; }
        public string FirstName { get; set; }
        public int Id { get; set; }
        public bool IsFinalRushStep { get; set; }
        public bool IsFinalStep { get; set; }
        public string LastName { get; set; }
        public decimal MaxApprovalAmount { get; set; }
        public int Step { get; set; }
        public int UserId { get; set; }
        public int ApproverId { get; set; }
        public int WorkflowTypeId { get; set; }
    }
}