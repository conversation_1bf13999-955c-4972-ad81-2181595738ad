<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="c79ff4b6-eadf-4eda-9eb2-f05b72d9e424" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="VendorWorkflowService.xsd" MetadataType="Schema" ID="5c757599-f991-423e-ae8b-51c836cd5760" SourceId="1" SourceUrl="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx?xsd=xsd2" />
    <MetadataFile FileName="ApprovalWorkflowService.wsdl" MetadataType="Wsdl" ID="74d34cb6-3bd2-44dc-bd41-16a95297bab3" SourceId="1" SourceUrl="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx?wsdl" />
    <MetadataFile FileName="VendorWorkflowService1.xsd" MetadataType="Schema" ID="5504b0c5-afa1-4c3c-b717-037da398cf70" SourceId="1" SourceUrl="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx?xsd=xsd1" />
    <MetadataFile FileName="VendorWorkflowService.wsdl" MetadataType="Wsdl" ID="96f31d26-238b-40df-8da8-426e5d84a6fe" SourceId="1" SourceUrl="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx?wsdl=wsdl0" />
    <MetadataFile FileName="VendorWorkflowService.disco" MetadataType="Disco" ID="918a653b-6d08-4589-81e9-5c71372444cb" SourceId="1" SourceUrl="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx?disco" />
    <MetadataFile FileName="VendorWorkflowService2.xsd" MetadataType="Schema" ID="98086041-8116-4bc0-9135-cb3dbbd1ce76" SourceId="1" SourceUrl="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx?xsd=xsd0" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>