﻿using RequisitionServices.DomainModel.SystemNotifications;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class SystemNotificationController : ApiController
    {
        private ISystemNotificationService systemNotificationService;

        public SystemNotificationController(ISystemNotificationService systemNotificationSvc)
        {
            this.systemNotificationService = systemNotificationSvc;
        }
        
        [HttpGet]
        public bool CheckSystemNotificationAuthorization(string userName)
        {
            return systemNotificationService.CheckSystemNotificationAuthorization(userName);
        }

        [HttpGet]
        public SystemNotifcationTabInfoDTO GetSystemNotificationTabInfo()
        {
            return systemNotificationService.GetSystemNotificationTabInfo();
        }

        [HttpGet]
        public List<SystemNotificationAdminDTO> SearchNewAdminUsingThreeFour(string searchUserName)
        {
            return systemNotificationService.SearchNewAdminUsingThreeFour(searchUserName);

        }

        [HttpGet]
        public List<SystemNotificationAdminDTO> SearchNewAdminUsingCOID(string searchString)
        {
            return systemNotificationService.SearchNewAdminUsingCOID(searchString);
        }

        [HttpPost]
        public List<SystemNotificationAdminDTO> UpdateAdminsWithAuthorization(List<SystemNotificationAdminDTO> systemNotificationAdmins)
        {
            return systemNotificationService.UpdateAdminsWithAuthorization(systemNotificationAdmins);
        }

        [HttpGet]
        public SystemNotification GetLatestNonExpiredSystemNotification()
        {
            return systemNotificationService.GetLatestNonExpiredSystemNotification();
        }

        [HttpPost]
        public SystemNotification SaveNewSystemNotification(SystemNotification notification)
        {
            notification.ExpirationDate.ToUniversalTime();

            return systemNotificationService.SaveNewSystemNotification(notification);
        }

        [HttpGet]
        public void RemoveOldSystemNotifications()
        {
            systemNotificationService.RemoveOldSystemNotifications();
        }
    }
}
