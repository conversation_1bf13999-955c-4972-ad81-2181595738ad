﻿using RequisitionServices.DomainModel.Clinical;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;

namespace RequisitionServices.MMISServices
{
    public class SmartPatientService : ISmartPatientService
    {
        private const string getPatientsMethod = "Patients/GetAllPatients/";
        private const string getPatientByIdMethod = "Patients/GetPatientById/";
        private const string searchPatientsByNameMethod = "Patients/GetPatientsByName/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IEnumerable<Patient> GetPatients(string userName, string coid)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            int intCOID;
            if (!Int32.TryParse(coid, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", coid));
            }

            var patientRecordModels = ApiUtility.ExecuteApiGetTo<IEnumerable<PatientRecordModel>>(endpoint, getPatientsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });

            var patients = new List<Patient>();
            if (patientRecordModels != null)
            {
                foreach (var patientRecordModel in patientRecordModels)
                {
                    patients.Add(patientRecordModel.MapToPatient());
                }
            }

            return patients;
        }

        public Patient GetPatient(string userName, string coid, string patientId)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            int intCOID;
            if (!Int32.TryParse(coid, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", coid));
            }

            var patientRecordModel = ApiUtility.ExecuteApiGetTo<PatientRecordModel>(endpoint, getPatientByIdMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "patientNumber", patientId }
                                                                            });

            Patient patient = null;
            if (patientRecordModel != null)
            {
                patient = patientRecordModel.MapToPatient();
            }

            return patient;
        }

        public IEnumerable<Patient> SearchPatientsByName(string userName, string coid, string patientName)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            int intCOID;
            if (!Int32.TryParse(coid, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", coid));
            }

            var patientRecordModels = ApiUtility.ExecuteApiGetTo<IEnumerable<PatientRecordModel>>(endpoint, searchPatientsByNameMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "patientName", patientName }
                                                                            });

            var patients = new List<Patient>();
            if (patientRecordModels != null)
            {
                foreach (var patientRecordModel in patientRecordModels)
                {
                    patients.Add(patientRecordModel.MapToPatient());
                }
            }

            return patients;
        }
    }
}
