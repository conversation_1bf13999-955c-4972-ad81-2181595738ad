﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.VPro;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Data Transfer Object for Requisition with detailed information.
    /// </summary>
    public class RequisitionWithDetailsDTO
    {
        /// <summary>
        /// Default constructor.
        /// </summary>
        public RequisitionWithDetailsDTO() { }

        /// <summary>
        /// Constructor to initialize RequisitionWithDetailsDTO with requisition, items, and parItems.
        /// </summary>
        /// <param name="requisition">Requisition object.</param>
        /// <param name="items">List of items.</param>
        /// <param name="parItems">List of par items.</param>
        public RequisitionWithDetailsDTO(Requisition requisition, List<Item> items, List<ParItem> parItems)
        {
            this.RequisitionId = requisition.RequisitionId;
            this.RequisitionStatusTypeId = requisition.RequisitionStatusTypeId;
            this.RequisitionTypeId = requisition.RequisitionTypeId;
            this.LocationIdentifier = requisition.LocationIdentifier;
            this.ApprovalStep = requisition.ApprovalStep;
            this.ApprovedAmount = requisition.ApprovedAmount;
            this.Comments = requisition.Comments;
            this.CreateDate = requisition.CreateDate;
            this.CreatedBy = requisition.CreatedBy;
            this.WorkflowInstanceId = requisition.WorkflowInstanceId;
            this.RequisitionParClass = requisition.RequisitionParClass;
            this.CountryCode = requisition.CountryCode;
            this.IsMobile = requisition.IsMobile;
            this.IsVendor = requisition.IsVendor;
            this.RequisitionSubmissionTypeId = requisition.RequisitionSubmissionTypeId;
            this.TotalReqAmount = requisition.TotalReqAmount;
            this.BadgeLogId = requisition.BadgeLogId;

            if (requisition.RequisitionItems != null)
            {
                this.RequisitionItems = new List<RequisitionItemWithDetailsDTO>();
                foreach(var reqItem in requisition.RequisitionItems)                
                {
                    Item item = null;
                    if(items != null)
                    {
                        var checkItem = new Item();

                        var matchingItems = items.Where(x => x.Id == reqItem.ItemId).Count();
                        if(matchingItems>1 && reqItem.SPRDetail != null)
                        {
                            checkItem = items.Where(x => x.Id == reqItem.ItemId
                            && x.Description == reqItem.SPRDetail.ItemDescription
                            && x.Vendor.Id == reqItem.SPRDetail.VendorId
                            && (x.ParId == reqItem.ParIdentifier || (reqItem.ParIdentifier == null && x.ParId == "")) 
                            && x.UOM == reqItem.SPRDetail.UOMCode
                            && x.Price == reqItem.SPRDetail.EstimatedPrice
                            ).FirstOrDefault();
                        }
                        else
                        {
                            checkItem = items.Where(x => x.Id == reqItem.ItemId).FirstOrDefault();
                        }
                        if (reqItem.SPRDetail != null)
                        {
                            if(checkItem != null && checkItem.Vendor != null && checkItem.Vendor.Id == reqItem.SPRDetail.VendorId)
                            {
                                item = checkItem;
                            }
                        }
                        else
                        {
                            item = checkItem;
                        }
                    }
                    List<ParItem> availableParItems = new List<ParItem>();
                    if(parItems != null && reqItem.SPRDetail == null && reqItem.ParIdentifier != null && reqItem.ParIdentifier != "EPR")
                    {
                        availableParItems = parItems.Where(x => x.ItemId.ToString() == reqItem.ItemId).GroupBy(par => par.ParId).Select(group => group.First()).ToList();
                    }

                    if (reqItem.VboHoldItemConversion != null)
                    {
                        reqItem.VboHoldItemConversion.ItemDetails = parItems.FirstOrDefault(x => string.Equals(x.ParId, requisition.RequisitionParClass, StringComparison.InvariantCultureIgnoreCase) 
                                                                            && x.ItemId == reqItem.VboHoldItemConversion.SmartItemNumber);
                    }

                    this.RequisitionItems.Add(new RequisitionItemWithDetailsDTO(reqItem, item, availableParItems));
                }
            }
        }

        /// <summary>
        /// Gets or sets the Requisition ID.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Status Type ID.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Status Type.
        /// </summary>
        public RequisitionStatusType RequisitionStatusType { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Type ID.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Type.
        /// </summary>
        public RequisitionType RequisitionType { get; set; }

        /// <summary>
        /// Gets or sets the Location Identifier.
        /// </summary>
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the Approval Step.
        /// </summary>
        public int? ApprovalStep { get; set; }

        /// <summary>
        /// Gets or sets the Approved Amount.
        /// </summary>
        public decimal ApprovedAmount { get; set; }

        /// <summary>
        /// Gets or sets the Comments.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the Created By.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the Create Date.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the list of Requisition Items with details.
        /// </summary>
        public List<RequisitionItemWithDetailsDTO> RequisitionItems { get; set; }

        private ICollection<RequisitionDigitalSignOff> _requisitionDigitalSignoff;

        /// <summary>
        /// Gets or sets the Requisition Digital Sign Off.
        /// </summary>
        public virtual ICollection<RequisitionDigitalSignOff> RequisitionDigitalSignOff
            {
            get { return _requisitionDigitalSignoff ?? (_requisitionDigitalSignoff = new Collection<RequisitionDigitalSignOff>()); }
            set { _requisitionDigitalSignoff = value; }
            }

        /// <summary>
        /// Gets or sets the Workflow Instance ID.
        /// </summary>
        public Guid? WorkflowInstanceId { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Par Class.
        /// </summary>
        public string RequisitionParClass { get; set; }

        /// <summary>
        /// Gets or sets the Country Code.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether it is mobile.
        /// </summary>
        public bool IsMobile { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether it is vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the Total Requisition Amount.
        /// </summary>
        public decimal? TotalReqAmount { get; set; }

        /// <summary>
        /// Gets or sets the Requisition Submission Type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the VPRO badge in ID.
        /// </summary>
        public int? BadgeLogId { get; set; }

        /// <summary>
        /// Gets or sets the VProBdageInDetails object
        /// </summary>
        public RequisitionVProBadgeLog VProBadgeLog { get; set; }
    }
}
