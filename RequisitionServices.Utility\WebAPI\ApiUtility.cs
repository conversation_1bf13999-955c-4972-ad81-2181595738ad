﻿using log4net;
using Microsoft.Web.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace RequisitionServices.Utility.WebAPI
{
    public static class ApiUtility
    {
        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);

        /// <summary>
        /// Executes a HTTP GET call to the specified URL/Action
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <param name="version"></param>
        /// <returns></returns>
        public static T ExecuteApiGetTo<T>(string apiUrl, string action, Dictionary<string, string> parameters, string username = null, string password = null, Dictionary<string, string> customHeaders = null, ApiVersion version = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            try
            {
                var client = ApiFactory.GetClient(apiUrl, useDefaultCredentials);
                ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

                Logger.Debug($"Initiating call {apiUrl + action}");
                var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
                var response = client.GetAsync(queryUrl).Result;

                CleanClientHeaders(client, useDefaultCredentials, customHeaders);

                if (response.IsSuccessStatusCode)
                {
                    var returnResponse = response.Content.ReadAsAsync<T>();
                    return returnResponse.Status == TaskStatus.Faulted ? default(T) : returnResponse.Result;
                }

                Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");

                if (version?.MajorVersion > 1)
                {
                    if (response.StatusCode == HttpStatusCode.BadRequest)
                    {
                        throw new BadRequestException(response.ReasonPhrase);
                    }
                }
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
            }
            catch (Exception ex)
            {
                Logger.Error($"Method: ExecuteAPIGetTo exception {ex.Message}, inner exception {(ex.InnerException != null ? JsonConvert.SerializeObject(ex.InnerException) : string.Empty)}");
                throw;
            }
        }
        
        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given HTTPContent
        /// </summary>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="content">HTTP Content to be posted</param>
        /// <returns></returns>
        public static T ExecuteApiPostToGetSecurityToken<T>(string apiUrl, HttpContent content)
        {
            var client = ApiFactory.GetClient(apiUrl, false);

            Logger.Debug($"Initiating call {apiUrl}");
            var response = client.PostAsync(apiUrl, content).Result;

            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsAsync<T>().GetAwaiter().GetResult();
            }

            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {apiUrl}");
        }

        /// <summary>
        /// Post Security api without query string params
        /// </summary>
        /// <param name="apiUrl"></param>
        /// <param name="parameters"></param>
        /// <param name="token"></param>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public static T ExecuteSecurityApiPostTo<T>(string apiUrl, Dictionary<string, object> parameters,
            string token = "")
        {
            var client = ApiFactory.GetSmartSecurityClient(apiUrl);
            HttpContent httpContent = new StringContent(Newtonsoft.Json.JsonConvert.SerializeObject(parameters), Encoding.UTF8, "application/json");
            
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer",
                string.IsNullOrWhiteSpace(token) ? HttpContext.Current.Request.Headers["PingAuthorization"] : token.Substring(7));
            
            Logger.Debug($"Initiating call {apiUrl}");
            var response = client.PostAsync(apiUrl, httpContent).Result;
            
            if (response.IsSuccessStatusCode) return response.Content.ReadAsAsync<T>().Result;
            
            Logger.Error($"{apiUrl} returned {response.StatusCode} with content {response.Content.ReadAsStringAsync().Result}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {apiUrl}. API Response: {response.Content}");         
        }

        
        public static T ExecuteSecurityTokenApiGetTo<T>(string apiUrl, Dictionary<string, string> parameters, string token = "", bool acceptEmptyParameters = false)
        {
            var client = ApiFactory.GetSmartSecurityClient(apiUrl);
            
            client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token.Substring(7));
            
            Logger.Debug($"Initiating call {apiUrl }");
            var queryUrl = QueryStringBuilder(apiUrl, parameters, acceptEmptyParameters);
            var response = client.GetAsync(queryUrl).GetAwaiter().GetResult();

            if (response.IsSuccessStatusCode) return response.Content.ReadAsAsync<T>().GetAwaiter().GetResult();

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}. API Response: {response.Content}");
        }

        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given an object. The object will be JSON'd for transmission to API.
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiUrl">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="objectContent">Object to be posted</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <returns></returns>
        public static T ExecuteApiPostWithContentTo<T>(string apiUrl, string action, Dictionary<string, string> parameters, object objectContent, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            var client = ApiFactory.GetClient(apiUrl, useDefaultCredentials);
            ApplyClientHeaders(client, useDefaultCredentials, username, password, customHeaders);

            Logger.Debug($"Initiating call {apiUrl + action}");
            var queryUrl = QueryStringBuilder(apiUrl + action, parameters);
            var response = client.PostAsJsonAsync(queryUrl, objectContent).Result;

            CleanClientHeaders(client, useDefaultCredentials, customHeaders);

            if (response.IsSuccessStatusCode)
            {
                return response.Content.ReadAsAsync<T>().Result;
            }

            Logger.Error($"{queryUrl} returned {response.StatusCode} with content {response.Content.ReadAsStringAsync()}{Environment.NewLine}");
            throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {queryUrl}");
        }

        /// <summary>
        /// Returns a URL-encoded query string with a supplied list of parameters
        /// </summary>
        /// <param name="url"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        private static string QueryStringBuilder(string url, Dictionary<string, string> parameters)
        {
            if (parameters?.Any() != true) return url;

            var query = new StringBuilder();
            foreach (var parameter in from parameter in parameters let hasValue = !string.IsNullOrWhiteSpace(parameter.Value) where hasValue select parameter)
            {
                if (query.Length > 0) query.Append("&");
                query.Append(HttpUtility.UrlEncode(parameter.Key));
                query.Append("=").Append(HttpUtility.UrlEncode(parameter.Value));
            }
    
            return query.Length > 0 ? $"{url}?{query}" : url;
        }
        
        /// <summary>
        /// Returns a URL-encoded query string with a supplied list of parameters
        /// </summary>
        /// <param name="url"></param>
        /// <param name="parameters"></param>
        /// <param name="acceptEmptyParameters">If true, allows constructing empty query string parameters. Defaults to false</param>
        /// <returns></returns>
        private static string QueryStringBuilder(string url, Dictionary<string, string> parameters, bool acceptEmptyParameters)
        {
            if (parameters?.Any() != true) return url;

            var query = new StringBuilder();
            foreach (var parameter in parameters)
            {
                var hasValue = !string.IsNullOrWhiteSpace(parameter.Value);
                if (!hasValue && !acceptEmptyParameters) continue;

                if (query.Length > 0) query.Append("&");
                query.Append(HttpUtility.UrlEncode(parameter.Key));
                if (hasValue) query.Append("=").Append(HttpUtility.UrlEncode(parameter.Value));
            }
    
            return query.Length > 0 ? $"{url}?{query}" : url;
        }

        private static void ApplyClientHeaders(HttpClient client, bool useDefaultCredentials, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            if (client == null) return;
            
            if (!useDefaultCredentials)
            {
                var authByteArray = Encoding.ASCII.GetBytes(username + ":" + password);
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("basic", Convert.ToBase64String(authByteArray));
            }

            if (customHeaders == null) return;
                
            foreach (var header in customHeaders.Where(header => !client.DefaultRequestHeaders.Contains(header.Key)))
                client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
        }

        private static void CleanClientHeaders(HttpClient client, bool useDefaultCredentials, Dictionary<string, string> customHeaders = null)
        {
            if (client == null) return;
            
            if (!useDefaultCredentials)
                client.DefaultRequestHeaders.Remove("Authorization");

            if (customHeaders == null) return;
            
            foreach (var header in customHeaders.Where(header => !client.DefaultRequestHeaders.Contains(header.Key)))
                client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
        }
    }
}
