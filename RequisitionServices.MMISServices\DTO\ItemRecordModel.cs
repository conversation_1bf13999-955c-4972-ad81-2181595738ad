﻿using log4net;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace RequisitionServices.MMISServices.DTO
{
    public class ItemRecordModel
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        public Item MapToItem()
        {
            try
            {
                return new Item()
                {
                    Id = this.ItemNumber.ToString(),
                    Name = this.Description,
                    Description = string.IsNullOrEmpty(this.LongItemDesc) ? this.Description : this.LongItemDesc,
                    Vendor = new Vendor() { Id = this.PurchasingVendor, Name = this.PurchasingVendorName, InvalidVendorFlag = this.InvalidVendorFlag },
                    ReorderNumber = this.ReorderNumber,
                    ManufacturerCatalogNumber = this.CatalogNumber,
                    Location = this.Location,
                    Account = this.PurchasingGLAccount.ToString(),
                    UOM = this.PUOM,
                    IUOM = this.IUOM,
                    PUOM = this.PUOM,
                    Price = this.Price,
                    Factor = this.Factor,
                    ProcCode = this.ProcedureCode,
                    LeadDays = this.LeadDays,
                    IsStock = (this.StockIndicator || string.Compare(this.DistributionPoint, "F", true) == 0),
                    IsTempStock = this.TempStock,
                    IsCapitated = this.IsCapitated,
                    ContractNumber = this.ContractNumber,
                    ComplianceCode = this.ComplianceCode,
                    GLSubCode = this.GLSubCode,
                    IsValidItem = true,
                    ParId = this.ParId,
                    DistributionPoint = this.DistributionPoint,
                    ItemType = this.ItemType,
                    IUOMUPN = this.IUOMUPN,
                    PUOMUPN = this.PUOMUPN,
                    LastPO = this.LastPO,
                    CostIUOM = this.CostIUOM,
                    CostPUOM = this.CostPUOM,
                    OnorderIUOM = this.OnorderIUOM,
                    OnorderPUOM = this.OnorderPUOM,
                    TaxCode = this.TaxCode,
                    AlgorInPatient = this.AlgorInPatient,
                    AlgorOutPatient = this.AlgorOutPatient,
                    PurchaseDept = this.PurchaseDept,
                    Labels = this.Labels,
                    PhysicalLoc = this.PhysicalLoc,
                    ReorderPoint = this.ReorderPoint,
                    MfgVendName = this.ManufacturerVendorDescription,
                    MfgVendNumber = this.ManufacturingVendor,
                    QuantityAvailable = this.QOH,
                    ExpGLAccount = this.ExpGLAccount,
                    InvGLAccount = this.InvGLAccount,
                    Chargeable = this.ChargeCode,
                    AlternateUOMs = this.AlternateUOMRecords == null ? new List<AlternateUOM>() : this.AlternateUOMRecords.Select(x => new AlternateUOM(x.AUOM, x.Factor)).ToList(),
                    IsActive = this.ItemStatus,
                    ORUOMFactor = this.UOMDetail == null? 0: this.UOMDetail.ORUOMFACTOR,
                    ORUOM = this.UOMDetail?.ORUOM
                };
            }
            catch (Exception ex)
            {
                log.Error("Exception occured while mapping Item :" + ex.Message, ex);
                return new Item();
            }
         
        }

        public int ItemCoid { get; set; }

        public int ItemNumber { get; set; }

        public string Description { get; set; }

        public string Description2 { get; set; }

        public string LongItemDesc { get; set; }

        public int PurchasingVendor { get; set; }

        public int ManufacturingVendor { get; set; }

        public string ReorderNumber { get; set; }

        public string CatalogNumber { get; set; }

        public string ItemClass { get; set; }

        public string ItemClassCategory { get; set; }

        public int PurchasingDepartment { get; set; }

        public decimal Price { get; set; }

        public decimal ContractPrice { get; set; }

        public string PUOM { get; set; }

        public string IUOM { get; set; }

        public int Factor { get; set; }

        public int MinQuantity { get; set; }

        public int MaxQuantity { get; set; }

        public int RoundingFactor { get; set; }

        public bool ItemStatus { get; set; }

        public bool NationalAgreement { get; set; }

        public int ContractNumber { get; set; }

        public bool ChargeCode { get; set; }

        public string ProcedureCode { get; set; }

        public bool HazardousIndicator { get; set; }

        public bool PharmacyFlag { get; set; }

        public bool StockIndicator { get; set; }

        public int GLSubCode { get; set; }

        public string PrimaryNDCNumber { get; set; }

        public string PurchasingVendorName { get; set; }
        
        public string Location { get; set; }

        public long PurchasingGLAccount { get; set; }
        
        public long IssueGLAccount { get; set; }

        public int LeadDays { get; set; }
        
        public string ComplianceCode { get; set; }

        public bool IsCapitated { get; set; }

        public bool TempStock { get; set; }

        public int QOH { get; set; }

        public bool LatexFlag { get; set; }
        
        public string ManufacturerVendorDescription { get; set; }

        public string DistributionPoint { get; set; }

        public string ParId { get; set; }

        public string ItemType { get; set; }

        public string IUOMUPN { get; set; }

        public string PUOMUPN { get; set; }

        public int LastPO { get; set; }

        public Decimal CostIUOM { get; set; }

        public Decimal CostPUOM { get; set; }

        public int OnorderIUOM { get; set; }

        public int OnorderPUOM { get; set; }

        public string TaxCode { get; set; }

        public string AlgorInPatient { get; set; }

        public string AlgorOutPatient { get; set; }

        public int PurchaseDept { get; set; }

        public bool Labels { get; set; }

        public string PhysicalLoc { get; set; }

        public int ReorderPoint { get; set; }

        public long ExpGLAccount { get; set; }

        public long InvGLAccount { get; set; }

        public List<AlternateUOMRecord> AlternateUOMRecords { get; set; }


        public List<FStoreDeptModel> FStore { get; set; }

        public bool InvalidVendorFlag { get; set; }
        public UOMDetail UOMDetail { get; set; }

    }
}
