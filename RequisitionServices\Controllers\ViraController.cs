﻿using log4net;
using RequisitionServices.DomainModel.Vira;
using RequisitionServices.DomainServices.Interface;
using System;
using System.Threading.Tasks;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class ViraController : ApiController
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(ViraController));
        readonly IViraService _viraService;
        public ViraController(IViraService viraService)
        {
            _viraService = viraService;
        }

        [HttpGet]
        public async Task<ViraItemStatus> GetViraItemStatusRecordById(int requisitionId, int requisitionItemId)
        {
            try
            {
                return await _viraService.GetViraItemStatusRecordById(requisitionId, requisitionItemId);
            }
            catch (Exception ex)
            {
                log.Error($"Error in GetViraItemStatusRecordById: {ex.Message}", ex);
                throw;
            }
        }

        [HttpPost]
        public async Task<ViraItemStatus> CreateViraItemStatusRecord(ViraItemStatus viraItemStatus)
        {
            try
            {
                return await _viraService.CreateViraItemStatusRecord(viraItemStatus);
            }
            catch (Exception ex)
            {
                log.Error($"Error in CreateViraItemStatusRecord: {ex.Message}", ex);
                throw;
            }
        }

        [HttpDelete]
        public async Task<string> DeleteViraItemStatusRecord(int requisitionId, int requisitionItemId)
        {
            try
            {
                return await _viraService.DeleteViraItemStatusRecord(requisitionId, requisitionItemId);
            }
            catch (Exception ex)
            {
                log.Error($"Error in DeleteViraItemStatusRecord: {ex.Message}", ex);
                throw;
            }
        }

        [HttpPost]
        public async Task<string> UpdateViraItemStatusRecord(ViraItemStatus viraItemStatus)
        {
            try
            {
                return await _viraService.UpdateViraItemStatusRecord(viraItemStatus);
            }
            catch (Exception ex)
            {
                log.Error($"Error in UpdateViraItemStatusRecord: {ex.Message}", ex);
                throw;
            }
        }
    }
}