﻿using System.Web.Http;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainServices.Interface;

namespace RequisitionServices.Controllers
{
    [RoutePrefix("facilityWorkflow")]
    public class FacilityWorkflowController : ApiController
    {
        readonly IFacilityWorkflowService _facilityWorkflowService;
        readonly IUserService _userService;

        public FacilityWorkflowController(IFacilityWorkflowService facilityWorkflowService, IUserService userService)
        {
            _facilityWorkflowService = facilityWorkflowService;
            _userService = userService;
        }

        [Route("")]
        [HttpGet]
        public FacilityWorkflowDTO Get(string coid, WorkflowTypeEnum workflowType)
        {
            return _facilityWorkflowService.Get(coid, workflowType);
        }

        [Route("")]
        [HttpPost]
        public void Save(SaveFacilityWorkflowDTO request)
        {
            _userService.SaveFacilityWorkflowSteps(request);
        }
    }
}