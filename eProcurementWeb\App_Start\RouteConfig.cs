﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace eProcurementWeb
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");
            routes.IgnoreRoute("favicon.ico");
            routes.IgnoreRoute("favicon.png");
            
            routes.MapRoute(
                name: "myRequisitions",
                url: "MyRequisitions/{anything}",
                defaults: new { controller = "Home", action = "Index", anything = UrlParameter.Optional });
            routes.MapRoute(
                name: "requisitionDetails",
                url: "MyRequisition/{requisitionId}",
                defaults: new { controller = "Home", action = "Index", requisitionId = UrlParameter.Optional });
            routes.MapRoute(
               name: "legacyRequisitionDetails",
               url: "MyRequisition/{requisitionId}/Coid/{coid}/CC/{countryCode}",
               defaults: new { controller = "Home", action = "Index", requisitionId = UrlParameter.Optional, coid = UrlParameter.Optional, countryCode = UrlParameter.Optional });
           routes.MapRoute(
                name: "myApprovals",
                url: "MyApprovals/{anything}",
                defaults: new { controller = "Home", action = "Index", anything = UrlParameter.Optional });              
            routes.MapRoute(
                name: "approvalDetails",
                url: "MyApproval/{requisitionId}",
                defaults: new { controller = "Home", action = "Index", requisitionId = UrlParameter.Optional });
            routes.MapRoute(
                name: "billOnlyReviewDetails",
                url: "BillOnlyReviewDetails/{requisitionId}",
                defaults: new { controller = "Home", action = "Index", requisitionId = UrlParameter.Optional });    
            routes.MapRoute(
                name: "myReports",
                url: "MyReports/{anything}",
                defaults: new { controller = "Home", action = "Index", anything = UrlParameter.Optional });
            routes.MapRoute(
                name: "admin",
                url: "Admin/{anything}",
                defaults: new { controller = "Home", action = "Index", anything = UrlParameter.Optional });
            routes.MapRoute(
                name: "CommentNotifications",
                url: "Comments/{anything}",
                defaults: new {controller = "Home", action = "Index", anything = UrlParameter.Optional});
            routes.MapRoute(
                name: "BillOnlyReview",
                url: "BillOnlyReview/{anything}",
                defaults: new {controller = "Home", action = "Index", anything = UrlParameter.Optional});
            routes.MapRoute(
                name: "error",
                url: "Error/{anything}",
                defaults: new { controller = "Home", action = "Index", anything = UrlParameter.Optional });
            routes.MapRoute(
               name: "unauthorizedAccess",
               url: "UnauthorizedAccess/{anything}",
               defaults: new { controller = "Home", action = "Index", anything = UrlParameter.Optional });
            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            );
        }
    }
}
