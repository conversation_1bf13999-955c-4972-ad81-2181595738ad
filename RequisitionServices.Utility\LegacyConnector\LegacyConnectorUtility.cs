﻿using log4net;
using Microsoft.Web.Http;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace RequisitionServices.Utility.LegacyConnector
{
    /// <summary>
    /// <Userstory>US119491</Userstory>
    /// <para>Class that ha methods to implement HTTP calls to legacy connector API</para>
    /// </summary>
    public static class LegacyConnectorUtility
    {
        private static readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);

        /// <summary>
        /// Executes a HTTP GET call to the specified URL/Action
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="legacyConnectorUrl">URL of API</param>
        /// <param name="actionMethod">Action on API to be executed</param>
        /// <param name="requestQueryparameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="requestHeaders"></param>
        /// <returns></returns>
        public static T ExecuteApiGetTo<T>(string legacyConnectorUrl, string actionMethod, Dictionary<string, string> requestQueryparameters, Dictionary<string, string> requestHeaders)
        {
            string finalLegacyConnectorUrl = null;
            HttpClient legacyConnectorClient = null;

            try
            {
                finalLegacyConnectorUrl = QueryStringBuilder(legacyConnectorUrl + actionMethod, requestQueryparameters);

                legacyConnectorClient = LegacyConnectorFactory.GetLegacyConnectorClient(legacyConnectorUrl);

                AddClientHeaders(legacyConnectorClient, requestHeaders);

                Logger.Debug($"Initiating call {finalLegacyConnectorUrl}");

                using (var legacyConnectorResponse = legacyConnectorClient.GetAsync(finalLegacyConnectorUrl).Result)
                {
                    CleanClientHeaders(legacyConnectorClient, requestHeaders);

                    if (legacyConnectorResponse.IsSuccessStatusCode)
                    {
                        var returnResponse = legacyConnectorResponse.Content.ReadAsAsync<T>();
                        return returnResponse.Status == TaskStatus.Faulted ? default(T) : returnResponse.Result;
                    }
                    else if (new[] { HttpStatusCode.BadRequest, HttpStatusCode.Unauthorized, HttpStatusCode.NotFound }.Contains(legacyConnectorResponse.StatusCode))
                    {
                        Logger.Info($"{finalLegacyConnectorUrl} returned {legacyConnectorResponse.StatusCode} with content {legacyConnectorResponse.Content}");
                        var returnResponse = legacyConnectorResponse.Content.ReadAsStringAsync();
                        T result = JsonConvert.DeserializeObject<T>(returnResponse.Result);
                        return returnResponse.Status == TaskStatus.Faulted ? default(T) : result;
                    }
                    else
                    {
                        Logger.Error($"{finalLegacyConnectorUrl} returned {legacyConnectorResponse.StatusCode} with content {legacyConnectorResponse.Content}");
                        throw new Exception($"HTTP {legacyConnectorResponse.StatusCode} Error calling API service. URL: {finalLegacyConnectorUrl}");
                    }
                }                
            }
            catch (Exception ex)
            {
                CleanClientHeaders(legacyConnectorClient, requestHeaders);
                Logger.Error($"Method: ExecuteAPIGetTo exception {ex.Message}, inner exception {(ex.InnerException != null ? JsonConvert.SerializeObject(ex.InnerException) : string.Empty)}");
                throw new Exception($"Method: ExecuteAPIGetTo exception {ex.Message}, inner exception {(ex.InnerException != null ? JsonConvert.SerializeObject(ex.InnerException) : string.Empty)}");
            }
        }

        /// <summary>
        /// Executes a HTTP GET call to the specified URL/Action
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="legacyConnectorTokenUrl">URL of apigee cloud token API</param>
        /// <param name="tokenUsername">Basic Auth username</param>
        /// <param name="TokenPassword">Basic Auth password</param>
        /// <param name="requestQueryparameters">Dictionary of Query parameters - parameter name, parameter value</param>
        /// <returns></returns>
        public static T ExecuteTokenApiGetTo<T>(string legacyConnectorTokenUrl, string tokenUsername, string TokenPassword, Dictionary<string, string> requestQueryparameters)
        {
            string finalLegacyConnectorUrl = null;
            HttpClient legacyConnectorTokenClient = null;

            try
            {
                legacyConnectorTokenClient = LegacyConnectorFactory.GetLegacyConnectorTokenClient(legacyConnectorTokenUrl);

                finalLegacyConnectorUrl = QueryStringBuilder(legacyConnectorTokenUrl, requestQueryparameters);
                
                var authByteArray = Encoding.ASCII.GetBytes(tokenUsername + ":" + TokenPassword);
                legacyConnectorTokenClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(authByteArray));

                Logger.Debug($"Initiating call {finalLegacyConnectorUrl}");

                using (var legacyConnectorResponse = legacyConnectorTokenClient.GetAsync(finalLegacyConnectorUrl).Result)
                {
                    if (legacyConnectorResponse.IsSuccessStatusCode)
                    {
                        var returnResponse = legacyConnectorResponse.Content.ReadAsAsync<T>();
                        return returnResponse.Status == TaskStatus.Faulted ? default(T) : returnResponse.Result;
                    }
                    else if (legacyConnectorResponse.StatusCode == HttpStatusCode.NotFound)
                    {
                        Logger.Info($"{finalLegacyConnectorUrl} returned {legacyConnectorResponse.StatusCode} with content {legacyConnectorResponse.Content}");
                        throw new KeyNotFoundException($"HTTP {legacyConnectorResponse.StatusCode} Error calling Token API service. URL: {finalLegacyConnectorUrl}");
                    }
                    else
                    {
                        Logger.Error($"{finalLegacyConnectorUrl} returned {legacyConnectorResponse.StatusCode} with content {legacyConnectorResponse.Content}");
                        throw new Exception($"HTTP {legacyConnectorResponse.StatusCode} Error calling Token API service. URL: {finalLegacyConnectorUrl}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"Method: ExecuteAPIGetTo exception {ex.Message}, inner exception {(ex.InnerException != null ? JsonConvert.SerializeObject(ex.InnerException) : string.Empty)}");
                throw new Exception($"Method: ExecuteAPIGetTo exception {ex.Message}, inner exception {(ex.InnerException != null ? JsonConvert.SerializeObject(ex.InnerException) : string.Empty)}");
            }
        }

        /// <summary>
        /// Returns a URL-encoded query string with a supplied list of parameters
        /// </summary>
        /// <param name="Url">URL of Legacy Connector API</param>
        /// <param name="requestQueryparameters">Dictionary of Query parameters - parameter name, parameter value</param>
        /// <returns> Encoded URL query string</returns>
        private static string QueryStringBuilder(string Url, Dictionary<string, string> requestQueryparameters)
        {
            var uriBuilder = new UriBuilder(Url)
            {
                Query = string.Join("&", requestQueryparameters.Select(rqp => $"{HttpUtility.UrlEncode(rqp.Key)}={HttpUtility.UrlEncode(rqp.Value)}"))
            };
            return uriBuilder.ToString();
        }

        /// <summary>
        /// Add the request headers to the client with a supplied list of parameters
        /// </summary>
        /// <param name="client">legacyConnector HTTP Client </param>
        /// <param name="clientHeaders">Dictionary of client Headers  - header name, header value</param>
        /// <returns></returns>
        private static void AddClientHeaders(HttpClient client, Dictionary<string, string> clientHeaders = null)
        {
            if (clientHeaders != null)
                foreach (var header in clientHeaders.Where(h => !client.DefaultRequestHeaders.Contains(h.Key)))
                {
                    client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                }
        }

        /// <summary>
        /// Removes the request headers from the client with a supplied list of parameters
        /// </summary>
        /// <param name="client">legacyConnector HTTP Client </param>
        /// <param name="clientHeaders">Dictionary of client Headers  - header name, header value</param>
        /// <returns></returns>
        private static void CleanClientHeaders(HttpClient client, Dictionary<string, string> clientHeaders = null)
        {
            if(clientHeaders != null)
                foreach (var header in clientHeaders.Where(h => client.DefaultRequestHeaders.Contains(h.Key)))
                {
                    client.DefaultRequestHeaders.Remove(header.Key);
                }
        }
    }
}
