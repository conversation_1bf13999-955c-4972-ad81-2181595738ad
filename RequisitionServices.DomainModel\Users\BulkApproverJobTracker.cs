﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Users
{
    public class BulkApproverJobTracker
    {
        [Key] public int JobId { get; set; }

        public Guid Id { get; set; }
        public int UserId { get; set; }

        [ForeignKey("UserId")] public User User { get; set; }

        public int Status { get; set; }

        [Column(TypeName = "varchar(MAX)")] public string JsonRequest { get; set; }

        [Column(TypeName = "DateTime2")] public DateTime CreateDateTime { get; set; }

        [Column(TypeName = "DateTime2")] public DateTime? StartDateTime { get; set; }

        [Column(TypeName = "DateTime2")] public DateTime? EndDateTime { get; set; }

        [Column(TypeName = "varchar(MAX)")] public string ErrorMessage { get; set; }
        
    }
}