﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class SPRStatusModel
    {
        public Requisition MapToRequisition()
        {
            if (this.ReqStatusHeader != null)
            {
                var requisition = new Requisition()
                {
                    LocationIdentifier = this.ReqStatusHeader.Coid.ToString() + "_" + this.ReqStatusHeader.Department.ToString(),
                    CreateDate = this.ReqStatusHeader.ReqDate
                };

                if (this.ReqDetails != null)
                {
                    requisition.RequisitionItems = new List<RequisitionItem>();                    
                    foreach (var reqDetail in this.ReqDetails)
                    {

                        var reqItem = new RequisitionItem()
                        {
                            Id = reqDetail.Id,
                            QuantityToOrder = reqDetail.ItemQuantity,
                            CreateDate = this.ReqStatusHeader.ReqDate,
                            ParentSystemId = reqDetail.SPRNumber,
                            PONumber = reqDetail.PONumber,
                            TotalCost = reqDetail.UnitPrice,
                            SPRDetail = new SPRDetail()
                            {
                                PartNumber = reqDetail.ReorderNumber,
                                VendorId = reqDetail.VendorNumber,
                                RejectCode = reqDetail.RejectCode,
                                RejectionComments = reqDetail.RejectionComments
                            }
                        };

                        //TODO: find out if the item number will be 0 if no SMART number was given
                        //if (reqDetail.SPRItemNumber != 0)
                        //{
                        //    reqItem.ItemId = reqDetail.SPRItemNumber.ToString();
                        //}

                        switch (reqDetail.Status)
                        {
                            case "NOT PROCESSED":
                                reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Processing;
                                break;
                            case "Temporary Purchase Order Created":
                                reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.POInProcess;
                                break;
                            case "Purchase Order Created":
                                reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.POCreated;
                                break;
                            case "Deleted":
                                reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Rejected;
                                break;
                            default:
                                reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Unknown;
                                break;
                        }

                        requisition.RequisitionItems.Add(reqItem);
                    }
                }

                return requisition;
            }
            else
            {
                return null;
            }
        }

        private RequisitionItemStatusTypeEnum GetPartialFillRequisitionStatus(int orderQuantity, int issuedQuantity, RequisitionItemStatusTypeEnum currentStatus)
        {
            if (issuedQuantity >= orderQuantity)
            {
                return currentStatus;
            }

            return RequisitionItemStatusTypeEnum.NoPartialFill;
        }

        public SPRStatusHeaderModel ReqStatusHeader { get; set; }
        
        public List<SPRStatusDetailModel> ReqDetails { get; set; }
    }
}
