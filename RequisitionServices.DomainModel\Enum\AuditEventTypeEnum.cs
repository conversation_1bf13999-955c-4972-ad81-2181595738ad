﻿using System.ComponentModel;

namespace RequisitionServices.DomainModel.Enum
{
    public enum AuditEventTypeEnum
    {
        [Description("Standard Approval Amount Changed")]
        StandardApprovalAmountChanged = 1,
        [Description("Capital Approval Amount Changed")]
        CapitalApprovalAmountChanged = 2,
        [Description("Delegate Added")]
        DelegateAdded = 3,
        [Description("Delegate Deleted")]
        DelegateDeleted = 4
    }
}
