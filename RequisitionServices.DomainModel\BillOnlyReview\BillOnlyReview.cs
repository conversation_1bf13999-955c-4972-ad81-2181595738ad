﻿using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class BillOnlyReview
    {
        public BillOnlyReview() { }
        public BillOnlyReview(BillOnlyReviewDTO billOnlyReviewDTO)
        {
            if(billOnlyReviewDTO!=null)
            {
                this.RequisitionId = billOnlyReviewDTO.RequisitionId;
                this.RequisitionStatusTypeId = billOnlyReviewDTO.RequisitionStatusTypeId;
                this.RequisitionStatusTypeDescription = billOnlyReviewDTO.RequisitionStatusTypeDescription;
                this.RequisitionTypeId = billOnlyReviewDTO.RequisitionTypeId;
                this.RequisitionTypeDescription = billOnlyReviewDTO.RequisitionTypeDescription;
                this.PatientId = billOnlyReviewDTO.PatientId;
                this.PatientName = billOnlyReviewDTO.PatientName;
                this.Provider = billOnlyReviewDTO.Provider;
                this.LocationIdentifier = billOnlyReviewDTO.LocationIdentifier;
                this.CreateDate = billOnlyReviewDTO.CreateDate;
                this.CreatedBy = billOnlyReviewDTO.CreatedBy;
                this.IsVendor = billOnlyReviewDTO.IsVendor;
                this.RequisitionSubmissionTypeId = billOnlyReviewDTO.RequisitionSubmissionTypeId;
                this.RequisitionerFirstName = billOnlyReviewDTO.RequisitionerFirstName;
                this.RequisitionerLastName = billOnlyReviewDTO.RequisitionerLastName;
                this.ProcedureDate = billOnlyReviewDTO.ProcedureDate;
                this.CountryCode = billOnlyReviewDTO.CountryCode;
            }
        }
        public int RequisitionId { get; set; }

        public int RequisitionStatusTypeId { get; set; }

        public List<RequisitionItem> RequisitionItems { get; set; }

        public string RequisitionStatusTypeDescription { get; set; }

        public string PatientId { get; set; }

        public string PatientName { get; set; }

        public string Provider { get; set; }

        public string LocationIdentifier { get; set; }

        public int RequisitionTypeId { get; set; }

        public string RequisitionTypeDescription { get; set; }

        public DateTime? CreateDate { get; set; }

        public string CreatedBy { get; set; }

        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type description.
        /// </summary>
        public string RequisitionSubmissionTypeDescription { get; set; }

        public string RequisitionerFirstName { get; set; }

        public string RequisitionerLastName { get; set; }

        public DateTime? ProcedureDate { get; set; }

        public string CountryCode { get; set; }
    }
}

