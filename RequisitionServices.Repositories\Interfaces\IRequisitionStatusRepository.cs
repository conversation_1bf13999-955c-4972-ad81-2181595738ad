﻿using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IRequisitionStatusRepository
    {
        IEnumerable<RequisitionItemStatusHistory> GetItemStatusHistory(int requisitionId);
        IEnumerable<RequisitionStatusHistory> GetStatusHistory(int requisitionId);

        void AddItemStatusHistory(RequisitionItemStatusHistory requisitionItemStatusHistory);

        void AddItemStatusHistories(IEnumerable<RequisitionItemStatusHistory> requisitionItemStatusHistories);

        void AddStatusHistory(RequisitionStatusHistory requisitionStatusHistory);
    }
}
