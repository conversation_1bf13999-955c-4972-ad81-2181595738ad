﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{EB4C59C9-7B1E-4069-9892-3CCEE7E71DA8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RequisitionServices.Tests</RootNamespace>
    <AssemblyName>RequisitionServices.Tests</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{3AC096D0-A1C2-E12C-1390-A8335801FDAB};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\QA\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Production\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\Sandbox\</OutputPath>
    <DefineConstants>TRACE;DEBUG;SANDBOX</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|AnyCPU'">
    <OutputPath>bin\Release\</OutputPath>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\QASandbox\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>full</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.10.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.4.1\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.2\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.2\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ApplicationInsights, Version=2.2.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\packages\Microsoft.ApplicationInsights.2.2.0\lib\net46\Microsoft.ApplicationInsights.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.WebApi.Versioning, Version=4.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Versioning.4.0.0\lib\net45\Microsoft.AspNet.WebApi.Versioning.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.VisualStudio.QualityTools.UnitTestFramework, Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Moq, Version=4.5.29.0, Culture=neutral, PublicKeyToken=69f491c39445e920, processorArchitecture=MSIL">
      <HintPath>..\packages\Moq.4.5.29\lib\net45\Moq.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.2\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.2\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.2\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.2\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.WebRequest" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppInsights\RequestFilteringTest.cs" />
    <Compile Include="Controllers\RequsitionControllerTest.cs" />
    <Compile Include="Controllers\UserControllerTest.cs" />
    <Compile Include="DomainServices\BillOnlyReviewServiceTests.cs" />
    <Compile Include="DomainServices\CartServiceTests.cs" />
    <Compile Include="DomainServices\CommentServiceTests.cs" />
    <Compile Include="DomainServices\ConfigurationServiceTests.cs" />
    <Compile Include="DomainServices\FacilityWorkflowServiceTests.cs" />
    <Compile Include="DomainServices\RequisitionServiceTests.cs" />
    <Compile Include="DomainServices\UserServiceTests.cs" />
    <Compile Include="DomainServices\ViraItemStatusServiceTests.cs" />
    <Compile Include="Helper\APIHelper.cs" />
    <Compile Include="Helper\AppInsightsTestData.cs" />
    <Compile Include="Helper\TestUserData.cs" />
    <Compile Include="Helper\UserServiceTestData.cs" />
    <Compile Include="MMISServices\Utilities\TestExtensionMethods.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="AuthenticationTests.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="App.config">
      <TransformOnBuild>true</TransformOnBuild>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RequisitionServices.Database\RequisitionServices.Database.csproj">
      <Project>{75cfcf81-401a-4388-be1f-ed579c161b41}</Project>
      <Name>RequisitionServices.Database</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.DomainModel\RequisitionServices.DomainModel.csproj">
      <Project>{6ebe285e-ac6c-4a2a-9aa6-0a906b7ba723}</Project>
      <Name>RequisitionServices.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.DomainServices\RequisitionServices.DomainServices.csproj">
      <Project>{7c18df69-7090-4cd5-944a-d9ed0b2debd0}</Project>
      <Name>RequisitionServices.DomainServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.MMISServices\RequisitionServices.MMISServices.csproj">
      <Project>{706343dc-ab56-4a18-a4fc-cb852c6ca64c}</Project>
      <Name>RequisitionServices.MMISServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Repositories\RequisitionServices.Repositories.csproj">
      <Project>{79f7e2ee-74bc-4854-b2d2-e9b5035ac1ee}</Project>
      <Name>RequisitionServices.Repositories</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Utility\RequisitionServices.Utility.csproj">
      <Project>{6a4f4b1c-f81d-45ff-8b83-b693a6dba39d}</Project>
      <Name>RequisitionServices.Utility</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices\RequisitionServices.csproj">
      <Project>{7DF917C3-6C1E-4034-9F56-5D8EA07816E9}</Project>
      <Name>RequisitionServices</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Debug.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.Production.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.QA.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.QASandbox.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.Release.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="App.Sandbox.config">
      <DependentUpon>App.config</DependentUpon>
      <IsTransformFile>True</IsTransformFile>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>