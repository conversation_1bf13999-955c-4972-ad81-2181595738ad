﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionPurchasingReportItemDTO
    {
        /// <summary>
        /// Requestion Id
        /// </summary>
        [Key]
        public int Id { get; set; }
        /// <summary>
        /// Requestion Item Id
        /// </summary>
        public string ItemId { get; set; }
        /// <summary>
        /// Requestion Item Status
        /// </summary>
        public string RequisitionItemStatus { get; set; }
        /// <summary>
        /// Vendor Id
        /// </summary>
        public int VendorId { get; set; }
        /// <summary>
        /// Vendor Name
        /// </summary>
        public string VendorName { get; set; }
        /// <summary>
        /// Requestion Item Status Type Id
        /// </summary>
        public int RequisitionItemStatusTypeId { get; set; }
        /// <summary>
        /// Parent System Id
        /// </summary>
        public string ParentSystemId { get; set; }
        /// <summary>
        /// Original Parent System Id
        /// </summary>
        public string OriginalParentSystemId { get; set; }
        /// <summary>
        /// Po Number
        /// </summary>
        public int? PONumber { get; set; }
        /// <summary>
        /// Parent Requisition Item Id
        /// </summary>
        public int? ParentRequisitionItemId { get; set; }
        /// <summary>
        /// Par Identifier
        /// </summary>
        public string ParIdentifier { get; set; }
        /// <summary>
        /// Discount
        /// </summary>
        public decimal? Discount { get; set; }
        /// <summary>
        /// Unit Cost
        /// </summary>
        public decimal? UnitCost { get; set; }
        /// <summary>
        /// SPR Detail
        /// </summary>
        public SPRDetailDTO SPRDetail { get; set; }
        /// <summary>
        /// Quantity to Order
        /// </summary>
        public int QuantityToOrder { get; set; }
        /// <summary>
        /// VBO Hold Item Conversion Unit Cost
        /// </summary>
        public decimal? VboHoldItemConversionUnitCost { get; set; }
    }
}
