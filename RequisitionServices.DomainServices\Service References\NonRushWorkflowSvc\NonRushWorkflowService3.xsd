<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://local-approvalworkflow.healthtrustpg.com/" elementFormDefault="qualified" targetNamespace="http://local-approvalworkflow.healthtrustpg.com/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://local-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?xsd=xsd1" namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" />
  <xs:element name="ApproveNonRushRequisition">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" minOccurs="0" name="Requisition" nillable="true" type="q1:Requisition" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>