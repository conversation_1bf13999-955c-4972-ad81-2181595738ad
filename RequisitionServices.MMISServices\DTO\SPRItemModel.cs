﻿using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class SPRItemModel
    {
        public int COID { get; set; }
        
        public int EXTREQID { get; set; }
        
        public string SPRType { get; set; }
        
        public int DeptNumber { get; set; }
        
        public string RequisitionerName { get; set; }
        
        public DateTime OriginalDate { get; set; }
        
        public string ReorderNumber { get; set; }
        
        public int VendorNumber { get; set; }
        
        public string VendorName { get; set; }
        
        public string ItemDescription { get; set; }
        
        public int ItemQuantity { get; set; }
        
        public string UnitOfMeasure { get; set; }
        
        public decimal ItemPrice { get; set; }

        public int ItemNumber { get; set; }

        public int GLAccountNumber { get; set; }

        public string ShipToFacilityName { get; set; }
        
        public string ShipToAddress { get; set; }
        
        public string ShipToCity { get; set; }
        
        public string ShipToState { get; set; }
        
        public string ShipToZip { get; set; }
        
        public string ShippingInstructions { get; set; }
        
        public string SpecialInstructions { get; set; }
        
        public string BudgetNumber { get; set; }
             
        public decimal TradeIn { get; set; }
        
        public string Justification { get; set; }
        
        public bool AttachmentFlag { get; set; }
    }
}
