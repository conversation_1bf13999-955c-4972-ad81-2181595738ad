USE [EProcurementQA]
GO

ALTER PROCEDURE [dbo].[usp_GetLastOrderedDetails]
	@Coid VARCHAR(5),
	@Dept VARCHAR(4),
	@ParItems [dbo].[ParItemTemplate] READONLY
AS
    BEGIN
		
	DECLARE @ParItemsTable TABLE(ItemId VARCHAR(50), ParId VARCHAR(50));

	INSERT INTO @ParItemsTable SELECT ItemId, ParId FROM @ParItems;

	WITH MaxDate_CTE (ItemId, MaxDate)
	AS
	(
		SELECT
			RI.ItemId,
			MAX(RSH.CreateDate) AS 'MaxDate'
		FROM [Requisitions] R
		INNER JOIN [RequisitionItems] RI
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN [RequisitionStatusHistories] RSH
			ON R.RequisitionId = RSH.RequisitionId
		INNER JOIN @ParItemsTable Items
			ON RI.ItemId = Items.ItemId
			AND RI.ParIdentifier = Items.ParId
		WHERE R.LocationIdentifier = @COID + '_' + @Dept
			AND RSH.RequisitionStatusTypeId = 4
			AND RI.QuantityToOrder <> 0
			AND RI.Discount IS NULL
		GROUP BY RI.ItemId
	)
		
	SELECT
		RI.ItemId,
		RI.ParIdentifier AS ParId,
		RI.QuantityToOrder AS LastOrderedQuantity,
		MaxDate.MaxDate AS LastOrderedDate
	FROM [RequisitionItems] RI
	INNER JOIN [RequisitionStatusHistories] RSH
		ON RI.RequisitionId = RSH.RequisitionId
	INNER JOIN MaxDate_CTE as MaxDate
		ON RI.ItemId = MaxDate.ItemId
		AND RSH.CreateDate = MaxDate.MaxDate
	WHERE 
		RI.Discount IS NULL

    END
GO
