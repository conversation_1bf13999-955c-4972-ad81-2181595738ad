<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="35808fc1-f464-4462-ba69-d8963ea68c37" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="ApprovalWorkflowService11.wsdl" MetadataType="Wsdl" ID="0a6ef242-379f-48f8-bebd-b86bb2a87821" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?wsdl" />
    <MetadataFile FileName="RushWorkflowService11.disco" MetadataType="Disco" ID="cb23100c-6883-44e8-8a03-63240b4a881f" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?disco" />
    <MetadataFile FileName="RushWorkflowService33.xsd" MetadataType="Schema" ID="a7974704-b12d-4973-80ef-11a3852da347" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd2" />
    <MetadataFile FileName="RushWorkflowService34.xsd" MetadataType="Schema" ID="f14911ee-964d-4126-a7e9-8371d3268ffb" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd1" />
    <MetadataFile FileName="RushWorkflowService35.xsd" MetadataType="Schema" ID="10a4a1eb-f068-43e6-b35e-e2623d8583d1" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd0" />
    <MetadataFile FileName="RushWorkflowService11.wsdl" MetadataType="Wsdl" ID="666125ac-1047-4fde-bbc3-d543b67a60f2" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?wsdl=wsdl0" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>