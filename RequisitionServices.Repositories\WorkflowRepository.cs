﻿using RequisitionServices.Database;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Data.Entity;

namespace RequisitionServices.Repositories
{
    public class WorkflowRepository : AbstractRepository, IWorkflowRepository
    {
        public WorkflowRepository(EProcurementContext context) : base(context) { }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string cOID)
        {
            return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                    .Where(x => x.User.AccountName.ToLower() == userName.ToLower() && x.COID == cOID);
        }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string cOID, int workflowTypeId)
        {
            return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                    .Where(x => x.User.AccountName.ToLower() == userName.ToLower() && x.COID == cOID && x.WorkflowTypeId == workflowTypeId);
        }

        public IEnumerable<UserWorkflowStep> GetMultipleUsersWorkflowStepsWithoutDelegates(IEnumerable<string> userNames, string COID)
        {
            var accountNames = userNames.ToList();
            return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                        .Where(x => x.COID == COID && x.DelegatedByUserId == null && accountNames.Any(y => y.ToLower() == x.User.AccountName.ToLower())).ToList();
        }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserName(string userName)
        {
            var approver = context.Approvers.Where(x => x.User.AccountName.ToLower() == userName.ToLower()).FirstOrDefault();
            if(approver != null)
            {
                return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                        .Where(x => x.DelegatedByUserId == approver.UserId);
            }

            return null;
        }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserId(int userId)
        {
            if (context.Approvers.Any(x => x.Id == userId))
            {
                return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                        .Where(x => x.DelegatedByUserId == userId);
            }
            return null;
        }

        public void DeleteDelegateSteps(int delegateApproverId)
        {
            var existingDelegatedSteps = context.UserWorkflowSteps.Where(x => x.ApproverId == delegateApproverId && x.DelegatedByUserId != null);
            if (existingDelegatedSteps != null && existingDelegatedSteps.Any())
            {
                context.UserWorkflowSteps.RemoveRange(existingDelegatedSteps);

                context.SaveChanges();
            }
        }

        public void DeleteDelegatedSteps(int DelegatedByUserId)
        {
            var existingDelegatedSteps = context.UserWorkflowSteps.Where(x => x.DelegatedByUserId == DelegatedByUserId);
            if(existingDelegatedSteps != null && existingDelegatedSteps.Any())
            {
                context.UserWorkflowSteps.RemoveRange(existingDelegatedSteps);

                context.SaveChanges();
            }
        }
        
        public IEnumerable<UserWorkflowStep> GetApproverWorkflowSteps(string approverUserName)
        {
            return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                    .Where(x => x.Approver.User.AccountName.ToLower() == approverUserName.ToLower());
        }

        public IEnumerable<UserWorkflowStep> GetApproverWorkflowSteps(string approverUserName, string COID)
        {
            return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                    .Where(x => x.Approver.User.AccountName.ToLower() == approverUserName.ToLower() && x.COID == COID);// Question: should we use .ToLower() for COID?
        }

        public IEnumerable<UserWorkflowStep> GetApproverWorkflowStepsList(string approverUserName, string coid)
        {
            return context.UserWorkflowSteps
                .Include(x => x.User)
                .Where(x => x.Approver.User.AccountName == approverUserName && x.COID == coid);
        }

        public List<UserWorkflowStep> GetDistinctWorkflowSteps(int userId, int workflowType, string COID)
        {
            return context.UserWorkflowSteps
                        .Include(x => x.Approver)
                        .Include(x => x.User)
                        .Include(x => x.Approver.User)
                   .Where(x => x.UserId == userId && x.WorkflowTypeId == workflowType && x.COID == COID)
                   .ToList();
        }

        public Task<List<UserWorkflowStep>> GetDistinctWorkflowStepsAsync(int userId, int workflowType, string coid)
        {
            return context.UserWorkflowSteps
                .Include(x => x.Approver)
                .Where(x => x.UserId == userId && x.WorkflowTypeId == workflowType && x.COID == coid)
                .ToListAsync();
        }

        public IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string userName, int workflowTypeId, string COID, IEnumerable<UserWorkflowStep> userworkflowSteps)
        {
            var existingSteps = context.UserWorkflowSteps.Where(x => x.User.AccountName.ToLower() == userName.ToLower() && x.WorkflowTypeId == workflowTypeId && x.COID.ToLower() == COID.ToLower()).ToList();

            //Delete any missing steps
            var deleteThese = existingSteps.Where(x => !userworkflowSteps.Select(y => y.Id).Contains(x.Id));

            if(deleteThese.Any())
                context.UserWorkflowSteps.RemoveRange(deleteThese);

            //Update existing
            foreach (var existingStep in existingSteps.Where(x => userworkflowSteps.Select(y => y.Id).Contains(x.Id)))
            {
                var newStep = userworkflowSteps.Where(x => x.Id == existingStep.Id).FirstOrDefault();
                if (newStep != null)
                {
                    context.Entry(existingStep).CurrentValues.SetValues(newStep);
                }
            }

            //Add new
            var newSteps = userworkflowSteps.Where(x => x.Id == 0);
            if (newSteps != null && newSteps.Any())
            {
                context.UserWorkflowSteps.AddRange(newSteps);
            }

            context.SaveChanges();

            //Get new fully-updated list
            return context.UserWorkflowSteps.Where(x => x.User.AccountName.ToLower() == userName.ToLower() && x.WorkflowTypeId == workflowTypeId && x.COID.ToLower() == COID.ToLower()).ToList();
        }
       
        public UserWorkflowStep InsertWorkflowStep(UserWorkflowStep userworkflowStep)
        {
            context.UserWorkflowSteps.Add(userworkflowStep);
            
            context.SaveChanges();
            
            return userworkflowStep;
        }

        public void DeleteWorkflowStep(UserWorkflowStep userworkflowStep)
        {
            context.UserWorkflowSteps.Attach(userworkflowStep);
            context.UserWorkflowSteps.Remove(userworkflowStep);
            context.SaveChanges();
        }

        public UserWorkflowStep GetWorkflowStepForStatusHistory(int approverId, int step, int workflowTypeId, string COID)
        {
            return context.UserWorkflowSteps.FirstOrDefault(x => x.ApproverId == approverId && x.Step == step && x.WorkflowTypeId == workflowTypeId && x.COID.ToLower() == COID.ToLower());
        }

        public UserWorkflowStep GetWorkflowStepForStatusHistory(int approverId, int step, int workflowTypeId, string COID, int userid)
        {
            return context.UserWorkflowSteps.FirstOrDefault(x => x.ApproverId == approverId && x.Step == step && x.WorkflowTypeId == workflowTypeId && x.COID.ToLower() == COID.ToLower() && x.UserId==userid);
        }

        public void SaveBulkApproverJobDetails(BulkApproverJobTracker bulkApproverJobTracker)
        {
            context.BulkApproverJobTrackers.Add(bulkApproverJobTracker);
            context.SaveChanges();
        }

        public List<BulkApproverJobTracker> GetBulkApproverJobDetails(string userName)
        {
            var response = from baj in context.BulkApproverJobTrackers
                           join u in context.Users
                           on baj.UserId equals u.Id
                           orderby baj.CreateDateTime descending
                           select new { Id = baj.Id, JobId = baj.JobId, UserId = baj.UserId, Status = baj.Status, JsonRequest = baj.JsonRequest, CreateDateTime = baj.CreateDateTime, StartDateTime = baj.StartDateTime, EndDateTime = baj.EndDateTime, User = u, ErrorMessage = baj.ErrorMessage };
            List<BulkApproverJobTracker> rowsReturned = new List<BulkApproverJobTracker>();
            
            foreach(var x in response)
            {
                rowsReturned.Add(new BulkApproverJobTracker() { Id = x.Id, JobId = x.JobId, UserId = x.UserId, Status = x.Status, JsonRequest = x.JsonRequest, CreateDateTime = x.CreateDateTime, StartDateTime = x.StartDateTime, EndDateTime = x.EndDateTime, User = x.User, ErrorMessage = x.ErrorMessage});
            }

            return rowsReturned;                
        }
    }
}
