﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Represents a requisition item used for purchasing reports
    /// </summary>
    public class PurchasingRequisitionItemDTO
    {
        /// <summary>
        /// Gets or sets the unique identifier for the requisition item.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the requisition associated with this item.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the item identifier.
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// Gets or sets the Periodic Automatic Replenishment (PAR) identifier.
        /// </summary>
        public string ParIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the main item identifier (if applicable).
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the parent requisition item (if applicable).
        /// </summary>
        public int? ParentRequisitionItemId { get; set; }

        /// <summary>
        /// Gets or sets the status type id of the requisition item.
        /// </summary>
        public int? RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the status type id of the requisition item.
        /// </summary>
        public string RequisitionItemStatusType { get; set; }

        /// <summary>
        /// Gets or sets the quantity to order for this item.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this is a rush order.
        /// </summary>
        public bool IsRushOrder { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created the requisition item.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the requisition item was created.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the parent system (if applicable).
        /// </summary>
        public string ParentSystemId { get; set; }

        /// <summary>
        /// Gets or sets the quantity fulfilled for this item.
        /// </summary>
        public int? QuantityFulfilled { get; set; }

        /// <summary>
        /// Gets or sets the original identifier of the parent system (if applicable).
        /// </summary>
        public string OriginalParentSystemId { get; set; }

        /// <summary>
        /// Gets or sets the collection of status history records for this requisition item.
        /// </summary>
        public List<RequisitionItemStatusHistory> RequisitionItemStatusHistories { get; set; }

        /// <summary>
        /// Gets or sets the details of the Special Purchase Request (SPR) associated with this item.
        /// </summary>
        public SPRDetail SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the VBO hold item conversion information.
        /// </summary>
        public VboHoldItemConversion VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the scheduled date and time for the requisition.
        /// </summary>
        public DateTimeOffset? RequisitionScheduledDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this item is a file item.
        /// </summary>
        public bool IsFileItem { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the file item has changed.
        /// </summary>
        public bool FileItemHasChanged { get; set; }

        /// <summary>
        /// Gets or sets the Purchase Order (PO) number associated with this item.
        /// </summary>
        public int? PONumber { get; set; }

        /// <summary>
        /// Gets or sets the smart item number.
        /// </summary>
        public int? SmartItemNumber { get; set; }

        /// <summary>
        /// Gets or sets the re-order information.
        /// </summary>
        public string ReOrder { get; set; }

        /// <summary>
        /// Gets or sets the catalog number of the item.
        /// </summary>
        public string CatalogNumber { get; set; }

        /// <summary>
        /// Gets or sets the PAR location.
        /// </summary>
        public string PARLocation { get; set; }

        /// <summary>
        /// Gets or sets the description of the item.
        /// </summary>
        public string ItemDescription { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the   
        //vendor supplying the item.
        /// </summary>
        public int VendorId { get; set; }

        /// <summary>
        /// Gets or sets the name of the vendor supplying the item.
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// Gets or sets the general ledger code.
        /// </summary>
        public string GeneralLedgerCode { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the item is in stock.
        /// </summary>
        public bool? StockIndicator { get; set; }

        /// <summary>
        /// Gets or sets the Unit of Measure (UOM) code.
        /// </summary>
        public string UOMCode { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the total cost of the item.
        /// </summary>
        public decimal? TotalCost { get; set; }

        /// <summary>
        /// Gets or sets the minimum stock level for the item.
        /// </summary>
        public int? MinStock { get; set; }

        /// <summary>
        /// Gets or sets the maximum stock level for the item.
        /// </summary>
        public int? MaxStock { get; set; }

        /// <summary>
        /// Gets or sets the discount applied to the item.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the parts warranty period in months.
        /// </summary>
        public byte? PartsWarrantyMonths { get; set; }

        /// <summary>
        /// Gets or sets the labor warranty period in months.
        /// </summary>
        public byte? LaborWarrantyMonths { get; set; }
    }
}

