﻿﻿using RequisitionServices.DomainModel.BillOnlyReview;
using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class BillOnlyReviewDTO
    {
        public BillOnlyReviewDTO() {}
        public BillOnlyReviewDTO(BillOnlyReview billOnlyReviewRequisition)
        {
            if (billOnlyReviewRequisition != null)
            {
                this.RequisitionId = billOnlyReviewRequisition.RequisitionId;
                this.RequisitionStatusTypeId = billOnlyReviewRequisition.RequisitionStatusTypeId;
                this.RequisitionStatusTypeDescription = billOnlyReviewRequisition.RequisitionStatusTypeDescription;
                this.RequisitionTypeId = billOnlyReviewRequisition.RequisitionTypeId;
                this.RequisitionTypeDescription = billOnlyReviewRequisition.RequisitionTypeDescription;
                this.PatientId = billOnlyReviewRequisition.PatientId;
                this.PatientName = billOnlyReviewRequisition.PatientName;
                this.Provider = billOnlyReviewRequisition.Provider;
                this.LocationIdentifier = billOnlyReviewRequisition.LocationIdentifier;
                this.CreateDate = billOnlyReviewRequisition.CreateDate;
                this.CreatedBy = billOnlyReviewRequisition.CreatedBy;
                this.IsVendor = billOnlyReviewRequisition.IsVendor;
                this.RequisitionSubmissionTypeId = billOnlyReviewRequisition.RequisitionSubmissionTypeId;
                this.RequisitionerFirstName = billOnlyReviewRequisition.RequisitionerFirstName;
                this.RequisitionerLastName =  billOnlyReviewRequisition.RequisitionerLastName;
                this.ProcedureDate = billOnlyReviewRequisition.ProcedureDate;
                this.CountryCode = billOnlyReviewRequisition.CountryCode;
                this.RequisitionSubmissionTypeDescription = billOnlyReviewRequisition.RequisitionSubmissionTypeDescription;
            }
        }

        public int RequisitionId { get; set; }

        public int RequisitionStatusTypeId { get; set; }

        public string RequisitionStatusTypeDescription { get; set; }

        public IEnumerable<BillOnlyReviewItemDTO> RequisitionItems { get; set; }

        public string PatientId { get; set; }

        public string PatientName { get; set; }

        public string Provider { get; set; }

        public IEnumerable<int?> PoNumbers { get; set; }

        public string LocationIdentifier { get; set; }

        public DateTime? CreateDate { get; set; }

        public int RequisitionTypeId { get; set; }

        public string RequisitionTypeDescription { get; set; }

        public string CreatedBy { get; set; }

        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type description.
        /// </summary>
        public string RequisitionSubmissionTypeDescription { get; set; }

        public string RequisitionerFirstName { get; set; }

        public string RequisitionerLastName { get; set; }

        public DateTime? ProcedureDate { get; set; }

        public string CountryCode { get; set; }

        public bool IsSPR { get; set; }
        
    }
}
