﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionItemStatusHistory
    {
        public int Id { get; set; }

        public int RequisitionItemId { get; set; }
        [ForeignKey("RequisitionItemId")]
        public virtual RequisitionItem RequisitionItem { get; set; }

        public int RequisitionItemStatusTypeId { get; set; }
        [ForeignKey("RequisitionItemStatusTypeId")]
        public virtual RequisitionItemStatusType RequisitionItemStatusType { get; set; }

        public string Comments { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }
        public DateTimeOffset CreateDate { get; set; }        

        [StringLength(100)]
        public string MessageId { get; set; }

    }
}
