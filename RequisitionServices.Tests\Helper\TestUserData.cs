﻿using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.Tests.Helper
{
    public class TestUserData
    {
        public string FirstName { get; set; }

        public string LastName { get; set; }

        public string Username { get; set; }

        public string Password { get; set; }

        public string EmailAddress { get; set; }

        public string PrimaryCOID { get; set; }

    }
    public class TestData
    {
        public static readonly TestUserData TestUser1 = new TestUserData
        {
            EmailAddress = @"<EMAIL>",
            FirstName = @"Test",
            LastName = @"User1",
            Password = @"P@ssword1",
            Username = @"nonaffildev/gpouser0d530d6533294",
            PrimaryCOID = "09726"
        };

        public static RequisitionItem ReqItemDBPONull = new RequisitionItem { Id = 10, PONumber = null };
        public static RequisitionItem ReqItemDBPONotNull = new RequisitionItem { Id = 10, PONumber = 12345 };
        public static RequisitionItem ReqItemSmart = new RequisitionItem { Id = 10, PONumber = 12345 };
    }
}
