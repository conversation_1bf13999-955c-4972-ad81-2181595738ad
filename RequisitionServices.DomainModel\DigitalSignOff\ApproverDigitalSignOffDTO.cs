﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.DigitalSignOff
{
    public class ApproverDigitalSignOffDTO
    {
        /// <summary>
        /// Title of who signed the Digital Sign off
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// First name of who signed the Digital Sign off
        /// </summary>
        public string FirstName { get; set; }
        /// <summary>
        /// Last name of who signed the Digital Sign off
        /// </summary>
        public string LastName { get; set; }
        /// <summary>
        /// Flag for if user was successfully validated against
        /// Active Directory during the signing process
        /// </summary>
        public bool ADValidated { get; set; }
        /// /// <summary>
        /// Holds value for the DSO clinicians 3/4
        /// </summary>
        public string AccountName { get; set; }
        /// <summary>
        /// Date the Digital Sign Off was signed and created
        /// </summary>
        public DateTime CreateDate { get; set; }
    }
}
