﻿using RequisitionServices.DomainModel.ItemInfo;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class ItemInfoController : ApiController
    {

        private IItemInfoService itemInfoService;
        public ItemInfoController(IItemInfoService itemInfoSrv)
        {
            itemInfoService = itemInfoSrv;
        }
        [HttpGet]
        public List<ItemInfoModel> GetItemInfoById(string userName, string coid, string itemId)
        {
            return itemInfoService.GetItemInfoById(userName, coid, itemId);
        }

        [HttpGet]
        public List<ItemInfoModel> GetItemInfoByReorderNbr(string userName, string coid, string reorderNumber)
        {
            return itemInfoService.GetItemInfoByReorderNbr(userName, coid, reorderNumber);
        }

    }
}