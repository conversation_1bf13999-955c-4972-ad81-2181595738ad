﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Utility.Domain;
using RequisitionServices.Utility.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.MMISServices.DTO
{
    public class BillOnlyRequisitionModel
    {
        public BillOnlyRequisitionModel() { }
        public BillOnlyRequisitionModel(Requisition requisition) 
        { 
            if(requisition != null)
            {
                var coid = Int32.Parse(LocationMapper.GetCOID(requisition.LocationIdentifier));

                this.RequisitionId = requisition.RequisitionId;
                this.SmartCountryCode = requisition.CountryCode;
                this.ParDepartment = Int32.Parse(LocationMapper.GetDepartmentId(requisition.LocationIdentifier));
                this.UserComments = requisition.Comments;

                if(requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillAndReplace)
                {
                    this.BillOnlySwitch = false;
                }
                else
                {
                    this.BillOnlySwitch = true;
                }

                if (requisition.RequisitionItems != null)
                {
                    this.ParClass = requisition.RequisitionParClass;

                    Dictionary<int, int> lsPairCounts = new Dictionary<int, int>();

                    this.RequisitionItems = new List<BillOnlyRequisitionItemModel>();
                    foreach (var reqItem in requisition.RequisitionItems)
                    {
                        var lotNumbers = new List<string>();
                        var serialNumbers = new List<string>();
                        if (reqItem.ClinicalUseDetails != null && reqItem.ClinicalUseDetails.Any())
                        {

                            if (!lsPairCounts.ContainsKey(reqItem.Id))
                            {
                                var submittedClinicalUseDetails = reqItem.ClinicalUseDetails.Where(x => !string.IsNullOrWhiteSpace(x.LotNumber) || !string.IsNullOrWhiteSpace(x.SerialNumber)).ToList();
                                lsPairCounts.Add(reqItem.Id, submittedClinicalUseDetails.Count);
                            }

                            foreach (var clinicalUseDetail in reqItem.ClinicalUseDetails)
                            {
                                if (!string.IsNullOrEmpty(clinicalUseDetail.LotNumber))
                                {
                                    lotNumbers.Add(clinicalUseDetail.LotNumber);
                                }
                                if (!string.IsNullOrEmpty(clinicalUseDetail.SerialNumber))
                                {
                                    serialNumbers.Add(clinicalUseDetail.SerialNumber);
                                }
                            }

                            this.DoctorName = reqItem.ClinicalUseDetails.First().Provider;
                            this.PatientId = reqItem.ClinicalUseDetails.First().PatientId;
                            this.PatientName = reqItem.ClinicalUseDetails.First().PatientName;
                            this.ProcedureDate = reqItem.ClinicalUseDetails.First().ProcedureDate ?? DateTime.Now;
                        }

                        int.TryParse(reqItem.ItemId, out var itemIdInteger);
                        int smartItemNumber = reqItem.Discount == null ? (reqItem.VboHoldItemConversion?.SmartItemNumber ?? itemIdInteger) : 0;

                        var newItem = new BillOnlyRequisitionItemModel()
                            {
                                Id = reqItem.Id,
                                RequisitionId = requisition.RequisitionId,
                                Coid = coid,
                                ItemNumber = smartItemNumber,
                                ItemQuantity = reqItem.QuantityToOrder,
                                LotNumbers = lotNumbers,
                                SerialNumbers = serialNumbers,
                            };

                        if(reqItem.SPRDetail != null && reqItem.VboHoldItemConversion == null)
                        {
                            if(reqItem.SPRDetail.FileAttachments.Any())
                            {
                                this.hasAttachment = true;
                            }

                            long glCode = 0;
                            Int64.TryParse(reqItem.SPRDetail.GeneralLedgerCode, out glCode);

                            newItem.ReOrderNumber = reqItem.SPRDetail.PartNumber;
                            newItem.VendorNumber = reqItem.SPRDetail.VendorId;
                            newItem.UOM = reqItem.SPRDetail.UOMCode;
                            newItem.GLAccount = glCode;
                            newItem.Description = reqItem.SPRDetail.ItemDescription;
                            newItem.CommentsJustification = EnumUtility.GetEnumDescription((SPRTypeEnum)reqItem.SPRDetail.SPRTypeId) + ":" + reqItem.SPRDetail.AdditionalInformation;
                            newItem.UnitCost = reqItem.SPRDetail.EstimatedPrice ?? 0;
                        }
                        else if(reqItem.Discount != null)
                        {
                            if (reqItem.VboHoldItemConversion == null)
                            {
                                newItem.ReOrderNumber = reqItem.ReOrder;
                                newItem.VendorNumber = reqItem.VendorId;
                                newItem.UOM = reqItem.UOMCode;
                                newItem.GLAccount = Int32.Parse(reqItem.GeneralLedgerCode);
                                newItem.Description = reqItem.ItemDescription;
                                newItem.CommentsJustification = GetWasteParCommentJustification(reqItem.Discount);
                                newItem.Discount = reqItem.Discount;
                                newItem.UnitCost = Math.Round((decimal) (reqItem.UnitCost) * (100 - (decimal) reqItem.Discount) / 100, 2);
                            }
                            else
                            {
                                newItem.ReOrderNumber = reqItem.VboHoldItemConversion.ItemDetails.Item.ReorderNumber;
                                newItem.VendorNumber = reqItem.VboHoldItemConversion.ItemDetails.Item.Vendor.Id;
                                newItem.UOM = reqItem.VboHoldItemConversion.ItemDetails.IssueUOM;
                                newItem.GLAccount = reqItem.VboHoldItemConversion.ItemDetails.GLAccount;
                                newItem.Description = reqItem.VboHoldItemConversion.ItemDetails.Item.Description;
                                newItem.CommentsJustification = GetWasteParCommentJustification(reqItem.Discount);
                                newItem.Discount = reqItem.Discount;
                                newItem.UnitCost = Math.Round((reqItem.VboHoldItemConversion.ItemDetails.ParPrice * (100 - (decimal)reqItem.Discount)) / 100, 2);
                            }
                        }

                        if (!String.IsNullOrWhiteSpace(newItem.ReOrderNumber) && reqItem.VboHoldItemConversion == null)
                        {
                            if(reqItem.SmartItemNumber != null)
                            {
                                newItem.ItemNumber = (int)reqItem.SmartItemNumber;
                            }
                            else
                            {
                                newItem.ItemNumber = 0;
                            }
                        }

                        if (reqItem.VboHoldItemConversion != null)
                        {
                            var duplicateItem = RequisitionItems.FirstOrDefault(x => x.ItemNumber == newItem.ItemNumber &&
                                                                                     string.Equals(x.ReOrderNumber, newItem.ReOrderNumber, StringComparison.InvariantCultureIgnoreCase));
                            if (duplicateItem != null)
                            {
                                reqItem.DuplicateItemId = duplicateItem.Id;
                                if (duplicateItem.Discount != newItem.Discount)
                                {
                                    duplicateItem.Discount = Math.Round(((duplicateItem.ItemQuantity * (decimal) duplicateItem.Discount) + (newItem.ItemQuantity * (decimal) newItem.Discount)) / (duplicateItem.ItemQuantity + newItem.ItemQuantity), 2);
                                    duplicateItem.CommentsJustification = GetWasteParCommentJustification(duplicateItem.Discount);
                                    duplicateItem.UnitCost = Math.Round((reqItem.VboHoldItemConversion.ItemDetails.ParPrice * (100 - (decimal) duplicateItem.Discount)) / 100, 2);
                                }

                                if (reqItem.ClinicalUseDetails != null && reqItem.ClinicalUseDetails.Any())
                                {
                                    foreach (var clinicalUseDetail in reqItem.ClinicalUseDetails)
                                    {
                                        if (lsPairCounts.ContainsKey(duplicateItem.Id) && lsPairCounts[duplicateItem.Id] < 10)
                                        {
                                            var addingLsPairDuplicate = false;

                                            if (!string.IsNullOrEmpty(clinicalUseDetail.LotNumber))
                                            {
                                                duplicateItem.LotNumbers.Add(clinicalUseDetail.LotNumber);
                                                addingLsPairDuplicate = true;
                                            }
                                            if (!string.IsNullOrEmpty(clinicalUseDetail.SerialNumber))
                                            {
                                                duplicateItem.SerialNumbers.Add(clinicalUseDetail.SerialNumber);
                                                addingLsPairDuplicate = true;
                                            }
                                            if (addingLsPairDuplicate)
                                            {
                                                lsPairCounts[duplicateItem.Id] += 1;
                                            }
                                        }
                                    }
                                }

                                duplicateItem.ItemQuantity += newItem.ItemQuantity;

                                continue;
                            }
                        }

                        this.RequisitionItems.Add(newItem);
                    }
                }
            }
        }

        private string GetWasteParCommentJustification(decimal? discount)
        {
            return $"{EnumUtility.GetEnumDescription(SPRTypeEnum.WastePar)} {discount}% Discount";
        }

        public int RequisitionId { get; set; }
        public string SmartCountryCode { get; set; }
        public int ParDepartment { get; set; }
        public string ParClass { get; set; }
        public string DoctorName { get; set; }
        public string PatientId { get; set; }
        public string PatientName { get; set; }
        public DateTime ProcedureDate { get; set; }
        public string UserComments { get; set; }
        public bool BillOnlySwitch { get; set; }

        public int ShipToCode { get; set; }

        public bool hasAttachment { get; set; }

        public List<BillOnlyRequisitionItemModel> RequisitionItems { get; set; }
    }
}
