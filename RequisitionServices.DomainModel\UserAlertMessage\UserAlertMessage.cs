﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.UserAlertMessage
{
    /// <summary>
    /// Represents a user alert message.
    /// </summary>
    public class UserAlertMessage
    {
        public UserAlertMessage() { }

        /// <summary>
        /// Gets or sets the unique identifier for the user alert message.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the message content.
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the start date of the alert message.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Gets or sets the end date of the alert message.
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the alert message is active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the type identifier for the user alert message.
        /// </summary>
        public int UserAlertMessageTypeId { get; set; }

        /// <summary>
        /// Gets or sets the user alert message type.
        /// </summary>
        [ForeignKey("UserAlertMessageTypeId")]
        public UserAlertMessageType UserAlertMessageType { get; set; }
    }
}
