﻿using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.DomainModel.Constants;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Utility;
using RequisitionServices.Utility.Domain;
using Smart.Core.Common.Utilities;

namespace RequisitionServices.DomainServices
{
    public class CommentService : ICommentService
    {
        readonly ICommentRepository _commentRepository;
        readonly IUserService _userService;
        readonly IRequisitionService _requisitionService;
        readonly IMapper _mapper;
        readonly IFacilityWorkflowService _facilityWorkflowService;

        public CommentService(ICommentRepository commentRepository, IUserService userService, IRequisitionService requisitionService, IMapper mapper, IFacilityWorkflowService facilityWorkflowService)
        {
            _commentRepository = commentRepository;
            _userService = userService;
            _requisitionService = requisitionService;
            _mapper = mapper;
            _facilityWorkflowService = facilityWorkflowService;
        }

        public CommentsResponse Get(int requisitionId, string username)
        {
            Throw.IfNegativeOrZero<BadRequestException>(() => requisitionId);
            Throw.IfNullOrEmpty<BadRequestException>(() => username);

            var requisition = _requisitionService.GetRequisition(username, requisitionId);
            var user = _userService.GetUserByAccountName(username);

            Throw.IfNull<BadRequestException>(() => requisition);
            Throw.IfNull<BadRequestException>(() => user);

            var response = new CommentsResponse
            {
                Comments = Get(requisitionId)
            };

            if (string.Equals(requisition.CreatedBy, username, StringComparison.InvariantCultureIgnoreCase))
            {
                response.CanAddComment = true;
            }
            else if (requisition.IsVendor)
            {
                var facilityWorkflowSteps = _facilityWorkflowService.Get(LocationMapper.GetCOID(requisition.LocationIdentifier), WorkflowTypeEnum.Vendor).Steps;
                response.CanAddComment = facilityWorkflowSteps.Any(ws => string.Equals(ws.Approver.User.AccountName, username, StringComparison.InvariantCultureIgnoreCase));
            }
            else
            {
               var userWorkflowSteps = _userService.GetUserWorkflowSteps(requisition.CreatedBy, LocationMapper.GetCOID(requisition.LocationIdentifier), (int)requisition.ApplicableWorkflowType);
               response.CanAddComment = userWorkflowSteps.Any(ws => string.Equals(ws.Approver.User.AccountName, username, StringComparison.InvariantCultureIgnoreCase));
            }

            _commentRepository.RemoveNotifications(requisition.RequisitionId, user.Id);

            return response;
        }

        public List<CommentDTO> Add(AddCommentDTO request)
        {
            Throw.IfNull<BadRequestException>(() => request);
            Throw.IfAnyNullOrEmpty<BadRequestException>(() => request.Username, () => request.Text);
            Throw.IfNegativeOrZero<BadRequestException>(() => request.RequisitionId);
            if (request.Text.Length > Values.RequisitionCommentMaxLength)
            {
                throw new BadRequestException($"Comment must be {Values.RequisitionCommentMaxLength} characters or less");
            }

            var requisition = _requisitionService.GetRequisition(request.Username, request.RequisitionId);
            var user = _userService.GetUserByAccountName(request.Username);

            Throw.IfNull<BadRequestException>(() => requisition);
            Throw.IfNull<BadRequestException>(() => user);
            Throw.IfNegativeOrZero<BadRequestException>(() => requisition.RequisitionId);
            Throw.IfNegativeOrZero<BadRequestException>(() => user.Id);

            List<User> usersToNotify = new List<User>();

            if (requisition.IsVendor)
            {
                var facilityWorkflow = _facilityWorkflowService.Get(LocationMapper.GetCOID(requisition.LocationIdentifier), WorkflowTypeEnum.Vendor);
                usersToNotify = facilityWorkflow == null ? new List<User>() :
                    facilityWorkflow.Steps.Select(step => step.Approver.User)
                    .Where(u => !string.Equals(u.AccountName, request.Username, StringComparison.InvariantCultureIgnoreCase))
                    .ToList();
            }
            else
            {
                var userWorkflowSteps = _userService.GetUserWorkflowSteps(requisition.CreatedBy, LocationMapper.GetCOID(requisition.LocationIdentifier), (int)requisition.ApplicableWorkflowType);
                usersToNotify = userWorkflowSteps == null ? new List<User>() :
                    userWorkflowSteps.Select(step => step.Approver.User)
                    .Where(u => !string.Equals(u.AccountName, request.Username, StringComparison.InvariantCultureIgnoreCase))
                    .ToList();
            }

            if (!string.Equals(request.Username, requisition.CreatedBy, StringComparison.InvariantCultureIgnoreCase) && !usersToNotify.Any(x => string.Equals(x.AccountName, requisition.CreatedBy, StringComparison.InvariantCultureIgnoreCase)))
            {
                usersToNotify.Add(_userService.GetUserByAccountName(requisition.CreatedBy));
            }

            var dateTime = DateTime.UtcNow;

            var comment = _commentRepository.Add(new Comment
            {
                RequisitionId = request.RequisitionId,
                UserId = user.Id,
                Text = request.Text,
                CreatedUtc = dateTime,
                LastUpdatedUtc = dateTime
            });

            AddNotifications(usersToNotify, comment.Id, requisition.RequisitionId, dateTime);

            return Get(request.RequisitionId);
        }

        public CommentNotificationRequisitionsDTO GetNotifications(string username, int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string filterText)
        {
            Throw.IfNullOrEmpty<BadRequestException>(() => username);

            var rows = _commentRepository.GetNotifications(username, rowOffset, pageSize, sortOrder, filterText);

            var response = new CommentNotificationRequisitionsDTO();

            if (rows != null && rows.Any())
            {
                var requisitions = rows.Select(row => _mapper.Map<CommentNotificationRequisition>(row)).ToList();
                
                response.Requisitions = requisitions;
                response.TotalCount = rows.First().TotalReqCount;
            }

            return response;
        }

        void AddNotifications(List<User> users, long commentId, int requistionId, DateTime dateTime)
        {
            if (users != null && users.Any())
            {
                var commentNotifications = users.Select(user => new UnreadComment
                {
                    CommentId = commentId,
                    CreateDateUtc = dateTime,
                    LastUpdatedUtc = dateTime,
                    RequisitionId = requistionId,
                    UserId = user.Id
                }).ToList();

                _commentRepository.AddNotifications(commentNotifications);
            }
        }

        List<CommentDTO> Get(int requisitionId)
        {
            var comments = _commentRepository.Get(requisitionId) ?? new List<Comment>();
            return _mapper.Map<List<CommentDTO>>(comments.OrderBy(c => c.CreatedUtc));
        }
    }
}
