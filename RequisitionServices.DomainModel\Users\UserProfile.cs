﻿
using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Users
{
    public class UserProfile
    {
        private string _userName;
        private string _domain;
        
        
        public string UserName { get => _userName; set => _userName = value.ToLower(); }
        public string Domain { get => _domain; set => _domain = value.ToLower(); }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }

        public string Title { get; set; }
        public string Department { get; set; }
        public string PhoneNumber { get; set; }
        public string PrimaryFacility { get; set; }

        /// <summary>
        /// User Type (Employee, Vendor)
        /// </summary>
        public string UserType { get; set; }
        public bool IsDeactivated { get; set; }

        public List<UserRoleNew> Roles { get; set; }

        public List<SpanOfControl> SpanOfControl
        {
            get { return SpanOfControls; }
            set { SpanOfControls = value; }
        }
        public List<SpanOfControl> SpanOfControls { get; set; }

        public string DomainSlashUserName
        {
            get
            {
                return this.Domain + "/" + this.UserName;
            }
        }

    }
}
