﻿using System;

namespace RequisitionServices.DomainModel.Vira
{
    /// <summary>
    /// Represents the status of a Vira item.
    /// </summary>
    public class ViraItemStatus
    {
        /// <summary>
        /// Gets or sets the unique identifier for the Vira item status.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the associated requisition item.
        /// </summary>
        public int RequisitionItemId { get; set; }

        
        /// <summary>
        /// Gets or sets the identifier for the associated requisition.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the publish status of the Vira item.
        /// </summary>
        public bool PublishStatus { get; set; }

        /// <summary>
        /// Gets or sets the retry status of the Vira item.
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// Gets or sets the date and time of the last retry attempt.
        /// </summary>
        public DateTime? LastRetry { get; set; }

        /// <summary>
        /// Gets or sets the approval status of the Vira item.
        /// </summary>
        public string ViraApprovalStatus { get; set; }
    }
}
