﻿using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.Interface;

namespace RequisitionServices.MMISServices.Utilities.SmartItemUtilities
{
    public class SmartSPRItemUtility : ISmartItemUtility //Used when getting information from Legacy's SPRService
    {
        public List<Requisition> GetRequisitionsFromSmart(string userName, string coid, List<string> reqIds, ref ISmartRequisitionService requisitionService)
        {
            return requisitionService.GetSPRs(userName, coid, reqIds);
        }

        public List<RequisitionItemWithSubItem> MatchRequisitionItemsToSmartItem(ref IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            IEnumerable<RequisitionItem> matchesReqItem = null;
            if (smartItem.Id != 0)
            {
                //Match up requisition item
                matchesReqItem = submittedReqItems.Where(x => x.ParentSystemId == smartItem.ParentSystemId
                                                   && x.SPRDetail != null
                                                   && smartItem.SPRDetail != null
                                                   && x.SPRDetail.VendorId == smartItem.SPRDetail.VendorId
                                                   && x.Id == smartItem.Id);
            }
            else
            {
                matchesReqItem = submittedReqItems.Where(x => x.ParentSystemId == smartItem.ParentSystemId
                                                       && x.SPRDetail != null
                                                       && smartItem.SPRDetail != null
                                                       && x.SPRDetail.VendorId == smartItem.SPRDetail.VendorId
                                                       && x.SPRDetail.PartNumber == smartItem.SPRDetail.PartNumber);
            }
            //SPR Service does not even have a subitem field, so no point in checking.  Just send the data back.
            return matchesReqItem.Select(x => 
                new RequisitionItemWithSubItem()
                {
                    originalRequisitionItem = x,
                    substitutedRequisitionItem = null
                })
            .ToList();
        }

        public List<RequisitionItemWithSubItem> MatchDiscountRequisitionItemsToSmartItem(ref IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem)
        {
            IEnumerable<RequisitionItem> matchesDiscountReqItem = null;
            if (smartItem.Id != 0)
            {
                matchesDiscountReqItem = submittedReqItems.Where(x => x.ParentSystemId == smartItem.ParentSystemId
                                                                                      && x.Discount != null
                                                                                      && x.SPRDetail == null
                                                                                      && smartItem.SPRDetail != null
                                                                                      && x.VendorId == smartItem.SPRDetail.VendorId
                                                                                      && x.Id == smartItem.Id
                                                                                      && Math.Round((decimal)(x.UnitCost * (100 - x.Discount) / 100), 2) * x.QuantityToOrder == smartItem.TotalCost);
            }
            else
            {
                matchesDiscountReqItem = submittedReqItems.Where(x => x.ParentSystemId == smartItem.ParentSystemId
                                                                                           && x.Discount != null
                                                                                           && x.SPRDetail == null
                                                                                           && smartItem.SPRDetail != null
                                                                                           && x.VendorId == smartItem.SPRDetail.VendorId
                                                                                           && Math.Round((decimal)(x.UnitCost * (100 - x.Discount) / 100), 2) * x.QuantityToOrder == smartItem.TotalCost);
            }
            //SPR Service does not even have a subitem field, so no point in checking.  Just send the data back.
            return matchesDiscountReqItem.Select(x =>
                new RequisitionItemWithSubItem()
                {
                    originalRequisitionItem = x,
                    substitutedRequisitionItem = null
                })
            .ToList();
        }

        public void UpdateRequisitionItemInfoFromSmartItem(ref RequisitionItem reqItem, RequisitionItem smartItem)
        {
            //Map over info needed
            reqItem.QuantityFulfilled = smartItem.QuantityFulfilled;
            reqItem.RequisitionItemStatusTypeId = smartItem.RequisitionItemStatusTypeId;
            reqItem.SPRDetail.RejectCode = smartItem.SPRDetail.RejectCode;
            reqItem.SPRDetail.RejectionComments = smartItem.SPRDetail.RejectionComments;
        }

        public void UpdateDiscountRequisitionItemInfoFromSmartItem(ref RequisitionItem reqItem, RequisitionItem smartItem)
        {
            //Map over info needed
            reqItem.RequisitionItemStatusTypeId = smartItem.RequisitionItemStatusTypeId;
            reqItem.RejectionComments = smartItem.SPRDetail.RejectionComments;
        }
    }
}
