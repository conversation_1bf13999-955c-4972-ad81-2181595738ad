﻿using System;

namespace RequisitionServices.DomainModel.Items
{
    public class ParItemWithLastOrdered : ParItem
    {
        public int? LastOrderedQuantity { get; set; }
        public DateTime? LastOrderedDate { get; set; }

        public ParItemWithLastOrdered() { }

        public ParItemWithLastOrdered(ParItem parItem, int? lastOrderedQuantity = null, DateTime? lastOrderedDate = null)
        {
            ParId = parItem.ParId;
            ItemId = parItem.ItemId;
            MinStock = parItem.MinStock;
            MaxStock = parItem.MaxStock;
            IssueUOM = parItem.IssueUOM;
            Item = parItem.Item;
            ParDescription = parItem.ParDescription;
            ParType = parItem.ParType;
            GLAccount = parItem.GLAccount;
            IGLAccount = parItem.IGLAccount;
            Location = parItem.Location;
            ParPrice = parItem.ParPrice;
            LastOrderedQuantity = lastOrderedQuantity;
            LastOrderedDate = lastOrderedDate;
            ManualOrderFlag = parItem.ManualOrderFlag;
           
        }
    }
}
