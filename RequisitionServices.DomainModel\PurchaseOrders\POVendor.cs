﻿
using System;
using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;
using RequisitionServices.DomainModel.Vendors;

namespace RequisitionServices.DomainModel.PurchaseOrders
{
    public class POVendor
    {
        public int VendorNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public Address VendorAddress { get; set; } 

        /// <summary>
        /// 
        /// </summary>
        public string WorkPhone { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WorkExtension { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HomePhone { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FaxNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int TermsCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Terms { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShippingMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FreightOnBoard { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int NumberOfDaysInOrderCycle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ContactName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int StandardNationalVendorNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool EdiFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EdiId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string JitFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string JitId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FaxFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool EmailFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal OrderFillRatio { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool SendFlag867 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FreightOnBoardOverrideFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FillKillFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BuyerId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int AccountsPayable { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string OrderDay { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SupplierId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int AddressId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ParentVendor { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ParentCoid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal MarkUpPercent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<string> SpecialOption { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool HealthIndustryNumberFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HealthIndustryNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool UniversalProductNumberFLAG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool MinorityFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FreightFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromCity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromCounty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromState { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromZip { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool ShipInCityFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceCity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceCounty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceState { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceZipCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool ShipOrderAcceptanceInCityFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int MinimumDollar { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int MinimumQuantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int YearToDateDollar { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int YearToDatePo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearToDateDollar { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearToDatePo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearYear { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int NumberOfDeliveries { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearNumberOfDeliveries { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime DateIncrement { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal AverageLine { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxNumber1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxNumber2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxNumber3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxIdentifier1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxIdentifier2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxIdentifier3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailAddress1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailAddress2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailAddress3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailIdentifier1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailIdentifier2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailIdentifier3 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public OrderTimes OrderTimes { get; set; }
    }
}
