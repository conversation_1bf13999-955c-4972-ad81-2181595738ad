﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace eProcurementWeb.AppInsights
{
    public static class AppInsightsRequestFilterKeywords
    {
        public static List<string> RequestFilterKeywords = new List<string>
        {
            "userId",
            "userName",
            "userNames",
            "updater",
            "delegateEmail",
            "delegateUserId",
            "patientId",
            "patientName",
            "patientNumber",
            "firstName",
            "lastName",
            "fullName",
            "reviewer",
            "id",
            "reviewerName",
            "accountName",
            "accountNames",
            "accountNumber",
            "accountStringPartial"
        };
    }
}