﻿using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class LocatorModel
    {
        public int Coid { get; set; }

        public int Dept { get; set; }

        public string Class { get; set; }

        public string UserId { get; set; }

        public int LineCount { get; set; }

        public List<LocatorLineModel> LocatorLines { get; set; }

        public List<LocatorErrorsModel> LocatorErrors { get; set; }

        public Locator MapToLocator()
        {
            var ConvLocatorErrors = new List<LocatorErrors>();
            var ConvLocatorLines = new List<LocatorLine>();

            foreach(var error in this.LocatorErrors)
            {
                ConvLocatorErrors.Add(error.MapToLocatorError());
            }

            foreach(var line in this.LocatorLines)
            {
                ConvLocatorLines.Add(line.MapToLocatorLine());
            }

            return new Locator()
            {
                Class = this.Class,
                Coid = this.Coid,
                Dept = this.Dept,
                LineCount = this.LineCount,
                LocatorErrors = ConvLocatorErrors,
                LocatorLines = ConvLocatorLines,
                UserId = this.UserId
            };
        }
    }
}
