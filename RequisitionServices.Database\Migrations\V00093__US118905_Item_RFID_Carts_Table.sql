USE [eProcurementQA]
GO

/****** Object:  Table [dbo].[Item_RFID_Carts]    Script Date: 2/5/2025 11:13:58 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Item_RFID_Carts](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[COID] [nvarchar](5) NOT NULL,
	[DepartmentNumber] [int] NOT NULL,
	[PARClass] [nvarchar](3) NOT NULL,
	[ScannedUtcCart] [datetime2](7) NOT NULL,
	[Username] [varchar](50) NOT NULL,
	[CreatedUtc] [datetime] NOT NULL,
 CONSTRAINT [PK__Item_RFID_Cart] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO


