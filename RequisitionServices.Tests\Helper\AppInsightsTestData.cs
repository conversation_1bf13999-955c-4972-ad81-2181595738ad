﻿using System.Collections.Generic;

namespace RequisitionServices.Tests.Helper
{
    public class AppInsightsTestData
    {
        public static List<string> APIList = new List<string> {
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/SaveRequisition?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/SubmitRequisition?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/SubmitApproversRequisition?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisition?requisitionId=6392&userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionForUpdate?requisitionId=6392&userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/UpdateRequisitionStatus",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/UpdateRequisitionItemStatus",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitions?COID=09391&startDate=2017-07-09T05:00:00.000Z&endDate=2017-08-09T05:00:00.000Z&userName=HCA%2FOUX7321&departmentId=746",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionsWithItemStatuses?userName=HCA%2FOUX7321&COID=09391&startDate=2017-07-09T05:00:00.000Z&endDate=2017-08-09T05:00:00.000Z&departmentId=746",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionsByUser?userName=HCA%2FOUX7321&pastDays=30",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetTemplatesByUser?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionHistoryByApprover?userName=HCA%2FOUX7321&pastDays=30",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionsByApprover?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionHistory?requisitionId=6392",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/AdvanceRequisition",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/ReInitializeAllWorkflow?key=123&userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/ReInitializeRequisitionInWorkflow?key=123&requisitionId=6392&userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetUserWorkflowStepsDelegatedByUser?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/RequestAdhocReview",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/ProvideAdhocReview",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetRequisitionAdhocReviews?requisitionId=6392",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/IsAdhocReviewAllowed?adhocReviewId=10&reviewer=HCA%2FOUX7321&reqId=6392",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/GetPendingAdhocReviewsForReviewer?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/Requisition/DeleteAttachment",
            "https://qasbx-api-requisitions.healthtrustpg.com/ClinicalData/GetProviders?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/ClinicalData/GetPatients?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/ClinicalData/GetPatient?userName=HCA%2FOUX7321&COID=09391&patientId=98765ABC",
            "https://qasbx-api-requisitions.healthtrustpg.com/ClinicalData/SearchPatientsByName?userName=HCA%2FOUX7321&COID=09391&patientName=SANDY",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/GetAllGLAccounts?userName=HCA%2FOUX7321&COID=09391&accountStringPartial=HCA",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/GetAllCostCodes?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/GetGLAccount?userName=HCA%2FOUX7321&COID=09391&accountNumber=98765ABC",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/GetAddress?userName=HCA%2FOUX7321&COID=09391&shipNumber=12345",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/GetAddresses?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/SaveFacilityNotifications?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/GetFacilityNotifications?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/Location/UpdateLocator?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/COID/getAllCOIDs?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetLocations?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetAllDepartments?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetDepartment?userName=HCA%2FOUX7321&COID=09391&departmentId=746",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/ValidateUserWorkflow?userName=HCA%2FOUX7321&workflowType=ABC&COID=09391&requisitionTotal=123",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/ValidateUserWorkflow",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetValidationOfUserWorkflows",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/RetrieveApproversForEdit",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/RetrieveUsersForEdit",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetUserEditUsers",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/RetrieveUserReportInfo",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/RetrieveUsersForDelegates",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/UpdateApprovers",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/SaveUserEditInfo?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetUserWorkflowSteps?userName=HCA%2FOUX7321&COID=09391&workflowTypeId=ABC",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetUserWorkflowsForExport",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetAllWorkflowTypes",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/SaveWorkflows?updater=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/SaveUserWorkflowSteps?updater=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/DeleteDelegatesForApprover?userName=HCA%2FOUX7321&delegateEmail=<EMAIL>",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/AssignDelegateForApprover?delegateUserId=10&delegateEmail=<EMAIL>&userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetApprover?userName=HCA%2FOUX7321&COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetApprover?userName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetUser?accountName=HCA%2FOUX7321",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/GetDelegateUserByApproverId?id=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/User/UpdateUserName?accountName=HCA&firstName=SANDY&lastName=SUNNY",
            "https://qasbx-api-requisitions.healthtrustpg.com/Par/GetParsByUserLocation?userName=HCA%2FOUX7321&COID=09391&departmentId=746",
            "https://qasbx-api-requisitions.healthtrustpg.com/Par/GetParItems?userName=HCA%2FOUX7321&COID=09391&departmentId=746&parId=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/Par/GetParItemsByItem?userName=HCA%2FOUX7321&COID=09391&departmentId=746&itemId=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/PO/GetDetailsByPO?userName=HCA%2FOUX7321&COID=09391&PONumber=10&stockIndicator=Y",
            "https://qasbx-api-requisitions.healthtrustpg.com/PO/GetPOConfirmationDetails?userName=HCA%2FOUX7321&COID=09391&PONumber=10&lineNumber=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/PO/getDetailsByDateRange?userName=HCA%2FOUX7321&COID=09391&startDate=2017-07-09T05:00:00.000Z&endDate=2017-08-09T05:00:00.000Z&department=610",
            "https://qasbx-api-requisitions.healthtrustpg.com/PO/GetPoByOptions?userId=HCA%2FOUX7321&coid=09391&startDate=2017-07-09T05:00:00.000Z&endDate=2017-08-09T05:00:00.000Z&poType=TEST&department=610&reorderNumber=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/PO/GetPoByProjectNumber?userName=HCA%2FOUX7321&coid=09391&projectNumber=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/PO/GetHistoryByPO?userId=HCA%2FOUX7321&coid=09391&poNumber=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/Vendor/GetPunchOutVendors?COID=09391&companyCode=HCA",
            "https://qasbx-api-requisitions.healthtrustpg.com/Vendor/GetVendorById?COID=09391&vendorId=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/Vendor/GetVendorDetailsById?COID=09391&vendorId=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/Vendor/GetAllVendors?COID=09391",
            "https://qasbx-api-requisitions.healthtrustpg.com/Item/GetItem?userName=HCA%2FOUX7321&COID=09391&itemId=10&parId=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/Item/GetItem?userName=HCA%2FOUX7321&COID=09391&itemId=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/Item/GetDeliveryMethods",
            "https://qasbx-api-requisitions.healthtrustpg.com/Item/GetContractDetails?userName=HCA%2FOUX7321&COID=09391&contractId=10&vendorNumber=10",
            "https://qasbx-api-requisitions.healthtrustpg.com/IINItem/GetIINItemById?userId=HCA%2FOUX7321&COID=09391&IINitemNumber=10"
        };

        public static List<string> keywords = new List<string> {
            "HCA%2FOUX7321",
            "SANDY",
            "SUNNY",
            "98765ABC",
            "<EMAIL>",
        };        

    }
}
