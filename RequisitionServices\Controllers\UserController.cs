﻿using System;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class UserController : ApiController
    {
        private IUserService userService;

        public UserController(IUserService userSvc)
        {
            this.userService = userSvc;
        }

        /// <summary>
        /// Get list of locations
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<Location> GetLocations(string userName)
        {
            return userService.GetLocations(userName);
        }

        /// <summary>
        /// Get list of all departments user has access to given a facility (COID)
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="userIsVendor"></param>
        /// <returns></returns>
        public IEnumerable<Department> GetAllDepartments(string userName, string COID, bool userIsVendor = false)
        {
            return userService.GetAllDepartments(userName, COID, userIsVendor);
        }

        /// <summary>
        /// Get the favorited COID for a certain user
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpGet]
        public string GetFavoriteFacilityId(string userName)
        {
            return userService.GetFavoriteFacilityId(userName);
        }

        /// <summary>
        /// (Re)sets the preferred COID for a certain user.
        /// </summary>
        /// <param name="personalizationDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public PersonalizationDTO SetFavoriteFacility(PersonalizationDTO personalizationDTO)
        {
            return userService.SetFavoriteFacility(personalizationDTO);
        }

        /// <summary>
        /// Removes the favorited COID for a certain user.
        /// </summary>
        /// <param name="userName"></param>
        [HttpGet]
        public void DeleteFavoriteFacility(string userName)
        {
            userService.DeleteFavoriteFacility(userName);
        }

        /// <summary>
        /// Get the ID of a favorited department, if one exists, given a facility (COID)
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpGet]
        public int GetFavoriteDepartmentId(string userName, string COID)
        {
            return userService.GetFavoriteDepartmentId(userName, COID);
        }

        /// <summary>
        /// Get the user's favorite Department, if one exists, given a facility (COID)
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="cOID"></param>
        /// <returns></returns>
        [HttpGet]
        public Department GetFavoriteDepartment(string userName, string cOID)
        {
            return userService.GetFavoriteDepartment(userName, cOID);
        }

        /// <summary>
        /// Sets a user's favorite department
        /// </summary>
        /// <param name="personalization"></param>
        /// <returns></returns>
        [HttpPost]
        public PersonalizationDTO SetFavoriteDepartment(PersonalizationDTO personalization)
        {
            return userService.SetFavoriteDepartment(personalization);
        }

        /// <summary>
        /// Removes a user's favorite department for the given facility.
        /// </summary>
        /// <param name="personalization"></param>
        /// <returns></returns>
        [HttpPost]
        public void DeleteFavoriteDepartment(PersonalizationDTO personalization)
        {
            userService.DeleteFavoriteDepartment(personalization);
        }

        /// <summary>
        /// Get the ID of a favorited PAR, if one exists, given a facility (COID) and department
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public string GetFavoriteParId(string userName, string COID, int departmentId)
        {
            return userService.GetFavoriteParId(userName, COID, departmentId);
        }

        /// <summary>
        /// Get the favorited PAR, of one exists, given a facility (COID) and department
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public Par GetFavoritePar(string userName, string COID, int departmentId)
        {
            return userService.GetFavoritePar(userName, COID, departmentId);
        }

        /// <summary>
        /// Sets a user's favorite PAR.
        /// </summary>
        /// <param name="personalization"></param>
        /// <returns></returns>
        [HttpPost]
        public PersonalizationDTO SetFavoritePar(PersonalizationDTO personalization)
        {
            return userService.SetFavoritePar(personalization);
        }

        /// <summary>
        /// Removes a user's favorite PAR for the given facility and department.
        /// </summary>
        /// <param name="personalization"></param>
        /// <returns></returns>
        [HttpPost]
        public void DeleteFavoritePar(PersonalizationDTO personalization)
        {
            userService.DeleteFavoritePar(personalization);
        }

        /// <summary>
        /// Get list of all departments user has access to given a facility (COID)
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        public IEnumerable<Department> GetAllDepartmentsForCache(string userName, string COID)
        {
            return userService.GetAllDepartmentsForCache(userName, COID);
        }

        /// <summary>
        /// Department
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public Department GetDepartment(string userName, string COID, int departmentId)
        {
            return userService.GetDepartment(userName, COID, departmentId);
        }

        /// <summary>
        /// Validates a user's workflow from database.
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="workflowType"></param>
        /// <param name="COID">Optional: Will apply Span of Control rules against COID</param>
        /// <param name="requisitionTotal">Optional: Will apply requisition total/approval amount rules</param>
        /// <returns></returns>
        [HttpGet]
        public WorkflowValidationDTO ValidateUserWorkflow(string userName, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal = null)
        {
            return userService.ValidateUserWorkflow(userName, workflowType, COID, requisitionTotal);
        }

        /// <summary>
        /// Validates a user's workflow passed in.
        /// </summary>
        /// <param name="workflowRequestValidationDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public WorkflowValidationDTO ValidateUserWorkflow(WorkflowRequestValidationDTO workflowRequestValidationDTO)
        {
            return userService.ValidateUserWorkflow(workflowRequestValidationDTO.UserWorkflowSteps, workflowRequestValidationDTO.UserName, workflowRequestValidationDTO.WorkflowType, workflowRequestValidationDTO.COID, workflowRequestValidationDTO.RequisitionTotal);
        }

        /// <summary>
        /// Validates all workflows for one user
        /// </summary>
        /// <param name="validateUserWorkflowsRequestDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public ValidationOfUserWorkflowsDTO GetValidationOfUserWorkflows(ValidateUserWorkflowsRequestDTO validateUserWorkflowsRequestDTO)
        {
            return userService.GetValidationOfUserWorkflows(validateUserWorkflowsRequestDTO);
        }

        /// <summary>
        /// Gets the User or Approver for each username passed in.
        /// </summary>
        /// <param name="userRequestDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public UserEditDTO GetUserEditUsers(UserReportInfoRequestDTO userRequestDTO)
        {
            return userService.GetUserEditUsers(userRequestDTO.UserName, userRequestDTO.Users, userRequestDTO.COID);
        }

        /// <summary>
        /// Returns the info needed for the User Report page.
        /// </summary>
        /// <param name="userReportInfoRequestDTO"></param>
        /// <returns></returns>
        [HttpPost] 
        public IEnumerable<UserReportInfoDTO> RetrieveUserReportInfo(UserReportInfoRequestDTO userReportInfoRequestDTO)
        {
            return userService.GetUserReportInfo(userReportInfoRequestDTO.UserName, userReportInfoRequestDTO.Users, userReportInfoRequestDTO.COID);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userRequestDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<User> RetrieveUsersForDelegates(UserRequestDTO userRequestDTO)
        {
            var users = userService.RetrieveUsers(userRequestDTO.UserName, userRequestDTO.COID, userRequestDTO.UsersNames);

            return users;
        }

        /// <summary>
        /// Updates all passed in approver accounts
        /// </summary>
        /// <param name="approverUpdateDTO"></param>
        /// <returns></returns>
        [HttpPost]
        [Obsolete]
        public IEnumerable<Approver> UpdateApprovers(ApproverUpdateDTO approverUpdateDTO)
        {
            var returnApprovers = userService.UpdateApprovers(approverUpdateDTO.UserName, approverUpdateDTO.Approvers, approverUpdateDTO.COID);

            return returnApprovers;
        }

        /// <summary>
        /// Updates all passed in approver accounts
        /// </summary>
        /// <param name="approverUpdateDto"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IEnumerable<Approver>> UpdateApproversAsync(ApproverUpdateDTO approverUpdateDto)
        {
            return await userService.UpdateApproversAsync(approverUpdateDto.UserName, approverUpdateDto.Approvers, approverUpdateDto.COID);
        }


        /// <summary>
        /// Saves the User's Approval amount (if applicable) and workflows (if applicable).
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="saveUserEditInfoDTO"></param>
        [HttpPost]
        [Obsolete]
        public void SaveUserEditInfo(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO)
        {
            userService.SaveUserEditInfo(userName, saveUserEditInfoDTO);
        }

        /// <summary>
        /// Saves the User's Approval amount (if applicable) and workflows (if applicable).
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="saveUserEditInfoDTO"></param>
        [HttpPost]
        public async Task<bool>  SaveUserEditInfoAsync(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO)
        {
            await userService.SaveUserEditInfoAsync(userName, saveUserEditInfoDTO);
            return true;
        }


        /// <summary>
        /// Get list of workflow steps for specified user
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="workflowTypeId"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, int? workflowTypeId = null)
        {
            var workflowSteps = userService.GetUserWorkflowSteps(userName, COID, workflowTypeId);

            return workflowSteps;
        }

        /// <summary>
        /// Grabs all the workflows for given users without delegated approvers
        /// </summary>
        /// <param name="workflowExportInputDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<WorkflowExportDTO> GetUserWorkflowsForExport(WorkflowExportInputDTO workflowExportInputDTO)
        {
            return userService.GetUserWorkflowsWithoutDelegates(workflowExportInputDTO.userNames, workflowExportInputDTO.COID, workflowExportInputDTO.SmartCountryCode);
        }

        /// <summary>
        /// Get list of workflow types
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<WorkflowType> GetAllWorkflowTypes()
        {
            var workflowTypes = userService.GetAllWorkflowTypes();

            return workflowTypes;
        }

        /// <summary>
        /// Get list of User workflow types
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<WorkflowType> GetAllUserWorkflowTypes()
        {
            return userService.GetAllUserWorkflowTypes();
        }

        /// <summary>
        /// Saves all user's workflows' steps. 
        /// </summary>
        /// <param name="updater"></param>
        /// <param name="saveWorkflowsDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<UserWorkflowDTO> SaveWorkflows(string updater, SaveWorkflowsDTO saveWorkflowsDTO)
        {
            return userService.SaveWorkflows(updater, saveWorkflowsDTO);
        }

        /// <summary>
        /// Save user workflow steps
        /// </summary>
        /// <param name="updater"></param>
        /// <param name="userworkflowDTO"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string updater, UserWorkflowDTO userworkflowDTO)
        {
            var userWorkflowSteps = userService.SaveUserWorkflowSteps(updater, userworkflowDTO.UserName, userworkflowDTO.WorkflowTypeId, userworkflowDTO.COID, userworkflowDTO.UserworkflowSteps);

            return userWorkflowSteps;
        }

        /// <summary>
        /// Delete all workflow steps that the approverId passed in has delegated
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="delegateEmail"></param>
        [HttpPost]
        public void DeleteDelegatesForApprover(string userName, string delegateEmail = null)//delegateEmail is made optional for Deleted users
        {
            userService.DeleteDelegatesForApprover(userName, delegateEmail);
        }

        /// <summary>
        /// Assign delegate to all steps user is in
        /// </summary>
        /// <param name="delegateUserId"></param>
        /// <param name="delegateEmail"></param>
        /// <param name="userName"></param>
        [HttpPost]
        public void AssignDelegateForApprover(int delegateUserId, string delegateEmail, string userName)
        {
            userService.AssignDelegateForApprover(delegateUserId, delegateEmail, userName);
        }

        /// <summary>
        /// Get an approver account by userName
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpGet]
        public Approver GetApproverByCoid(string userName, string COID)
        {
            return userService.GetApproverByUserNameAndCOID(userName, COID);
        }

        /// <summary>
        /// Get all active approvers accounts with COIDs
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<ActiveApproversDto>> GetActiveApprovers()
        {
            return await userService.GetActiveApproversAsync();
        }

        /// <summary>
        /// Get the approver's workflows
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<IEnumerable<ApproverWorkflowDto>> GetApproverWorkflows(int approverUserId)
        {
            return await userService.GetApproverWorkflowsAsync(approverUserId);
        }

        
        /// <summary>
        /// Gets the User from database by accountName
        /// </summary>
        /// <param name="accountName"></param>
        /// <returns></returns>
        [HttpGet]
        public User GetUser(string accountName)
        {
            return userService.GetUserByAccountName(accountName);
        }

        /// <summary>
        /// Gets the User from database by accountName
        /// </summary>
        /// <param name="accountName"></param>
        /// <returns></returns>
        [HttpGet]
        public User GetUserWithoutDomain(string accountName)
        {
            return userService.GetUserByAccountNameWithoutDomain(accountName);
        }

        [HttpGet]
        public Approver GetApprover(string userName)
        {
            return userService.GetFirstApproverByUserName(userName);
        }

        [HttpGet]
        public User GetDelegateUserByApproverId(int id)
        {
            return userService.GetDelegateUserByApproverId(id);
        }

        /// <summary>
        /// updates the User's first and last name
        /// </summary>
        /// <param name="accountName"></param>
        /// <param name="firstName"></param>
        /// <param name="lastName"></param>
        [HttpPost]
        public void UpdateUserName(string accountName, string firstName, string lastName)
        {
            userService.UpdateUserName(accountName, firstName, lastName);
        }

        //TODO: COMMENTED OUT UNTIL WE CAN DO ACTUAL TESTING FOR THIS.

        ///// <summary>
        ///// Adds any missing users (along with an approver record) for the given COID
        ///// </summary>
        ///// <param name="accountName"></param>
        ///// <param name="COID"></param>
        //[HttpGet]
        //public string BulkAddMissingUsersByCoid(string accountName, string COID)
        //{
        //    return userService.BulkAddMissingUsersByCoid(accountName, COID);
        //}

        /// <summary>
        /// Save Bulk Approver Remove or Exchange Data
        /// </summary>
        [HttpPost]
        public void SaveBulkApproverJob(string userName, BulkApproverJobTracker bulkApproverJobTracker)
        {
            userService.SaveBulkApproverJobDetails(userName, bulkApproverJobTracker);
        }
        
        /// <summary>
        /// GEt Bulk Approver Remove or Exchange Data to Display On Bulk Edit Status Tab
        /// </summary>
        [HttpGet]
        public GetBulkJobDetailsDTO GetBulkApproverJobDetails(string userName)
        {
            return userService.GetBulkApproverJobDetails(userName);
        }

        /// <summary>
        /// Gets the last bulk approver tracker job by bulkApproverId
        /// </summary>
        /// <param name="bulkApproverId"></param>
        [HttpGet]
        public BulkApproverJobTracker GetBulkApproverJobTracker(Guid bulkApproverId)
        {
            return userService.GetBulkApproverJobTracker(bulkApproverId);
        }

        /// <summary>
        /// Updates the bulk approver job status
        /// </summary>
        /// <param name="bulkApproverJobStatusDto"></param>
        [HttpPost]
        public void UpdateBulkApproverJobStatus(BulkApproverJobStatusDTO bulkApproverJobStatusDto)
        {
            userService.UpdateBulkApproverJobStatus(bulkApproverJobStatusDto);
        }

        /// <summary>
        /// Gets the approver by userid and coid
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="coid"></param>
        /// <returns></returns>
        [HttpGet]
        public Approver GetApproverByUserId(int userId, string coid)
        {
            return userService.GetApproverByUserId(userId, coid);
        }

        /// <summary>
        /// Get the approver list for bulk exchange
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public async Task<ActiveApproversDto> GetActiveApproversForBulkExchange(string searchAccountName, int selectedApproverUserId)
        {
            return await userService.GetActiveApproversForBulkExchangeAsync(searchAccountName, selectedApproverUserId);
        }
    }
}
