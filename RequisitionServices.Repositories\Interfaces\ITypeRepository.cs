﻿using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using System.Collections.Generic;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface ITypeRepository
    {
        IEnumerable<DeliveryMethodType> GetDeliveryMethods();

        DeliveryMethodType GetDeliveryMethod(int deliveryMethodTypeId);

        IEnumerable<WorkflowType> GetWorkflowTypes();
    }
}
