﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Represents a singular data row for returning results for purchasing report stored procedures
    /// </summary>
    [NotMapped]
    public class RequisitionPurchasingReportPageRow
    {
        /// <summary>
        /// Gets or sets the unique identifier for the requisition.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the requisition status type.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition status type.
        /// </summary>
        public string RequisitionStatusType { get; set; }

        /// <summary>
        /// Gets or sets the location identifier for the requisition.
        /// </summary>
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the comments associated with the requisition.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the requisition was created.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the requisition type.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition type.
        /// </summary>
        public string RequisitionType { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the requisition item.
        /// </summary>
        public int? RequisitionItemId { get; set; }

        /// <summary>
        /// Gets or sets the requisition identifier associated with the requisition item.
        /// </summary>
        public int RequisitionItemRequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the user who created the requisition item.
        /// </summary>
        public string RequisitionItemCreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the requisition item was created.
        /// </summary>
        public DateTime RequisitionItemCreateDate { get; set; }

        /// <summary>
        /// Gets or sets the item number of the requisition item.
        /// </summary>
        public string RequisitionItemNumber { get; set; }

        /// <summary>
        /// Gets or sets the status type identifier of the requisition item.
        /// </summary>
        public int? RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition item status type.
        /// </summary>
        public string RequisitionItemStatusType { get; set; }

        /// <summary>
        /// Gets or sets the parent system identifier for the requisition item.
        /// </summary>
        public string RequisitionItemParentSystemId { get; set; }

        /// <summary>
        /// Gets or sets the original parent system identifier for the requisition item.
        /// </summary>
        public string RequisitionItemOriginalParentSystemId { get; set; }

        /// <summary>
        /// Gets or sets the PAR identifier for the requisition item.
        /// </summary>
        public string RequisitionItemParIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the quantity of the requisition item that has been fulfilled.
        /// </summary>
        public int QuantityFulfilled { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition item is a file item.
        /// </summary>
        public bool IsFileItem { get; set; }

        /// <summary>
        /// Gets or sets the smart item number for the requisition item.
        /// </summary>
        public int SmartItemNumber { get; set; }

        /// <summary>
        /// Gets or sets the general ledger code for the requisition item.
        /// </summary>
        public string GeneralLedgerCode { get; set; }

        /// <summary>
        /// Gets or sets the catalog number for the requisition item.
        /// </summary>
        public string CatalogNumber { get; set; }

        /// <summary>
        /// Gets or sets the discount applied to the requisition item.
        /// </summary>
        public decimal? Discount { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the vendor associated with the requisition item.
        /// </summary>
        public int? VendorId { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the vendor associated with the requisition item.
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// Gets or sets the unit cost of the requisition item.
        /// </summary>
        public decimal? UnitCost { get; set; }

        /// <summary>
        /// Gets or sets the quantity of the requisition item to be ordered.
        /// </summary>
        public int? QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the PO number associated with the requisition item.
        /// </summary>
        public int? RequisitionItemPONumber { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the parent requisition item.
        /// </summary>
        public int? RequisitionItemParentItemId { get; set; }

        /// <summary>
        /// Gets or sets the vendor identifier from the SPR details.
        /// </summary>
        public int? SprDetailsVendorId { get; set; }

        /// <summary>
        /// Gets or sets the vendor name from the SPR details.
        /// </summary>
        public string SprDetailsVendorName { get; set; }

        /// <summary>
        /// Gets or sets the part number from the SPR details.
        /// </summary>
        public string SprDetailsPartNumber { get; set; }

        /// <summary>
        /// Gets or sets the file attachment identifier from the SPR details.
        /// </summary>
        public int? SprDetailsFileAttachment { get; set; }

        /// <summary>
        /// Gets or sets the file name from the SPR details.
        /// </summary>
        public string SprDetailsFileName { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the file attachment in the SPR details was created.
        /// </summary>
        public DateTime? SprDetailsFileAttachmentCreateDate { get; set; }

        /// <summary>
        /// Gets or sets the user who created the file attachment in the SPR details.
        /// </summary>
        public string SprDetailsFileAttachmentCreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the user who created the requisition.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the full name of the user who created the requisition.
        /// </summary>
        public string CreatedByFullName { get; set; }

        /// <summary>
        /// Gets or sets the total number of requisitions.
        /// </summary>
        public long TotalReqCount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition was created on a mobile device.
        /// </summary>
        public bool IsMobile { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is associated with a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the unit cost from the VBO hold item conversion.
        /// </summary>
        public decimal? VboHoldItemConversionUnitCost { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type identifier.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition submission type.
        /// </summary>
        public string RequisitionSubmissionType { get; set; } = "Standard";

    }
}
