﻿using System;

namespace RequisitionServices.Utility.Model
{
	public static class StringUtility
	{
		// ripped straight from .net perls
		public static string Slice(this string source, int start, int end)
		{
			if (end < 0) // Keep this for negative end support
			{
				end = source.Length + end;
			}

			int len = end - start;               // Calculate length
			return source.Substring(start, len); // Return Substring of length
		}

		/// <summary>
		/// Gives the substring from the first occurrence of start to the first occurrence of end
		/// </summary>
		/// <param name="source"></param>
		/// <param name="start"></param>
		/// <param name="end"></param>
		/// <returns></returns>
		public static string Slice(this string source, char start, char end)
		{
			var startIndex = source.IndexOf(start) + 1;
			var endIndex = source.IndexOf(end);

			var len = endIndex - startIndex;

			return source.Substring(startIndex, len);
		}

		/// <summary>
		/// Gives the substring starting from the beginning of the string to the first occurrence of end<PERSON>haracter
		/// </summary>
		/// <param name="source"></param>
		/// <param name="end<PERSON>haracter"></param>
		/// <returns></returns>
		public static string Slice(this string source, char endCharacter)
		{
			return source.Substring(0, source.IndexOf(endCharacter));
		}
        public static Boolean IsInt(string value)
        {
            int number;
            var isInt = int.TryParse(value, out number);
            return isInt;
        }
    }
}
