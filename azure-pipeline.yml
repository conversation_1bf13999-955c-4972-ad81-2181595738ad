trigger: 
  tags:
    include:
      - v*
  branches:   
    include:    
      - releases/*
      - refs/heads/release-*

pr: none

pool:
  name: 'HealthTrust - Windows'
  demands: Agent.OS -equals Windows_NT

resources:
- repo: self

variables:
- group: 'smart-shared'
- name:  project
  value: '**/*.sln'
- name:  buildPlatform
  value: 'Any CPU'
- name: buildConfiguration
  value: 'CI_CD'
- name: CUSTOM_BUILD_VERSION
  value: $(Build.BuildNumber)
- name: http_proxy
  value: 'http://proxy.nas.medcity.net:80'
- name: profile
  value: 'CI_CD'
- name: package
  value: 'CI_CD_RequisitionServices.nuspec'
- name: component
  value: 'RequisitionServices'
- name: archiveFilePatterns
  value : 'eProcurementReqSvcsDeploy*.nupkg'

steps:
# - task: DownloadSecureFile@1
#   name: nugetconfig
#   inputs:
#     secureFile: 'nuget-devops.config'

# - task: PowerShell@2
#   inputs:
#     targetType: 'inline'
#     script: 'move-Item $(nugetconfig.secureFilePath) ./nuget.config -Force -ErrorAction SilentlyContinue'

# - task: NuGetCommand@2
#   inputs:
#     command: custom
#     arguments: 'sources update -Name "$(nuget-devops-name)" -Source "$(nuget-devops-url)" -UserName "$(nuget-devops-user)" -Password $(nuget-devops-pass) -ConfigFile ./nuget.config'

- task: NuGetToolInstaller@1
  inputs:
    versionSpec: '4.7.1'

- task: NuGetCommand@2
  inputs:
    command: 'restore'
    restoreSolution: $(project)
    feedsToUse: 'select'
    
- task: VSBuild@1
  inputs:
    solution: '**\*.sln'
    msbuildArgs: '/p:PublishProfile=$(profile) /p:DeployOnBuild=true'
    platform: '$(buildPlatform)'
    configuration: '$(buildConfiguration)'

- task: NuGetCommand@2
  displayName: Nuget Pack
  inputs:
    command: 'pack'
    packagesToPack: $(package)
    configuration: '$(buildConfiguration)'
    versioningScheme: 'byEnvVar'
    versionEnvVar: 'CUSTOM_BUILD_VERSION'


# - task: PublishBuildArtifacts@1
#   inputs:
#     PathtoPublish: '$(build.artifactstagingdirectory)'
#     ArtifactName: 'PublishBuildArtifacts'
#     publishLocation: 'Container'


- task: ExtractFiles@1
  displayName: Extract Web NuGet Package
  inputs:
    archiveFilePatterns: '$(Build.ArtifactStagingDirectory)/$(archiveFilePatterns)'
    destinationFolder: '$(Build.ArtifactStagingDirectory)/$(component)'
    cleanDestinationFolder: true
    overwriteExistingFiles: true

- task: DeleteFiles@1
  displayName: Remove Unneeded NuGet Package Files
  inputs:
    SourceFolder: '$(Build.ArtifactStagingDirectory)'
    Contents: |
      *.nupkg
      $(archiveFilePatterns)
      # */_rels
      # */package
      # */[[]Content_Types].xml

- task: ArchiveFiles@2
  displayName: Archive $(component) Folder
  inputs:
    rootFolderOrFile: '$(build.artifactstagingdirectory)/$(component)'
    includeRootFolder: false
    archiveType: 'zip'
    archiveFile: '$(Build.ArtifactStagingDirectory)/$(Build.DefinitionName).zip'
    replaceExistingArchive: true

- task: DeleteFiles@1
  displayName: Remove $(component) Folder
  inputs:
    SourceFolder: '$(Build.ArtifactStagingDirectory)'
    Contents: '/$(component)'

- task: PublishPipelineArtifact@1
  inputs:
    targetPath: '$(build.artifactstagingdirectory)'
    artifact: 'PublishBuildArtifacts'
    publishLocation: 'pipeline'