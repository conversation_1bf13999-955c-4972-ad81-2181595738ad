﻿
namespace RequisitionServices.DomainModel.Items
{
    public class ParItemDetails
    {
        public string ParId { get; set; }

        public int ParDept { get; set; }

        public int ItemNumber { get; set; }

        public int Min { get; set; }

        public int Max { get; set; }

        public string IUOM { get; set; }

        public long GLAccount { get; set; }

        public bool ChargableFlag { get; set; }

        public string Location { get; set; }

        public string ItemType { get; set; }

        public bool ConsignmentFlag { get; set; }

        public int PARType { get; set; }

        public string ParDescription { get; set; }

        public string ItemDescription { get; set; }

        public decimal UnitPrice { get; set; }

        public int PurchVendorNumber { get; set; }

        public string PurchVendorName { get; set; }

        public bool StockFlag { get; set; }

        public bool IsCapitated { get; set; }

        public bool TempStock { get; set; }

        public string ComplianceCode { get; set; }

        public string DistributionPoint { get; set; }

        public string ReorderNumber { get; set; }

        public int ContractNumber { get; set; }

        public string ProcedureCode { get; set; }

        public int QuantityAvailable { get; set; }

        public long IGLAccount { get; set; }

        public string Chargeable { get; set; }

        public string CatalogNum { get; set; }

        public string PUOM { get; set; }

        public int Factor { get; set; }

    }
}
