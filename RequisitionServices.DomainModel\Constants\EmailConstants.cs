﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Email;

namespace RequisitionServices.DomainModel.Constants
{
    public static class Email
    {
        public static Dictionary<int, EmailModel> Emails = new Dictionary<int, EmailModel>
        {
            { (int)EmailType.PendingApproval, new EmailModel() {
                 Headline = EmailHeadline.PendingApproval,
                 BodyMessage = EmailBodyMessage.PendingApproval,
                 Subject = EmailSubject.PendingApproval,
                 BodyTemplate = BodyTemplatePath.PendingApproval }
             },
            { (int)EmailType.PendingApprovalRush, new EmailModel() {
                 Headline = EmailHeadline.PendingApproval,
                 BodyMessage = EmailBodyMessage.PendingApprovalRush,
                 Subject = EmailSubject.PendingApprovalRush,
                 BodyTemplate = BodyTemplatePath.PendingApprovalRush }
             },
            { (int)EmailType.AssignedDelegatedApprover, new EmailModel() {
                 Headline = EmailHeadline.DelegatedApprover,
                 BodyMessage = EmailBodyMessage.AssignedDelegatedApprover,
                 Subject = EmailSubject.AssignedDelegatedApprover,
                 BodyTemplate = BodyTemplatePath.SimpleMessage }
             },
            { (int)EmailType.UnassignedDelegatedApprover, new EmailModel() {
                 Headline = EmailHeadline.DelegatedApprover,
                 BodyMessage = EmailBodyMessage.UnassignedDelegatedApprover,
                 Subject = EmailSubject.UnassignedDelegatedApprover,
                 BodyTemplate = BodyTemplatePath.SimpleMessage }
             },
            { (int)EmailType.RequisitionReviewRequest, new EmailModel() {
                 Headline = EmailHeadline.RequisitionReview,
                 BodyMessage = EmailBodyMessage.RequisitionReviewRequest,
                 Subject = EmailSubject.RequisitionReviewRequest,
                 BodyTemplate = BodyTemplatePath.RequisitionReviewRequest }
             },
            { (int)EmailType.RequisitionReviewed, new EmailModel() {
                 Headline = EmailHeadline.RequisitionReview,
                 BodyMessage = EmailBodyMessage.RequisitionReviewed,
                 Subject = EmailSubject.RequisitionReviewed,
                 BodyTemplate = BodyTemplatePath.RequisitionReviewed }
             },
            { (int)EmailType.RequisitionDenied, new EmailModel() {
                 Headline = EmailHeadline.RequisitionDenied,
                 BodyMessage = EmailBodyMessage.RequisitionDenied,
                 Subject = EmailSubject.RequisitionDenied,
                 BodyTemplate = BodyTemplatePath.RequisitionDenied }
             },
            { (int)EmailType.RequisitionSubmitted, new EmailModel() {
                 Headline = EmailHeadline.RequisitionSubmitted,
                 BodyMessage = EmailBodyMessage.RequisitionSubmitted,
                 Subject = EmailSubject.RequisitionSubmitted,
                 BodyTemplate = BodyTemplatePath.RequisitionSubmitted }
             },
            { (int)EmailType.RequisitionSubmittedRush, new EmailModel() {
                 Headline = EmailHeadline.RequisitionSubmitted,
                 BodyMessage = EmailBodyMessage.RequisitionSubmittedRush,
                 Subject = EmailSubject.RequisitionSubmittedRush,
                 BodyTemplate = BodyTemplatePath.RequisitionSubmittedRush }
             },
            { (int)EmailType.WorkflowIssueTeamAlert, new EmailModel() {
                 Headline = EmailHeadline.WorkflowIssue,
                 BodyMessage = EmailBodyMessage.WorkflowIssueTeamAlert,
                 Subject = EmailSubject.WorkflowIssueTeamAlert,
                 BodyTemplate = BodyTemplatePath.WorkflowIssueTeamAlert }
             },
            { (int)EmailType.WorkflowIssueUserAlert, new EmailModel() {
                 Headline = EmailHeadline.WorkflowIssue,
                 BodyMessage = EmailBodyMessage.WorkflowIssueUserAlert,
                 Subject = EmailSubject.WorkflowIssueUserAlert,
                 BodyTemplate = BodyTemplatePath.SimpleMessage }
             },
            { (int)EmailType.PendingApprovalOnHold, new EmailModel() {
                 Headline = EmailHeadline.PendingApprovalOnHold,
                 BodyMessage = EmailBodyMessage.PendingApprovalOnHold,
                 Subject = EmailSubject.PendingApproval,
                 BodyTemplate = BodyTemplatePath.PendingApprovalOnHold }
             }
        };

        public static string GetSubjectMessage(EmailType emailType, int? requisitionId = null)
        {
            return requisitionId != null ? Emails[(int)emailType].Subject.Replace("$requisitionId$", requisitionId.ToString()) : Emails[(int)emailType].Subject;
        } 
    }
    public static class EmailHeadline
    {
        public const string PendingApproval = "Pending Approval";
        public const string PendingApprovalOnHold = "On Hold and Pending Approval";
        public const string DelegatedApprover = "Delegated Approver";
        public const string RequisitionReview = "Requisition Review";
        public const string RequisitionDenied = "Requisition Denied";
        public const string RequisitionSubmitted = "Requisition Submitted";
        public const string WorkflowIssue = "Workflow Issue";
        public const string ApprovalIssue = "Approval Issue";
    }

    public static class EmailBodyMessage
    {
        //Pending Approval
        public const string PendingApproval = "Requisition $requisitionId$ requires approval.";
        public const string PendingApprovalRush = "RUSH requisition $requisitionId$ requires approval.";
        public const string PendingApprovalOnHold = "Requisition $requisitionId$ is on hold and requires approval.";

        //Delegated Aprover
        public const string AssignedDelegatedApprover = "You have been assigned by $senderName$ to act as their delegate approver.";
        public const string UnassignedDelegatedApprover = "You have been unassigned by $senderName$ to act as their delegate approver.";

        //Requisition Review
        public const string RequisitionReviewRequest = "Requisition $requisitionId$ requires review.";
        public const string RequisitionReviewed = "Requisition $requisitionId$ has been reviewed.";

        //Requisition Denied
        public const string RequisitionDenied = "Requisition $requisitionId$ was denied.";

        //Requisition Submitted
        public const string RequisitionSubmitted = "Requisition $requisitionId$ was submitted.";
        public const string RequisitionSubmittedRush = "RUSH Requisition $requisitionId$ was submitted.";

        //Workflow Issue
        public const string WorkflowIssueTeamAlert = "A workflow issue was detected in production: ";
        public const string WorkflowIssueUserAlert = " Alert: there was an error processing requisition $requisitionId$. The help desk has been notified and will contact you after determining how best to resolve the issue.";
    }

    public static class EmailSubject
    {
        //Pending Approval
        public const string PendingApproval = "Requisition $requisitionId$ requires approval";
        public const string PendingApprovalRush = "RUSH Requisition $requisitionId$ requires approval";

        //Delegated Aprover
        public const string AssignedDelegatedApprover = "Assigned as Delegate Approver";
        public const string UnassignedDelegatedApprover = "Unassigned as Delegate Approver";

        //Requisition Review
        public const string RequisitionReviewRequest = "Requisition $requisitionId$ requires review.";
        public const string RequisitionReviewed = "Requisition $requisitionId$ has been reviewed.";

        //Requisition Denied
        public const string RequisitionDenied = "Requisition $requisitionId$ was denied.";

        //Requisition Submitted
        public const string RequisitionSubmitted = "Requisition $requisitionId$ was submitted.";
        public const string RequisitionSubmittedRush = "RUSH Requisition $requisitionId$ was submitted.";

        //Workflow Issue
        public const string WorkflowIssueTeamAlert = "Workflow Issue Detected (REQ ID $requisitionId$)";
        public const string WorkflowIssueUserAlert = "Approval Issue Detected (REQ ID $requisitionId$)";
    }

    public static class BodyTemplatePath
    {
        public const string PendingApproval = "EmailTemplates/ConsolidatedTemplates/PendingApprovalBody.html";
        public const string PendingApprovalRush = "EmailTemplates/ConsolidatedTemplates/PendingApprovalRushBody.html";
        public const string PendingApprovalOnHold = "EmailTemplates/ConsolidatedTemplates/PendingApprovalOnHoldBody.html";
        public const string RequisitionDenied = "EmailTemplates/ConsolidatedTemplates/RequisitionDeniedBody.html";
        public const string RequisitionReviewRequest = "EmailTemplates/ConsolidatedTemplates/RequisitionReviewRequestBody.html";
        public const string RequisitionReviewed = "EmailTemplates/ConsolidatedTemplates/RequisitionReviewedBody.html";
        public const string RequisitionSubmitted = "EmailTemplates/ConsolidatedTemplates/RequisitionSubmittedBody.html";
        public const string RequisitionSubmittedRush = "EmailTemplates/ConsolidatedTemplates/RequisitionSubmittedRushBody.html";
        public const string SimpleMessage = "EmailTemplates/ConsolidatedTemplates/SimpleMessageBody.html";
        public const string WorkflowIssueTeamAlert = "EmailTemplates/ConsolidatedTemplates/WorkflowIssueTeamAlertBody.html";
    }

}
