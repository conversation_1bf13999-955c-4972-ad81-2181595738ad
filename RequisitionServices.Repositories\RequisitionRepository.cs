﻿using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Constants;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.Utility.Model;
using System.Data.SqlClient;
using EntityFramework.BulkInsert.Extensions;
using RequisitionServices.DomainModel.Items;
using System.Data;
using System.Data.Entity.Validation;
using System.Configuration;
using System.Data.Entity.Infrastructure;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.DomainModel.VPro;

namespace RequisitionServices.Repositories
{
    public class RequisitionRepository : AbstractRepository, IRequisitionRepository
    {
        readonly ILog log = LogManager.GetLogger(typeof(RequisitionRepository));
        private const string eproParIdentifier = "EPR";

        public RequisitionRepository(EProcurementContext context) : base(context) { }

        public Requisition GetRequisition(int id)
        {
            return context.Requisitions.Where(x => x.RequisitionId == id)
                .Include("RequisitionItems.SPRDetail")
                .Include("RequisitionItems.VboHoldItemConversion")
                .Include("RequisitionItems.ClinicalUseDetails")
                .FirstOrDefault();
        }

        public Requisition GetRequisition(int id, string coid)
        {
            return context.Requisitions.Where(x => x.RequisitionId == id && x.LocationIdentifier.Contains(coid))
                .Include("RequisitionItems.SPRDetail")
                .Include("RequisitionItems.VboHoldItemConversion")
                .Include("RequisitionItems.ClinicalUseDetails")
                .FirstOrDefault();
        }

        public ClinicalUseDetail GetClinicalUseDetail(int id)
        {
            return context.ClinicalUseDetails.Where(x => x.Id == id).FirstOrDefault();
        }

        public IQueryable<Requisition> GetRequisitions()
        {
            return context.Requisitions;
        }

        public List<Requisition> GetRequisitionsWithDetailsByDateRange(string COID, int departmentId, DateTime startDate, DateTime endDate)
        {
            SqlParameter[] sqlParams = new SqlParameter[4];

            sqlParams[0] = new SqlParameter("@coid", COID);
            sqlParams[0].DbType = System.Data.DbType.String;

            sqlParams[1] = departmentId == 0 ? new SqlParameter("@departmentId", DBNull.Value) : new SqlParameter("@departmentId", departmentId);
            sqlParams[1].DbType = System.Data.DbType.String;

            sqlParams[2] = new SqlParameter("@startDate", startDate);
            sqlParams[2].DbType = System.Data.DbType.DateTime;

            sqlParams[3] = new SqlParameter("@endDate", endDate);
            sqlParams[3].DbType = System.Data.DbType.DateTime;

            var reqs = context.Database.SqlQuery<RequisitionDashboard>("exec dbo.usp_RequisitionDashboardGet @coid, @departmentId, @startDate, @endDate", sqlParams).ToList();

            Dictionary<int, Requisition> reqsReturned = new Dictionary<int, Requisition>();

            foreach (RequisitionDashboard req in reqs)
            {

                RequisitionItem newReqItem = req.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = req.RequisitionItemId ?? 0,
                    SPRDetail = req.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        PartNumber = req.SprDetailsPartNumber
                    }
                };

                if (reqsReturned.ContainsKey(req.RequisitionId))
                {
                    reqsReturned[req.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(req.RequisitionId, new Requisition()
                    {
                        RequisitionId = req.RequisitionId,
                        RequisitionStatusTypeId = req.RequisitionStatusTypeId,
                        RequisitionTypeId = req.RequisitionTypeId,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        CreateDate = req.CreateDate
                    });
                }
            }

            return reqsReturned.Values.ToList();
        }

        public RequisitionReportResults GetRequisitionsForReport(int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string coid, DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations)
        {
            var storedProcedureName = "dbo.usp_RequisitionsReportGet";

            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetDateRangeParameters(startDate, endDate));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            var departmentTableName = "@departmentIds";
            var table = getIdTemplateDataTable(departmentTableName, departmentIds);

            sqlParams.Add(new SqlParameter(departmentTableName, table.Rows.Count > 0 ? table : null) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var vendorAffiliationsTableName = "@vendorAffiliations";
            if (vendorAffiliations != null)
            {
                sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();
                storedProcedureName = "dbo.usp_VendorUserRequisitionsReportGet";
                sqlParams.Add(new SqlParameter(vendorAffiliationsTableName, getIdTemplateDataTable(vendorAffiliationsTableName, vendorAffiliations)) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            }

            var rowsReturned = context.Database.SqlQuery<RequisitionReportPageRow>($"exec { storedProcedureName } @rowOffset, @pageSize, @coid, @departmentIds, @startDate, @endDate, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, {(vendorAffiliations == null ? "@vboFirst" : vendorAffiliationsTableName)}", sqlParams.ToArray()).ToList();

            Dictionary<int, Requisition> reqsReturned = new Dictionary<int, Requisition>();

            long totalRows = 0;

            foreach (RequisitionReportPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    PONumber = row.RequisitionItemPONumber,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, //VendorId is not nullable in the database, so this is really just to make the code build.
                                                  //It should never be needed.
                    UnitCost = row.UnitCost,
                    QuantityToOrder = row.QuantityToOrder ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new Requisition()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        Comments = row.Comments,
                        CreatedBy = row.FirstName == null ? row.CreatedById : row.FirstName + " " + row.LastName,
                        CreateDate = row.CreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.LocationIdentifier,
                        IsMobile = row.IsMobile,
                        IsVendor = row.IsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                    });
                }
            }
            return new RequisitionReportResults(reqsReturned.Values.ToList(), totalRows);
        }

        public RequisitionReportResults GetVBORequisitionsForReport(int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string coid, DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations)
        {
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetDateRangeParameters(startDate, endDate));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));
            sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();

            #region DepartmentId Table Parameter
            var table = new DataTable("@departmentIds");
            table.Columns.Add("Id", typeof(int));

            foreach (var id in departmentIds)
            {
                table.Rows.Add(id);
            }

            sqlParams.Add(new SqlParameter("@departmentIds", table.Rows.Count > 0 ? table : null) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            #endregion 

            #region Vendor Affiliation Table Parameter
            var vendorIdTable = new DataTable("@vendorAffiliations");
            vendorIdTable.Columns.Add("Id", typeof(int));

            if (vendorAffiliations != null)
            {
                foreach (var id in vendorAffiliations)
                {
                    vendorIdTable.Rows.Add(id);
                }
            }

            sqlParams.Add(new SqlParameter("@vendorAffiliations", vendorIdTable.Rows.Count > 0 ? vendorIdTable : null) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            sqlParams.Add(new SqlParameter("@userIsVendor", vendorAffiliations != null ? 1 : 0) { SqlDbType = SqlDbType.Bit });

            #endregion

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<RequisitionReportPageRow>("exec dbo.usp_VBORequisitionsReportGet @rowOffset, @pageSize, @coid, @departmentIds, @startDate, @endDate, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, @vendorAffiliations, @userIsVendor", sqlParams.ToArray()).ToList();

            Dictionary<int, Requisition> reqsReturned = new Dictionary<int, Requisition>();

            long totalRows = 0;

            foreach (RequisitionReportPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    PONumber = row.RequisitionItemPONumber,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, //VendorId is not nullable in the database, so this is really just to make the code build.
                                                  //It should never be needed.
                    UnitCost = row.UnitCost,
                    QuantityToOrder = row.QuantityToOrder ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new Requisition()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        Comments = row.Comments,
                        CreatedBy = row.FirstName == null ? row.CreatedById : row.FirstName + " " + row.LastName,
                        CreateDate = row.CreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.LocationIdentifier,
                        IsMobile = row.IsMobile,
                        IsVendor = row.IsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                    });
                }
            }

            return new RequisitionReportResults(reqsReturned.Values.ToList(), totalRows);
        }

        public RequisitionReportExportResults GetRequisitionsForReportExport(RequisitionSortOrder sortOrder, string coid, DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations)
        {
            var storedProcedureName = "dbo.usp_RequisitionsReportExportGet";

            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetDateRangeParameters(startDate, endDate));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            var departmentTableName = "@departmentIds";
            var table = getIdTemplateDataTable(departmentTableName, departmentIds);

            sqlParams.Add(new SqlParameter(departmentTableName, table.Rows.Count > 0 ? table : null) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var vendorAffiliationsTableName = "@vendorAffiliations";
            if (vendorAffiliations != null)
            {
                sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();
                storedProcedureName = "dbo.usp_VendorUserRequisitionsReportExportGet";
                sqlParams.Add(new SqlParameter(vendorAffiliationsTableName, getIdTemplateDataTable(vendorAffiliationsTableName, vendorAffiliations)) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            }

            int maxExportedRequisitionCount;

            if (int.TryParse(ConfigurationManager.AppSettings.Get("MaxExportedRequisitionCount"), out maxExportedRequisitionCount))
            {
                sqlParams.Add(new SqlParameter("@maxExportedRequisitionCount", maxExportedRequisitionCount));
            }

            var rowsReturned = context.Database.SqlQuery<RequisitionReportExportRow>($"exec {storedProcedureName} @coid, @departmentIds, @startDate, @endDate, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, @maxExportedRequisitionCount, {(vendorAffiliations == null ? "@vboFirst" : vendorAffiliationsTableName)}", sqlParams.ToArray()).ToList();

            var reqsReturned = new Dictionary<int, RequisitionForExport>();

            foreach (var row in rowsReturned)
            {
                var newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    ReOrder = row.RequisitionItemReorderNumber,
                    PONumber = row.RequisitionItemPoNumber,
                    QuantityToOrder = row.RequisitionItemQuantityOrdered ?? 0,
                    SmartItemNumber = row.RequisitionItemSmartItemNumber,
                    UOMCode = row.RequisitionItemUomCode,
                    UnitCost = row.RequisitionItemUnitCost,
                    VendorId = row.RequisitionItemVendorId ?? 0,
                    VendorName = row.RequisitionItemVendorName,
                    ItemDescription = row.RequisitionItemDescription,
                    GeneralLedgerCode = row.RequisitionItemGeneralLedgerCode,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    Discount = row.Discount,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        UOMCode = row.SprDetailsUomCode,
                        ItemDescription = row.SprDetailsDescription,
                        TradeInValue = row.SprDetailsTradeInValue,
                        BudgetNumber = row.SprDetailsBudgetNumber,
                        GeneralLedgerCode = row.SprDetailsGeneralLedgerCode,
                        EstimatedPrice = row.SprDetailsEstimatedUnitPrice
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new RequisitionForExport
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        CreatedBy = row.RequisitionerId,
                        CreateDate = row.RequisitionCreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem> { newReqItem },
                        LocationIdentifier = row.RequisitionLocationIdentifier,
                        IsMobile = row.RequisitionIsMobile,
                        IsVendor = row.RequisitionIsVendor,
                        CreatedByFullName = $"{row.RequisitionerFirstName} {row.RequisitionerLastName}",
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId,
                        BadgeLogId = row.BadgeLogId,
                        BadgeInStatusId = row.BadgeLogId == null ? null : row.BadgeInStatusId
                    });
                }
            }

            return new RequisitionReportExportResults(reqsReturned.Values.ToList());
        }

        public RequisitionReportExportResults GetVBORequisitionsForReportExport(RequisitionSortOrder sortOrder, string coid, DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations)
        {
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetDateRangeParameters(startDate, endDate));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));
            sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();

            #region DepartmentId Table Parameter
            var table = new DataTable("@departmentIds");
            table.Columns.Add("Id", typeof(int));

            foreach (var id in departmentIds)
            {
                table.Rows.Add(id);
            }
            #endregion

            #region Vendor Affiliation Table Parameter
            var vendorIdTable = new DataTable("@vendorAffiliations");
            vendorIdTable.Columns.Add("Id", typeof(int));


            if (vendorAffiliations != null)
            {
                foreach (var id in vendorAffiliations)
                {
                    vendorIdTable.Rows.Add(id);
                }
            }
            #endregion

            sqlParams.Add(new SqlParameter("@vendorAffiliations", vendorIdTable.Rows.Count > 0 ? vendorIdTable : null) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            sqlParams.Add(new SqlParameter("@userIsVendor", vendorAffiliations != null ? 1 : 0) { SqlDbType = SqlDbType.Bit });

            sqlParams.Add(new SqlParameter("@departmentIds", table.Rows.Count > 0 ? table : null) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            int maxExportedRequisitionCount;

            if (int.TryParse(ConfigurationManager.AppSettings.Get("MaxExportedRequisitionCount"), out maxExportedRequisitionCount))
            {
                sqlParams.Add(new SqlParameter("@maxExportedRequisitionCount", maxExportedRequisitionCount));
            }

            var rowsReturned = context.Database.SqlQuery<RequisitionReportExportRow>("exec dbo.usp_VBORequisitionsReportExportGet @coid, @departmentIds, @startDate, @endDate, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, @maxExportedRequisitionCount, @vendorAffiliations, @userIsVendor", sqlParams.ToArray()).ToList();

            var reqsReturned = new Dictionary<int, RequisitionForExport>();

            foreach (var row in rowsReturned)
            {
                var newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    ReOrder = row.RequisitionItemReorderNumber,
                    PONumber = row.RequisitionItemPoNumber,
                    QuantityToOrder = row.RequisitionItemQuantityOrdered ?? 0,
                    SmartItemNumber = row.RequisitionItemSmartItemNumber,
                    UOMCode = row.RequisitionItemUomCode,
                    UnitCost = row.RequisitionItemUnitCost,
                    VendorId = row.RequisitionItemVendorId ?? 0,
                    VendorName = row.RequisitionItemVendorName,
                    ItemDescription = row.RequisitionItemDescription,
                    GeneralLedgerCode = row.RequisitionItemGeneralLedgerCode,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    Discount = row.Discount,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        UOMCode = row.SprDetailsUomCode,
                        ItemDescription = row.SprDetailsDescription,
                        TradeInValue = row.SprDetailsTradeInValue,
                        BudgetNumber = row.SprDetailsBudgetNumber,
                        GeneralLedgerCode = row.SprDetailsGeneralLedgerCode,
                        EstimatedPrice = row.SprDetailsEstimatedUnitPrice
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new RequisitionForExport
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        CreatedBy = row.RequisitionerId,
                        CreateDate = row.RequisitionCreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem> { newReqItem },
                        LocationIdentifier = row.RequisitionLocationIdentifier,
                        IsMobile = row.RequisitionIsMobile,
                        IsVendor = row.RequisitionIsVendor,
                        CreatedByFullName = $"{row.RequisitionerFirstName} {row.RequisitionerLastName}",
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId,
                        BadgeLogId = row.BadgeLogId,
                        BadgeInStatusId = row.BadgeLogId == null ? null : row.BadgeInStatusId
                    });
                }
            }

            return new RequisitionReportExportResults(reqsReturned.Values.ToList());
        }

        public RequisitionReportResults GetRequisitionsForReportByVendor(int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string coid, string vendorIdName, string filterText, List<int> vendorAffiliations, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var storedProcedureName = "dbo.usp_RequisitionsVendorReportGet";

            var sqlParams = new List<SqlParameter>()
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetReportVendorParameters(vendorIdName));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var vendorAffiliationsTableName = "@vendorAffiliations";
            if (vendorAffiliations != null)
            {
                sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();
                storedProcedureName = "dbo.usp_VendorUserRequisitionsVendorReportGet";
                sqlParams.Add(new SqlParameter(vendorAffiliationsTableName, getIdTemplateDataTable(vendorAffiliationsTableName, vendorAffiliations)) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            }

            var sqlCommand = $"exec {storedProcedureName} @rowOffset, @pageSize, @coid, @VendorId, @VendorName, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, {(vendorAffiliations == null ? "@vboFirst" : vendorAffiliationsTableName)}";

            var rowsReturned = context.Database.SqlQuery<RequisitionReportPageRow>(sqlCommand, sqlParams.ToArray()).ToList();

            Dictionary<int, Requisition> reqsReturned = new Dictionary<int, Requisition>();

            long totalRows = 0;

            foreach (RequisitionReportPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    PONumber = row.RequisitionItemPONumber,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, //VendorId is not nullable in the database, so this is really just to make the code build.
                                                  //It should never be needed.
                    UnitCost = row.UnitCost,
                    QuantityToOrder = row.QuantityToOrder ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new Requisition()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        Comments = row.Comments,
                        CreatedBy = row.FirstName == null ? row.CreatedById : row.FirstName + " " + row.LastName,
                        CreateDate = row.CreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.LocationIdentifier,
                        IsMobile = row.IsMobile,
                        IsVendor = row.IsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                    });
                }
            }

            return new RequisitionReportResults(reqsReturned.Values.ToList(), totalRows);
        }

        public RequisitionReportExportResults GetRequisitionsForReportByVendorExport(RequisitionSortOrder sortOrder, string coid, string vendorIdName, string filterText, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations)
        {
            var storedProcedureName = "dbo.usp_RequisitionsVendorReportExportGet";
            var sqlParams = new List<SqlParameter>()
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetReportVendorParameters(vendorIdName));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var vendorAffiliationsTableName = "@vendorAffiliations";
            if (vendorAffiliations != null)
            {
                sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();
                storedProcedureName = "dbo.usp_VendorUserRequisitionsVendorReportExportGet";
                sqlParams.Add(new SqlParameter(vendorAffiliationsTableName, getIdTemplateDataTable(vendorAffiliationsTableName, vendorAffiliations)) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            }

            var rowsReturned = context.Database.SqlQuery<RequisitionReportExportRow>($"exec {storedProcedureName} @coid, @VendorId, @VendorName, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, {(vendorAffiliations == null ? "@vboFirst" : vendorAffiliationsTableName)}", sqlParams.ToArray()).ToList();

            Dictionary<int, RequisitionForExport> reqsReturned = new Dictionary<int, RequisitionForExport>();

            foreach (RequisitionReportExportRow row in rowsReturned)
            {
                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    ReOrder = row.RequisitionItemReorderNumber,
                    PONumber = row.RequisitionItemPoNumber,
                    QuantityToOrder = row.RequisitionItemQuantityOrdered ?? 0,
                    SmartItemNumber = row.RequisitionItemSmartItemNumber,
                    UOMCode = row.RequisitionItemUomCode,
                    UnitCost = row.RequisitionItemUnitCost,
                    VendorId = row.RequisitionItemVendorId ?? 0,
                    VendorName = row.RequisitionItemVendorName,
                    ItemDescription = row.RequisitionItemDescription,
                    GeneralLedgerCode = row.RequisitionItemGeneralLedgerCode,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    Discount = row.Discount,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        UOMCode = row.SprDetailsUomCode,
                        ItemDescription = row.SprDetailsDescription,
                        TradeInValue = row.SprDetailsTradeInValue,
                        BudgetNumber = row.SprDetailsBudgetNumber,
                        GeneralLedgerCode = row.SprDetailsGeneralLedgerCode,
                        EstimatedPrice = row.SprDetailsEstimatedUnitPrice
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new RequisitionForExport()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        CreatedBy = row.RequisitionerId,
                        CreateDate = row.RequisitionCreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.RequisitionLocationIdentifier,
                        CreatedByFullName = row.RequisitionerFirstName + " " + row.RequisitionerLastName,
                        IsMobile = row.RequisitionIsMobile,
                        IsVendor = row.RequisitionIsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId,
                        BadgeLogId = row.BadgeLogId,
                        BadgeInStatusId = row.BadgeLogId == null ? null : row.BadgeInStatusId
                    });
                }
            }

            return new RequisitionReportExportResults(reqsReturned.Values.ToList());
        }

        public RequisitionReportResults GetRequisitionsForReportByItemNumber(int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string coid, string searchText, string filterText, List<int> vendorAffiliations, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var storedProcedureName = "dbo.usp_RequisitionsReportByItemNumberGet";
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String },
                new SqlParameter("@searchText", searchText) { DbType = DbType.String }
            };

            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var vendorAffiliationsTableName = "@vendorAffiliations";
            if (vendorAffiliations != null)
            {
                sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();
                storedProcedureName = "dbo.usp_VendorUserRequisitionsReportByItemNumberGet";
                sqlParams.Add(new SqlParameter(vendorAffiliationsTableName, getIdTemplateDataTable(vendorAffiliationsTableName, vendorAffiliations)) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            }

            var rowsReturned = context.Database.SqlQuery<RequisitionReportPageRow>($"exec {storedProcedureName} @rowOffset, @pageSize, @coid, @searchText, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, {(vendorAffiliations == null ? "@vboFirst" : vendorAffiliationsTableName)}", sqlParams.ToArray()).ToList();

            Dictionary<int, Requisition> reqsReturned = new Dictionary<int, Requisition>();

            long totalRows = 0;

            foreach (RequisitionReportPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    PONumber = row.RequisitionItemPONumber,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, //VendorId is not nullable in the database, so this is really just to make the code build.
                                                  //It should never be needed.
                    UnitCost = row.UnitCost,
                    QuantityToOrder = row.QuantityToOrder ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new Requisition()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        Comments = row.Comments,
                        CreatedBy = row.FirstName == null ? row.CreatedById : row.FirstName + " " + row.LastName,
                        CreateDate = row.CreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.LocationIdentifier,
                        IsMobile = row.IsMobile,
                        IsVendor = row.IsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                    });
                }
            }

            return new RequisitionReportResults(reqsReturned.Values.ToList(), totalRows);
        }

        public RequisitionReportExportResults GetRequisitionsForReportByItemNumberExport(RequisitionSortOrder sortOrder, string coid, string searchText, string filterText, List<int> vendorAffiliations, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var storedProcedureName = "dbo.usp_RequisitionsReportByItemNumberExportGet";
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@coid", coid) { DbType = DbType.String },
                new SqlParameter("@searchText", searchText) { DbType = DbType.String }
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            var vendorAffiliationsTableName = "@vendorAffiliations";
            if (vendorAffiliations != null)
            {
                sqlParams = sqlParams.Where(x => x.ParameterName != "@vboFirst").ToList();
                storedProcedureName = "dbo.usp_VendorUserRequisitionsReportByItemNumberExportGet";
                sqlParams.Add(new SqlParameter(vendorAffiliationsTableName, getIdTemplateDataTable(vendorAffiliationsTableName, vendorAffiliations)) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });
            }

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<RequisitionReportExportRow>($"exec {storedProcedureName} @coid, @searchText, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter, {(vendorAffiliations == null ? "@vboFirst" : vendorAffiliationsTableName)}", sqlParams.ToArray()).ToList();

            Dictionary<int, RequisitionForExport> reqsReturned = new Dictionary<int, RequisitionForExport>();

            foreach (RequisitionReportExportRow row in rowsReturned)
            {
                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    ReOrder = row.RequisitionItemReorderNumber,
                    PONumber = row.RequisitionItemPoNumber,
                    QuantityToOrder = row.RequisitionItemQuantityOrdered ?? 0,
                    SmartItemNumber = row.RequisitionItemSmartItemNumber,
                    UOMCode = row.RequisitionItemUomCode,
                    UnitCost = row.RequisitionItemUnitCost,
                    VendorId = row.RequisitionItemVendorId ?? 0,
                    VendorName = row.RequisitionItemVendorName,
                    ItemDescription = row.RequisitionItemDescription,
                    GeneralLedgerCode = row.RequisitionItemGeneralLedgerCode,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    Discount = row.Discount,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        UOMCode = row.SprDetailsUomCode,
                        ItemDescription = row.SprDetailsDescription,
                        TradeInValue = row.SprDetailsTradeInValue,
                        BudgetNumber = row.SprDetailsBudgetNumber,
                        GeneralLedgerCode = row.SprDetailsGeneralLedgerCode,
                        EstimatedPrice = row.SprDetailsEstimatedUnitPrice
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new RequisitionForExport()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        CreatedBy = row.RequisitionerId,
                        CreateDate = row.RequisitionCreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.RequisitionLocationIdentifier,
                        CreatedByFullName = row.RequisitionerFirstName + " " + row.RequisitionerLastName,
                        IsMobile = row.RequisitionIsMobile,
                        IsVendor = row.RequisitionIsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId,
                        BadgeLogId = row.BadgeLogId,
                        BadgeInStatusId = row.BadgeLogId == null ? null : row.BadgeInStatusId
                    });
                }
            }

            return new RequisitionReportExportResults(reqsReturned.Values.ToList());
        }

        public RequisitionReportResults GetRequisitionsForUser(string username, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@userName", username) { DbType = DbType.String },
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<RequisitionPageRow>("exec dbo.usp_RequisitionsGet @userName, @rowOffset, @pageSize, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @mobileReqs, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter", sqlParams.ToArray()).ToList();

            Dictionary<int, Requisition> reqsReturned = new Dictionary<int, Requisition>();

            long totalRows = 0;

            foreach (RequisitionPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    PONumber = row.PONumber,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, //VendorId is not nullable in the database, so this is really just to make the code build.
                                                  //It should never be needed.
                    UnitCost = row.UnitCost,
                    ParIdentifier = row.ParIdentifier == eproParIdentifier ? null : row.ParIdentifier,
                    QuantityToOrder = row.RequisitionItemQuantityToOrder ?? 0,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new Requisition()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        RequisitionTypeId = row.RequisitionTypeId,
                        Comments = row.Comments,
                        CreateDate = row.CreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                        LocationIdentifier = row.LocationIdentifier,
                        IsMobile = row.IsMobile,
                        IsVendor = row.IsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                    });
                }
            }

            return new RequisitionReportResults(reqsReturned.Values.ToList(), totalRows);
        }

        private List<SqlParameter> GetReportVendorParameters(string vendorIdName)
        {
            if (StringUtility.IsInt(vendorIdName))
            {
                return new List<SqlParameter>
                {
                    new SqlParameter("@VendorId", vendorIdName) { DbType = DbType.String },
                    new SqlParameter("@VendorName", DBNull.Value) { DbType = DbType.String }
                };
            }
            else
            {
                return new List<SqlParameter>
                {
                    new SqlParameter("@VendorId", DBNull.Value) { DbType = DbType.String },
                    new SqlParameter("@VendorName", vendorIdName) { DbType = DbType.String }
                };
            }
        }

        private SqlParameter GetFilterTextParameter(string filterText)
        {
            var filterParam = filterText == null ? new SqlParameter("@filterText", DBNull.Value) : new SqlParameter("@filterText", filterText);
            filterParam.DbType = DbType.String;
            return filterParam;
        }

        private List<SqlParameter> GetPaginationParameters(int rowOffset, int pageSize)
        {
            return new List<SqlParameter>
            {
                new SqlParameter("@rowOffset", rowOffset) { DbType = DbType.Int32 },
                new SqlParameter("@pageSize", pageSize) { DbType = DbType.Int32 }
            };
        }

        private List<SqlParameter> GetRequisitionSortingParameters(RequisitionSortOrder sortOrder, bool approvalsList = false)
        {
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@statusSorting", sortOrder == RequisitionSortOrder.Status) { DbType = DbType.Boolean },   // 0 = status sorting OFF, 1 = status sorting ON
                new SqlParameter("@oldestFirst", sortOrder == RequisitionSortOrder.Oldest) { DbType = DbType.Boolean },
                new SqlParameter("@vboFirst", sortOrder == RequisitionSortOrder.VBO) { DbType = DbType.Boolean },
            };

            if (!approvalsList)
            {
                parameters.Add(new SqlParameter("@mobileReqs", sortOrder == RequisitionSortOrder.Mobile) { DbType = DbType.Boolean });
            }

            var table = new DataTable("@reqTypeIdGroup");
            table.Columns.Add("Id", typeof(int));
            switch (sortOrder)
            {
                case RequisitionSortOrder.Rush:
                    table.Rows.Add(RequisitionTypeEnum.Rush);
                    break;
                case RequisitionSortOrder.Capital:
                    table.Rows.Add(RequisitionTypeEnum.Capital);
                    break;
                case RequisitionSortOrder.BillOnlyBillReplace:
                    table.Rows.Add(RequisitionTypeEnum.BillOnly);
                    table.Rows.Add(RequisitionTypeEnum.BillAndReplace);
                    table.Rows.Add(RequisitionTypeEnum.CapitatedBillOnly);
                    table.Rows.Add(RequisitionTypeEnum.CapitatedBillAndReplace);
                    break;
                default:
                    table.Rows.Add(999); // by default this should not equal any existing req type ID
                    break;
            }
            parameters.Add(new SqlParameter("@reqTypeIdGroup", table) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });

            return parameters;
        }

        private List<SqlParameter> GetDateRangeParameters(DateTime startDate, DateTime endDate)
        {
            return new List<SqlParameter>
            {
                new SqlParameter("@startDate", startDate) { DbType = DbType.DateTime },
                new SqlParameter("@endDate", endDate) { DbType = DbType.DateTime }
            };
        }

        private List<SqlParameter> GetFacilityDepartmentFilterParameters(List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var facilityFilterTableName = "@facilitiesMatchedOnFilter";
            var facilityFilterTable = new DataTable(facilityFilterTableName);
            facilityFilterTable.Columns.Add("VarcharVal", typeof(string));
            if (facilitiesMatchedOnFilter != null)
            {
                foreach (var facilityCoid in facilitiesMatchedOnFilter)
                {
                    facilityFilterTable.Rows.Add(facilityCoid);
                }
            }

            var departmentFilterTableName = "@departmentsMatchedOnFilter";
            var departmentFilterTable = new DataTable(departmentFilterTableName);
            departmentFilterTable.Columns.Add("VarcharVal", typeof(string));
            if (departmentsMatchedOnFilter != null)
            {
                foreach (var locationIdentifier in departmentsMatchedOnFilter)
                {
                    departmentFilterTable.Rows.Add(locationIdentifier);
                }
            }

            return new List<SqlParameter>
            {
                new SqlParameter(facilityFilterTableName, facilityFilterTable) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.Varchar50Template" },
                new SqlParameter(departmentFilterTableName, departmentFilterTable) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.Varchar50Template" }
            };
        }

        public RequisitionReportResults GetTemplatesforAUser(string username, int rowOffset, int pageSize, string filterText, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@userName", username) { DbType = DbType.String }
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<RequisitionPageRow>("exec dbo.usp_TemplatesGet @userName, @rowOffset, @pageSize, @filterText, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter", sqlParams.ToArray()).ToList();

            Dictionary<int, Requisition> templatesReturned = new Dictionary<int, Requisition>();

            long totalRows = 0;

            foreach (RequisitionPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ParIdentifier = row.ParIdentifier == eproParIdentifier ? null : row.ParIdentifier,
                };

                if (templatesReturned.ContainsKey(row.RequisitionId))
                {
                    templatesReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    templatesReturned.Add(row.RequisitionId, new Requisition()
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        LocationIdentifier = row.LocationIdentifier,
                        RequisitionTypeId = row.RequisitionTypeId,
                        Comments = row.Comments,
                        RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem }
                    });
                }
            }
            return new RequisitionReportResults(templatesReturned.Values.ToList(), totalRows);
        }

        public ApprovalPageResults GetPendingApprovalsForApprover(string userName, bool isExcludeVboChecked, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var sqlParams = new List<SqlParameter>()
            {
                new SqlParameter("@userName", userName) { DbType = DbType.String },
                new SqlParameter("@isExcludeVboChecked", isExcludeVboChecked) { DbType = DbType.Boolean }
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder, true));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<ApprovalPageRow>("exec dbo.usp_PendingApprovalsGet @userName, @isExcludeVboChecked, @rowOffset, @pageSize, @filterText,  @reqTypeIdGroup, @statusSorting, @oldestFirst, @vboFirst, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter", sqlParams.ToArray()).ToList();

            Dictionary<ApprovalIdKey, Approval> reqsReturned = new Dictionary<ApprovalIdKey, Approval>();

            long totalRows = 0;

            foreach (ApprovalPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;
                ApprovalIdKey key = new ApprovalIdKey()
                {
                    RequisitionId = row.RequisitionId,
                    AdHocReviewId = row.ReviewId
                };

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    ItemId = row.RequisitionItemNumber,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    QuantityToOrder = row.RequisitionItemQuantityToOrder,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, //VendorId is not nullable in the database, so this is really just to make the code build.
                                                  //It should never be needed.
                    UnitCost = row.UnitCost,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        EstimatedPrice = row.SprDetailsEstimatedPrice,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(key))
                {
                    reqsReturned[key].Requisition.RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(key, new Approval()
                    {
                        Requisition = new Requisition()
                        {
                            RequisitionId = row.RequisitionId,
                            RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                            RequisitionTypeId = row.RequisitionTypeId,
                            Comments = row.RequisitionComments,
                            CreatedBy = row.RequisitionerFirstName == null ? row.RequisitionerId : row.RequisitionerFirstName + " " + row.RequisitionerLastName,
                            CreateDate = row.RequisitionCreateDate,
                            RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                            LocationIdentifier = row.RequisitionLocationIdentifier,
                            CountryCode = row.CountryCode,
                            IsVendor = row.IsVendor,
                            RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                        },
                        AdhocReview = row.RequesterId == null ? null : new AdhocReview()
                        {
                            Id = row.ReviewId ?? 0,
                            RequisitionId = row.RequisitionId,
                            Requester = row.RequesterFirstName == null ? row.RequesterId : row.RequesterFirstName + " " + row.RequesterLastName,
                            RequesterComments = row.RequesterComments,
                            CreateDate = row.RequestCreateDate == null ? new DateTime() : (DateTime)row.RequestCreateDate
                        },
                        PendingReviewsExist = row.PendingReviewsExist
                    });
                }
            }
            return new ApprovalPageResults(reqsReturned.Values.ToList(), totalRows);
        }

        public ApprovalPageResults GetUpcomingApprovalsForApprover(string userName, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@userName", userName) { DbType = DbType.String }
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder, true));

            sqlParams = sqlParams.Where(x => x.ParameterName != "@statusSorting").ToList();

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<ApprovalPageRow>("exec dbo.usp_UpcomingApprovalsGet @userName, @rowOffset, @pageSize, @filterText, @reqTypeIdGroup, @oldestFirst, @vboFirst, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter", sqlParams.ToArray()).ToList();

            Dictionary<ApprovalIdKey, Approval> reqsReturned = new Dictionary<ApprovalIdKey, Approval>();

            long totalRows = 0;

            foreach (ApprovalPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;
                ApprovalIdKey key = new ApprovalIdKey()
                {
                    RequisitionId = row.RequisitionId,
                    AdHocReviewId = row.ReviewId
                };

                RequisitionItem newReqItem = row.RequisitionItemId == 0 ? null : new RequisitionItem()
                {
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    }
                };

                if (reqsReturned.ContainsKey(key))
                {
                    reqsReturned[key].Requisition.RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(key, new Approval()
                    {
                        Requisition = new Requisition()
                        {
                            RequisitionId = row.RequisitionId,
                            RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                            RequisitionTypeId = row.RequisitionTypeId,
                            Comments = row.RequisitionComments,
                            CreatedBy = row.RequisitionerFirstName == null ? row.RequisitionerId : row.RequisitionerFirstName + " " + row.RequisitionerLastName,
                            CreateDate = row.RequisitionCreateDate,
                            RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                            LocationIdentifier = row.RequisitionLocationIdentifier,
                            IsVendor = row.IsVendor,
                            RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                        },
                        PendingReviewsExist = row.PendingReviewsExist
                    });
                }
            }
            return new ApprovalPageResults(reqsReturned.Values.ToList(), totalRows);
        }

        public ApprovalPageResults GetApprovalHistoryForApprover(string userName, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter)
        {
            List<SqlParameter> sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@userName", userName) { DbType = DbType.String }
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder, true));

            sqlParams.AddRange(GetFacilityDepartmentFilterParameters(facilitiesMatchedOnFilter, departmentsMatchedOnFilter));

            var rowsReturned = context.Database.SqlQuery<ApprovalHistoryPageRow>("exec dbo.usp_ApprovalHistoryGet @userName, @rowOffset, @pageSize, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @vboFirst, @facilitiesMatchedOnFilter, @departmentsMatchedOnFilter", sqlParams.ToArray()).ToList();

            Dictionary<int, Approval> reqsReturned = new Dictionary<int, Approval>();

            long totalRows = 0;

            foreach (ApprovalHistoryPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                RequisitionItem newReqItem = row.RequisitionItemId == null ? null : new RequisitionItem()
                {
                    Id = row.RequisitionItemId ?? 0,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    QuantityToOrder = row.RequisitionItemQuantityToOrder,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        EstimatedPrice = row.SprDetailsEstimatedPrice,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment() }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.HistoryItemId))
                {
                    reqsReturned[row.HistoryItemId].Requisition.RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.HistoryItemId, new Approval()
                    {
                        Requisition = new Requisition()
                        {
                            RequisitionId = row.RequisitionId,
                            RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                            RequisitionTypeId = row.RequisitionTypeId,
                            Comments = row.RequisitionComments,
                            CreatedBy = row.RequisitionerFirstName == null ? row.RequisitionerId : row.RequisitionerFirstName + " " + row.RequisitionerLastName,
                            RequisitionItems = newReqItem == null ? null : new List<RequisitionItem>() { newReqItem },
                            LocationIdentifier = row.RequisitionLocationIdentifier,
                            IsVendor = row.IsVendor,
                            RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId
                        },
                        AdhocReview = row.RequesterId == null ? null : new AdhocReview()
                        {
                            RequisitionId = row.RequisitionId,
                            Requester = row.RequesterFirstName == null ? row.RequesterId : row.RequesterFirstName + " " + row.RequesterLastName,
                            RequesterComments = row.RequesterComments,
                            ReviewerComments = row.ReviewerComments,
                            Recommended = row.ReviewerRecommended
                        },
                        HistoryItem = new RequisitionStatusHistory()
                        {
                            Id = row.HistoryItemId,
                            CreateDate = row.HistoryItemCreateDate,
                            RequisitionStatusTypeId = row.HistoryItemStatusTypeId
                        },
                        PendingReviewsExist = row.PendingReviewsExist
                    });
                }
            }

            return new ApprovalPageResults(reqsReturned.Values.ToList(), totalRows);
        }

        public void InsertRequisition(Requisition requisition, string userName)
        {
            context.Requisitions.Add(requisition);

            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    //Add item audit history
                    context.RequisitionItemStatusHistories.Add(new RequisitionItemStatusHistory()
                    {
                        RequisitionItem = requisitionItem,
                        CreateDate = new DateTimeOffset(DateTime.Now),
                        CreatedBy = requisition.CreatedBy,
                        Comments = null,
                        RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId
                    });
                }
            }

            context.RequisitionStatusHistories.Add(new RequisitionStatusHistory()
            {
                RequisitionId = requisition.RequisitionId,
                CreateDate = DateTime.Now,
                CreatedBy = userName,
                Comments = requisition.Comments,
                RequisitionStatusTypeId = requisition.RequisitionStatusTypeId,
                ApprovalStep = requisition.ApprovalStep,
                DelegatedByApproverId = requisition.DenyDelegateByApproverId
            });
            requisition.DenyDelegateByApproverId = null;

            try
            {
                context.SaveChanges();
            }
            catch(Exception ex)
            {
                if (ex is DbEntityValidationException validationException && validationException.EntityValidationErrors.Any())
                {
                    var errors = validationException.EntityValidationErrors
                        .SelectMany(e => e.ValidationErrors)
                        .Select(e => $"{e.PropertyName}: {e.ErrorMessage}");

                    var messages = string.Join($"{Environment.NewLine}", errors);
                    log.Error($"Validation Errors occurred while saving Requisition: {messages}", ex);
                }
                else
                {
                    log.Error(ex);
                }
                throw;
            }

            foreach (var reqItem in requisition.RequisitionItems)
            {
                if (reqItem.SPRDetail != null && reqItem.SPRDetail.FileAttachments != null)
                {
                    foreach (var file in reqItem.SPRDetail.FileAttachments)
                    {
                        if (file.Id == 0)
                        {
                            var dbFile = context.FileAttachments.Where(x => x.FileName == file.FileName).FirstOrDefault();
                            file.Id = dbFile.Id;
                        }
                    }
                }
            }
        }

        public void UpdateRequisitionStatusToSubmittedIfNeeded(string messageId, int reqItemId, bool isFromMQMessage)
        {
            // IMPORTANT: To be used for MQ Message updating ONLY!
            if (isFromMQMessage)
            {
                var requisition = (from ri in context.RequisitionItems
                                   join r in context.Requisitions
                                       on new { p1 = ri.Id, p2 = ri.RequisitionId } equals new { p1 = reqItemId, p2 = r.RequisitionId }
                                   select r).FirstOrDefault();
                if (requisition == null)
                {
                    log.Info(string.Format("MQ Message Error: Could not find requisition for RequisitionItemId: {0}, MQ messageId: {1}", reqItemId, messageId));
                }
                else if (requisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Submitted)
                {
                    context.Entry(requisition).Property("RequisitionStatusTypeId").CurrentValue = (int)RequisitionStatusTypeEnum.Submitted;
                    this.AddRequisitionStatusHistory(requisition, "System", (int)RequisitionStatusTypeEnum.Submitted);
                    context.SaveChanges();
                }
            }
        }

        public RequisitionItem GetReqRequisitionItem(int? id, string coid, string parentSystemId, string itemId)
        {
            if (id > 0)
            {
                return context.RequisitionItems
                    .Include(x => x.RequisitionItemStatusHistories)
                    .FirstOrDefault(item => item.Id == id);
            }

            return context.RequisitionItems
                .Include(x => x.RequisitionItemStatusHistories)
                .FirstOrDefault(x => x.Requisition.LocationIdentifier.StartsWith(coid)
                && string.Compare(x.ItemId, itemId, true) == 0
                && string.Compare(x.ParentSystemId, parentSystemId, true) == 0);

        }

        public RequisitionItem GetSprRequisitionItem(int? id, string coid, string parentSystemId, int vendorId, string partNumber)
        {
            if (id > 0)
            {
                return context.RequisitionItems
                    .Include(x => x.RequisitionItemStatusHistories)
                    .FirstOrDefault(item => item.Id == id);
            }

            return context.RequisitionItems
                .Include(x => x.SPRDetail)
                .Include(x => x.RequisitionItemStatusHistories)
                .FirstOrDefault(x => x.Requisition.LocationIdentifier.StartsWith(coid)
                && string.Compare(x.ParentSystemId, parentSystemId, true) == 0
                && string.Compare(x.SPRDetail.PartNumber, partNumber, true) == 0
                && x.SPRDetail.VendorId == vendorId);
        }

        protected void UpdateRequisitionItem(Requisition updatedRequisition, string userName, bool isMQEnabled)
        {
            foreach (var item in updatedRequisition.RequisitionItems)
            {
                var reqItem = item;
                var currentReqItem = context.RequisitionItems.Where(x => x.Id == item.Id).FirstOrDefault();

                if (currentReqItem != null)
                {
                    if (isMQEnabled)
                    {
                        //get the PO Number and Parent System Id that has been provided by SMART
                        var poNumber = item.PONumber;
                        var parentSysId = item.ParentSystemId;
                        var status = item.RequisitionItemStatusTypeId;

                        //Set the current Item equal to the Item from the database, essecentially making it as if it were unchanged, 
                        //and add the PO Number and Parent System Id to it so that they can be applied; this was created to correct not
                        //saving the PO Number and Parent System Id when MQ is down.
                        reqItem = currentReqItem;
                        reqItem.PONumber = poNumber;
                        reqItem.ParentSystemId = parentSysId;
                        reqItem.RequisitionItemStatusTypeId = status == (int)RequisitionItemStatusTypeEnum.SystemError ? status : reqItem.RequisitionItemStatusTypeId;
                    }

                    try
                    {
                        context.Entry(currentReqItem).CurrentValues.SetValues(reqItem);
                    }
                    catch (Exception ex)
                    {
                        if (isMQEnabled)
                        {
                            log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisition", "Updating system error items if MQEnabled"), ex);
                            throw;
                        }
                        else
                        {
                            log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisition", "Updating all submitted items if MQ not enabled"), ex);
                            throw;
                        }
                    }
                }
            }
        }

        public int UpdateRequisitionItemPONumber(RequisitionItem requisitionItem)
        {
            if (requisitionItem != null)
            {
                return context.SaveChanges();
            }

            return default(int);
        }

        public void UpdateRequisitionOnReorderWorkflowAndAddHistory(Requisition requisition, string updater)
        {
            // DO NOT Change the order of this add history (this is how it should show up in the UI)
            this.AddRequisitionStatusHistory(requisition, updater, (int)RequisitionStatusTypeEnum.WorkflowChanged);
            this.AddRequisitionStatusHistory(requisition, "System", requisition.RequisitionStatusTypeId);
            requisition.DenyDelegateByApproverId = null;
            context.SaveChanges();
        }

        private void AddRequisitionStatusHistory(Requisition requisition, string updater, int reqStatusTypeId)
        {
            context.RequisitionStatusHistories.Add(new RequisitionStatusHistory()
            {
                RequisitionId = requisition.RequisitionId,
                CreateDate = DateTime.Now,
                CreatedBy = updater,
                Comments = requisition.Comments,
                RequisitionStatusTypeId = reqStatusTypeId,
                ApprovalStep = requisition.ApprovalStep,
                DelegatedByApproverId = requisition.DenyDelegateByApproverId
            });
        }

        public RequisitionStatusHistory BuildRequisitionStatusHistory (Requisition requisition, string updater, int reqStatusTypeId)
        {
            return new RequisitionStatusHistory
            {
                RequisitionId = requisition.RequisitionId,
                CreateDate = DateTime.Now,
                CreatedBy = updater,
                Comments = requisition.Comments,
                RequisitionStatusTypeId = reqStatusTypeId,
                ApprovalStep = requisition.ApprovalStep,
                DelegatedByApproverId = requisition.DenyDelegateByApproverId
            };
        }

        public void BulkInsertRequisitionStatusHistory(IEnumerable<RequisitionStatusHistory> requisitionStatusHistories)
        {
            context.BulkInsert(requisitionStatusHistories);
            context.SaveChanges();
        }


        public void UpdateRequisition(Requisition updatedRequisition, string userName, bool isAudited, bool isMQEnabled, bool isFromSMARTResponse, bool isApprover = false)
        {
            UpdateRequisitionChangesInDatabase(updatedRequisition, userName, isAudited, isMQEnabled, isFromSMARTResponse, isApprover);

            if (isAudited)
            {
                WriteRequisitionAndRequisitionItemHistoriesToDatabase(updatedRequisition, updatedRequisition.Comments, userName, isMQEnabled, isFromSMARTResponse);
            }

            var deadlockRetryCount = 0;
            var maxDeadlockRetryCount = 3;
            var isDeadlock = false;

            do
            {
                try
                {
                    context.SaveChanges();
                    isDeadlock = false;
                }
                catch (DbUpdateException ex)
                {
                    //This will call for a retry with any DbUpdateException
                    isDeadlock = true;
                    var retryMessage = deadlockRetryCount > 0 ? $", Retry: {deadlockRetryCount}" : "";
                    deadlockRetryCount++;
                    log.Error($"Requisition: {updatedRequisition.RequisitionId}, Method: {nameof(UpdateRequisition)}, Area: Saving changes{retryMessage}", ex);
                }
                catch (Exception ex)
                {
                    log.Error(
                        string.Format("Requisition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId,
                            "RequisitionRepository.UpdateRequisition", "Saving changes"), ex);
                }
            } while (isDeadlock && deadlockRetryCount < maxDeadlockRetryCount);

            try
            {
                foreach (var reqItem in updatedRequisition.RequisitionItems)
                {
                    if (reqItem.SPRDetail != null && reqItem.SPRDetail.FileAttachments != null)
                    {
                        foreach (var file in reqItem.SPRDetail.FileAttachments)
                        {
                            if (file.Id == 0)
                            {
                                var dbFile = context.FileAttachments.Where(x => x.FileName == file.FileName).FirstOrDefault();
                                file.Id = dbFile.Id;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(string.Format("Requisition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisition", "Editing file attachment"), ex);
                throw;
            }
        }

        public void UpdateRequisitionChangesInDatabaseAsApprover(Requisition updatedRequisition, string userName, bool isAudited, bool isMQEnabled, bool isFromSMARTResponse)
        {
            UpdateRequisitionChangesInDatabaseAsApprover(updatedRequisition, userName);

            if (isAudited)
            {
                var userFullName = context.Users.Where(x => x.AccountName.ToLower() == userName.ToLower()).FirstOrDefault();
                var historyComment = "Requisition Details Edited By ";
                historyComment += (userFullName != null && userFullName.FirstName != null && userFullName.LastName != null) ? userFullName.FirstName + " " + userFullName.LastName : userName.Split('/')[1];
                WriteRequisitionAndRequisitionItemHistoriesToDatabase(updatedRequisition, historyComment, userName, isMQEnabled, isFromSMARTResponse);
            }

            context.SaveChanges();

            try
            {
                foreach (var reqItem in updatedRequisition.RequisitionItems)
                {
                    if (reqItem.SPRDetail != null && reqItem.SPRDetail.FileAttachments != null)
                    {
                        foreach (var file in reqItem.SPRDetail.FileAttachments)
                        {
                            if (file.Id == 0)
                            {
                                var dbFile = context.FileAttachments.Where(x => x.FileName == file.FileName).FirstOrDefault();
                                file.Id = dbFile.Id;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(string.Format("Requisition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisitionAsApprover", "Editing file attachment"), ex);
                throw;
            }
        }

        public void UpdatePriceForVboHoldItemConversions(List<VboHoldItemConversionDto> updateItems)
        {
            var reqItemIds = updateItems.Select(x => x.RequisitionItemId).ToList();
            var oldConversions = context.VboHoldItemConversions.Where(x => reqItemIds.Contains(x.RequisitionItemId)).ToList();

            oldConversions.ForEach(x =>
            {
                var newPrice = updateItems.First(y => y.RequisitionItemId == x.RequisitionItemId).ItemDetails.ParPrice;

                x.UnitCost = newPrice;
            });

            context.SaveChanges();
        }

        protected void UpdateRequisitionChangesInDatabase(Requisition updatedRequisition, string userName, bool isAudited, bool isMQEnabled, bool isFromSMARTResponse, bool isApprover)
        {
            var oldRequisition = this.GetRequisition(updatedRequisition.RequisitionId);

            context.Entry(oldRequisition).CurrentValues.SetValues(updatedRequisition);

            if (oldRequisition.RequisitionItems == null)
            {
                oldRequisition.RequisitionItems = new List<RequisitionItem>();
            }
            if (!isFromSMARTResponse)
            {
                if (updatedRequisition.RequisitionItems != null && updatedRequisition.RequisitionItems.Any())
                {
                    var deletedItems = new List<RequisitionItem>();
                    try
                    {
                        foreach (var item in oldRequisition.RequisitionItems)
                        {
                            var updatedItem = updatedRequisition.RequisitionItems.FirstOrDefault(x => x.Id == item.Id);
                            if (updatedItem != null)
                            {
                                try
                                {
                                    UpdateRequisitionItemAndDetails(item, updatedItem, userName, updatedRequisition.IsMobile, isApprover);
                                }
                                catch (Exception ex)
                                {
                                    log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisition", "updating item " + item.Id), ex);
                                    throw;
                                }
                            }
                            else
                            {
                                deletedItems.Add(item);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisition", "looping through existing items"), ex);
                        throw;
                    }

                    if (deletedItems.Any())
                    {
                        DeleteRequisitionItems(deletedItems);
                    }

                    var newItems = updatedRequisition.RequisitionItems.Where(x => x.Id == 0);

                    if (newItems.Any())
                    {
                        AddNewRequisitionItemsToRequisition(newItems, userName, ref oldRequisition);
                    }
                }
                else
                {
                    DeleteRequisitionItems(oldRequisition.RequisitionItems);
                }
            }
            else
            {
                UpdateRequisitionItem(updatedRequisition, userName, isMQEnabled);
            }
        }

        protected void UpdateRequisitionChangesInDatabaseAsApprover(Requisition updatedRequisition, string username)
        {
            var oldRequisition = this.GetRequisition(updatedRequisition.RequisitionId);

            context.Entry(oldRequisition).CurrentValues.SetValues(updatedRequisition);

            if (oldRequisition.RequisitionItems == null)
            {
                oldRequisition.RequisitionItems = new List<RequisitionItem>();
            }

            foreach (var requisitionItem in updatedRequisition.RequisitionItems.Where(ApproverUpdatedRequisitionItem))
            {
                var oldReqItem = oldRequisition.RequisitionItems.First(x => x.Id == requisitionItem.Id);

                oldReqItem.QuantityToOrder = requisitionItem?.HasQuantityToOrderChanged ?? false ? requisitionItem.QuantityToOrder : oldReqItem.QuantityToOrder;
                oldReqItem.Discount = requisitionItem?.HasDiscountChanged ?? false ? requisitionItem.Discount : oldReqItem.Discount;

                if ((requisitionItem.SPRDetail?.HasApproverChangedEstimatedPrice ?? false) && oldReqItem.SPRDetail != null)
                {
                    oldReqItem.SPRDetail.EstimatedPrice = requisitionItem.SPRDetail.EstimatedPrice;
                }

                if (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any(x => x.ChangeStatus != ChangeStatus.NoChange))
                {
                    var lotSerialPairCountLimit = Math.Min(Values.MaxLotSerialPairs, oldReqItem.QuantityToOrder) - oldReqItem.ClinicalUseDetails.Count;
                    var provider = oldReqItem.ClinicalUseDetails.First().Provider;
                    var patientId = oldReqItem.ClinicalUseDetails.First().PatientId;
                    var patientName = oldReqItem.ClinicalUseDetails.First().PatientName;

                    foreach (var clinicalUseDetail in requisitionItem.ClinicalUseDetails.Where(x => x.ChangeStatus == ChangeStatus.Deleted))
                    {
                        var oldDetails = oldReqItem.ClinicalUseDetails.FirstOrDefault(x => x.Id == clinicalUseDetail.Id);
                        if (oldDetails != null)
                        {
                            context.ClinicalUseDetails.Remove(oldDetails);
                            lotSerialPairCountLimit++;
                        }
                    }

                    var emptyOriginalClinicalUseDetails = requisitionItem.ClinicalUseDetails.FirstOrDefault(x => string.IsNullOrWhiteSpace(x.LotNumber) && string.IsNullOrWhiteSpace(x.SerialNumber) && x.ChangeStatus == ChangeStatus.NoChange);
                    if (emptyOriginalClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Count > 1)
                    {
                        var oldDetails = oldReqItem.ClinicalUseDetails.FirstOrDefault(x => x.Id == emptyOriginalClinicalUseDetails.Id);
                        if (oldDetails != null && string.IsNullOrWhiteSpace(oldDetails.LotNumber) && string.IsNullOrWhiteSpace(oldDetails.SerialNumber))
                        {
                            context.ClinicalUseDetails.Remove(oldDetails);
                            emptyOriginalClinicalUseDetails.ChangeStatus = ChangeStatus.Deleted;
                            lotSerialPairCountLimit++;
                        }
                    }

                    if (requisitionItem.ClinicalUseDetails.Any(x => x.ChangeStatus == ChangeStatus.Added))
                    {
                        foreach (var clinicalUseDetail in requisitionItem.ClinicalUseDetails.Where(x => x.ChangeStatus == ChangeStatus.Added))
                        {
                            if (lotSerialPairCountLimit > 0)
                            {
                                clinicalUseDetail.Provider = provider;
                                clinicalUseDetail.PatientId = patientId;
                                clinicalUseDetail.PatientName = patientName;
                                context.ClinicalUseDetails.Add(clinicalUseDetail);
                                lotSerialPairCountLimit--;
                            }
                        }
                    }

                    foreach (var clinicalUseDetail in requisitionItem.ClinicalUseDetails.Where(x => x.ChangeStatus != ChangeStatus.Deleted && x.ChangeStatus != ChangeStatus.Added))
                    {
                        var oldDetails = oldReqItem.ClinicalUseDetails.FirstOrDefault(x => x.Id == clinicalUseDetail.Id);
                        if (oldDetails != null)
                        {
                            oldDetails.LotNumber = clinicalUseDetail.HasLotNumberChanged ? clinicalUseDetail.LotNumber : oldDetails.LotNumber;
                            oldDetails.SerialNumber = clinicalUseDetail.HasSerialNumberChanged ? clinicalUseDetail.SerialNumber : oldDetails.SerialNumber;
                        }
                    }

                    requisitionItem.ClinicalUseDetails = requisitionItem.ClinicalUseDetails.Where(x => x.ChangeStatus != ChangeStatus.Deleted).ToList();
                }

                if (requisitionItem?.HasConversionChanged ?? false)
                {
                    oldReqItem.RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId;

                    if (requisitionItem.VboHoldItemConversion != null)
                    {
                        requisitionItem.VboHoldItemConversion.CreateDate = DateTime.Now;
                        requisitionItem.VboHoldItemConversion.CreatedBy = username;

                        if (oldReqItem.VboHoldItemConversion != null)
                        {
                            context.Entry(oldReqItem.VboHoldItemConversion).CurrentValues.SetValues(requisitionItem.VboHoldItemConversion);
                        }
                        else
                        {
                            context.VboHoldItemConversions.Add(requisitionItem.VboHoldItemConversion);
                        }
                    }
                    else if (oldReqItem.VboHoldItemConversion != null)
                    {
                        context.VboHoldItemConversions.Remove(oldReqItem.VboHoldItemConversion);

                    }
                }
            }
        }

        protected void WriteRequisitionAndRequisitionItemHistoriesToDatabase(Requisition updatedRequisition, string historyComments, string userName, bool isMQEnabled, bool isFromSMARTResponse)
        {
            //After updating, if needed, update the status histories for requisition and requisitionItems
            var lastStatus = context.RequisitionStatusHistories.Where(x => x.RequisitionId == updatedRequisition.RequisitionId).OrderBy(y => y.Id).ToList().LastOrDefault();
            bool canWriteReqSubmissionStatus = (lastStatus == null || (lastStatus != null && lastStatus.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Submitted));

            var changesMade = base.context.ChangeTracker.Entries().Any(e => (e.State == EntityState.Modified || e.State == EntityState.Added)
                                            && (e.Entity is Requisition || e.Entity is SPRDetail || e.Entity is FileAttachment || e.Entity is ClinicalUseDetail));
            var changedRequisitionItems = base.context.ChangeTracker.Entries().Where(e => (e.State == EntityState.Modified || e.State == EntityState.Added)
                                                                                            && e.Entity is RequisitionItem);

            if ((changesMade || (changedRequisitionItems != null && changedRequisitionItems.Any())) && canWriteReqSubmissionStatus)
            {
                var reqStatusHist = updatedRequisition.RequisitionStatusHistories;
                context.RequisitionStatusHistories.Add(new RequisitionStatusHistory()
                {
                    RequisitionId = updatedRequisition.RequisitionId,
                    CreateDate = DateTime.Now,
                    CreatedBy = userName,
                    Comments = historyComments,
                    RequisitionStatusTypeId = updatedRequisition.RequisitionStatusTypeId,
                    ApprovalStep = updatedRequisition.ApprovalStep,
                    DelegatedByApproverId = updatedRequisition.DenyDelegateByApproverId
                });
                updatedRequisition.DenyDelegateByApproverId = null;
            }

            try
            {
                if (isMQEnabled == false || (isMQEnabled && isFromSMARTResponse == false))
                {
                    foreach (var requisitionItem in changedRequisitionItems)
                    {
                        context.RequisitionItemStatusHistories.Add(new RequisitionItemStatusHistory()
                        {
                            RequisitionItem = requisitionItem.Entity as RequisitionItem,
                            CreateDate = new DateTimeOffset(DateTime.Now),
                            CreatedBy = userName,
                            Comments = null,
                            RequisitionItemStatusTypeId = (requisitionItem.Entity as RequisitionItem).RequisitionItemStatusTypeId
                        });
                    }
                }
                else if (isMQEnabled && isFromSMARTResponse && changedRequisitionItems.Any(x => (x.Entity as RequisitionItem).RequisitionItemStatusTypeId == (int)RequisitionItemStatusTypeEnum.SystemError))
                {
                    foreach (var requisitionItem in changedRequisitionItems.Where(x => (x.Entity as RequisitionItem).RequisitionItemStatusTypeId == (int)RequisitionItemStatusTypeEnum.SystemError))
                    {
                        context.RequisitionItemStatusHistories.Add(new RequisitionItemStatusHistory()
                        {
                            RequisitionItem = requisitionItem.Entity as RequisitionItem,
                            CreateDate = new DateTimeOffset(DateTime.Now),
                            CreatedBy = userName,
                            Comments = null,
                            RequisitionItemStatusTypeId = (requisitionItem.Entity as RequisitionItem).RequisitionItemStatusTypeId
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", updatedRequisition.RequisitionId, "RequisitionRepository.UpdateRequisition", "Writing status histories for items"), ex);
                throw;
            }

        }

        public static void UpdateNonPrivateDataInClinicalDetails(EProcurementContext context, RequisitionItem updatingReqItem, bool isApprover)
        {
            if (updatingReqItem.ClinicalUseDetails.Any())
            {
                foreach (var clinicalDetails in updatingReqItem.ClinicalUseDetails.ToList())
                {
                    var clinicalUseDetailsToBeUpdated = context.ClinicalUseDetails.Find(clinicalDetails.Id);
                    if (clinicalUseDetailsToBeUpdated != null && context.Entry(clinicalUseDetailsToBeUpdated) != null)
                    {
                        context.Entry(clinicalUseDetailsToBeUpdated).State = EntityState.Detached;
                    }

                    context.ClinicalUseDetails.Attach(clinicalDetails);
                    context.Entry(clinicalDetails).State = EntityState.Modified;
                    if (!isApprover)
                    {

                        context.Entry(clinicalDetails).Property(rec => rec.Provider).IsModified = false;
                        context.Entry(clinicalDetails).Property(rec => rec.PatientId).IsModified = false;
                        context.Entry(clinicalDetails).Property(rec => rec.PatientName).IsModified = false;
                    }

                }
            }

        }

        public List<Requisition> GetPendingRequisitionsForUserCOID(string userName, string COID)
        {
            var pendingApprovalsQuery = context.Requisitions.Where(x => !x.IsVendor && x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval && x.ApprovalStep != null
                                        && x.CreatedBy.ToLower() == userName.ToLower() && x.LocationIdentifier.StartsWith(COID))
                                        .Include("RequisitionItems")
                                        .Include("RequisitionItems.SPRDetail");
            return pendingApprovalsQuery.ToList();
        }

        public List<Requisition> GetRequisitionsByWorkflowOwners(string userName, IEnumerable<UserWorkflowStep> stepsCanAffect, string COID)
        {
            var query = (from x in context.Requisitions
                         join y in stepsCanAffect on x.CreatedBy equals y.User.AccountName
                         where !x.IsVendor && x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval && x.ApprovalStep != null
                         && x.LocationIdentifier.StartsWith(COID) && x.CreatedBy == userName
                         select x)
             .Include("RequisitionItems")
             .Include("RequisitionItems.SPRDetail");

            return query.ToList();
        }

        public List<Requisition> GetPendingRequisitionsByFacilityWorkflow(string COID)
        {
            var query = (from x in context.Requisitions
                         where x.IsVendor
                         && (x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval || x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.OnHold)
                         && x.ApprovalStep != null
                         && x.LocationIdentifier.StartsWith(COID)
                         select x)
             .Include("RequisitionItems")
             .Include("RequisitionItems.SPRDetail");

            return query.ToList();
        }

        public bool DeleteAttachment(List<string> fileNames)
        {
            var files = context.FileAttachments.Where(x => fileNames.Any(y => y == x.FileName)).ToList();
            context.FileAttachments.RemoveRange(files);
            context.SaveChanges();
            return true;
        }

        /// <summary>
        /// Get PAR Requistion Items
        /// </summary>
        /// <param name="id"></param>
        /// <param name="coid"></param>
        /// <param name="parentSystemId"></param>
        /// <param name="itemId"></param>
        /// <param name="loadRequisitionItemStatusHistory"></param>
        /// <returns></returns>
        public List<RequisitionItem> GetReqRequisitionItems(int? id, string coid, string parentSystemId, string itemId, bool loadRequisitionItemStatusHistory = false)
        {
            var query = context.RequisitionItems;

            if (loadRequisitionItemStatusHistory)
            {
                query.Include(x => x.RequisitionItemStatusHistories);
            }

            if (id > 0)
            {
                return query.Where(item => item.Id == id).ToList();
            }

            return query
                .Where(x => x.Requisition.LocationIdentifier.StartsWith(coid)
                && string.Compare(x.ItemId, itemId, true) == 0
                && string.Compare(x.ParentSystemId, parentSystemId, true) == 0).ToList();

        }

        /// <summary>
        /// Get Spr Req Item for MMIS status update
        /// </summary>
        /// <param name="id"></param>
        /// <param name="coid"></param>
        /// <param name="parentSystemId"></param>
        /// <param name="vendorId"></param>
        /// <param name="partNumber"></param>
        /// <returns></returns>
        public RequisitionItem GetSprRequisitionItem(int? id, string coid, string parentSystemId, int vendorId, string partNumber, bool loadRequisitionItemStatusHistory = false)
        {
            var query = context.RequisitionItems;

            if (loadRequisitionItemStatusHistory)
            {
                query.Include(x => x.RequisitionItemStatusHistories);
            }

            if (id > 0)
            {
                return query
                    .FirstOrDefault(item => item.Id == id);
            }

            return query
                .Include(x => x.SPRDetail)
                .FirstOrDefault(x => x.Requisition.LocationIdentifier.StartsWith(coid)
                && string.Compare(x.ParentSystemId, parentSystemId, true) == 0
                && string.Compare(x.SPRDetail.PartNumber, partNumber, true) == 0
                && x.SPRDetail.VendorId == vendorId);
        }

        /// <summary>
        /// Add requisition items and histories
        /// </summary>
        /// <param name="requisitionItems"></param>
        public void UpdateRequisitionItemAndAddHistory(IEnumerable<RequisitionItem> requisitionItems)
        {
            foreach (var requisitionItem in requisitionItems)
            {
                context.RequisitionItems.Attach(requisitionItem);
                context.Entry(requisitionItem).State = EntityState.Modified;
                var addedHistory = requisitionItem.RequisitionItemStatusHistories.First(x => x.Id == 0);
                context.Entry(addedHistory).State = EntityState.Added;
            }
            context.SaveChanges();
        }

        public List<FileAttachment> GetFileAttachments(int requisitionItemId)
        {
            if (requisitionItemId == 0)
            {
                return null;
            }
            else
            {
                var formattedFiles = new List<FileAttachment>();
                var files = context.FileAttachments.Where(x => x.RequisitionItemId == requisitionItemId).ToList();
                foreach (var file in files)
                {
                    var user = context.Users.Where(x => x.AccountName == file.CreatedBy).FirstOrDefault();
                    var name = user.FirstName + " " + user.LastName;
                    var formattedFile = new FileAttachment() { Id = file.Id, FileName = file.FileName, FilePath = "", CreateDate = file.CreateDate, CreatedBy = name, RequisitionId = file.RequisitionId, RequisitionItemId = file.RequisitionItemId };
                    formattedFiles.Add(formattedFile);
                }
                return formattedFiles;
            }
        }

        /// <summary>
        /// Add requisition items and histories
        /// </summary>
        /// <param name="sprdetails"></param>
        public void UpdateSprDetails(IEnumerable<SPRDetail> sprDetails, string userName)
        {
            foreach (var sprDetail in sprDetails)
            {
                if (sprDetail != null)
                {
                    var currentSprItem = context.SPRDetails.FirstOrDefault(x => x.RequisitionItemId == sprDetail.RequisitionItemId);
                    if (currentSprItem != null)
                    {
                        context.Entry(currentSprItem).CurrentValues.SetValues(sprDetail);
                    }
                }
            }
            context.SaveChanges();
        }

        public List<int> GetPoNumbersByProjectNumberAndCoid(string coid, string projectNumber)
        {
            var data = new List<int?>();
            var returnList = new List<int>();

            if (string.IsNullOrWhiteSpace(projectNumber))
            {
                return new List<int>();
            }
            else
            {
                data = (from spr in context.SPRDetails
                        join ri in context.RequisitionItems
                            on new { p1 = spr.BudgetNumber.ToLower(), p2 = spr.RequisitionItemId } equals new { p1 = projectNumber.ToLower(), p2 = ri.Id }
                        join r in context.Requisitions
                            on new { p1 = ri.RequisitionId, p2 = coid } equals new { p1 = r.RequisitionId, p2 = r.LocationIdentifier.Substring(0, (r.CountryCode == "U" ? 5 : 3)/*UK Note: couldn't use LocationMapper.*/) }
                        select ri.PONumber).Distinct().ToList();
            }

            data.Where(x => x != null).ToList().ForEach(y => returnList.Add((int)y));

            return returnList;

        }

        public Tuple<RequisitionItem, int> AddNewSubstituteRequisitionItemtoRequisition(RequisitionItem originalItem, RequisitionItem subItem, string userName)
        {
            int originalQty = originalItem.QuantityToOrder;
            try
            {
                List<RequisitionItem> itemList = new List<RequisitionItem>() { subItem };
                Requisition req = this.GetRequisition(subItem.RequisitionId);

                this.AddNewRequisitionItemsToRequisition(itemList, userName, ref req);
                context.SaveChanges(); //Troy didn't put it in the Add RequisitionItem method, so we'll save here I guess

                originalQty = UpdateQuantityToOrderForItemBeingSubstituted(originalItem.Id, originalItem.QuantityToOrder, subItem.QuantityToOrder);

            }
            catch (System.Data.Entity.Infrastructure.DbUpdateException ex)
            {
                log.Error(String.Format("Method: {0}, Error adding new User into database", "RequisitionRepository.AddNewSubstituteRequisitionItemtoRequisition"), ex);
                // return the subItem already in the database
                var dbSubItem = context.RequisitionItems.Where(x => x.ItemId.ToLower() == subItem.ItemId.ToLower() && x.ParentRequisitionItemId == subItem.ParentRequisitionItemId).FirstOrDefault();
                var dbOriginalItem = context.RequisitionItems.Where(y => y.Id == subItem.ParentRequisitionItemId).FirstOrDefault();
                return new Tuple<RequisitionItem, int>(dbSubItem ?? subItem,
                                                        dbOriginalItem != null ? dbOriginalItem.QuantityToOrder : originalQty);
                //ensures we do not send a null item if the insertion failed for 
                //reasons other than violation of UNIQUE KEY constraint 
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error adding new SubItem into database", "UserRepository.InsertUser"), ex);
                return new Tuple<RequisitionItem, int>(subItem, originalQty);
                /*  
                 *  The reason we are still returning the subItem, even though the save failed, is so that we still show the new subItem in the UI.
                 *  If it was a duplicate subItem it would have gone to the earlier catch block, so we will just save the new sub Item later
                 *  when we load the requisition again and the save doesn't fail... hopefully.
                 */
            }

            return new Tuple<RequisitionItem, int>(subItem, originalQty);
        }

        int UpdateQuantityToOrderForItemBeingSubstituted(int originalItemId, int originalQuantity, int subQuantity)
        {
            var _reqItem = context.RequisitionItems.FirstOrDefault(x => x.Id == originalItemId);
            if (_reqItem != null && _reqItem.QuantityToOrder > 0 && subQuantity > 0)
            {
                _reqItem.QuantityToOrder = _reqItem.QuantityToOrder - subQuantity;
                context.Entry(_reqItem).Property(x => x.QuantityToOrder).IsModified = true;
                context.SaveChanges();
                return _reqItem.QuantityToOrder;
            }
            else
            {
                return originalQuantity;
            }
        }

        public RequisitionItem GetRequisitionItemById(int Id)
        {
            return context.RequisitionItems.FirstOrDefault(x => x.Id == Id);
        }

        public LastOrderDetailsDTO GetLastOrderDetails(string coid, string dept, string parClass, string itemId)
        {
            var sqlParams = new []
            {
                new SqlParameter("@COID", coid)
                {
                    DbType = DbType.AnsiString
                },
                new SqlParameter("@DEPT", dept)
                {
                    DbType = DbType.AnsiString
                },
                new SqlParameter("@PAR", parClass)
                {
                    DbType = DbType.AnsiString
                },
                new SqlParameter("@ITEMID", itemId)
                {
                    DbType = DbType.AnsiString
                }
            };

            var rowsReturned = context.Database.SqlQuery<LastOrderDetailsDTO>("exec dbo.usp_GetLastOrderDetails @ITEMID, @PAR, @COID, @DEPT", sqlParams).ToList();
            return rowsReturned.FirstOrDefault();
        }

        public void DeleteClinicalUseDetails(IEnumerable<ClinicalUseDetail> clinicalUseDetails)
        {
            if (clinicalUseDetails.Any())
            {
                context.ClinicalUseDetails.RemoveRange(clinicalUseDetails);
            }
        }

        public void DeleteRequisitionItems(IEnumerable<RequisitionItem> reqItems)
        {
            if (reqItems != null && reqItems.Any())
            {
                try
                {
                    var itemInventories = reqItems.Select(inv => inv.Inventory).Where(x => x != null).ToList();
                    if (itemInventories.Any())
                    {
                        context.ItemInventories.RemoveRange(itemInventories);
                    }
                    var reqItemStatusHistories = reqItems.SelectMany(rish => rish.RequisitionItemStatusHistories).ToList();
                    if (reqItemStatusHistories.Any())
                    {
                        context.RequisitionItemStatusHistories.RemoveRange(reqItemStatusHistories);
                    }
                    var reqItemClinicalUseDetails = reqItems.SelectMany(item => item.ClinicalUseDetails).ToList();
                    if (reqItemClinicalUseDetails.Any())
                    {
                        context.ClinicalUseDetails.RemoveRange(reqItemClinicalUseDetails);
                    }
                    var sprDetails = reqItems.Select(spr => spr.SPRDetail).Where(x => x != null).ToList();
                    if (sprDetails.Any())
                    {
                        var fileAttachments = sprDetails.SelectMany(fa => fa.FileAttachments).ToList();
                        if (fileAttachments.Any())
                        {
                            context.FileAttachments.RemoveRange(fileAttachments);
                        }
                        context.SPRDetails.RemoveRange(sprDetails);
                    }
                    context.RequisitionItems.RemoveRange(reqItems);
                }
                catch (Exception ex)
                {
                    log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", reqItems.First().RequisitionId, "RequisitionRepository.DeleteRequisitionItems", "deleting items no longer in requisition"), ex);
                    throw;
                }
            }
        }

        private RequisitionItem GetAutoSubRequisitionItem(string parentSystemId, string originalParentSystemId, string itemId, int? parentRequisitionItemId)
        {
            return context.RequisitionItems.FirstOrDefault(x => parentSystemId == x.ParentSystemId
                                                            && originalParentSystemId == x.OriginalParentSystemId
                                                            && itemId == x.ItemId
                                                            && parentRequisitionItemId == x.ParentRequisitionItemId);
        }

        private void AddNewRequisitionItemsToRequisition(IEnumerable<RequisitionItem> newItems, string userName, ref Requisition oldRequisition)
        {
            try
            {
                foreach (var item in newItems)
                {
                    item.CreateDate = DateTime.Now;
                    item.CreatedBy = userName;
                    oldRequisition.RequisitionItems.Add(item);
                    if (item.SPRDetail != null)
                    {
                        foreach (var file in item.SPRDetail.FileAttachments)
                        {
                            file.FilePath = null;
                            file.CreatedBy = userName;
                            file.CreateDate = DateTime.Now;
                            file.RequisitionId = item.RequisitionId;
                            file.RequisitionItemId = item.Id;
                            context.FileAttachments.Add(file);
                        }
                        context.SPRDetails.Add(item.SPRDetail);
                    }
                    if (item.Inventory != null)
                    {
                        item.CreateDate = DateTime.UtcNow;
                        item.Inventory.CreateDate = DateTime.UtcNow;
                        item.Inventory.LastUpdatedUTC = DateTime.UtcNow;
                        context.ItemInventories.Add(item.Inventory);
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Requsition: {0}, Method: {1}, Area: {2}", oldRequisition.RequisitionId, "RequisitionRepository.AddNewRequisitionItemsToRequisition", "adding new items to the database"), ex);
                throw;
            }
        }

        private void UpdateRequisitionItemAndDetails(RequisitionItem oldRequisitionItem, RequisitionItem newRequisitionItem, string userName, bool isMobile, bool isApprover)
        {
            context.Entry(oldRequisitionItem).CurrentValues.SetValues(newRequisitionItem);

            if (oldRequisitionItem.ClinicalUseDetails != null && oldRequisitionItem.ClinicalUseDetails.Any() && newRequisitionItem.ClinicalUseDetails != null && newRequisitionItem.ClinicalUseDetails.Any())
            {
                if (oldRequisitionItem.CreatedBy == userName)
                {
                    var existingClinicalUseDetails = newRequisitionItem.ClinicalUseDetails.Where(x => oldRequisitionItem.ClinicalUseDetails.Select(y => y.Id).Contains(x.Id)).ToList();
                    foreach (var newClinicalDetail in existingClinicalUseDetails)
                    {
                        context.Entry(oldRequisitionItem.ClinicalUseDetails.FirstOrDefault(x => x.Id == newClinicalDetail.Id)).CurrentValues.SetValues(newClinicalDetail);
                        //check whether user is the owner of the requisition.  If not, don't allow update of patient or provider info
                    }
                    var newClinicalDetails = newRequisitionItem.ClinicalUseDetails.Where(x => x.Id == 0).ToList();
                    foreach (var newClinicalDetail in newClinicalDetails)
                    {
                        context.ClinicalUseDetails.Add(newClinicalDetail);
                    }

                    var deletedClinicalUseDetails = oldRequisitionItem.ClinicalUseDetails.Where(x => !newRequisitionItem.ClinicalUseDetails.Select(y => y.Id).Contains(x.Id)).ToList();
                    foreach (var deletedClinicalUseDetail in deletedClinicalUseDetails)
                    {
                        context.Entry(deletedClinicalUseDetail).State = EntityState.Deleted;
                    }
                }
                else
                {
                    UpdateNonPrivateDataInClinicalDetails(context, newRequisitionItem, isApprover);
                }
            }
            else if (newRequisitionItem.ClinicalUseDetails != null && newRequisitionItem.ClinicalUseDetails.Any())
            {
                context.ClinicalUseDetails.Add(newRequisitionItem.ClinicalUseDetails.First());
            }

            //SPRDetails
            if (oldRequisitionItem.SPRDetail != null && newRequisitionItem.SPRDetail != null)
            {
                //FYI: Hack here (Don't save file path field. ever. :) )
                foreach (var file in newRequisitionItem.SPRDetail.FileAttachments)
                {
                    if (file.Id == 0)
                    {
                        var insertFile = new FileAttachment() { FileName = file.FileName, FilePath = null, CreateDate = DateTime.Now, CreatedBy = userName, RequisitionId = newRequisitionItem.RequisitionId, RequisitionItemId = newRequisitionItem.Id };
                        context.FileAttachments.Add(insertFile);
                    }
                }
                context.Entry(oldRequisitionItem.SPRDetail).CurrentValues.SetValues(newRequisitionItem.SPRDetail);
            }
            else if (newRequisitionItem.SPRDetail != null)
            {
                //FYI: Hack here (Don't save file path field. ever. :) )
                foreach (var file in newRequisitionItem.SPRDetail.FileAttachments)
                {
                    file.FilePath = null;
                    file.CreatedBy = userName;
                    file.CreateDate = DateTime.Now;
                    file.RequisitionId = newRequisitionItem.RequisitionId;
                    file.RequisitionItemId = newRequisitionItem.Id;
                    context.FileAttachments.Add(file);
                }
                context.SPRDetails.Add(newRequisitionItem.SPRDetail);
            }

            if (oldRequisitionItem.VboHoldItemConversion != null && newRequisitionItem.VboHoldItemConversion != null)
            {
                context.Entry(oldRequisitionItem.VboHoldItemConversion).CurrentValues.SetValues(newRequisitionItem.VboHoldItemConversion);
            }
            else if (newRequisitionItem.VboHoldItemConversion != null)
            {
                newRequisitionItem.CreateDate = DateTime.Now;
                context.VboHoldItemConversions.Add(newRequisitionItem.VboHoldItemConversion);
            }
            else if (oldRequisitionItem.VboHoldItemConversion != null)
            {
                context.VboHoldItemConversions.Remove(oldRequisitionItem.VboHoldItemConversion);
            }

            if (oldRequisitionItem.Inventory != null && newRequisitionItem.Inventory != null)
            {
                //since the newRequisitionItem.Inventory is generated in eproAPI before being sent down, we need to
                //make sure that we do not change ItemInventory's UserName or CreateDate in the database.
                newRequisitionItem.Inventory.CreateDate = oldRequisitionItem.Inventory.CreateDate;
                newRequisitionItem.Inventory.UserName = oldRequisitionItem.Inventory.UserName;

                newRequisitionItem.Inventory.LastUpdatedUTC = DateTime.UtcNow;

                context.Entry(oldRequisitionItem.Inventory).CurrentValues.SetValues(newRequisitionItem.Inventory);
            }
            else if (newRequisitionItem.Inventory != null)
            {
                newRequisitionItem.Inventory.CreateDate = DateTime.UtcNow;
                newRequisitionItem.Inventory.LastUpdatedUTC = DateTime.UtcNow;
                context.ItemInventories.Add(newRequisitionItem.Inventory);
            }
            else if (oldRequisitionItem.Inventory != null && !isMobile)
            {
                context.ItemInventories.Remove(oldRequisitionItem.Inventory);
            }

        }

        private void AddItemStatusHistoryRecord(RequisitionItem subItem)
        {
            try
            {
                context.RequisitionItemStatusHistories.Add(new RequisitionItemStatusHistory()
                {
                    RequisitionItemStatusTypeId = subItem.RequisitionItemStatusTypeId,
                    RequisitionItemId = subItem.Id,
                    CreatedBy = subItem.CreatedBy,
                    CreateDate = DateTimeOffset.Now
                });
                context.SaveChanges();
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error adding item status history to database", "UserRepository.InsertUser"), ex);
            }
        }

        public SmartItemRequisitionIdDTO GetSmartItemRequisitionIdByPONumber(SmartItemRequisitionIdDTO smartItemRequisitionIdDTO)
        {
            var poNumbers = smartItemRequisitionIdDTO.ItemRequistionIds.Select(p => p.PONumber).Distinct().ToList();
            var parentSystemIdList = smartItemRequisitionIdDTO.ItemRequistionIds.Select(l => l.ParentSystemId).Distinct().ToList();
            var ItemNumberList = smartItemRequisitionIdDTO.ItemRequistionIds.Select(I => I.ItemNumber).ToList();
            var ReOrderNumberList = smartItemRequisitionIdDTO.ItemRequistionIds.Select(I => I.ReorderNumber).ToList();
            var SDTItemNumberList = smartItemRequisitionIdDTO.ItemRequistionIds.Select(s =>s.STDItemNumber).ToList();

            var result = new SmartItemRequisitionIdDTO();
            try
            {
                var poItems = (from RI in context.RequisitionItems
                               join R in context.Requisitions
                               on RI.RequisitionId equals R.RequisitionId
                               where (poNumbers.Contains((int)RI.PONumber) || parentSystemIdList.Contains(RI.ParentSystemId))
                               && (ItemNumberList.Contains(RI.ItemId) || ReOrderNumberList.Contains(RI.ItemId) || SDTItemNumberList.Contains(RI.ItemId))
                               && R.LocationIdentifier.Contains(smartItemRequisitionIdDTO.COID)
                               select new
                               {
                                   ItemId = RI.ItemId,
                                   RequisitionId = RI.RequisitionId,
                                   LocationIdentifier = R.LocationIdentifier,
                                   PONumber = RI.PONumber,
                                   ParentSystemId = RI.ParentSystemId
                               }).Distinct();
                if (poItems != null && poItems.Count() > 0)
                {
                    var requisitionItemsLIst = poItems.ToList().SelectMany(
                    poItem => smartItemRequisitionIdDTO.ItemRequistionIds.Where(itemReq =>
                    (poItem.ItemId == itemReq.ItemNumber || poItem.ItemId == itemReq.ReorderNumber || poItem.ItemId== itemReq.STDItemNumber) && (
                    (poItem.PONumber == itemReq.PONumber || poItem.ParentSystemId == itemReq.ParentSystemId))
                    && poItem.LocationIdentifier == smartItemRequisitionIdDTO.COID + "_" + itemReq.DepartmentId),
                    (poItem, itemReq) => new ItemRequistionId() { PONumber = itemReq.PONumber, ItemNumber = itemReq.ItemNumber,
                        RequisitionId = poItem.RequisitionId, DepartmentId = itemReq.DepartmentId, ParentSystemId = itemReq.ParentSystemId, ReorderNumber= itemReq.ReorderNumber }
                 ).ToList();
                    if (requisitionItemsLIst != null && requisitionItemsLIst.Count() > 0)
                    {
                        result.ItemRequistionIds = requisitionItemsLIst;
                    }
                }
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error selecting requisition Id from database", "GetSmartItemRequisitionIdByPONumber"), ex);
            }
            return result;
        }

        public List<ParItemWithLastOrdered> GetLastOrderedDetails(string coid, string department, string parId, List<ParItem> parItems)
        {
            SqlParameter[] sqlParams = new SqlParameter[3];

            sqlParams[0] = new SqlParameter("@Coid", coid);
            sqlParams[0].DbType = System.Data.DbType.String;

            sqlParams[1] = new SqlParameter("@Dept", department);
            sqlParams[1].DbType = System.Data.DbType.String;

            var table = new DataTable("@parItems");
            table.Columns.Add("ItemId", typeof(string));
            table.Columns.Add("ParId", typeof(string));

            foreach (var item in parItems)
            {
                table.Rows.Add(item.ItemId.ToString(), item.ParId);
            }

            sqlParams[2] = new SqlParameter("@ParItems", table);
            sqlParams[2].SqlDbType = SqlDbType.Structured;
            sqlParams[2].TypeName = "dbo.ParItemTemplate";

            var rowsReturned = context.Database.SqlQuery<LastOrderedPageRow>("exec dbo.usp_GetLastOrderedDetails @Coid, @Dept, @ParItems", sqlParams).ToList();

            List<ParItemWithLastOrdered> returnList = (from item in parItems
                                                       join lastOrdered in rowsReturned
                                                        on new { ItemId = item.ItemId.ToString(), item.ParId }
                                                        equals new { lastOrdered.ItemId, lastOrdered.ParId } into match
                                                       from result in match.DefaultIfEmpty()
                                                       select new ParItemWithLastOrdered(item, result?.LastOrderedQuantity, result?.LastOrderedDate
                                                       )).ToList();

            return returnList;
        }

        private DataTable getIdTemplateDataTable(string tableName, List<int> ids)
        {
            var table = new DataTable(tableName);

            table.Columns.Add("Id", typeof(int));

            foreach (var id in ids)
            {
                table.Rows.Add(id);
            }

            return table;
        }

        private bool ApproverUpdatedRequisitionItem(RequisitionItem requisitionItem)
        {
            if (requisitionItem == null)
            {
                return false;
            }

            return (requisitionItem.HasConversionChanged ?? false)
                || requisitionItem.HasDiscountChanged
                || requisitionItem.HasQuantityToOrderChanged
                || (requisitionItem.SPRDetail?.HasApproverChangedEstimatedPrice ?? false)
                || (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any(x => x.ChangeStatus != ChangeStatus.NoChange));
        }

        public void UpdateRequisitionAsApprover(Requisition updatedRequisition, string userName, bool isAudited, bool isMQEnabled, bool isFromSMARTResponse)
        {
            //optional isApprover parameter is only needed for saving PatientId, PatientName, or Provider
            bool isApprover = true;
            UpdateRequisition(updatedRequisition, userName, isAudited, isMQEnabled, isFromSMARTResponse, isApprover);
        }

        public PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport(PurchasingRequisitionReportParameters reportParameters)
        {
            if (reportParameters.AdvancedFiltersDto?.Vendors != null &&
                reportParameters.AdvancedFiltersDto.Vendors.Count > 0 &&
                reportParameters.AdvancedFiltersDto.Vendors[0].VendorId == 0)
            {
                reportParameters.AdvancedFiltersDto.Vendors = new List<VendorModel>();
            }

            var coidsTable = new DataTable();
            coidsTable.Columns.Add("Coid", typeof(string));
            foreach (var coid in reportParameters.Coids)
            {
                coidsTable.Rows.Add(coid);
            }

            var vendorsTable = new DataTable();
            vendorsTable.Columns.Add("Id", typeof(int));
            if (reportParameters.AdvancedFiltersDto?.Vendors != null)
            {
                foreach (var vendor in reportParameters.AdvancedFiltersDto.Vendors)
                {
                    vendorsTable.Rows.Add(vendor.VendorId);
                }
            }

            var buyersTable = new DataTable();
            buyersTable.Columns.Add("VarcharVal", typeof(string));
            if (reportParameters.AdvancedFiltersDto?.Buyers != null)
            {
                foreach (var buyer in reportParameters.AdvancedFiltersDto.Buyers)
                {
                    buyersTable.Rows.Add(buyer.BuyerId);
                }
            }

            var reqTypeTable = new DataTable();
            reqTypeTable.Columns.Add("Id", typeof(int));
            if (reportParameters.AdvancedFiltersDto?.ReqTypes != null)
            {
                foreach (var reqType in reportParameters.AdvancedFiltersDto.ReqTypes)
                {
                    reqTypeTable.Rows.Add(reqType.ReqTypeId);
                }
            }

            var parameters = new List<SqlParameter>
        {
            new SqlParameter("@coids", coidsTable) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.CoidList" },
            new SqlParameter("@vendors", vendorsTable) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" },
            new SqlParameter("@buyers", buyersTable) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.Varchar50Template" },
            new SqlParameter("@reqTypes", reqTypeTable) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" },
            new SqlParameter("@filterText", reportParameters.AdvancedFiltersDto.FilterText ?? (object)DBNull.Value),
            new SqlParameter("@pageNumber", reportParameters.PageNumber),
            new SqlParameter("@pageSize", reportParameters.PageSize),
            new SqlParameter("@sortColumn", reportParameters.SortColumn),
            new SqlParameter("@sortDirection", reportParameters.SortType),
            new SqlParameter("@startDate", reportParameters.AdvancedFiltersDto.startDate ?? (object)DBNull.Value),
            new SqlParameter("@endDate", reportParameters.AdvancedFiltersDto.endDate ?? (object)DBNull.Value)

        };

            var rowsReturned = context.Database.SqlQuery<RequisitionPurchasingReportPageRow>("EXEC [dbo].[usp_PurchasingRequisitionsGet] @coids, @vendors," +
                "@buyers, @reqTypes, @filterText, @pageNumber, @pageSize, @sortColumn, @sortDirection, @startDate, @endDate",
                parameters.ToArray()
                ).ToList();

            Dictionary<int, PurchasingRequisitionDTO> reqsReturned = new Dictionary<int, PurchasingRequisitionDTO> ();
            long totalRows = 0;

            foreach (RequisitionPurchasingReportPageRow row in rowsReturned)
            {
                totalRows = row.TotalReqCount;

                PurchasingRequisitionItemDTO newReqItem = row.RequisitionItemId == null ? null : new PurchasingRequisitionItemDTO()
                {
                    Id = row.RequisitionItemId ?? 0,
                    RequisitionId = row.RequisitionItemRequisitionId,
                    ItemId = row.RequisitionItemNumber,
                    CreatedBy = row.RequisitionItemCreatedBy,
                    CreateDate = row.RequisitionItemCreateDate,
                    RequisitionItemStatusTypeId = row.RequisitionItemStatusTypeId ?? 0,
                    RequisitionItemStatusType = row.RequisitionItemStatusType,
                    ParentSystemId = row.RequisitionItemParentSystemId,
                    OriginalParentSystemId = row.RequisitionItemOriginalParentSystemId,
                    PONumber = row.RequisitionItemPONumber,
                    ParentRequisitionItemId = row.RequisitionItemParentItemId,
                    ParIdentifier = row.RequisitionItemParIdentifier == eproParIdentifier ? null : row.RequisitionItemParIdentifier,
                    Discount = row.Discount,
                    VendorId = row.VendorId ?? 0, 
                    UnitCost = row.UnitCost,
                    QuantityToOrder = row.QuantityToOrder ?? 0,
                    QuantityFulfilled = row.QuantityFulfilled,
                    IsFileItem = row.IsFileItem,
                    SmartItemNumber = row.SmartItemNumber,
                    GeneralLedgerCode = row.GeneralLedgerCode,
                    CatalogNumber = row.CatalogNumber,
                    SPRDetail = row.SprDetailsPartNumber == null ? null : new SPRDetail()
                    {
                        VendorId = row.SprDetailsVendorId ?? 0,
                        VendorName = row.SprDetailsVendorName,
                        PartNumber = row.SprDetailsPartNumber,
                        FileAttachments = row.SprDetailsFileAttachment == null ? null : new List<FileAttachment>() { new FileAttachment()
                        {
                        RequisitionId = row.RequisitionId,
                        FileName = row.SprDetailsFileName,
                        CreatedBy = row.SprDetailsFileAttachmentCreatedBy,
                        CreateDate = row.CreateDate
                        } }
                    },
                    VboHoldItemConversion = row.VboHoldItemConversionUnitCost == null ? null : new VboHoldItemConversion
                    {
                        UnitCost = (decimal)row.VboHoldItemConversionUnitCost
                    }
                };

                if (reqsReturned.ContainsKey(row.RequisitionId))
                {
                    reqsReturned[row.RequisitionId].RequisitionItems.Add(newReqItem);
                }
                else
                {
                    reqsReturned.Add(row.RequisitionId, new PurchasingRequisitionDTO
                    {
                        RequisitionId = row.RequisitionId,
                        RequisitionStatusTypeId = row.RequisitionStatusTypeId,
                        RequisitionStatus = row.RequisitionStatusType,
                        RequisitionTypeId = row.RequisitionTypeId,
                        RequisitionType = row.RequisitionType,
                        Comments = row.Comments,
                        CreatedBy = row.CreatedBy,
                        CreatedByFullName = row.CreatedByFullName,
                        CreateDate = row.CreateDate,
                        RequisitionItems = newReqItem == null ? null : new List<PurchasingRequisitionItemDTO>() { newReqItem },
                        LocationIdentifier = row.LocationIdentifier,
                        IsMobile = row.IsMobile,
                        IsVendor = row.IsVendor,
                        RequisitionSubmissionTypeId = row.RequisitionSubmissionTypeId,
                        RequisitionSubmissionType = row.RequisitionSubmissionType
                    });
                }
            }

            var groupedRequisitions = reqsReturned.Values
               .SelectMany(r => r.RequisitionItems, (r, ri) => new { Requisition = r, RequisitionItem = ri })
                 .GroupBy(x => new
                 {
                     x.Requisition.RequisitionId,
                     VendorId = (x.RequisitionItem.VendorId == 0 && x.RequisitionItem.SPRDetail?.VendorId != null)
                        ? x.RequisitionItem.SPRDetail.VendorId
                        : (x.RequisitionItem.VendorId != 0 ? x.RequisitionItem.VendorId : 0)
                 })
                 .Select(g => new
                 {
                     g.First().Requisition,
                     g.Key.RequisitionId,
                     g.Key.VendorId,
                     RequisitionItems = g.Select(x => new { RequisitionItemId = x.RequisitionItem.Id, VendorId = g.Key.VendorId }).ToList()
                 });

            var totalCount = groupedRequisitions.Count();

            var paginatedRequisitions = groupedRequisitions
            .Skip((reportParameters.PageNumber - 1) * reportParameters.PageSize)
            .Take(reportParameters.PageSize);

            var requisitionData = paginatedRequisitions
                            .Select(g => new PurchasingRequisitionDTO
                            {
                                VendorId = g.VendorId,
                                LocationIdentifier = g.Requisition.LocationIdentifier,
                                RequisitionId = g.RequisitionId,
                                RequisitionStatusTypeId = g.Requisition.RequisitionStatusTypeId,
                                RequisitionStatus = g.Requisition.RequisitionStatus,
                                RequisitionTypeId = g.Requisition.RequisitionTypeId,
                                RequisitionType = g.Requisition.RequisitionType,
                                ApprovalStep = g.Requisition.ApprovalStep,
                                Comments = g.Requisition.Comments,
                                ApprovedAmount = g.Requisition.ApprovedAmount,
                                WorkflowInstanceId = g.Requisition.WorkflowInstanceId,
                                CreatedBy = g.Requisition.CreatedBy,
                                CreatedByFullName = g.Requisition.CreatedByFullName,
                                CreateDate = g.Requisition.CreateDate,
                                CountryCode = g.Requisition.CountryCode,
                                RequisitionParClass = g.Requisition.RequisitionParClass,
                                IsVendor = g.Requisition.IsVendor,
                                IsMobile = g.Requisition.IsMobile,
                                RequisitionItems = g.Requisition.RequisitionItems
                                .Where(ri =>
                                 (ri.VendorId != 0 && ri.VendorId == g.VendorId) ||
                                (ri.VendorId == 0 && ri.SPRDetail?.VendorId == g.VendorId)
                                 ).ToList()
                            }).ToList();

            return new PurchasingRequisitionReportResultsDTO(requisitionData, totalCount);
        }

       
/// <summary>
/// Gets values to populate advanced filters for Purchasing
/// </summary>
/// <param name="filterList"></param>
/// <returns>lists of values for available vendors, buyers, and requisition types for a list of coids</returns>
public RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList)
        {
            var advancedFilters = new RequisitionPurchasingAdvancedFiltersDto();

            var result = (from r in context.Requisitions
                          join ri in context.RequisitionItems on r.RequisitionId equals ri.RequisitionId
                          join rt in context.RequisitionTypes on r.RequisitionTypeId equals rt.Id
                          join u in context.Users on r.CreatedBy equals u.AccountName
                          select new
                          {
                              r.CreatedBy,
                              ri.VendorId,
                              ri.VendorName,
                              r.LocationIdentifier,
                              u.AccountName,
                              u.FirstName,
                              u.LastName
                          })
            .Where(r => filterList.CoIds.Contains(r.LocationIdentifier.Substring(0,5)))
            .Select(r => new
            {

                BuyerId = r.CreatedBy,
                BuyerName = r.FirstName + " " + r.LastName,
                r.VendorId,
                r.VendorName
            });

            var reqTypeList = (from rt in context.RequisitionTypes
                               select new
                               {
                ReqTypeId=rt.Id,
                                   ReqTypeDescription = rt.Description
                               });

            advancedFilters.Buyers = result
            .GroupBy(r => r.BuyerId)
            .Select(g => g.FirstOrDefault())
            .Select(r => new BuyerModel { BuyerId= r.BuyerId, BuyerName = r.BuyerName })
            .ToList();

            advancedFilters.Buyers.ForEach(y => y.BuyerId = y.BuyerId.Split('/')[1]);

            advancedFilters.Vendors = result
            .Where(r => r.VendorId != 0)
            .Select(r => new VendorModel { VendorId = r.VendorId, VendorName = r.VendorName })
            .Distinct().ToList();

            advancedFilters.ReqTypes = reqTypeList
            .GroupBy(rt => rt.ReqTypeId)
            .Select(g => g.FirstOrDefault())
            .Select(rt => new ReqTypeModel { ReqTypeId = rt.ReqTypeId, ReqTypeDescription = rt.ReqTypeDescription })
            .ToList();

            return advancedFilters;
        }

        public List<RequisitionDTO> GetRequisitionAndItemsByPONumber(int poNumber, string coid)
        {
            var reqs = context.Requisitions.Where(r => r.LocationIdentifier.StartsWith(coid + "_"))
                            .Where(r => r.RequisitionItems.Any(ri => ri.PONumber == poNumber))
                            .AsEnumerable()
                            .Select(r => new RequisitionDTO(r))
                            .ToList();
            return reqs;
        }

    }
}