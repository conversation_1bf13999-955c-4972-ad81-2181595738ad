﻿using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.Utility.Domain;

namespace RequisitionServices.MMISServices.DTO
{
    public class LegacyRequisitionItemsModel
    {
        public Requisition MapToRequisition(int reqId, string coid, string countryCode)
        {
            var reqItems = new List<RequisitionItem>();
            var reqItemHeaderDetails = this.LegacyRequisitionItemModels.First();
            LegacyRequisitionItemModel capitatedMainItem = null;

            var requisition = new Requisition()
            {
                RequisitionId = reqId,
                RequisitionTypeId = RequisitionTypeMapper.GetRequisitionType(reqItemHeaderDetails.RequisitionType),
                LocationIdentifier = reqItemHeaderDetails.Coid.ToString().PadLeft(LocationMapper.GetCOIDCharacterLength(countryCode), '0') + "_" + reqItemHeaderDetails.Department,
                RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.Legacy,
                RequisitionParClass = reqItemHeaderDetails.Par,
                CreatedBy = reqItemHeaderDetails.UserId,
                CreateDate = reqItemHeaderDetails.CreateDate
            };

            //Capitated requisitions -- need to associate parent and children items using MainItemId (parent Id) field
            if (requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace || requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillOnly)
            {
                capitatedMainItem = this.LegacyRequisitionItemModels.First(x => x.IsCapitatedMainItem);
                reqItems.Add(capitatedMainItem.MapToRequisitionItem()); //Add main item FIRST in list
            }

            //Add all items or rest of Capitated children items
            foreach (var legacyReqItem in this.LegacyRequisitionItemModels)
            {
                var mappedItem = legacyReqItem.MapToRequisitionItem();
                if (capitatedMainItem == null)
                {
                    reqItems.Add(mappedItem);
                }
                else if (capitatedMainItem != null && !legacyReqItem.IsCapitatedMainItem)
                {
                    mappedItem.MainItemId = capitatedMainItem.ItemId;
                    reqItems.Add(mappedItem);
                }
            }

            requisition.RequisitionItems = reqItems;
            return requisition;
        }
        public List<LegacyRequisitionItemModel> LegacyRequisitionItemModels { get; set; }
    }
}
