﻿using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;

namespace RequisitionServices.MMISServices.Utilities
{
    public static class SmartAPIReqMapper
    {
        public static BillOnlyRequisitionModel MapToBillOnlyRequisitionModel(Requisition requisition, string requisitionerFullName, IEnumerable<ParItemDetails> items = null)
        {
            
            foreach(var reqItem in requisition.RequisitionItems)
            {
                if(reqItem.SPRDetail == null && reqItem.ParIdentifier != null && reqItem.ParIdentifier.ToLower() != requisition.RequisitionParClass.ToLower() && reqItem.Discount == null)
                reqItem.SPRDetail = CreateSPRDetailProxy(reqItem);
            }

            var billOnlyRequisitionModel = new BillOnlyRequisitionModel(requisition);

            requisition.RequisitionItems.Where(x => x.SPRDetail != null && x.SPRDetail.RequisitionItemId == 0).ToList().ForEach(y =>
            {
                y.SPRDetail = null;
            });

            billOnlyRequisitionModel.RequisitionItems.ForEach(ri => ri.RequisitionerName = requisitionerFullName);

            return billOnlyRequisitionModel;
        }
        

        private static SPRDetail CreateSPRDetailProxy(RequisitionItem item)
        {
            return new SPRDetail()
            {
                PartNumber = item.ReOrder,
                GeneralLedgerCode = item.GeneralLedgerCode,
                VendorId = item.VendorId,
                UOMCode = item.UOMCode,
                ItemDescription = item.ItemDescription,
                SPRTypeId = 0,
                AdditionalInformation = "",
                EstimatedPrice = item.UnitCost
            };
        }
    }
}
