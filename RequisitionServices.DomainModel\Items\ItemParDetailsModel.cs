﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Items;

namespace RequisitionServices.MMISServices.DTO
{
    public class ItemParDetailsModel
    {
        public int Coid { get; set; }
        public int Department { get; set; }
        public string ParClass { get; set; }
        public bool AreDetailsFromParentFile { get; set; }
        public bool AreDetailsFromContractFile { get; set; }

        // File Item details
        public int StandardItemNumber { get; set; }
        public int FacilityItemNumber { get; set; }
        public string ItemDescription { get; set; }
        public string ShortItemDescription { get; set; }
        public string LongItemDescription { get; set; }
        public string SupplierVendorName { get; set; }
        public int SupplierVendorNumber { get; set; }
        public string SupplierReorderNumber { get; set; }
        public string ManufacturingVendorName { get; set; }
        public int ManufacturingVendorNumber { get; set; }
        public string ManufacturingCatalogNumber { get; set; }
        public ItemType ItemType { get; set; }
        public bool IsStock { get; set; }
        public bool IsTempStock { get; set; }
        public DistributionPoint DistributionPoint { get; set; }
        public int QuantityInStockAtWarehouse { get; set; }
        public bool IsCapitated { get; set; }
        public bool IsHazardous { get; set; }
        public bool IsLatex { get; set; }
        public bool HasLabels { get; set; }
        public string TaxCode { get; set; }
        public int SubGeneralLedger { get; set; }
        public int LastPurchaseOrderNumber { get; set; }
        public int DistributionCoid { get; set; }
        public string PurchasingLocation { get; set; }
        public string DistributionLocation { get; set; }
        public decimal? NextPrice { get; set; }
        public decimal? LastPrice { get; set; }

        // Unit of measure details
        public string ListPurchaseUom { get; set; }
        public decimal ListPurchaseUomPrice { get; set; }
        public int ListPurchaseUomFactor { get; set; }
        public string ListIssueUom { get; set; }
        public decimal ListIssueUomPrice { get; set; }
        public string ContractPurchaseUom { get; set; }
        public decimal ContractPurchaseUomPrice { get; set; }
        public int ContractPurchaseUomFactor { get; set; }
        public string ContractIssueUom { get; set; }
        public decimal ContractIssueUomPrice { get; set; }
        public string FacilityPurchaseUom { get; set; }
        public decimal FacilityPurchaseUomPrice { get; set; }
        public int FacilityPurchaseUomFactor { get; set; }
        public string FacilityIssueUom { get; set; }
        public decimal FacilityIssueUomPrice { get; set; }
        public string OperatingRoomUom { get; set; }
        public decimal OperatingRoomUomPrice { get; set; }
        public int OperatingRoomUomFactor { get; set; }
        public List<AlternateUOMRecordModel> AlternateUoms { get; set; }
        public List<UnitOfMeasureModel> AvailableUoms { get; set; }

        // Patient charge details
        public string PatientChargeAlgorithmUom { get; set; }
        public decimal PatientChargeAlgorithmUomPrice { get; set; }
        public int PatientChargeAlgorithmUomFactor { get; set; }
        public bool IsChargeable { get; set; }
        public string ChargeCode { get; set; }
        public string ChargeCodeDescription { get; set; }
        public decimal InPatientChargeAmount { get; set; }
        public string InPatientChargeAlgorithm { get; set; }
        public long InPatientChargeAccount { get; set; }
        public decimal OutPatientChargeAmount { get; set; }
        public string OutPatientChargeAlgorithm { get; set; }
        public long OutPatientChargeAccount { get; set; }
        public string HealthcareCommonProcedureCodingSystemCode { get; set; }
        public int RevenueCode { get; set; }
        public int RevenueChargeDepartment { get; set; }

        // Contract details
        public int ContractNumber { get; set; }
        public decimal ContractPrice { get; set; }
        public int ContractTier { get; set; }
        public string TaxCategory { get; set; }
        public bool IsManualContractPrice { get; set; }
        public int ManualContractNumber { get; set; }
        public string ContractComplianceCode { get; set; }

        // PAR-Item details
        public string ParUom { get; set; }
        public decimal ParUomPrice { get; set; }
        public long ParExpenseGeneralLedger { get; set; }
        public int Min { get; set; }
        public int Max { get; set; }
        public string ParLocation { get; set; }
    }
}
