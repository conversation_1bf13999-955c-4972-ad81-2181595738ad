﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Users
{
    public class User
    {
        // Backing field for AccountName to ensure it is always lowercase
        private string _accountName;
        
        // Backing field for CreatedBy to ensure it is always lowercase
        private string _createdBy;
        
        public User() { }
        public User (User user, UserProfile profile)
        {
            Id = user.Id;
            AccountName = user.AccountName;
            CreatedBy = user.CreatedBy;
            CreateDate = user.CreateDate;
            FirstName = user.FirstName;
            LastName = user.FirstName;
            UserProfile = profile;
        }

        public int Id { get; set; }
        
        /// <summary>
        /// AccountName for User
        /// Converted to lowercase
        /// </summary>
        [Required]
        [StringLength(100)]
        public string AccountName
        {
            get => _accountName;
            set => _accountName = value.ToLower();
        }

        [StringLength(255)]
        public string FirstName { get; set; }

        [StringLength(255)]
        public string LastName { get; set; }
        
        /// <summary>
        /// Created<PERSON><PERSON> is the user who created the record
        /// Converted to lowercase
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CreatedBy 
        {
            get => _createdBy;
            set => _createdBy = value.ToLower();
        }
        
        public DateTime CreateDate { get; set; }

        [NotMapped]
        public UserProfile UserProfile { get; set;}

        [NotMapped]
        public UserSetupWorkflows UserSetupWorkflows { get; set; }
    }
}
