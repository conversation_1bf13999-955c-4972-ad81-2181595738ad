/****** Script for SelectTopNRows command from SSMS  ******/


-- ================================================
-- Template generated from Template Explorer using:
-- Create Procedure (New Menu).SQL
--
-- Use the Specify Values for Template Parameters 
-- command (Ctrl-Shift-M) to fill in the parameter 
-- values below.
--
-- This block of comments will not be included in
-- the definition of the procedure.
-- ================================================
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Dhruv>
-- Create date: <25 Jan 2025>
-- Description:	<Insert empty bin details>
-- =============================================
CREATE OR ALTER  PROCEDURE SaveEmptyBinDetail 
	-- Add the parameters for the stored procedure here
	@CartId Int
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;	
	INSERT INTO dbo.[EmptyBinHistory]
	(COID,
	DepartmentNumber,
	PARId,
	ItemNumber,
	SubmittedDateTime,
	[Username]) 
	SELECT C.Coid,
	C.DepartmentNumber,
	C.ParId,
	CI.ItemNumber,
	GETUTCDATE(),
	C.Username
	FROM CartItems CI INNER JOIN Carts C ON C.Id = CI.CartId 
	WHERE C.Id = @CartId AND CI.PARTypeId = 3 --ParTypeId =3 (emptyBin)
END
GO
