﻿using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Reflection;
using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.Repositories.Interfaces;

namespace RequisitionServices.Repositories
{
    public class FacilityWorkflowRepository : AbstractRepository, IFacilityWorkflowRepository
    {
        readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public FacilityWorkflowRepository(EProcurementContext context) : base(context) { }

        public IEnumerable<FacilityWorkflowStep> Get(string coid, WorkflowTypeEnum workflowType)
        {
            try
            {
                return context.FacilityWorkflowSteps
                    .Include(x => x.Approver)
                    .Include(x => x.Approver.User)
                    .Where(x => x.Coid == coid && x.WorkflowTypeId == (int) workflowType);
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                throw;
            }
        }

        public void Save(SaveFacilityWorkflowDTO request)
        {
            try
            {
                var currentWorkflow = Get(request.Workflow.Coid, (WorkflowTypeEnum) request.Workflow.WorkflowTypeId);

                var deleteSteps = currentWorkflow.Where(x => request.Workflow.Steps.All(y => y.Id != x.Id));
                context.FacilityWorkflowSteps.RemoveRange(deleteSteps);

                var updatedSteps = 
                    from originalStep in currentWorkflow
                    join newStep in request.Workflow.Steps on originalStep.Id equals newStep.Id
                    select new StepGroup
                    {
                        OriginalStep = originalStep,
                        NewStep = newStep
                    };
                foreach (var updateStep in updatedSteps)
                {
                    context.Entry(updateStep.OriginalStep).CurrentValues.SetValues(updateStep.NewStep);
                }

                var newSteps = request.Workflow.Steps.Where(x => x.Id == 0).ToList();
                newSteps.ForEach(x =>
                {
                    x.Approver = null;
                    x.WorkflowType = null;
                    x.CreatedBy = request.Username;
                    x.CreateDateUtc = DateTime.UtcNow;
                });
                context.FacilityWorkflowSteps.AddRange(newSteps);

                context.SaveChanges();
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                throw;
            }
        }

        public void InsertStep(FacilityWorkflowStep step)
        {
            context.FacilityWorkflowSteps.Add(step);

            context.SaveChanges();
        }

        public void DeleteDelegatedSteps(int delegatedByUserId)
        {
            var existingDelegatedSteps = context.FacilityWorkflowSteps.Where(x => x.DelegatedByUserId == delegatedByUserId);
            if (existingDelegatedSteps.Any())
            {
                context.FacilityWorkflowSteps.RemoveRange(existingDelegatedSteps);

                context.SaveChanges();
            }
        }

        public List<FacilityWorkflowStep> GetStepsByApproverUsername(string username)
        {
            var result = context.FacilityWorkflowSteps
                .Include(x => x.Approver)
                .Include(x => x.Approver.User)
                .Where(x => x.Approver.User.AccountName.ToLower() == username.ToLower());

            return result.ToList();
        }

        class StepGroup
        {
            public FacilityWorkflowStep OriginalStep { get; set; }
            public FacilityWorkflowStep NewStep { get; set; }
        }
    }
}
