﻿using System;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.PurchaseOrders
{
    public class POLineItem
    {
        public int LineNumber { get; set; }

        public int Department { get; set; }

        public long GLAccountNumber { get; set; }

        public int ItemNumber { get; set; }

        public int STDItemNumber { get; set; }

        public string ItemDescription { get; set; }

        public int QuantityOrdered { get; set; }

        public int QuantityReturned { get; set; }

        public int QuantityReceived { get; set; }

        public int QuantityPaid { get; set; }

        public decimal AmountPaid { get; set; }

        public string Category { get; set; }

        public string Generic { get; set; }

        public string PurchaseUOM { get; set; }

        public string IssueUOM { get; set; }

        public string ReorderNumber { get; set; }

        public int Factor { get; set; }

        public string ComplianceCode { get; set; }

        public decimal UnitCost { get; set; }

        public DateTime ItemReceivedDate { get; set; }

        public decimal TotalCost { get; set; }

        public int RequisitionNumber { get; set; }

        public string LineNotes { get; set; }

        public Boolean Tax { get; set; }

        public Boolean BackorderFlag { get; set; }

        public string BackorderComment { get; set; }

        public int RequisitionItemId { get; set; }

        public string BudgetNumber { get; set; }

        public string ParentSystemId { get; set; }

        public int RequisitionId { get; set; }

        public List<FileAttachment> FileAttachments { get; set; }

        public decimal TotalCreditExpected { get; set; }

        public int CreditExpected { get; set; }

        public int CreditReceived { get; set; }

        public string PARClass { get; set; }

        public string AcquisitionType { get; set; }
        public string EquipmentType { get; set; }
        public byte? LaborWarrantyMonths { get; set; }
        public byte? PartsWarrantyMonths { get; set; }

        public POLineItem()
        {
            FileAttachments = new List<FileAttachment>();
        }

    }
}