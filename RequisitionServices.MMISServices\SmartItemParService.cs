﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;
using log4net;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Utility.WebAPI;

namespace RequisitionServices.MMISServices
{
    public class SmartItemParService : ISmartItemParService
    {
        private ILog logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        const string getItemParDetails = "ItemPar/GetItemParDetails";

        private readonly string _endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IEnumerable<ItemParDetailsModel> GetItemParDetails(GetItemParDetailsRequest request)
        {
            var username = request.UserId;
            SmartInputValidator.CheckUserName(ref username);
            request.UserId = username;
            SmartInputValidator.CheckCountryCode(request.CountryCode);

            try
            {
                var models = ApiUtility.ExecuteApiPostWithContentTo<List<ItemParDetailsModel>>(_endpoint, getItemParDetails, null, request);

                if (models != null)
                {
                    foreach (var model in models)
                    {
                        SetAvailableUnitsOfMeasure(model, request.CountryCode);
                    }
                }

                return models;
            }
            catch (Exception ex)
            {
                logger.Error($"Error on SmartItemParService with {request.Coid}, {request.UserId}", ex);
                return new List<ItemParDetailsModel>();
            }
        }

        static void SetAvailableUnitsOfMeasure(ItemParDetailsModel model, string countryCode)
        {
            var uoms = new List<UnitOfMeasureModel>();

            void AddUom(string uom, int factor, decimal price, UnitOfMeasureType type)
            {
                if (!string.IsNullOrWhiteSpace(uom) && !uoms.Exists(u => string.Equals(u.Code, uom, StringComparison.InvariantCultureIgnoreCase)))
                {
                    uoms.Add(new UnitOfMeasureModel { Code = uom, Factor = factor, Price = price, Type = type });
                }
            }

            switch (countryCode)
            {
                case "U":
                    AddUom(model.FacilityIssueUom, 1, model.FacilityIssueUomPrice, UnitOfMeasureType.IUOM);
                    AddUom(model.FacilityPurchaseUom, model.FacilityPurchaseUomFactor, model.FacilityPurchaseUomPrice, UnitOfMeasureType.PUOM);
                    break;
                case "I":
                    AddUom(model.ListIssueUom, 1, model.ListIssueUomPrice, UnitOfMeasureType.IUOM);
                    AddUom(model.ListPurchaseUom, model.ListPurchaseUomFactor, model.ListPurchaseUomPrice, UnitOfMeasureType.PUOM);
                    break;
                default:
                    throw new NotSupportedException($"Country Code {countryCode} is not supported");
            }

            AddUom(model.OperatingRoomUom, model.OperatingRoomUomFactor, model.OperatingRoomUomPrice, UnitOfMeasureType.ORUOM);
            AddUom(model.PatientChargeAlgorithmUom, model.PatientChargeAlgorithmUomFactor, model.PatientChargeAlgorithmUomPrice, UnitOfMeasureType.PCAUOM);

            if (null != model.AlternateUoms)
            {
                foreach (var alternateUom in model.AlternateUoms)
                {
                    AddUom(alternateUom.AUOM, alternateUom.Factor, alternateUom.Price, UnitOfMeasureType.AUOM);
                }
            }

            model.AvailableUoms = uoms;
        }
    }
}
