﻿using System;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionStatusHistoryDTO
    {
        public RequisitionStatusHistoryDTO() { }
        public RequisitionStatusHistoryDTO(RequisitionStatusHistory requisitionStatusHistory) 
        {
            this.Id = requisitionStatusHistory.Id;
            this.RequisitionId = requisitionStatusHistory.RequisitionId;
            this.RequisitionStatusType = requisitionStatusHistory.RequisitionStatusTypeId;
            this.Comments = requisitionStatusHistory.Comments;
            this.ApprovedAmount = requisitionStatusHistory.ApprovedAmount;
            this.ApprovalStep = requisitionStatusHistory.ApprovalStep;
            this.CreatedBy = requisitionStatusHistory.CreatedBy;
            this.CreateDate = requisitionStatusHistory.CreateDate;
            this.DelegatedByApproverId =  requisitionStatusHistory.DelegatedByApproverId;
            this.IsCERReviewer = requisitionStatusHistory.IsCERReviewer;
        }

        public int Id { get; set; }

        public int RequisitionId { get; set; }
        
        public int RequisitionStatusType { get; set; }

        public string Comments { get; set; }

        public decimal? ApprovedAmount { get; set; }

        public int? ApprovalStep { get; set; }

        public string CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }       

        public int? DelegatedByApproverId { get; set; }

        public bool IsCERReviewer { get; set; }
    }
}
