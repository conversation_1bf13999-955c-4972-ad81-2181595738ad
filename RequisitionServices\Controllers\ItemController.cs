﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.DTO;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;
using Microsoft.Web.Http;

namespace RequisitionServices.Controllers
{
    /// <summary>
    /// Item service
    /// </summary>
    [ApiVersion("1.0", Deprecated = true)]
    [ApiVersion("2.0")]
    public class ItemController : ApiController
    {
        private IItemService itemService;

        public ItemController(IItemService itemSvc)
        {
            this.itemService = itemSvc;
        }

        /// <summary>
        /// Get an item by Id
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="itemId"></param>
        /// <param name="parId"></param>
        /// <returns></returns>
        [HttpGet]
        public Item GetItemByParId(string userName, string COID, string itemId, string parId)
        {
            return itemService.GetItemByParId(userName, COID, itemId, parId);
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="itemId"></param>
        /// <returns></returns>
        [HttpGet]
        public Item GetItem(string userName, string COID, string itemId)
        {
            return itemService.GetItem(userName, COID, itemId);
        }

        /// <summary>
        /// Look up one Item with available pars and corrected QIS and UOM
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <param name="itemPar"></param>
        /// <returns></returns>
        [HttpPost, MapToApiVersion("1.0"), MapToApiVersion("2.0")]
        [Route("Item/GetItemWithDetails")]
        [Route("v{version:apiVersion}/Item/GetItemWithDetails")]
        public ItemDetailsDTO GetItemWithDetails(string userName, string COID, int departmentId, ItemParDTO itemPar)
        {
            return itemService.GetItemsWithDetails(userName, COID, departmentId, new List<ItemParDTO>() { itemPar }, Request.GetRequestedApiVersion()).FirstOrDefault();
        }

        /// <summary>
        /// look up multiple items with available pars and corrected QIS and UOM
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <param name="itemPars"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<ItemDetailsDTO> GetItemsWithDetails(string userName, string COID, int departmentId, List<ItemParDTO> itemPars)
        {
            return itemService.GetItemsWithDetails(userName, COID, departmentId, itemPars);
        }

        /// <summary>
        /// Get the available list of delivery methods
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<DeliveryMethodType> GetDeliveryMethods()
        {
            return itemService.GetDeliveryMethods();
        }

        [HttpGet]
        public IEnumerable<UnitOfMeasureModel> GetUomsForItem(string username, string coid, string countryCode, int itemNumber)
        {
            return itemService.GetUomsForItem(username, coid, countryCode, itemNumber);
        }
        /// <summary>
        /// Get Item Price  details for the vendor Id or re-order number
        /// </summary>
        /// <param name="username">User Id</param>
        /// <param name="coid"> Facility number</param>
        /// <param name="reordernumber">re-order|vendor part number</param>
        /// <param name="vendornumber">Vendor Id</param>
        /// <returns></returns>
        [HttpGet]
        public ItemPriceDetails GetItemPrice(string username,string coid, string reordernumber, string vendornumber)
        {
            return itemService.GetItemPrice(username, coid, reordernumber, vendornumber);
        }
    }
}
