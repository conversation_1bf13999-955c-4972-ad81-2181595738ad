﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.DomainModel.Audit;
using RequisitionServices.DomainModel.SystemNotifications;
using System;
using System.Data.Entity;
using RequisitionServices.DomainModel.Contracts;
using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Vira;
using RequisitionServices.DomainModel.UserAlertMessage;
using RequisitionServices.DomainModel;
using RequisitionServices.DomainModel.VPro;

namespace RequisitionServices.Database
{
    public class EProcurementContext : DbContext
    {
        public DbSet<Requisition> Requisitions { get; set; }
        public virtual DbSet<RequisitionItem> RequisitionItems { get; set; }
        public DbSet<RequisitionItemStatusHistory> RequisitionItemStatusHistories { get; set; }
        public DbSet<RequisitionItemStatusType> RequisitionItemStatusTypes { get; set; }
        public DbSet<RequisitionSubmissionTypes> RequisitionSubmissionTypes { get; set; }
        public DbSet<RequisitionStatusHistory> RequisitionStatusHistories { get; set; }
        public DbSet<RequisitionStatusType> RequisitionStatusTypes { get; set; }
        public DbSet<RequisitionType> RequisitionTypes { get; set; }
        public DbSet<ClinicalUseDetail> ClinicalUseDetails { get; set; }
        public DbSet<DeliveryMethodType> DeliveryMethodTypes { get; set; }
        public DbSet<SPRDetail> SPRDetails { get; set; }
        public DbSet<Approver> Approvers { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<UserWorkflowStep> UserWorkflowSteps { get; set; }
        public DbSet<FacilityNotification> FacilityNotifications { get; set; }
        public DbSet<FacilityNotificationType> FacilityNotificationTypes { get; set; }
        public DbSet<SPRType> SPRTypes { get; set; }
        public DbSet<WorkflowType> WorkflowTypes { get; set; }
        public DbSet<PunchOutVendor> PunchOutVendors { get; set; }
        public DbSet<AdhocReview> AdhocReviews { get; set; }
        public DbSet<FileAttachment> FileAttachments { get; set; }
        public DbSet<AuditEvents> AuditEvents { get; set; }
        public DbSet<AuditEventTypes> AuditEventTypes { get; set; }
        public DbSet<SystemNotificationAuthorizedUser> SystemNotificationAuthorizedUsers { get; set; }
        public DbSet<SystemNotification> SystemNotifications { get; set; }
        public DbSet<Personalization> Personalizations { get; set; }
        public DbSet<Contract> Contracts { get; set; }
        public DbSet<ItemInventory> ItemInventories { get; set; }
        public DbSet<BulkApproverJobTracker> BulkApproverJobTrackers { get; set; }
        public DbSet<Cart> Carts { get; set; }
        public DbSet<CartItem> CartItems { get; set; }
        public DbSet<Comment> Comments { get; set; }
        public DbSet<UnreadComment> UnreadComments { get; set; }
        public DbSet<FacilityWorkflowStep> FacilityWorkflowSteps { get; set; }
        public DbSet<VboHoldItemConversion> VboHoldItemConversions { get; set; }
        public DbSet<DigitalSignOffUser> DigitalSignoffUsers { get; set; }
        public DbSet<RequisitionDigitalSignOff> RequisitionsDigitalSignoff{ get; set; }
        public DbSet<ViraItemStatus> ViraItemStatus { get; set; }
        public DbSet<UserAlertMessage> UserAlertMessages { get; set; }
        public DbSet<UserAlertMessageType> UserAlertMessageType { get; set; }
        public DbSet<RequisitionVProBadgeLog> RequisitionVProBadgeLogs { get; set; }
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            modelBuilder.Properties<string>().Configure(c => c.HasColumnType("varchar"));
            modelBuilder.Properties<string>().Configure(c => c.HasMaxLength(255));

            modelBuilder.Entity<ClinicalUseDetail>().Property(x => x.UpchargeCost).HasPrecision(11, 3);
            modelBuilder.Entity<SPRDetail>().Property(x => x.EstimatedPrice).HasPrecision(11, 3);
            modelBuilder.Entity<SPRDetail>().Property(x => x.TradeInValue).HasPrecision(16, 3);
            modelBuilder.Entity<Approver>().Property(x => x.MaxApprovalAmount).HasPrecision(16, 3);

            modelBuilder.Entity<Requisition>().Property(x => x.ApprovedAmount).HasPrecision(16, 3);
            modelBuilder.Entity<RequisitionStatusHistory>().Property(x => x.ApprovedAmount).HasPrecision(16, 3);
            modelBuilder.Entity<BulkApproverJobTracker>().Property(e => e.JsonRequest).HasMaxLength(Int32.MaxValue);
            modelBuilder.Entity<BulkApproverJobTracker>().Property(e => e.ErrorMessage).HasMaxLength(Int32.MaxValue);
        }
    }
}