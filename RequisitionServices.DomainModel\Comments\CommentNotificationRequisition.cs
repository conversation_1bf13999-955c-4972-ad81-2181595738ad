﻿using System;

namespace RequisitionServices.DomainModel.Comments
{
    public class CommentNotificationRequisition
    {
        public int RequisitionId { get; set; }
        public int RequisitionStatusTypeId { get; set; }
        public int RequisitionTypeId { get; set; }
        public string LocationIdentifier { get; set; }
        public string Comments { get; set; }
        public string RequisitionerName { get; set; }
        public DateTime CreateDate { get; set; }
        public string CountryCode { get; set; }
        public bool IsMobile { get; set; }
        public bool IsVendor { get; set; }
        public bool HasSPRItems { get; set; }
        public bool HasFileAttachment { get; set; }
        public int NewCommentCount { get; set; }
    }
}
