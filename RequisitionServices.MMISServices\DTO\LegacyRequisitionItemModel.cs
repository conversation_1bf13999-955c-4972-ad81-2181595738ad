﻿using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Utility.Domain;
using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class LegacyRequisitionItemModel
    {
        public RequisitionItem MapToRequisitionItem()
        {
            var reqItem = new RequisitionItem()
            {
                ItemId = this.ItemId.ToString(),
                ReOrder = this.ReorderNumber,
                CatalogNumber = this.CatalogNumber,
                ItemDescription = this.Description,
                PONumber = this.PurchaseOrderNumber,
                QuantityToOrder = this.Quantity,
                UOMCode = this.UOM,
                VendorId = this.VendorId,
                VendorName = this.VendorName,
                UnitCost = this.UnitPrice,
                GeneralLedgerCode = this.GLAccount.ToString(),
                StockIndicator = this.Stock, 
                MinStock = this.Min,
                MaxStock = this.Max,
                QuantityFulfilled = this.QuantityRecieved,
                RequisitionScheduledDate = this.ScheduleDate,
                ParIdentifier = this.Par,
                RequisitionItemStatusTypeId = RequisitionItemStatusTypeMapper.GetRequisitionItemStatusType(this.RequisitionStatus, this.Quantity, this.QuantityRecieved)
            };

            return reqItem;
        }

        //Header Level -- transform this in req API
        public int Coid { get; set; }
        public int Department { get; set; }
        public string Par { get; set; }
        public string UserId { get; set; }
        public DateTime CreateDate { get; set; }

        //Item Level
        public int ItemId { get; set; }
        public string ReorderNumber { get; set; }
        public string CatalogNumber { get; set; }
        public string Description { get; set; }
        public string RequisitionStatus { get; set; } //Item status
        public int PurchaseOrderNumber { get; set; }
        public int Quantity { get; set; }
        public string UOM { get; set; }
        public int VendorId { get; set; }
        public string RequisitionType { get; set; }
        public string VendorName { get; set; }
        public decimal UnitPrice { get; set; }
        public int GLAccount { get; set; }
        public string Location { get; set; }
        public bool Stock { get; set; }
        public int Min { get; set; }
        public int Max { get; set; }
        public int QuantityRecieved { get; set; }
        public DateTime? ScheduleDate { get; set; }
        public bool IsCapitatedMainItem { get; set; }
        public string RejectionComments { get; set; }
    }
}
