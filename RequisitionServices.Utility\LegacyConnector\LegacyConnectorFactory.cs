﻿using log4net;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.Utility.LegacyConnector
{
    /// <summary>
    /// <Userstory>US119491</Userstory>
    /// <para>Class that has methods to return HTTP clients</para>
    /// </summary>
    internal static class LegacyConnectorFactory
    {
        private static HttpClient _client { get; set; }
        private static HttpClient _tokenClient { get; set; }
        private static HttpWebRequest _httpWebRequestClient { get; set; }

        private static readonly ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);

        /// <summary>
        /// Legacy connector client
        /// </summary>
        /// <param name="legacyConnectorUrl">URL of API</param>
        /// <returns>Legacy connector HTTP client</returns>
        internal static HttpClient GetLegacyConnectorClient(string legacyConnectorUrl)
        {
            if (_client != null) return _client;

            var handler = new WebRequestHandler();

            Log.Debug($"Creating new HTTPClient for {legacyConnectorUrl}");
            _client = new HttpClient(handler);
            _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            return _client;
        }

        /// <summary>
        /// Legacy connector token client
        /// </summary>
        /// <param name="legacyConnectorUrl">URL of API</param>
        /// <returns>Legacy connector token HTTP client</returns>
        internal static HttpClient GetLegacyConnectorTokenClient(string legacyConnectorUrl)
        {
            if (_tokenClient != null) return _tokenClient;

            var handler = new WebRequestHandler();

            Log.Debug($"Creating new HTTPClient for {legacyConnectorUrl}");
            _tokenClient = new HttpClient(handler);
            _tokenClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            return _tokenClient;
        }

    }
}

