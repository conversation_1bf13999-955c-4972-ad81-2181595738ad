USE [eProcurementQA]
GO

SET ANSI_NULLS ON
GO

-- Item RFID Table
CREATE TABLE [eProcurementQA].[dbo].[Item_RFID](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[COID] [nvarchar](5) NOT NULL,
	[DepartmentN<PERSON>ber] [int] NOT NULL,
	[PARClass] [nvarchar](3) NOT NULL,
	[ItemNumber] [int] NOT NULL,
	[QuantityOnHand] [int] NOT NULL,
	[UOM] [nvarchar](5) NOT NULL,
	[ScannedUtc] [datetime2] NOT NULL,
 CONSTRAINT [PK__Item_RFID] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

-------------------------------------------------------------------------------
-- Item RFID Detail Table
CREATE TABLE [eProcurementQA].[dbo].[Item_RFID_Detail](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[ItemRFIDId] [bigint] NOT NULL,
	[Tag] [nvarchar](20) NOT NULL,
	[Serial] [nvarchar](50) NOT NULL,
	[LotNumber] [int] NOT NULL,
	[ExpirationDate] [datetime2](7) NOT NULL,
 CONSTRAINT [PK__Item_RFID_Detail] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO


--------------------------------------------------------------------------------
-- Indexing Item RFID

CREATE NONCLUSTERED INDEX [FK__Item_RFID__COID_DepartmentNumber_PARClass_ScannedUtc] ON [eProcurementQA].[dbo].[Item_RFID]
(
	[COID] ASC,
	[DepartmentNumber] ASC,
	[PARClass] ASC,
	[ScannedUtc] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IX__Item_RFID_Detail__Item] ON [eProcurementQA].[dbo].[Item_RFID]
(
	[ItemNumber] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO

----------------------------------------------------------------------------------
-- Indexing Item RFID Detail
CREATE NONCLUSTERED INDEX [IX__Item_RFID_Detail__Tag] ON [eProcurementQA].[dbo].[Item_RFID_Detail]
(
	[Tag] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IX__Item_RFID_Detail__Serial] ON [eProcurementQA].[dbo].[Item_RFID_Detail]
(
	[Serial] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IX__Item_RFID_Detail__LotNumber] ON [eProcurementQA].[dbo].[Item_RFID_Detail]
(
	[LotNumber] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
CREATE NONCLUSTERED INDEX [IX__Item_RFID_Detail__ExpirationDate] ON [eProcurementQA].[dbo].[Item_RFID_Detail]
(
	[ExpirationDate] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
ALTER TABLE [eProcurementQA].[dbo].[Item_RFID_Detail]  WITH CHECK ADD  CONSTRAINT [FK__Item_RFID_Detail__Item_RFID] FOREIGN KEY([ItemRFIDId])
REFERENCES [eProcurementQA].[dbo].[Item_RFID] ([Id])
GO
ALTER TABLE [eProcurementQA].[dbo].[Item_RFID_Detail] CHECK CONSTRAINT [FK__Item_RFID_Detail__Item_RFID]
GO

----------------------------------------------------------------------------------
-- CART TYPE Table
CREATE TABLE [eProcurementQA].[dbo].[CartType](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[Name] [varchar](20) NOT NULL,
 CONSTRAINT [PK_CartType] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, FILLFACTOR = 95) ON [PRIMARY]
) ON [PRIMARY]
GO

INSERT INTO [eProcurementQA].[dbo].[CartType]
(
    [Name]
)
VALUES
    ('Procurement'),
    ('POU'),
    ('RFID')
GO

----------------------------------------------------------------------------------
-- Alter CART Table
ALTER TABLE [eProcurementQA].[dbo].[Carts]
    ADD [CartTypeId] INTEGER,
    FOREIGN KEY([CartTypeId]) REFERENCES [eProcurementQA].[dbo].[CartType]([Id])
GO

DECLARE @ProcurementCartTypeId INT
SELECT @ProcurementCartTypeId = [Id] FROM  [eProcurementQA].[dbo].[CartType] WHERE [Name] = 'Procurement'

DECLARE @POUCartTypeId INT
SELECT @POUCartTypeId = [Id] FROM  [eProcurementQA].[dbo].[CartType] WHERE [Name] = 'POU'

DECLARE @RFIDCartTypeId INT
SELECT @RFIDCartTypeId = [Id] FROM  [eProcurementQA].[dbo].[CartType] WHERE [Name] = 'RFID'

UPDATE [eProcurementQA].[dbo].[Carts]
SET [CartTypeId] = @ProcurementCartTypeId
WHERE [IsPOU] = 0

UPDATE [eProcurementQA].[dbo].[Carts]
SET [CartTypeId] = @POUCartTypeId
WHERE [IsPOU] = 1
GO


----------------------------------------------------------------------------------
--Drop IsPOU coulmn constraints
ALTER TABLE [eProcurementQA].[dbo].[Carts] DROP CONSTRAINT DF__Carts__IsPOU__4341E1B1

-- Drop IsPOU Column
ALTER TABLE [eProcurementQA].[dbo].[Carts] DROP COLUMN [IsPOU]
GO


----------------------------------------------------------------------------------
-- Create Stored Procedure CreateRFIDCart
CREATE OR ALTER PROCEDURE [dbo].[CreateRFIDCart]
    @COID [nvarchar](5),
    @DepartmentNumber [int],
    @PARClass [nvarchar](3),
	@UserName [nvarchar](20)
AS
BEGIN
    DECLARE @MostRecentScannedUtc DATETIME2 = NULL
    DECLARE @CartId BIGINT = 0

    -- GET MOST RECENT RFID SCAN
    SELECT 
        @MostRecentScannedUtc = MAX(ScannedUtc)
    FROM 
        [eProcurementQA].[dbo].[Item_RFID]
    WHERE 
        [COID] = @COID 
        AND [DepartmentNumber] = @DepartmentNumber 
        AND [ParClass] = @ParClass
    GROUP BY
        [COID]
        ,[DepartmentNumber]
        ,[ParClass]

    IF (@MostRecentScannedUtc IS NOT NULL)
    BEGIN
        DECLARE @RFIDCartTypeId INT
        SELECT @RFIDCartTypeId = [Id] FROM  [eProcurementQA].[dbo].[CartType] WHERE [Name] = 'RFID'
        
        -- REMOVE EXISTING CART
        SELECT
            @CartId = [Id]
        FROM
            [eProcurementQA].[dbo].[Carts]
        WHERE
            [Coid] = @COID
            AND [DepartmentNumber] = @DepartmentNumber
            AND [ParId] = @PARClass
            AND [CartTypeId] = @RFIDCartTypeId

        IF (@CartId > 0)
        BEGIN
            DELETE
                [eProcurementQA].[dbo].[CartItems]
            WHERE
                [CartId] = @CartId

            DELETE
                [eProcurementQA].[dbo].[Carts]
            WHERE
                [Id] = @CartId
        END

        INSERT INTO [eProcurementQA].[dbo].[Carts]
            (
                [Coid]
                ,[DepartmentNumber]
                ,[ParId]
                ,[Username]
                ,[CreatedUtc]
                ,[LastUpdatedUtc]
                ,[CartTypeId]
            )
        SELECT Top 1
            [COID],
            [DepartmentNumber],
            [PARClass],
            @UserName,
            GETUTCDATE(),
            GETUTCDATE(),
            @RFIDCartTypeId
        FROM 
            [eProcurementQA].[dbo].[Item_RFID]
        WHERE
            [COID] = @COID 
            AND [DepartmentNumber] = @DepartmentNumber 
            AND [ParClass] = @ParClass
            AND [ScannedUtc] = @MostRecentScannedUtc

        SELECT @CartId = SCOPE_IDENTITY()

        DECLARE @PARTypeId INT

        SELECT 
            @PARTypeId = [Id]
        FROM 
            [eProcurementQA].[dbo].[PARType] 
        WHERE 
            [Name] = 'Default'

        INSERT INTO [eProcurementQA].[dbo].[CartItems]
            (
                [CartId]
                ,[ItemNumber]
                ,[Quantity]
                ,[LastUpdatedUtc]
                ,[PARTypeId]
            )
        SELECT
            @CartId,
            [ItemNumber],
            [QuantityOnHand],
            GETUTCDATE(),
            @PARTypeId
        FROM 
            [eProcurementQA].[dbo].[Item_RFID]
        WHERE
            [COID] = @COID 
            AND [DepartmentNumber] = @DepartmentNumber 
            AND [ParClass] = @ParClass
            AND [ScannedUtc] = @MostRecentScannedUtc

    END
       
     SELECT
        @MostRecentScannedUtc AS MostRecentScannedUtc,
        @CartId AS CartId
END
GO