﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Utility.Domain;
using System;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class RequisitionRecordModel
    {
        public Requisition MapToRequisition()
        {
            if(this.ReqHeader != null)
            {
                var requisition = new Requisition() 
                    {
                        LocationIdentifier = this.ReqHeader.CoidNumber.ToString() + "_" + this.ReqHeader.Department.ToString(),
                        CreateDate = this.ReqHeader.ReqDate
                    };

                if(this.ReqDetails != null) 
                {
                    requisition.RequisitionItems = new List<RequisitionItem>();
                    foreach(var reqDetail in this.ReqDetails)
                    {
                        requisition.CreatedBy = reqDetail.ReqUserId;

                        var reqItem = new RequisitionItem()
                        {
                            Id = reqDetail.Id,
                            ItemId = reqDetail.ItemNumber.ToString(),
                            ParIdentifier = this.ReqHeader.HClass,
                            QuantityToOrder = reqDetail.ItemQuantity,
                            QuantityFulfilled = reqDetail.IssuedQuantity,
                            CreateDate = this.ReqHeader.ReqDate,
                            CreatedBy = reqDetail.ReqUserId,
                            ParentSystemId = reqDetail.ReqNumber.ToString(),
                            PONumber = reqDetail.PurchaseOrderNumber,
                            IsStatusChange = reqDetail.IsStatusChange, //TODO: [DEPRECATED] Remove
                            OriginalParentSystemId = reqDetail.OriginalReqId == null ? null : reqDetail.OriginalReqId.ToString(),
                            RequisitionItemStatusTypeId = RequisitionItemStatusTypeMapper.GetRequisitionItemStatusType(reqDetail.ItemStatus, reqDetail.ItemQuantity, reqDetail.IssuedQuantity),
                            RequisitionScheduledDate = reqDetail.ReqScheduledDate,
                            RequisitionItemAutoSubFlag = reqDetail.SubItemFlag
                        };

                        reqItem.RequisitionScheduledDate = reqItem.RequisitionItemStatusTypeId == (int) RequisitionItemStatusTypeEnum.Scheduled ? reqDetail.ReqScheduledDate : (DateTime?) null;
                        requisition.RequisitionItems.Add(reqItem);
                    }
                }

                return requisition;
            }
            else
            {
                return null;
            }
        }

        private RequisitionItemStatusTypeEnum GetPartialFillRequisitionStatus(int orderQuantity, int issuedQuantity, RequisitionItemStatusTypeEnum currentStatus)
        {
            if(issuedQuantity >= orderQuantity)
            {
                return currentStatus;
            }

            return RequisitionItemStatusTypeEnum.NoPartialFill;
        }

        public RequisitionHeaderModel ReqHeader { get; set; }

        public List<RequisitionDetailModel> ReqDetails { get; set; }
    }
}
