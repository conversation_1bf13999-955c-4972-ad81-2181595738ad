USE [eProcurement]
GO

/****** Object:  View [dbo].[edwRequisitionItems]    Update Date: 7/3/2024 9:04:00 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


ALTER VIEW [dbo].[edwRequisitionItems]
AS
SELECT ReqItems.[Id]
      ,ReqItems.[RequisitionId]
	    ,SUBSTRING(Req.[LocationIdentifier], 0, (CHARINDEX('_', Req.[LocationIdentifier]))) AS 'COID' 
      ,ReqItems.[ItemId]
      ,ReqItems.[ParIdentifier]
      ,ReqItems.[RequisitionItemStatusTypeId]
      ,ReqItems.[QuantityToOrder]
      ,ReqItems.[IsRushOrder]
      ,ReqItems.[CreatedBy]
      ,ReqItems.[CreateDate]
      ,ReqItems.[ParentSystemId]
      ,ReqItems.[MainItemId]
      ,ReqItems.[IsFileItem]
      ,ReqItems.[FileItemHasChanged]
      ,ReqItems.[QuantityFulfilled]
      ,ReqItems.[OriginalParentSystemId]
      ,ReqItems.[RequisitionScheduledDate]
      ,ReqItems.[PONumber]
      ,ReqItems.[SmartItemNumber]
      ,ReqItems.[ReOrder]
      ,ReqItems.[PARLocation]
      ,ReqItems.[ItemDescription]
      ,ReqItems.[VendorId]
      ,ReqItems.[VendorName]
      ,ReqItems.[GeneralLedgerCode]
      ,ReqItems.[StockIndicator]
      ,ReqItems.[UOMCode]
      ,ReqItems.[UnitCost]
      ,ReqItems.[TotalCost]
      ,ReqItems.[MinStock]
      ,ReqItems.[MaxStock]
      ,ReqItems.[CatalogNumber]
      ,ReqItems.[Discount]
	  ,CASE WHEN ReqItems.[Discount] IS NULL THEN 0
		ELSE 1 END AS 'Waste'
      ,ReqItems.[ParentRequisitionItemId]
	   ,ReqItems.[PartsWarrantyMonths]
		,ReqItems.[LaborWarrantyMonths]
		,RISH.[LastUpdated]
  FROM [dbo].[RequisitionItems] ReqItems
  INNER JOIN [dbo].[Requisitions] Req ON ReqItems.RequisitionId=Req.RequisitionId
  JOIN
  (
         SELECT RIH.RequisitionItemID, 
                MAX(RIH.[CreateDate]) AS LastUpdated
         FROM [dbo].[RequisitionItemStatusHistories] RIH
         GROUP BY RIH.RequisitionItemID
     ) AS RISH ON RISH.RequisitionItemID = ReqItems.Id;
GO


