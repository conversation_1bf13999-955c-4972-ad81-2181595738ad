﻿using System;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.Tests.Helper;
using RequisitionServices.AppInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;


namespace RequisitionServices.Tests.AppInsights
{
    [TestClass]
    public class RequestFilteringTest
    {
        private static Mock<ITelemetryProcessor> mockTelemetryProcessor;
        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            mockTelemetryProcessor = new Mock<ITelemetryProcessor>();
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            mockTelemetryProcessor = null;
        }

        [TestMethod]
        public void TestProcessItemRequestTelemetry()
        {
            RequestFilter rf = new RequestFilter(mockTelemetryProcessor.Object);

            AppInsightsTestData.APIList.ForEach(api => {
                var requestTelemetry = new RequestTelemetry { Url = new Uri(api) };
                rf.ProcessItemRequest(ref requestTelemetry);
                var updatedRequestTelemetryURL = requestTelemetry.Url.OriginalString;
                if (updatedRequestTelemetryURL.Contains("?"))
                {
                    Assert.Fail("Query string not updated for URL: " + updatedRequestTelemetryURL);
                }
            });
        }

        [TestMethod]
        public void TestProcessItemDependencyTelemetry()
        {
            RequestFilter rf = new RequestFilter(mockTelemetryProcessor.Object);

            AppInsightsTestData.APIList.ForEach(api => {
                var dependencyTelemetry = new DependencyTelemetry { Data = api };
                rf.ProcessItemRequest(ref dependencyTelemetry);
                var updatedRequestTelemetryURL = dependencyTelemetry.Data;
                if (updatedRequestTelemetryURL.Contains("?"))
                {
                    Assert.Fail("Query string not updated for URL: " + updatedRequestTelemetryURL);
                }
            });
        }
    }
}
