﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartItemService
    {
        Item GetItemByItemId(string userName, string coid, string itemNumber);

        IEnumerable<Item> GetItems(string userName, string coid, List<ItemParDTO> itemPars);
    }
}
