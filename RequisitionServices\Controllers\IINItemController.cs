﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainServices.Interface;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class IINItemController : ApiController
    {
        private IIINItemService IINItemService;

        public IINItemController(IIINItemService IINItemSvc)
        {
            this.IINItemService = IINItemSvc;
        }

        /// <summary>
        /// Get an IIN Item by Id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="COID"></param>
        /// <param name="IINitemNumber"></param>
        /// <returns></returns>
        [HttpGet]
        public IINItemRecordModel GetIINItemById(string userId, string COID, string IINitemNumber)
        {
            return IINItemService.GetIINItemById(userId, COID, IINitemNumber);
        }
    }
}