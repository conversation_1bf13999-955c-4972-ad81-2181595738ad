﻿using RequisitionServices.DomainModel.UserAlertMessage;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IUserAlertMessageRepository
    {
        /// <summary>
        /// Retrieves all user alert messages.
        /// </summary>
        /// <returns>A collection of user alert messages.</returns>
        Task<List<UserAlertMessage>> GetAllMessages();

        /// <summary>
        /// Retrieves a user alert message by its ID.
        /// </summary>
        /// <param name="messageId">The ID of the message to retrieve.</param>
        /// <returns>The user alert message with the specified ID.</returns>
        Task<UserAlertMessage> GetMessageById(int messageId);

        /// <summary>
        /// Creates a new user alert message.
        /// </summary>
        /// <param name="messageRequest">The request object containing the details of the message to create.</param>
        /// <returns>The created user alert message.</returns>
        Task<UserAlertMessage> CreateMessage(UserAlertMessageRequest messageRequest);

        /// <summary>
        /// Updates an existing user alert message.
        /// </summary>
        /// <param name="messageRequest">The request object containing the updated details of the message.</param>
        /// <returns>A UserAlertMessage indicating the result of the update operation.</returns>
        Task<UserAlertMessage> UpdateMessage(UserAlertMessageRequest messageRequest);

        /// <summary>
        /// Deletes a user alert message by its ID.
        /// </summary>
        /// <param name="messageId">The ID of the message to delete.</param>
        /// <returns></returns>
        Task DeleteMessage(int messageId);
    }
}
