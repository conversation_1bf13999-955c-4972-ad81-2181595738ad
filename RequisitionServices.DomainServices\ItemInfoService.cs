﻿using RequisitionServices.DomainModel.ItemInfo;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices
{
    public class ItemInfoService : IItemInfoService
    {
        private readonly ISmartItemInfoService _smartItemInfoService;

        public ItemInfoService(ISmartItemInfoService smartItemSvc)
        {
            _smartItemInfoService = smartItemSvc;
        }

        public List<ItemInfoModel> GetItemInfoById(string userId, string coid, string itemNum)
        {
            return _smartItemInfoService.GetItemInfoById(userId, coid, itemNum);
        }

        public List<ItemInfoModel> GetItemInfoByReorderNbr(string userId, string coid, string reorderNum)
        {
            return _smartItemInfoService.GetItemInfoByReorderNbr(userId, coid, reorderNum);
        }
    }
}
