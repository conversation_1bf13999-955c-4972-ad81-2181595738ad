﻿using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Cart.Requests
{
    public class CartRequest
    {
        public string Username { get; set; }
        public string Coid { get; set; }
        public int DepartmentNumber { get; set; }
        public string ParId { get; set; }
        public long? CartId { get; set; }
        public CartType CartType { get; set; }
        public int CartTypeId { get; set; }
    }

}
