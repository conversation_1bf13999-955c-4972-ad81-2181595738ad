﻿using RequisitionServices.DomainModel.Vendors;


namespace RequisitionServices.MMISServices.DTO
{
    public class VendorHeaderModel
    {
        public int VendorNumber { get; set; }

        public int Coid { get; set; }

        public string VendorName { get; set; }

        public int StandardNationalVendorNumber { get; set; }

        public string Status { get; set; }

        public bool IsPOOnly { get; set; }

        public string OrderType { get; set; }

        public VendorHeaderInfo MapToVendorHeaderInfo()
        {
            return new VendorHeaderInfo()
            {
                Id = this.VendorNumber,
                Coid = this.Coid,
                Name = this.VendorName,
                StandardNationalVendorNumber = this.StandardNationalVendorNumber,
                Status = this.Status,
                IsPOOnly = this.IsPOOnly,
                OrderType = this.OrderType
            };
        }

    }
}
