﻿using RequisitionServices.DomainServices.Interface;
using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class COIDController : ApiController
    {
        private ICOIDService COIDService;
        private IUserService UserService;

        public COIDController(ICOIDService COIDSrvc, IUserService userSrvc)
        {
            COIDService = COIDSrvc;
            UserService = userSrvc;
        }
        public List<int> getAllCOIDs(string userName)
        {
            return COIDService.GetAllCOIDs(userName);
        }

        public Location GetCOIDAsLocation(string userName, string COID)
        {
            return UserService.GetLocation(userName, COID);
        }
    }
}