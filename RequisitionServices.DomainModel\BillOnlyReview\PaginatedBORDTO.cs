﻿using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    /// <summary>
    /// Represents a paginated list of Bill Only Review requisitions.
    /// </summary>
    public class PaginatedBORDTO
    {
        /// <summary>
        /// Gets or sets the list of displayed Bill Only Review requisitions.
        /// </summary>
        public List<BillOnlyReviewDTO> DisplayedBORRequisitions { get; set; }

        /// <summary>
        /// Gets or sets the total count of Bill Only Review requisitions.
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Gets or sets the current page number.
        /// </summary>
        public int PageNumber { get; set; }

        /// <summary>
        /// Gets or sets the size of the page.
        /// </summary>
        public int PageSize { get; set; }
    }
}
