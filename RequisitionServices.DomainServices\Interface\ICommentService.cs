﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.DomainServices.Interface
{
    public interface ICommentService
    {
        CommentsResponse Get(int requisitionId, string username);
        List<CommentDTO> Add(AddCommentDTO request);
        CommentNotificationRequisitionsDTO GetNotifications(string username, int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string filterText);
    }
}