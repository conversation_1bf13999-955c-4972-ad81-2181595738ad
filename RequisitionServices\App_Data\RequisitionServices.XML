<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RequisitionServices</name>
    </assembly>
    <members>
        <member name="M:RequisitionServices.Controllers.BillOnlyReviewController.#ctor(RequisitionServices.DomainServices.Interface.IBillOnlyReviewService)">
            <summary>
            Initializes a new instance of the <see cref="T:RequisitionServices.Controllers.BillOnlyReviewController"/> class.
            </summary>
            <param name="billOnlyReviewService">The bill only review service.</param>
        </member>
        <member name="M:RequisitionServices.Controllers.BillOnlyReviewController.GetBillOnlyReviewRequisitions(RequisitionServices.DomainModel.Requisitions.BillOnlyReviewRequest)">
            <summary>
            Retrieves a list of Bill Only Review Requisitions based on the provided request parameters.
            </summary>
            <param name="request">The request parameters for retrieving Bill Only Review Requisitions.</param>
            <returns>A list of Bill Only Review Requisitions.</returns>
            <exception cref="T:System.Exception">Thrown when an error occurs while retrieving the requisitions.</exception>
        </member>
        <member name="M:RequisitionServices.Controllers.BillOnlyReviewController.GetRequisitionsDetailsForBORPrint(RequisitionServices.DomainModel.BillOnlyReview.BillOnlyReviewPrintRequest)">
            <summary>
            Retrieves the details of requisitions for BillOnlyReview printing based on the provided a passed list of RequisitionIds.
            </summary>
            <param name="request">A list of RequisitionIds and the UserName for retrieving requisition details.</param>
            <returns>A list of requisitions with details included for a BillOnlyReview Printout report.</returns>
            <exception cref="T:System.Exception">Thrown when an error occurs while retrieving the requisition details.</exception>
        </member>
        <member name="T:RequisitionServices.Controllers.DigitalSignOffController">
            <summary>
            Handles the CRUD endpoints for the Digital Sign Off Process
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.DigitalSignOffController.GetApproverDigitalSignOff(System.Int32)">
            <summary>
            Brings back the specific data for the Approvers section of procurement needed for DSO
            </summary>
            <returns>DigitalSignOffUser</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.IINItemController.GetIINItemById(System.String,System.String,System.String)">
            <summary>
            Get an IIN Item by Id
            </summary>
            <param name="userId"></param>
            <param name="COID"></param>
            <param name="IINitemNumber"></param>
            <returns></returns>
        </member>
        <member name="T:RequisitionServices.Controllers.ItemController">
            <summary>
            Item service
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.ItemController.GetItemByParId(System.String,System.String,System.String,System.String)">
            <summary>
            Get an item by Id
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="itemId"></param>
            <param name="parId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ItemController.GetItem(System.String,System.String,System.String)">
            <summary>
            
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ItemController.GetItemWithDetails(System.String,System.String,System.Int32,RequisitionServices.MMISServices.DTO.ItemParDTO)">
            <summary>
            Look up one Item with available pars and corrected QIS and UOM
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <param name="itemPar"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ItemController.GetItemsWithDetails(System.String,System.String,System.Int32,System.Collections.Generic.List{RequisitionServices.MMISServices.DTO.ItemParDTO})">
            <summary>
            look up multiple items with available pars and corrected QIS and UOM
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <param name="itemPars"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ItemController.GetDeliveryMethods">
            <summary>
            Get the available list of delivery methods
            </summary>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ItemController.GetItemPrice(System.String,System.String,System.String,System.String)">
            <summary>
            Get Item Price  details for the vendor Id or re-order number
            </summary>
            <param name="username">User Id</param>
            <param name="coid"> Facility number</param>
            <param name="reordernumber">re-order|vendor part number</param>
            <param name="vendornumber">Vendor Id</param>
            <returns></returns>
        </member>
        <member name="T:RequisitionServices.Controllers.ParController">
            <summary>
            Methods for interacting with Pars
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.ParController.GetParsByUserLocation(System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Retrieves a list of available Pars for a given user and location
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ParController.GetParItems(System.String,System.String,System.Int32,System.String)">
            <summary>
            Get the PAR item details for a given Par
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <param name="parId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.ParController.GetParItemsByItem(System.String,System.String,System.Int32,System.String)">
            <summary>
            Get the PAR item details for a given Item
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <param name="itemId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.POController.GetDetailsByPO(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieves all the details related to COID and PONumber
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="PONumber"></param>
            <param name="stockIndicator"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.POController.GetPOConfirmationDetails(System.String,System.String,System.String,System.String)">
            <summary>
            Retrieves all Confirmation Details associated with an item in a PO
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="PONumber"></param>
            <param name="lineNumber"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.POController.getDetailsByDateRange(System.String,System.String,System.DateTime,System.DateTime,System.Int32)">
            <summary>
            Retrieves all the PO details related to Date Range and COID
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="startDate"></param>
            <param name="endDate"></param>
            <param name="department"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.POController.GetPoByOptions(System.String,System.String,System.DateTime,System.DateTime,System.String,System.Int32,System.String)">
            <summary>
            Gets PONumbers based on a Date Range
            </summary>
            <param name="userName"></param>
            <param name="coid"></param>
            <param name="startDate"></param>
            <param name="endDate"></param>
            <param name="poType"></param>
            <param name="department"></param>
            <param name="reorderNumber"></param>
            <returns>Returns POOption</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.POController.GetPoByProjectNumber(System.String,System.String,System.String)">
            <summary>
            Returns POs that have items on it that are for a project number
            </summary>
            <param name="userName"></param>
            <param name="coid"></param>
            <param name="projectNumber"></param>
            <returns></returns>
        </member>
        <member name="T:RequisitionServices.Controllers.RequisitionController">
            <summary>
            Methods for interacting with requisitions
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.SaveRequisition(RequisitionServices.DomainModel.Requisitions.RequisitionDTO,System.String)">
            <summary>
            Save a requisition
            </summary>
            <param name="requisition"></param>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.SaveRequisitionAsApprover(RequisitionServices.DomainModel.Requisitions.RequisitionDTO,System.String)">
            <summary>
            Save requisition as an approver
            </summary>
            <param name="requisition"></param>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.SubmitRequisition(RequisitionServices.DomainModel.Requisitions.RequisitionDTO,System.String,System.Nullable{System.Int64})">
            <summary>
            Submits a requisition
            </summary>
            <param name="requisition">Requisition Object to submit</param>
            <param name="userName">Full username for user performing action (This should match the Domain/Username that PASS has)</param>
            <param name="cartId">Used if submitting a Requisition that was created from a Cart</param>
            <returns>This methods returns a populated requisition object which contains the updated status of the requisition as well as the parent System Id</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.SubmitApproversRequisition(RequisitionServices.DomainModel.Requisitions.RequisitionDTO,System.String,System.Nullable{System.Int64})">
            <summary>
            Submits a requisition
            </summary>
            <param name="requisition">Requisition Object to submit</param>
            <param name="userName">Full username for user performing action (This should match the Domain/Username that PASS has)</param>
            <param name="cartId">Used if submitting a Requisition that was created from a Cart</param>
            <returns>This methods returns a populated requisition object which contains the updated status of the requisition as well as the parent System Id</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisition(System.Int32,System.String)">
            <summary>
            Retrieves a requisition with details by its ID
            </summary>
            <param name="requisitionId"></param>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionForUpdate(System.Int32,System.String)">
            <summary>
            Retrieves a requisition without details by its ID
            </summary>
            <param name="requisitionId"></param>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.UpdateRequisitionStatus(RequisitionServices.DomainModel.Requisitions.RequisitionStatusHistory)">
            <summary>
            Updates a requisition status given the values specified in the posted object
            </summary>
            <param name="requisitionStatusHistory"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.SmartLegacySubmissionAvailabilityCheck(System.String,System.String)">
            <summary>
            Checks if SMART Legacy GetSprStatus service is available
            </summary>
            <param name="userId"></param>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.UpdateRequisitionItemStatus">
            <summary>
            Update requisition item status
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitions(System.String,System.DateTime,System.DateTime,System.String,System.Int32)">
            <summary>
            Get a list of requisitions for a date range and COID
            </summary>
            <param name="COID"></param>
            <param name="startDate"></param>
            <param name="endDate"></param>
            <param name="userName"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsWithItemStatuses(System.String,System.String,System.DateTime,System.DateTime,System.Int32)">
            <summary>
            Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
            </summary>
            <param name="userName"></param>
            <param name="coid"></param>
            <param name="startDate"></param>
            <param name="endDate"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsByVendor(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Retrieves requisition matching Vendor Id or Vendor Name
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsByVendorReportExport(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for Vendor Id or Vendor Name and COID, with item details. Drafts and Templates NOT included.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsForReport(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetVBORequisitionsForReport(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a list of Vendor Bill Only requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsForReportExport(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetVBORequisitionsForReportExport(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsForReportByItemNumber(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a paged list of requisitions for an item/catalog/reorder number and COID 
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsForReportByItemNumberExport(RequisitionServices.DomainModel.Requisitions.RequisitionReportRequestDto)">
            <summary>
            Get a list of requisitions for an item/catalog/reorder number and COID 
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetMyRequisitionsResults(RequisitionServices.DomainModel.Requisitions.RequisitionListMultiRequestDto)">
            <summary>
            Retrieves active requisitions and/or templates on the MyRequisitions page for the user
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetMyApprovalsResults(RequisitionServices.DomainModel.Requisitions.RequisitionListMultiRequestDto)">
            <summary>
            Retrieves pending approvals and/or approval history on the MyApprovals page for the user
            </summary>
            <param name="request"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetUpcomingApprovalsForApprover(RequisitionServices.DomainModel.Requisitions.RequisitionListRequestDto)">
            <summary>
            Get a paginated list of upcoming approvals for an approver's Upcoming Approvals widget.
            </summary>
            <param name="request"></param>
            <returns>A list of Approval objects which contain requisition details</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionHistory(System.Int32)">
            <summary>
            Get a list of history records for a given Requisition
            </summary>
            <param name="requisitionId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.AdvanceRequisition(RequisitionServices.DomainModel.Requisitions.RequisitionAdvanceDTO)">
            <summary>
            Escalate the provided requisition to the provided workflow step
            </summary>
            <param name="requisitionAdvanceDTO"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.ReInitializeAllWorkflow(System.String,System.String)">
            <summary>
            Re-start all requisitions in Pending Approval Status
            </summary>
            <param name="key"></param>
            <param name="userName"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.ReInitializeRequisitionInWorkflow(System.String,System.Int32,System.String)">
            <summary>
            Re-start requisition in Pending Approval Status by Id
            </summary>
            <param name="key"></param>
            <param name="requisitionId"></param>
            <param name="userName"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetUserWorkflowStepsDelegatedByUser(System.String)">
            <summary>
            Return a list of user workflow steps where the user has delegated approval authority
            </summary>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.RequestAdhocReview(RequisitionServices.DomainModel.Requisitions.AdhocReviewDTO)">
            <summary>
            Request an ad-hoc review for requisition
            </summary>
            <param name="adhocReview"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.ProvideAdhocReview(RequisitionServices.DomainModel.Requisitions.AdhocReviewDTO)">
            <summary>
            Provide ad hoc review
            </summary>
            <param name="adhocReview"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionAdhocReviews(System.Int32)">
            <summary>
            Get requisition ad hoc reviews
            </summary>
            <param name="requisitionId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.IsAdhocReviewAllowed(System.Int32,System.String,System.Int32)">
            <summary>
            IsAdhocReview allowed
            </summary>
            <param name="adhocReviewId"></param>
            <param name="reviewer"></param>
            <param name="reqId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionsForPurchasingReport(RequisitionServices.DomainModel.Requisitions.PurchasingRequisitionReportParameters)">
             <summary>
             Retrieves a queue of purchasing requisitions for the purchasing report.
             </summary>
             <remarks>
             This endpoint retrieves paginated and filtered purchasing requisitions for generating a report. 
             It accepts a JSON object in the request body with the following parameters:
             
             **Required Parameters:**
             
             * **`Coids`**: (List of string) 
                - The list of COIDs to filter by.
                - Enter comma-separated values without quotes (e.g., `09,332`).
                - Example: `"Coids": ["123", "321"]`
            
             **Optional Parameters:**
             
             * **`advancedFilters`**: (object) 
                - An object containing advanced filter options.
                  * **`ReqTypes`**: (List of int) 
                      - The list of requisition type IDs to filter by.
                      - Enter comma-separated IDs without quotes (e.g., `1,2,3`).
                  * **`Vendors`**: (List of int) 
                      - The list of vendor IDs to filter by.
                      - Enter comma-separated IDs without quotes (e.g., `101,205`).
                  * **`Buyers`**: (List of string) 
                      - The list of buyer names to filter by.
                      - Enter comma-separated names without quotes (e.g., `John Doe, Jane Smith`).
                  * **`FilterText`**: (string) 
                      - A string value to search certain columns by.
                      - Searchable columns include: User First and Last Name, Requisition Status Description, Requisition Submission Type Description, Comments, PO Number, Parent System ID, Original Parent System ID, Location Identifier, and PAR Identifier.
                  * **`startDate`**: (Date)
                      - The starting date for filtering requisitions by creation date.
                      - If not provided, defaults to the minimum date allowed by the database.
                  * **`endDate`**: (Date)
                      - The ending date for filtering requisitions by creation date.
                      - If not provided, defaults to the maximum date allowed by the database.
             * **`pageNumber`**: (int, defaults to 1) 
                - The page number for pagination.
             * **`pageSize`**: (int, defaults to 25) 
                - The number of records to return per page.
             * **`sortColumn`**: (string, defaults to 'RequisitionId') 
                - The column to sort by.
                - Valid values are: `RequisitionId`, `LocationIdentifier`, `VendorNumber`, `VendorName`, `Date`, `RequisitionTypeId`, `FileAttachmentItemId`.
             * **`sortType`**: (string, defaults to 'ASC') 
                - The sort direction.
                - Valid values are: `ASC` (Ascending), `DESC` (Descending).
             </remarks>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetAdvancedFiltersForPurchasingReport(RequisitionServices.DomainModel.Requisitions.RequisitionPurchasingAdvancedFilterRequest)">
            <summary>
            Get a list of filters for the advanced filter for Purchasing with an input list of CoIds.
            </summary>
            <param name="filterList"></param>
            <returns>lists of values</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.RequisitionController.GetRequisitionAndItemsByPONumber(System.String,System.String)">
            <summary>
            Handles an HTTP GET request to retrieve a list of requisitions based on the provided purchase order number and COID.
            The requisitions are returned in a JSON format.
            </summary>
            <param name="poNumber">The purchase order number.</param>
            <param name="coid">The facility ID.</param>
            <returns>A list of <see cref="T:RequisitionServices.DomainModel.Requisitions.RequisitionDTO"/> objects that match the specified criteria.</returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.GetAllGLAccounts(System.String,System.String,System.String)">
            <summary>
            Get All GL Accounts for a specific by 3 character filter
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="accountStringPartial"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.GetAllCostCodes(System.String,System.String)">
            <summary>
            Get all cost codes for a specific facility
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.GetGLAccount(System.String,System.String,System.Int64)">
            <summary>
            Get a specific GL Account by it's account number
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="accountNumber"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.GetAddress(System.String,System.String,System.Int32)">
            <summary>
            Get a specific address by it's ship number (External System Id)
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="shipNumber"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.GetAddresses(System.String,System.String)">
            <summary>
            Get a list of addresses for a facility
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.SaveFacilityNotifications(System.Collections.Generic.IEnumerable{RequisitionServices.DomainModel.Locations.FacilityNotification},System.String,System.String)">
            <summary>
            Saves updated list of facility notifications
            </summary>
            <param name="facilityNotifications"></param>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.GetFacilityNotifications(System.String,System.String)">
            <summary>
            Get a list of all notifications available for a facility
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.LocationController.UpdateLocator(System.String,RequisitionServices.DomainModel.Locations.Locator)">
            <summary>
            Update Locations and return results
            </summary>
            <param name="userName"></param>
            <param name="locator"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.VendorController.GetPunchOutVendors(RequisitionServices.DomainModel.Locations.Facility)">
            <summary>
            Get list of punch-out vendors for a COID
            </summary>
            <param name="facility"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.VendorController.GetVendorById(System.String,System.Int32)">
            <summary>
            Get a vendor for a COID by vendor ID
            </summary>
            <param name="COID"></param>
            <param name="vendorId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.VendorController.GetVendorInformationById(System.String,System.Int32)">
            <summary>
            Get Vendor Information for a coid by Vendor Id
            </summary>
            <param name="COID"></param>
            <param name="vendorId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.VendorController.GetVendorDetailsById(System.String,System.Int32)">
            <summary>
            Returns the details of a vendor for a COID by vendor ID
            </summary>
            <param name="COID"></param>
            <param name="vendorId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.VendorController.GetAllVendors(System.String)">
            <summary>
            Returns header information for all vendors for a COID.
            </summary>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetLocations(System.String)">
            <summary>
            Get list of locations
            </summary>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetAllDepartments(System.String,System.String,System.Boolean)">
            <summary>
            Get list of all departments user has access to given a facility (COID)
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="userIsVendor"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetFavoriteFacilityId(System.String)">
            <summary>
            Get the favorited COID for a certain user
            </summary>
            <param name="userName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SetFavoriteFacility(RequisitionServices.DomainModel.Users.PersonalizationDTO)">
            <summary>
            (Re)sets the preferred COID for a certain user.
            </summary>
            <param name="personalizationDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.DeleteFavoriteFacility(System.String)">
            <summary>
            Removes the favorited COID for a certain user.
            </summary>
            <param name="userName"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetFavoriteDepartmentId(System.String,System.String)">
            <summary>
            Get the ID of a favorited department, if one exists, given a facility (COID)
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetFavoriteDepartment(System.String,System.String)">
            <summary>
            Get the user's favorite Department, if one exists, given a facility (COID)
            </summary>
            <param name="userName"></param>
            <param name="cOID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SetFavoriteDepartment(RequisitionServices.DomainModel.Users.PersonalizationDTO)">
            <summary>
            Sets a user's favorite department
            </summary>
            <param name="personalization"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.DeleteFavoriteDepartment(RequisitionServices.DomainModel.Users.PersonalizationDTO)">
            <summary>
            Removes a user's favorite department for the given facility.
            </summary>
            <param name="personalization"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetFavoriteParId(System.String,System.String,System.Int32)">
            <summary>
            Get the ID of a favorited PAR, if one exists, given a facility (COID) and department
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetFavoritePar(System.String,System.String,System.Int32)">
            <summary>
            Get the favorited PAR, of one exists, given a facility (COID) and department
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SetFavoritePar(RequisitionServices.DomainModel.Users.PersonalizationDTO)">
            <summary>
            Sets a user's favorite PAR.
            </summary>
            <param name="personalization"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.DeleteFavoritePar(RequisitionServices.DomainModel.Users.PersonalizationDTO)">
            <summary>
            Removes a user's favorite PAR for the given facility and department.
            </summary>
            <param name="personalization"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetAllDepartmentsForCache(System.String,System.String)">
            <summary>
            Get list of all departments user has access to given a facility (COID)
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetDepartment(System.String,System.String,System.Int32)">
            <summary>
            Department
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="departmentId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.ValidateUserWorkflow(System.String,RequisitionServices.DomainModel.Enum.WorkflowTypeEnum,System.String,System.Nullable{System.Decimal})">
            <summary>
            Validates a user's workflow from database.
            </summary>
            <param name="userName"></param>
            <param name="workflowType"></param>
            <param name="COID">Optional: Will apply Span of Control rules against COID</param>
            <param name="requisitionTotal">Optional: Will apply requisition total/approval amount rules</param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.ValidateUserWorkflow(RequisitionServices.DomainModel.Users.WorkflowRequestValidationDTO)">
            <summary>
            Validates a user's workflow passed in.
            </summary>
            <param name="workflowRequestValidationDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetValidationOfUserWorkflows(RequisitionServices.DomainModel.Users.ValidateUserWorkflowsRequestDTO)">
            <summary>
            Validates all workflows for one user
            </summary>
            <param name="validateUserWorkflowsRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetUserEditUsers(RequisitionServices.DomainModel.Users.UserReportInfoRequestDTO)">
            <summary>
            Gets the User or Approver for each username passed in.
            </summary>
            <param name="userRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.RetrieveUserReportInfo(RequisitionServices.DomainModel.Users.UserReportInfoRequestDTO)">
            <summary>
            Returns the info needed for the User Report page.
            </summary>
            <param name="userReportInfoRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.RetrieveUsersForDelegates(RequisitionServices.DomainModel.Users.UserRequestDTO)">
            <summary>
            
            </summary>
            <param name="userRequestDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.UpdateApprovers(RequisitionServices.DomainModel.Users.ApproverUpdateDTO)">
            <summary>
            Updates all passed in approver accounts
            </summary>
            <param name="approverUpdateDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.UpdateApproversAsync(RequisitionServices.DomainModel.Users.ApproverUpdateDTO)">
            <summary>
            Updates all passed in approver accounts
            </summary>
            <param name="approverUpdateDto"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SaveUserEditInfo(System.String,RequisitionServices.DomainModel.Users.SaveUserEditInfoDTO)">
            <summary>
            Saves the User's Approval amount (if applicable) and workflows (if applicable).
            </summary>
            <param name="userName"></param>
            <param name="saveUserEditInfoDTO"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SaveUserEditInfoAsync(System.String,RequisitionServices.DomainModel.Users.SaveUserEditInfoDTO)">
            <summary>
            Saves the User's Approval amount (if applicable) and workflows (if applicable).
            </summary>
            <param name="userName"></param>
            <param name="saveUserEditInfoDTO"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetUserWorkflowSteps(System.String,System.String,System.Nullable{System.Int32})">
            <summary>
            Get list of workflow steps for specified user
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <param name="workflowTypeId"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetUserWorkflowsForExport(RequisitionServices.DomainModel.Users.WorkflowExportInputDTO)">
            <summary>
            Grabs all the workflows for given users without delegated approvers
            </summary>
            <param name="workflowExportInputDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetAllWorkflowTypes">
            <summary>
            Get list of workflow types
            </summary>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetAllUserWorkflowTypes">
            <summary>
            Get list of User workflow types
            </summary>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SaveWorkflows(System.String,RequisitionServices.DomainModel.Users.SaveWorkflowsDTO)">
            <summary>
            Saves all user's workflows' steps. 
            </summary>
            <param name="updater"></param>
            <param name="saveWorkflowsDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SaveUserWorkflowSteps(System.String,RequisitionServices.DomainModel.Users.UserWorkflowDTO)">
            <summary>
            Save user workflow steps
            </summary>
            <param name="updater"></param>
            <param name="userworkflowDTO"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.DeleteDelegatesForApprover(System.String,System.String)">
            <summary>
            Delete all workflow steps that the approverId passed in has delegated
            </summary>
            <param name="userName"></param>
            <param name="delegateEmail"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.AssignDelegateForApprover(System.Int32,System.String,System.String)">
            <summary>
            Assign delegate to all steps user is in
            </summary>
            <param name="delegateUserId"></param>
            <param name="delegateEmail"></param>
            <param name="userName"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetApproverByCoid(System.String,System.String)">
            <summary>
            Get an approver account by userName
            </summary>
            <param name="userName"></param>
            <param name="COID"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetActiveApprovers">
            <summary>
            Get all active approvers accounts with COIDs
            </summary>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetApproverWorkflows(System.Int32)">
            <summary>
            Get the approver's workflows
            </summary>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetUser(System.String)">
            <summary>
            Gets the User from database by accountName
            </summary>
            <param name="accountName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetUserWithoutDomain(System.String)">
            <summary>
            Gets the User from database by accountName
            </summary>
            <param name="accountName"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.UpdateUserName(System.String,System.String,System.String)">
            <summary>
            updates the User's first and last name
            </summary>
            <param name="accountName"></param>
            <param name="firstName"></param>
            <param name="lastName"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.SaveBulkApproverJob(System.String,RequisitionServices.DomainModel.Users.BulkApproverJobTracker)">
            <summary>
            Save Bulk Approver Remove or Exchange Data
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetBulkApproverJobDetails(System.String)">
            <summary>
            GEt Bulk Approver Remove or Exchange Data to Display On Bulk Edit Status Tab
            </summary>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetBulkApproverJobTracker(System.Guid)">
            <summary>
            Gets the last bulk approver tracker job by bulkApproverId
            </summary>
            <param name="bulkApproverId"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.UpdateBulkApproverJobStatus(RequisitionServices.DomainModel.Users.BulkApproverJobStatusDTO)">
            <summary>
            Updates the bulk approver job status
            </summary>
            <param name="bulkApproverJobStatusDto"></param>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetApproverByUserId(System.Int32,System.String)">
            <summary>
            Gets the approver by userid and coid
            </summary>
            <param name="userId"></param>
            <param name="coid"></param>
            <returns></returns>
        </member>
        <member name="M:RequisitionServices.Controllers.UserController.GetActiveApproversForBulkExchange(System.String,System.Int32)">
            <summary>
            Get the approver list for bulk exchange
            </summary>
            <returns></returns>
        </member>
        <member name="T:RequisitionServices.Controllers.VProController">
            <summary>
            Handles HTTP requests for VPro badge operations.
            </summary>
        </member>
        <member name="T:RequisitionServices.Filters.UnhandledExceptionFilterAttribute">
            <summary>
            Represents the an attribute that provides a filter for unhandled exceptions.
            </summary>
        </member>
        <member name="M:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:RequisitionServices.Filters.UnhandledExceptionFilterAttribute"/> class.
            </summary>
        </member>
        <member name="F:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.DefaultHandler">
            <summary>
            Gets a delegate method that returns an <see cref="T:System.Net.Http.HttpResponseMessage"/> 
            that describes the supplied exception.
            </summary>
            <value>
            A <see cref="T:System.Func`3"/> delegate method that returns 
            an <see cref="T:System.Net.Http.HttpResponseMessage"/> that describes the supplied exception.
            </value>
        </member>
        <member name="F:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.GetContentOf">
            <summary>
            Gets a delegate method that extracts information from the specified exception.
            </summary>
            <value>
            A <see cref="T:System.Func`2"/> delegate method that extracts information 
            from the specified exception.
            </value>
        </member>
        <member name="P:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.Handlers">
            <summary>
            Gets the exception handlers registered with this filter.
            </summary>
            <value>
            A <see cref="T:System.Collections.Concurrent.ConcurrentDictionary`2"/> collection that contains 
            the exception handlers registered with this filter.
            </value>
        </member>
        <member name="M:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.OnException(System.Web.Http.Filters.HttpActionExecutedContext)">
            <summary>
            Raises the exception event.
            </summary>
            <param name="actionExecutedContext">The context for the action.</param>
        </member>
        <member name="M:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.Register``1(System.Net.HttpStatusCode)">
            <summary>
            Registers an exception handler that returns the specified status code for exceptions of type <typeparamref name="TException"/>.
            </summary>
            <typeparam name="TException">The type of exception to register a handler for.</typeparam>
            <param name="statusCode">The HTTP status code to return for exceptions of type <typeparamref name="TException"/>.</param>
            <returns>
            This <see cref="T:RequisitionServices.Filters.UnhandledExceptionFilterAttribute"/> after the exception handler has been added.
            </returns>
        </member>
        <member name="M:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.Register``1(System.Func{System.Exception,System.Net.Http.HttpRequestMessage,System.Net.Http.HttpResponseMessage})">
            <summary>
            Registers the specified exception <paramref name="handler"/> for exceptions of type <typeparamref name="TException"/>.
            </summary>
            <typeparam name="TException">The type of exception to register the <paramref name="handler"/> for.</typeparam>
            <param name="handler">The exception handler responsible for exceptions of type <typeparamref name="TException"/>.</param>
            <returns>
            This <see cref="T:RequisitionServices.Filters.UnhandledExceptionFilterAttribute"/> after the exception <paramref name="handler"/> 
            has been added.
            </returns>
            <exception cref="T:System.ArgumentNullException">The <paramref name="handler"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:RequisitionServices.Filters.UnhandledExceptionFilterAttribute.Unregister``1">
            <summary>
            Unregisters the exception handler for exceptions of type <typeparamref name="TException"/>.
            </summary>
            <typeparam name="TException">The type of exception to unregister handlers for.</typeparam>
            <returns>
            This <see cref="T:RequisitionServices.Filters.UnhandledExceptionFilterAttribute"/> after the exception handler 
            for exceptions of type <typeparamref name="TException"/> has been removed.
            </returns>
        </member>
    </members>
</doc>
