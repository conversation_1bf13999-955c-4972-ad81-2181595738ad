﻿using RequisitionServices.DomainModel.Users;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.FacilityWorkflow
{
    public class FacilityWorkflowStep
    {
        public int Id { get; set; }
        public string Coid { get; set; }
        public int ApproverId { get; set; }
        [ForeignKey("ApproverId")]
        public Approver Approver { get; set; }
        public int? DelegatedByUserId { get; set; }
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }
        public DateTime CreateDateUtc { get; set; }
        public int WorkflowTypeId { get; set; }
        [ForeignKey("WorkflowTypeId")]
        public virtual WorkflowType WorkflowType { get; set; }

        public FacilityWorkflowStep CreateDeepCopy(FacilityWorkflowStep workflow)
        {
            FacilityWorkflowStep copy = (FacilityWorkflowStep)MemberwiseClone();
            copy.Coid = workflow.Coid;
            copy.Approver = workflow.Approver;
            copy.ApproverId = workflow.ApproverId;
            copy.WorkflowType = workflow.WorkflowType;
            copy.WorkflowTypeId = workflow.WorkflowTypeId;
            copy.CreateDateUtc = workflow.CreateDateUtc;
            copy.CreatedBy = workflow.CreatedBy;
            copy.DelegatedByUserId = workflow.DelegatedByUserId;
            copy.Id = workflow.Id;
            return copy;
        }
    }
}
