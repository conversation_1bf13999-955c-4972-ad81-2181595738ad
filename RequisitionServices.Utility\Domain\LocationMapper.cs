﻿using System;
using System.Diagnostics;
using System.Collections.Generic;
using System.Reflection;
using log4net;
using System.Globalization;
using System.Linq;

namespace RequisitionServices.Utility.Domain
{
    public static class LocationMapper
    {
        private static readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private static Dictionary<string, CountryCodeInformation> countryCodeInformationList = new Dictionary<string, CountryCodeInformation>()
        {
            { "U", new CountryCodeInformation("U") },
            { "I", new CountryCodeInformation("I") }
        };

        public static bool CountryCodeIsRecognized(string smartCountryCode)
        {
            return countryCodeInformationList.Any(x => string.Equals(x.Key, smartCountryCode, StringComparison.InvariantCultureIgnoreCase));
        }

        public static string GetCOID(string locationIdentifier)
        {
            string cOID = null;
            var locationInfo = locationIdentifier.Split('_');
            if (locationInfo != null && locationInfo.Length == 2)
            {
                cOID = locationInfo[0];
            }

            return cOID;
        }

        public static string GetDepartmentId(string locationIdentifier)
        {
            string departmentId = null;
            var locationInfo = locationIdentifier.Split('_');
            if (locationInfo != null && locationInfo.Length == 2)
            {
                departmentId = locationInfo[1];
            }

            return departmentId;
        }
        
        public static int GetCOIDCharacterLength(string smartCountryCode)
        {
            var dictEntry = GetCountryInfo(smartCountryCode);
            return dictEntry.CoidLength;
        }  

        public static int GetDepartmentLength(string smartCountryCode)
        {
            var dictEntry = GetCountryInfo(smartCountryCode);
            return dictEntry.DepartmentLength;
        }

        public static int GetGeneralLedgerLength(string smartCountryCode)
        {
            var dictEntry = GetCountryInfo(smartCountryCode);
            return dictEntry.GeneralLedgerCodeLength;
        }

        public static int GetCostCodeLength(string smartCountryCode)
        {
            var dictEntry = GetCountryInfo(smartCountryCode);
            return dictEntry.CostCodeLength;
        }

        public static IFormatProvider GetCultureForFormattingUsingSmartCountryCode(string smartCountryCode)
        {
            var dictEntry = GetCountryInfo(smartCountryCode);
            return dictEntry.Culture;
        }

        private static CountryCodeInformation GetCountryInfo(string smartCountryCode)
        {
            CountryCodeInformation dictEntry = null;
            countryCodeInformationList.TryGetValue(smartCountryCode.ToUpper(), out dictEntry);
            if (dictEntry == null)
            {
                dictEntry = new CountryCodeInformation(smartCountryCode);
                LogInvalidCountryCodeError(smartCountryCode);
            }
            return dictEntry;
        }

        private static void LogInvalidCountryCodeError(string invalidCountryCode)
        {
            StackTrace trace = new StackTrace();
            log.Info(string.Format("Method {0} used invalid country code {1}", trace.GetFrame(2).GetMethod().Name, invalidCountryCode.ToUpper()));
        }
    }

    internal class CountryCodeInformation
    {
        public CountryCodeInformation(string countryCode)
        {
            CountryCode = countryCode.ToUpper();
            switch (CountryCode)
            {
                case "I":
                    CoidLength = 5;
                    DepartmentLength = 4;
                    GeneralLedgerCodeLength = 8;
                    CostCodeLength = 4;

                    Culture = new CultureInfo("en-GB");

                    break;

                //U.S. "U" is the default Country Code
                default:
                    CoidLength = 5;
                    DepartmentLength = 3;
                    GeneralLedgerCodeLength = 6;
                    CostCodeLength = 3;

                    var culture = new CultureInfo("en-US");
                    //make sure that it uses leading zeroes by setting it as the only option for shortdate formatting
                    culture.DateTimeFormat.ShortDatePattern = "MM/dd/yyyy";
                    Culture = culture;

                    break;
            }
        }

        public string CountryCode { get; private set; }

        public int CoidLength { get; private set; }

        public int DepartmentLength { get; private set; }

        public int GeneralLedgerCodeLength { get; private set; }

        public int CostCodeLength { get; private set; }

        public IFormatProvider Culture { get; private set; }


    }
}
