﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RequisitionServices.DomainModel.Items;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class VboHoldItemConversion
    {
        public VboHoldItemConversion() { }

        public VboHoldItemConversion(VboHoldItemConversionDto conversion)
        {
            RequisitionItemId = conversion.RequisitionItemId;
            SmartItemNumber = conversion.SmartItemNumber;
            ItemDetails = conversion.ItemDetails;
            UnitCost = conversion.UnitCost;
            CreatedBy = conversion.CreatedBy;
            CreateDate = conversion.CreateDate;
        }

        [Key, ForeignKey("RequisitionItem")]
        public int RequisitionItemId { get; set; }
        public virtual RequisitionItem RequisitionItem { get; set; }
        public int SmartItemNumber { get; set; }
        [NotMapped]
        public ParItem ItemDetails { get; set; }
        public decimal UnitCost { get; set; }
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }
    }
}
