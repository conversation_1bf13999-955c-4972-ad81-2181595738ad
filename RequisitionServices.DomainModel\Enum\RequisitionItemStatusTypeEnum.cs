﻿using System.ComponentModel;

namespace RequisitionServices.DomainModel.Enum
{
    public enum RequisitionItemStatusTypeEnum
    {
        [Description("Unknown")]
        Unknown = 0,
        [Description("Valid")]
        Valid = 1,
        [Description("Item Not Found")]
        ItemNotFound = 2,
        [Description("Multiples Found")]
        MultiplesFound = 3,
        [Description("Item Not Approved")]
        ItemNotApproved = 4,
        [Description("Item Not Compliant")]
        ItemNotCompliant = 5,
        [Description("Item Not On Contract")]
        ItemNotOnContract = 6,
        [Description("Approved")]
        Approved = 7,
        [Description("Max Qty Exceeded")]
        MaxQtyExceeded = 8,
        [Description("No Pars Available")]
        NoParsAvailable = 9,
        [Description("Need To Print")]
        NeedToPrint = 10,
        [Description("Being Pulled")]
        BeingPulled = 11,
        [Description("Added To Previous")]
        AddedToPrevious = 12,
        [Description("Requisition Open")]
        RequisitionOpen = 13,
        [Description("PO In Process")]
        POInProcess = 14,
        [Description("PO Created")]
        POCreated = 15,
        [Description("Item Received")]
        ItemReceived = 16,
        [Description("Need To Send To Warehouse")]
        NeedToSendToWH = 17,
        [Description("Sent To Warehouse")]
        SentToWH = 18,
        [Description("Requisition Filled")]
        RequisitionFilled = 19,
        [Description("Processing")]
        Processing = 20,
        [Description("System Error")]
        SystemError = 21,
        [Description("Template Item")]
        TemplateItem = 22,
        [Description("Scheduled")]
        Scheduled = 23,
        [Description("No Partial Fill")]
        NoPartialFill = 24,
        [Description("Rejected")]
        Rejected = 25,
        [Description("Min Qty Not Met")]
        MinQtyNotMet = 26,
        [Description("Invalid Discount Percentage")]
        NoDiscount = 27,
        [Description("Canceled Item")]
        CanceledItem = 28,
        [Description("QOO+QOH is over Min")]
        ItemNotOrdered = 29,
        [Description("Invalid Vendor")]
        InvalidVendor = 30,
        [Description("Hold")]
        Hold = 31
    }
}
