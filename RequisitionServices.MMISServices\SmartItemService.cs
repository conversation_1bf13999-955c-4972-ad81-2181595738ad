﻿using log4net;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    public class SmartItemService : ISmartItemService
    {
        const string GetItemsMethod = "Items/GetItemsByItemNumbers/";
        const string GetItemByItemIdMethod = "Items/GetItemByItemId/";
        readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        readonly string _endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public Item GetItemByItemId(string userName, string coid, string itemNumber)
        {
            if (string.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }

            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            if (!int.TryParse(coid, out _))
            {
                throw new ArgumentException($"Smart does not support non-numeric characters for Coid. Coid = {coid}");
            }

            if (!int.TryParse(itemNumber, out _))
            {
                throw new ArgumentException($"Smart does not support non-numeric characters for Item Number. Item Number = {itemNumber}");
            }

            ItemRecordModel itemRecord;
            try
            {
                itemRecord = ApiUtility.ExecuteApiGetTo<ItemRecordModel>(_endpoint, GetItemByItemIdMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "itemNumber", itemNumber },
                                                                                { "parId", "0" }
                                                                            });
            }
            catch (Exception ex)
            {
                _log.Error("Exception calling method GetItemByItemId", ex);
                return new Item();
            }

            var item = new Item();
            if (itemRecord.ItemNumber!=0 )
            {
                item = itemRecord.MapToItem();
            }

            return item;
        }

        public IEnumerable<Item> GetItems(string userName, string coid, List<ItemParDTO> itemPars)
        {
            if (string.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }

            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            if (!int.TryParse(coid, out _))
            {
                throw new ArgumentException($"Smart does not support non-numeric characters for Coid. Coid = {coid}");
            }

            ItemRecordsModel itemRecords;
            try
            {
                itemRecords = ApiUtility.ExecuteApiPostWithContentTo<ItemRecordsModel>(_endpoint, GetItemsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            }, itemPars);
            }
            catch(Exception ex)
            {
                _log.Error("Exception calling method GetItemsbyItemNumber", ex);
                return new List<Item>();
            }

            var items = new List<Item>();
            if (itemRecords?.Items != null)
            {
                foreach (var itemRecord in itemRecords.Items)
                {
                    items.Add(itemRecord.MapToItem());            
                }
            }

            return items;
        }
    }
}
