﻿/* FONTS */
@font-face {
    font-family: 'open_sansregular';
    src: url('../fonts/OpenSans-Regular-webfont.eot');
    src: url('../fonts/OpenSans-Regular-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans-Regular-webfont.woff') format('woff'), url('../fonts/OpenSans-Regular-webfont.ttf') format('truetype'), url('../fonts/OpenSans-Regular-webfont.svg#open_sansregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'open_sanssemibold';
    src: url('../fonts/OpenSans-Semibold-webfont.eot');
    src: url('../fonts/OpenSans-Semibold-webfont.eot?#iefix') format('embedded-opentype'), url('../fonts/OpenSans-Semibold-webfont.woff') format('woff'), url('../fonts/OpenSans-Semibold-webfont.ttf') format('truetype'), url('../fonts/OpenSans-Semibold-webfont.svg#open_sanssemibold') format('svg');
    font-weight: normal;
    font-style: normal;
}

.bodyWrapper {
    min-height: 300px;
}

.contentMargin {
    margin-top: 20px;
}

/* TYPOGRAPHY */
h1, h2, h3, h4, h5 {
    font-family: open_sansregular;
    text-rendering: auto;
}

h1 {
    color: #1B365D;
    font-size: 18px;
    line-height: 28px;
    font-weight: normal;
}

h2 {
    color: #768692;
    font-size: 20px;
    line-height: 22px;
    padding-bottom: 8px;
    text-align: center;
}

    h2.description {
        font-size: 20px;
        font-family: open_sanssemibold;
        color: #768692;
        /*margin-bottom: 22px;*/
        line-height: 26px;
        margin-top: -4px;
        padding-bottom: 0px;
    }

    h2.itemDescription {
        text-transform:uppercase;
        margin:0px;
    }

    h2 a {
        color: #1B365D;
    }

        h2 a:hover {
            color: #1B365D;
            padding-bottom: 0px;
        }

    h2.filterContainerTitle {
        margin: 0px;
        padding-bottom: 8px;
    }

h3 {
    color: #1B365D;
    font-size: 15px;
    line-height: normal;
    font-family: open_sansregular;
}

    h3 a {
        color: #1B365D;
    }

    h3.title {
        padding-bottom: 8px;
    }


h4 {
    font-size: 14px;
    color: #00335b;
}

h5 {
    font-size: 12px;
    color: #4D4D4D;
}

    h5.subtitle {
        margin: 0px;
    }

h6 {
    font-family: Verdana, Helvetica, Sans-Serif;
    text-transform: uppercase;
    color: #1B365D;
    font-weight: bold;
    font-size: 11px;
}

    h6.wellHeader {
        padding-bottom: 4px;
        line-height: normal;
    }

    h6 a {
        color: #1B365D;
    }

.lowercase {
    text-transform: lowercase;
}

.italic {
    font-style: italic;
}

.indent25{
    text-indent: 25px;
}

.center {
    min-width: 290px;
    text-align: center;
}

.attestation-modal-middle {
    margin-bottom: -15px;
    margin-top: 10px;
}

.attestation-modal-footer {
    margin-bottom: 10px;
    padding-left: 2%;
}

.textAlignRight {
    text-align: right;
}

#acceptSign {
    position: relative;
}
#acceptSign:disabled {
    pointer-events: auto;
}

#acceptSign:enabled:after {
    content: none;
}

#acceptSign::after {
    content: "You must sign off on all listed items above before Accept and Sign";
    display: none; 
    position: absolute; 
    bottom: 100%;
    left: 50%;
    width: 80%;
    transform: translateX(-50%); 
    background-color: #000; 
    color: #fff;
    padding: 5px; 
    border-radius: 5px; 
    white-space: wrap;
}

#acceptSign:hover::after { 
    display: block;  
  } 

p {
    margin-bottom: 16px;
}

.font-weight-normal {
    font-weight: normal;
}

.flip-text {
    -moz-transform: scale(-1, 1);
    -webkit-transform: scale(-1, 1);
    -o-transform: scale(-1, 1);
    -ms-transform: scale(-1, 1);
    transform: scale(-1, 1);
}

.conversion-button {
    margin-bottom: 4px;
}

/* HEADER */
.stripe {
    height: 18px;
    background-color: #1B365D;
    width: 100%;
    color: #fff;
    font-size: 10px;
    padding-top: 1px;
}

.stripeWelcome {
    text-align:right;
}

.stripeLink {
    color:#ed8b00;
}

    .stripeLink:hover {
        color:#fff;
    }

/* add rules for pageHeader-inner */
.pageHeader-inner {
    padding: 2px 0;
    border-top: 1px solid #ED8B00;
    border-bottom: 1px solid #ED8B00;
    background: #FFF;
    background: -moz-linear-gradient(top, #dddfe3 0%, #ffffff 100%); /* FF3.6+ */
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#dddfe3), color-stop(100%,#ffffff)); /* Chrome,Safari4+ */
    background: -webkit-linear-gradient(top, #dddfe3 0%,#ffffff 100%); /* Chrome10+,Safari5.1+ */
    background: -o-linear-gradient(top, #dddfe3 0%,#ffffff 100%); /* Opera 11.10+ */
    background: -ms-linear-gradient(top, #dddfe3 0%,#ffffff 100%); /* IE10+ */
    background: linear-gradient(to bottom, #dddfe3 0%,#ffffff 100%); /* W3C */
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#dddfe3', endColorstr='#ffffff',GradientType=0 ); /* IE6-9 */
}

#headerMenu .nav .dropdown-toggle .caret {
    border-top-color: #ffffff;
    border-bottom-color: #ffffff;
}

#headerMenu .nav .dropdown-toggle:hover .caret,
#headerMenu .nav .dropdown-toggle:focus .caret {
    border-top-color: #ed8b00;
    border-bottom-color: #ed8b00;
}

/* overrides */
#headerMenu .nav-pills li a {
    padding: 4px;
    font-size: 11px;
    color: white;
    background-color: #768692;
    -webkit-border-radius: 3px;
    -moz-border-radius: 3px;
    border-radius: 3px;
}

    #headerMenu .nav-pills li a:hover {
        background-color: #28496b;
        border-bottom: 0px;
    }

#headerMenu .nav-pills > .active > a,
#headerMenu .nav-pills > .active > a:hover,
#headerMenu .nav-pills > .active > a:focus {
    background-color: #28496b;
    color: #ffffff;
}

#headerMenu .dropdown-menu li a {
    text-align: left;
    color: #00335b;
}

/* New Rule as override */
#headerMenu .nav-pills .dropdown-menu {
    border: 0px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    padding: 0px;
}
/* New Rule as override, each item in dropdown */
#headerMenu .nav-pills li ul li a,
#headerMenu .nav-pills li ul li a:visited,
#headerMenu .nav-pills li ul li a:focus {
    background-color: #768692;
    color: #FFF;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
    border-top: 1px solid #FFF;
}

    #headerMenu .nav-pills li ul li a:hover {
        background-color: #00335b;
        color: #FFF;
        border-top: 1px solid #FFF;
    }

#headerMenu .dropdown-menu > li > a:hover,
#headerMenu .dropdown-menu > li > a:focus,
#headerMenu .dropdown-submenu:hover > a,
#headerMenu .dropdown-submenu:focus > a {
    color: #ffffff;
    border: 0px solid #000000;
    background-color: #00335b;
    background: -webkit-gradient(linear, left top, left bottom, from(#28496b), to(#00335b));
    background: -moz-linear-gradient(top, #28496b, #00335b);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#28496b', endColorstr='#00335b');
}

#topSubMenu .nav {
    font-size: 12px;
    /*margin-bottom: 16px;*/
}

#topSubMenu .nav-pills li a {
    padding: 4px 8px;
    color: #28496b;
}

    #topSubMenu .nav-pills li a:hover {
        color: white;
        background-color: #28496b;
        border-bottom: 0px;
        padding: 4px 8px;
    }

#topSubMenu .nav-pills > .active > a,
#topSubMenu .nav-pills > .active > a:hover,
#topSubMenu .nav-pills > .active > a:focus {
    background-color: #28496b;
    color: #ffffff;
    padding: 4px 8px;
}

/* LEFT PANEL */
#leftPanel {
    position: absolute;
    top: 94px;
    left: 0;
    z-index: 1005;
    background-color: #1b365d;
    background: -webkit-gradient(linear, left top, right bottom, from(#1b365d), to(#27518c));
    background: -moz-linear-gradient(top, #1b365d, #27518c);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1b365d', endColorstr='#27518c');
    -webkit-box-shadow: inset -8px 2px 8px 0 #162252;
    -mox-box-shadow: inset -8px 2px 8px 0 #162252;
    box-shadow: inset -8px 2px 8px 0 #162252;
    color: #dddfe3;
    width: 220px;
    display: block;
    overflow: hidden;
    height: 100%;
    font-size: 13px;
}

    #leftPanel h2 {
        color: #dddfe3;
        padding: 10px 8px 0;
    }

    #leftPanel ul {
        list-style: none;
        margin-left: 10px;
    }

        #leftPanel ul li {
            margin-left: 5px;
        }

            #leftPanel ul li.nav-header {
                color: #7BAFD4;
                /*border-bottom:1px solid #7BAFD4;*/
                padding-left: 0px;
                text-shadow: none;
                margin-left: 0px;
                font-size: 14px;
                text-transform: none;
                margin-top: 8px;
            }

#main {
    display: block;
    margin-left: 220px;
}

/* NAVIGATION */
.menuNav {
    text-transform: uppercase;
}

.errorPageMenuNav {
    background: #00335b;
    -webkit-box-shadow: inset 0 1px 0 #28496b;
    -moz-box-shadow: inset 0 1px 0 #28496b;
    box-shadow: inset 0 1px 0 #28496b;
    margin-bottom: 25px;
    height: 42px;
}

#pageslide {
    text-transform: lowercase;
    /* MUST be included. Do not change. */
    display: none;
    position: absolute;
    position: fixed;
    top: 0;
    height: 100%;
    z-index: 999999;
    /* the width of the pageslide */
    width: 260px;
    padding: 0 10px;
    /* the look of the pageslide */
    background-color: #333;
    color: #FFF;
    -webkit-box-shadow: inset 0 0 5px 5px #222;
    -moz-box-shadow: inset 0 0 5px 5px #222;
    box-shadow: inset 0 0 5px 5px #222;
}

/* FILTERS */
.sidebar-nav {
    padding: 10px 0px;
}

.nav-list {
    margin-top: 2px;
    margin-bottom: 24px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
    font-family: open_sansregular, sans;
    text-transform: uppercase;
    font-size: 12px;
}

    .nav-list .nav-header {
        color: #1b365d;
        font-family: open_sansregular, sans;
        text-transform: uppercase;
        font-size: 12px;
        border-top: 1px solid #DEE0E4;
        border-bottom: 1px solid #DEE0E4;
        padding: 4px 8px;
    }

    .nav-list > li > a {
        margin-bottom: 3px;
        color: #768692;
        padding: 8px;
    }

        .nav-list > .active > a,
        .nav-list > .active > a:hover,
        .nav-list > .active > a:focus, .nav-list > li > a:hover,
        .nav-list > li > a.active {
            color: #1B365D;
            -webkit-text-shadow: none;
            text-shadow: none;
            background: #DEE0E4;
            background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(222,224,228,1) 100%);
            background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,1)), color-stop(100%,rgba(222,224,228,1)));
            background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
            background: -o-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
            background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
            background: linear-gradient(to bottom, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
            filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#dee0e4',GradientType=0 );
            border-bottom: none;
        }

            .nav-list > .active > a > [class^="icon-"],
            .nav-list > .active > a > [class*=" icon-"] {
                background-image: url(../bootstrap/img/glyphicons-halflings.png);
            }


.checkMark {
    float: right;
    background-image: url("/Content/images/check16.png");
    background-repeat: no-repeat;
    height: 18px;
    width: 18px;
    margin-top: 2px;
}

.filterContainer{
    background-color:#f5f5f5;
    padding:8px 12px;
    border-radius:4px;
}

    .filterContainer h2 {
        margin-bottom:0px;
    }

    .filterContainer h5 {
        margin-bottom:0px;
    }

/* autocomplete/typeahead */
.autocomplete-suggestions { border: 1px solid #ccc; background: #FFF; overflow: auto; }
.autocomplete-suggestion { padding: 2px 5px; white-space: nowrap; overflow: hidden; }
.autocomplete-selected { background: #ddd; }
.autocomplete-suggestions strong { font-weight: normal; color: #ed8b00; }


/* SEARCH */
.searchBox {
    margin-top: 6px;
}

.sortBy {
    margin-top: 0px;
    font-size: 12px;
    padding-bottom: 0px;
    font-weight: normal;
}

    .sortBy select {
        margin-top: 5px;
    }

.searchResult {
    margin-bottom: 20px;
}

    .searchResult p {
        margin-bottom: 0px;
    }

    .searchResult h3 {
        padding: 0px;
        margin: 0px;
        text-transform:uppercase;
    }

    .searchResult h6,
    .searchResult h5 {
        margin: 0px;
    }

.resultsPerPage {
    margin-bottom: 16px;
}

p.showingResults {
    font-size: 13px;
    font-weight: bold;
    /*margin-top: 8px;*/
    margin-top: 10px;
}

#advancedSearch {
    margin-bottom: 20px;
}

.advancedSearchLink {
    margin-top: 10px;
    margin-right: 8px;
}

.advancedSearchContainer {
    background: #ededed;
    padding: 10px;
    padding-bottom: 15px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

    .advancedSearchContainer .tab-content {
        background: #fff;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        border-bottom: 1px solid #ddd;
        -webkit-border-radius: 0px 0px 4px 4px;
        -moz-border-radius: 0px 0px 4px 4px;
        border-radius: 0px 0px 4px 4px;
        padding: 10px;
    }

.advancedSearchArrowContainer {
    height: 15px;
}

.advancedSearchArrow {
    border-bottom: 15px solid #ededed;
    border-right: 15px solid transparent;
    border-left: 15px solid transparent;
    width: 1px;
    left: 70%;
    position: absolute;
    z-index: 1000;
}

.advancedSearchButton {
    margin-right: 2px;
}


input[disabled].facilitySearch,
input[readonly].facilitySearch {
    background-color: #fff;
}

/*.advancedSearch1Textbox {
    margin-left: 45px;
}

.advancedSearchOtherTextbox {
    margin-left: 15px;
}*/

#advancedSearchTabs.nav {
    margin-bottom: 0px;
}

#searchResults ul.thumbnails {
    overflow:hidden;
    display:table;
}

#searchResults .thumbnails li .span3 {
    padding-bottom:9015px;
    margin-bottom:-9000px;
    display:table-cell;
}

#searchResults .thumbnail h3, #searchResults .thumbnail h5 {
    margin: 0px 0px 4px;
}

#searchResults .thumbnail {
    position:relative;
    padding-bottom:30px;
}

#searchResults .thumbnail p {
    color:#333;
}

.searchResultDate {
    border-top: 1px solid #ddd;
    margin: 8px -4px 0px;
    padding: 4px 0px;
    text-align: center;
    bottom: 0;
    position: absolute;
    width: 100%;
    color:#333;
}

/* Other results */
.otherResultItem {
    border-bottom: 1px dotted #cccccc;
    padding-left: 8px;
    padding-bottom: 8px;
    margin-bottom: 36px;
    border-left: 2px solid #ed8b00;
}

    .otherResultItem h2,
    .otherResultItem h3 {
        margin: 0px;
    }

    .otherResultItem h5,
    .otherResultItem h6 {
        margin-bottom: 0px;
        padding: 0px;
    }

    .otherResultItem p {
        margin-bottom: 0px;
    }

.otherResultItemDetails {
    margin-right: -10px;
}

.otherResultItemHeadings {
    font-size: 12px;
    padding: 4px 8px;
    background: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#eeeeee));
    background: -moz-linear-gradient(top, #ffffff, #eeeeee);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee');
    border-bottom: 1px solid #cccccc;
}

    .otherResultItemHeadings h5 {
        margin-bottom: 0px;
    }

    .otherResultItemHeadings h6 {
        margin: 0px;
    }

.credits {
    font-size: 12px;
}

/* Page Title */
.pageWhiteTitleBar {
    /*background: #ffffff;*/
    margin-bottom: -10px;
}

/*.pageTitleNoTypeLabel
{
	padding: 8px 0px;
}*/

.pageTitleWithTypeLabel {
    margin-top: -35px;
    padding: 16px 0px;
}

.pageTitleWrapper {
    height: 48px;
    overflow: hidden;
    background-color: #e6e6e6;
    margin-bottom: 24px;
    margin-left: 0px;
}

.pageTitleOrangeBar {
    height: inherit;
    background: transparent url(/assets/healthtrust_portal/img/orange_bar_bkg.png) no-repeat right 50%;
    font-size: 11px;
    margin-left: 0px;
}

    .pageTitleOrangeBar h6 {
        display: inline-block;
        color: #fff;
    }

    .pageTitleOrangeBar strong {
        display: block;
        font-size: 14px;
    }

.pageTitleOrangeBarText {
    padding-left: 8px;
    display: table-cell;
    vertical-align: middle;
    height: inherit;
    color: #fff;
}

.pageTitleGrayBar {
    display: table-cell;
    background-color: #e6e6e6;
    height: inherit;
    color: #00335b;
}

    .pageTitleGrayBar h1 {
        color: #00335b;
        vertical-align: middle;
        display: table-cell;
        height: inherit;
        padding-right: 4px;
        font-size: 20px;
    }

.contentTitleMargin {
    margin-top: -10px;
}

/* LINKS */
a, .linkColor {
    color: #0069aa;
    cursor: pointer;
}

    a.detailLink {
        border-bottom: 2px solid transparent;
    }

    a:hover {
        border-bottom: 1px dotted #0069aa;
        padding-bottom: 1px;
        text-decoration: none;
    }

    a.logo:hover {
        border-bottom: none;
    }

    a.logout {
        color: #ed8b00;
    }

        a.logout:hover {
            color: #ffffff;
            border-color: #ffffff;
        }

    a.noUnderline:hover, a.collapseLink:hover, a.noUnderline.pull-right:hover {
        border-bottom: 1px solid transparent;
    }

    a.btn:hover {
        border-bottom: 1px solid transparent;
    }

    a.pull-right {
        border-bottom: 1px solid transparent;
        padding-bottom: 0px;
    }

        a.pull-right:hover {
            border-bottom: 1px dotted #0069aa;
            padding-bottom: 0px;
            text-decoration: none;
        }

    a.btn-large {
        text-align: center;
    }

        a.btn-large:hover {
            padding: 11px 19px;
            border-bottom: 1px solid transparent;
        }

    a.iconLink:hover {
        border-bottom-color: transparent;
        filter: alpha(opacity=65);
        -khtml-opacity: 0.65;
        -moz-opacity: 0.65;
        opacity: 0.65;
    }
    /* ALPHABET */
    a.btn.alphabet {
        padding: 5px 10px;
    }

        a.btn.alphabet:hover {
            border-bottom: 1px solid #dddddd;
        }

        a.btn.alphabet.blue {
            padding: 5px 10px;
            border: 0px;
        }

.alphabetActive {
    background-color: #dddddd;
}

.alphabetContent {
    margin: 20px;
}

    .alphabetContent td {
        padding: 2px 0px;
    }

a#passwordRequirements:hover {
    border-bottom: 1px solid transparent;
}

/* LABELS */
.miniLabel {
    font-size: 10px;
}

.smallLabel {
    margin-bottom: -10px;
    font-size: 11px;
}

    .smallLabel p {
        margin-bottom: 0px;
    }

label {
    cursor: default;
    margin-bottom: 0px;
}

label, input, button, select, textarea {
    font-size: 13px;
}

.label, .badge {
    text-shadow: none;
    font-weight: normal;
    padding: 1px 4px;
}

    .label label, .badge label {
        margin-bottom: 0px;
        font-size: 10px;
        font-weight: 900;
    }

    .label.super, .badge.super {
        vertical-align: super;
    }

.label-info,
.badge-info,
.label-info[href],
.badge-info[href] {
    /*background-color: #7BAFD4;
    -webkit-text-shadow: none;
    text-shadow: none;*/
}

.label-warning,
.badge-warning {
    background-color:#ed8b00;
    text-transform: none;
}

.label-edited {
    background-color: #fdf289;
}

.badge-notification {
    text-transform: none;
    display: inline-block;
    min-width: 10px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    background-color: #a94442;
    border-radius: 10px;
    border: none;
}

.label-primary {
     text-transform: none;
}

.badge-darkBlue, .label-darkBlue {
    background-color: #1b365d;
    text-transform: none;
}

/* WELLS */
.page-wrapper .page-content .page-row {
    margin-bottom: 30px;
}

.box.box-border-left {
    border-left: 5px solid #ed8b00;
}

.box.box-border-right {
    border-right: 5px solid #ed8b00;
}

.box {
    background: #f5f5f5;
    padding: 15px;
    margin-bottom: 24px;
}

    .box h6, .box h5 {
        margin-top: 0px;
        margin-bottom: 0px;
    }

    .box ul {
        list-style: none;
        margin-left: 0px;
    }

        .box ul.listWithBullets {
            list-style: circle;
            margin-left: 20px;
        }

.whiteBox {
    padding-bottom: 10px;
    border-bottom: 1px dotted #ddd;
    margin-bottom: 24px;
}

    .whiteBox h5, .whiteBox h6 {
        margin-bottom: 0px;
    }

.well {
    -ms-word-wrap: break-word;
    word-wrap: break-word;
}

    .well.contactCard {
        padding: 10px;
    }

        .well.contactCard h4 {
            /*text-transform: uppercase;*/
            margin: 0px;
        }

        .well.contactCard div {
            margin-bottom: 5px;
        }

.sideWell, .borderWell {
    margin-bottom: 24px;
    border: 1px solid #dddddd;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
}

    .sideWell td {
        padding: 0px 8px 10px 0px;
    }

    .sideWell h6 {
        padding: 8px 8px 0px;
    }

    .sideWell .date {
        background: #e8e8e8;
        text-align: center;
        margin: 4px 0px;
        padding: 0px 4px;
    }

    .sideWell a {
        font-size: 12px;
    }

p.sideWellText {
    padding-left: 8px;
    font-size: 12px;
}

.sideWellDivider {
    border-top: 1px dotted #dddddd;
    padding-bottom: 8px;
}

ul.sideWellUl {
    list-style: none;
    padding: 0px 8px;
}

    ul.sideWellUl li {
        margin-bottom: 8px;
    }

.infoWell {
    border-bottom: 1px solid #ed8b00;
    font-size: 12px;
    color: #666;
    line-height: 18px;
    padding-bottom: 16px;
    margin-bottom: 16px;
}

    .infoWell p {
        margin: 0;
    }

    .infoWell a {
        font-size: 14px;
    }

.smallWell {
    background-color: #ffffff;
    padding: 10px;
    font-size: 12px;
    margin-bottom: 20px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    -khtml-border-radius: 2px;
    border-radius: 2px;
}

.centerContent {
    padding-bottom: 24px;
    overflow: hidden;
}

    .centerContent h1,
    .centerContent h2 {
        margin-bottom: 8px;
    }

    .centerContent h3 {
        margin-bottom: 4px;
    }

    .centerContent ul, .centerContent ol {
        margin-left: 35px;
        margin-bottom: 16px;
    }

    .centerContent blockquote {
        margin-top: 10px;
        margin-left: 10px;
        border: none;
        background-image: url(../img/quote.png);
        background-repeat: no-repeat;
    }

        .centerContent blockquote, .centerContent blockquote p {
            font-size: 14px;
            font-style: italic;
            padding: 8px 20px 0px 20px;
            color: #000000;
        }

.landingPageWell {
    margin-bottom: 48px;
}

    .landingPageWell h2 {
        padding-bottom: 4px;
    }

    .landingPageWell p {
        margin-bottom: 10px;
    }

    .landingPageWell a {
        font-size: 12px;
    }

.centerArtifacts {
    margin-bottom: 24px;
    overflow: hidden;
}

    .centerArtifacts .accordion {
        margin-bottom: 0px;
    }

    .centerArtifacts h3 {
        border-bottom: 1px solid #cccccc;
        padding-bottom: 4px;
    }

    .centerArtifacts .date {
        text-align: right;
    }

    .centerArtifacts p.sideWellText {
        padding-left: 0px;
        font-size: 12px;
    }

.artifactsHeader, .contractLinkSection h3 {
    border-bottom: 1px solid #cccccc;
    padding-bottom: 4px;
}

.centerArtifacts .date {
    text-align: right;
}

.centerArtifacts p.sideWellText {
    padding-left: 0px;
    font-size: 12px;
}

.artifactLinks td {
    padding: 0px 8px 10px 0px;
}

    .artifactLinks td.mimeIcon {
        padding: 3px 0px 0px 0px;
    }

    .artifactLinks td a {
        font-size: 12px;
    }

.matrixLinks td.mimeIcon img {
    padding: 4px 0px 4px 4px;
}

.matrixLinks td.newsLink {
    padding: 4px;
}

.relatedNewsWell, .blockHeaderSideWell {
    margin-bottom: 24px;
}

.relatedNewsWellHeader, .blockHeader {
    background: #e8e8e8;
    padding: 10px 8px;
    -moz-border-radius: 4px 4px 0px 0px;
    -webkit-border-radius: 4px 4px 0px 0px;
    border-radius: 4px 4px 0px 0px;
    overflow: hidden;
}

    .relatedNewsWellHeader h4, .blockHeader h4 {
        color: #666666;
        text-transform: none;
        display: inline-block;
        font-family: Verdana, Geneva, 'DejaVu Sans', sans-serif;
        letter-spacing: normal;
        font-weight: bold;
        font-size: 14px;
        margin: 0;
    }

.relatedNewsWellBody, .blockHeaderSideWellBody {
    font-size: 12px;
    padding: 0px 4px;
}

ul.relatedNewsWellBody {
    list-style: none;
}

.blockHeaderSideWellBody ul {
    list-style: none;
}

.blockHeaderSideWellBody li {
    padding: 10px 8px;
    border-bottom: 1px dotted #cccccc;
}

.relatedNewsWellBody p {
    margin-bottom: 0px;
}

.relatedNewsWellBody .relatedContracts {
    padding: 10px 4px;
}

.relatedContractsHeader {
    font-weight: bold;
}

.relatedNewsWellBody .noMimeLink {
    margin: 10px 4px 8px;
}

.relatedNewsWellBody td.mimeIcon {
    padding-left: 0px;
}

.relatedNewsWellBody .mimeIcon img {
    padding: 10px 0px 10px 4px;
}

.relatedNewsWellBody .newsLink, .blockHeaderSideWellBody .newsLink {
    padding: 10px 4px 10px 4px;
}

.relatedNewsWellBody .dottedBorder, .blockHeaderSideWellBody .dottedBorderf {
    border-bottom: 1px dotted #cccccc;
    margin: 0px;
}

.relatedNewsWell p.sideWellText {
    padding: 5px 8px;
    font-size: 12px;
}

.contractListing .relatedNewsWell {
    margin-bottom: 10px;
}

.externalWell .well {
    -moz-border-radius: 0px 0px 4px 4px;
    -webkit-border-radius: 0px 0px 4px 4px;
    border-radius: 0px 0px 4px 4px;
}

.externalWellHeading {
    color: #ffffff;
    border: solid 1px #1b365d;
    background-color: #1b365d;
    background: -webkit-gradient(linear, left top, left bottom, from(#27518c), to(#1b365d));
    background: -moz-linear-gradient(top, #27518c, #1b365d);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#27518c', endColorstr='#1b365d');
    -moz-border-radius: 4px 4px 0px 0px;
    -webkit-border-radius: 4px 4px 0px 0px;
    border-radius: 4px 4px 0px 0px;
    padding: 0px 10px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.05);
    -moz-box-shadow: inset 0 1px 1px rgba(0,0,0,.05);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.05);
}

    .externalWellHeading h1 {
        color: #ffffff;
    }

.well.formWell {
    padding: 19px 10px;
}

.smallGreyContainer, .datesContainer {
    background-color: #e8e8e8;
    padding: 0px 4px;
    text-align: left;
    margin-bottom: 24px;
    text-align: center;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

    .smallGreyContainer .titles, .datesContainer .titles {
        text-align: right;
        display: inline-block;
    }

    .smallGreyContainer .info, .datesContainer .dates {
        display: inline-block;
        padding-left: 2px;
        text-align: left;
        font-size: 12px;
    }

    .smallGreyContainer h6, .datesContainer h6 {
        margin: 0px;
    }

        .datesContainer h6.expiresDate {
            color: #bd362f;
        }

    .smallGreyContainer p, .datesContainer p {
        margin-bottom: 0px;
    }

p.contractDateRange {
    margin-bottom: 0px;
}

.greyHeaderBorderedWell {
    border: 1px solid #eee;
    margin-bottom: 24px;
    border-radius: 4px;
}

    .greyHeaderBorderedWell .greyHeader {
        background-color: #eee;
        padding: 6px;
        border-radius: 4px 4px 0px 0px;
    }

        .greyHeaderBorderedWell .greyHeader h4 {
            margin: 0px;
            padding: 0px;
            text-transform: uppercase;
            color: #333;
        }

    .greyHeaderBorderedWell .greyBorderedBody {
        padding: 8px;
    }

        .greyHeaderBorderedWell .greyBorderedBody p {
            margin-bottom: 0px;
        }

            .greyHeaderBorderedWell .greyBorderedBody p span {
                margin-top: 4px;
            }


/* TEASERS */
.teaserContainer {
    display: block;
    margin-bottom: 24px;
    overflow: hidden;
}

    .teaserContainer img {
        float: left;
        margin-right: 10px;
        margin-bottom: 28px;
        max-width: 150px;
    }

    .teaserContainer p {
        display: block;
        margin-bottom: 8px;
    }

    .teaserContainer.marketingTeaser p {
        margin-bottom: 8px;
    }

.largeMarketingTeaser {
    background-color: #e3e3e3;
    padding: 10px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    overflow: hidden;
}

    .largeMarketingTeaser .teaserContainer {
        margin-bottom: 0px;
    }

    .largeMarketingTeaser h2 {
        font-size: 18px;
        font-weight: normal;
    }

.smallMarketingTeaser {
    padding: 10px;
    margin-bottom: 24px;
    background-image: url(../img/shattered.png);
    background-repeat: no-repeat;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    overflow: hidden;
}

    .smallMarketingTeaser p {
        color: #000000;
    }

    .smallMarketingTeaser .teaserContainer {
        margin-bottom: 0px;
    }

.smallSingleTeaser {
    /*padding: 4px 8px;*/
    background-color: #fefefe;
    overflow: hidden;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
}

    .smallSingleTeaser h2 {
        margin-bottom: 0px;
        margin-top: 0px;
    }

    .smallSingleTeaser p {
        padding: 0px 8px;
    }

.teaserContainer .smallSingleTeaser p {
    margin-bottom: 0px;
}

/* ACCORDION */
#distAccordion .accordion, #myDistAccordion .accordion {
    margin-bottom: 24px;
}

#distAccordion .according-group, #myDistAccordion .accordion-group {
    border: 1px solid #e6e6e6;
}

#distAccordion .accordion-heading, #myDistAccordion .accordion-heading {
    font-weight: bold;
    text-transform: uppercase;
    background: #e6e6e6;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e6e6e6));
    background: -moz-linear-gradient(top, #ffffff, #e6e6e6);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e6e6e6');
}

    #distAccordion .accordion-heading a, #myDistAccordion .accordion-heading a {
        color: #333333;
    }

        #myDistAccordion .accordion-heading a:hover {
            border-bottom: none;
            cursor: default;
        }

        #distAccordion .accordion-heading a:hover {
            border-bottom: 0px;
            background: #ffffff;
        }

        #distAccordion .accordion-heading a.active, #myDistAccordion .accordion-heading a.active {
            background-color: #ffffff;
            /*color: #ffffff;
			border: 1px solid #7bafd4;
			-moz-border-radius: 4px 4px 0px 0px;
			-webkit-border-radius: 4px 4px 0px 0px;
			border-radius: 4px 4px 0px 0px;
			background: #7bafd4;
			background: -webkit-gradient(linear, left top, left bottom, from(#acd7f6), to(#7bafd4));
			background: -moz-linear-gradient(top, #acd7f6, #7bafd4);
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#acd7f6', endColorstr='#7bafd4');*/
        }

#distAccordion .accordion-inner img {
    float: left;
    margin-right: 10px;
    margin-bottom: 10px;
    max-width: 150px;
}

#distAccordion .accordion-inner h4 {
    margin-top: 0px;
    margin-bottom: 5px;
}


.collapse-open {
    background: url(../images/arrow_on.png) center center no-repeat;
    display: block;
    float: right;
    padding: 10px;
}

.collapse-close {
    display: block;
    float: right;
    background: url(../images/arrow_off.png) center center no-repeat;
    padding: 10px;
}

/* HIGHLIGHTED PROGRAMS */
#highlightedProgram h2 {
    margin-bottom: 8px;
}

#highlightedProgram h3 {
    text-align: center;
    margin-top: 4px;
}

#highlightedProgram img {
    max-height: 140px;
}

/* LISTS */
dl {
    margin-top: 5px;
}

dd {
    margin-bottom: 8px;
}

ul#linkBullets {
    margin-left: 18px;
}

#linkBullets li span {
    margin-left: -5px;
}

ul.tableList {
    margin-left: 15px;
}

ul#pendingListApproval {
    list-style-type: none;
}

#pendingListApproval li {
    padding-top: 10px;
}

/* IMAGES */
.contentIcon {
    float: left;
    margin: 20px 8px 8px 0px;
}

/* ICONS */
td.mimeIcon {
    width: 20px;
    padding: 3px 0px 0px 8px;
    vertical-align: top;
    text-align: left;
}

    td.mimeIcon a {
        display: block;
        border-bottom: 1px solid transparent;
    }

        td.mimeIcon a:hover {
            border-bottom: 0px;
        }

.noMimeLink {
    margin-left: 8px;
}

td.iconHover a:hover {
    padding-bottom: 0px;
}

/* INDICATORS */
.sortArrow {
    font-size: 10px;
}

.statusIndicator {
    padding: 5px 0px 8px;
}

.ajaxImage {
    padding-top: 100px;
    text-align: center;
}

/* PADDINGS & MARGINS */
.paddingLeft0 {
    padding-left: 0px;
}

.paddingLeft8 {
    padding-left: 8px;
}

.paddingLeft15 {
    padding-left: 15px;
}

.paddingRight0 {
    padding-right: 0px;
}

.paddingRight15 {
    padding-right: 15px;
}

.paddingRight25 {
    padding-right: 25px;
}

.paddingTop8 {
    padding-top: 8px;
}

.paddingTop10 {
    padding-top: 10px;
}

.paddingBottom5 {
    padding-bottom: 5px;
}

.paddingBottom10 {
    padding-bottom: 10px;
}

.marginLeft0 {
    margin-left: 0;
}

.marginLeft5 {
    margin-left: 5px;
}

.marginLeft8 {
    margin-left: 8px;
}

.marginLeft10 {
    margin-left: 10px;
}

.marginLeft20 {
    margin-left:20px;
}

.marginLeft40 {
    margin-left: 40px;
}

.marginRight0 {
    margin-right: 0;
}

.marginRight2 {
    margin-right: 2px;
}

.marginRight4 {
    margin-right: 4px;
}

.marginRight10 {
    margin-right: 10px;
}

.marginTop0 {
    margin-top: 0px;
}

.marginTop4 {
    margin-top: 4px;
}

.marginTop5 {
    margin-top: 5px;
}

.marginTop10 {
    margin-top: 10px;
}

.marginTop15 {
    margin-top: 15px;
}

.marginTop20 {
    margin-top: 20px;
}

.marginTop30 {
    margin-top: 30px;
}

.marginBottom0 {
    margin-bottom: 0px;
}

.marginBottom2 {
    margin-bottom: 2px;
}

.marginBottom5 {
    margin-bottom: 5px;
}

.marginBottom8 {
    margin-bottom: 8px;
}

.marginBottom10 {
    margin-bottom: 10px;
}

.marginBottom15 {
    margin-bottom: 15px;
}

.marginBottom20 {
    margin-bottom: 20px;
}

.marginBottom30 {
    margin-bottom: 30px;
}

.poBladeMarginTop30 {
    margin-top: 30px;
}

/*BUTTONS*/
.btnWrapper {
    padding: 16px 0;
}

.btn {
    padding: 4px 16px;
    font-weight: normal;
    margin: 2px;
    -webkit-text-shadow: none !important;
    text-shadow: none !important;
    border: 1px solid #dddddd;
    background: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e6e6e6));
    background: -moz-linear-gradient(top, #ffffff, #e6e6e6);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e6e6e6');
    white-space: normal;
}

    .btn:hover, .btn:focus {
        padding: 4px 16px;
        background: #e6e6e6;
        background: -webkit-gradient(linear, left top, left bottom, from(#e6e6e6), to(#ffffff));
        background: -moz-linear-gradient(top, #e6e6e6, #ffffff);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e6e6e6', endColorstr='#ffffff');
    }

    .btn[disabled], .btn[disabled]:hover, .btn.large[disabled], .btn.small[disabled], .submit-button.btn.disabled, .submit-button.btn[disabled] {
        cursor: not-allowed;
        background: #e6e6e6;
        border: 1px solid #dddddd;
        background-image: none;
        filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
        filter: alpha(opacity=65);
        -khtml-opacity: 0.65;
        -moz-opacity: 0.65;
        opacity: 0.65;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

    .btn .btn-large {
        padding: 11px 19px;
        font-size: 17.5px;
        -webkit-border-radius: 6px;
        -moz-border-radius: 6px;
        border-radius: 6px;
    }

button.btn.btn-mini, input[type="submit"].btn.btn-mini, .btn-mini:focus {
    padding: 0 6px;
}

.createBtnContainer {
    margin-left: 0px;
}

input.searchMaxWidth {
    max-width: 200px;
}

select.searchMaxWidth {
    max-width: 210px;
}

.buttonContainer {
    border-top: 1px solid #dddddd;
    padding-top: 16px;
}

.btn-group > .btn, .btn-group > .dropdown-menu {
    font-size: 12px;
}

a.phoneMenu, a.phoneMenu:hover {
    padding: 5px 8px;
}

.createButton {
    /*margin-bottom: 24px;*/
    margin-top: -50px;
}

.btn-mini, .btn-mini:hover {
    padding: 0 6px;
}

.btn-link {
    background: none;
    border-color: transparent;
    padding: 0px 0px 1px 0px;
    color: #0069aa;
    font-family: Verdana, Helvetica, Sans-Serif;
}

    .btn-link:hover,
    .btn-link:active,
    .btn-link:focus {
        background: none;
        border-bottom: 1px dotted #0069aa;
        padding: 0px 0px 1px 0px;
        text-decoration: none;
        color: #0069aa;
    }

button.btn.btn-link.btn-mini,
button.btn.btn-link.btn-mini:hover {
    padding: 0px;
}

.expandCollapse {
    margin-bottom: 10px;
}

/* COLORS */
/*flatDarkBlue*/
.flatDarkBlue {
    color: #ffffff;
    border: solid 1px #000000;
    background-color: #00335b;
    background: -webkit-gradient(linear, left top, left bottom, from(#00335b), to(#00335b));
    background: -moz-linear-gradient(top, #00335b, #00335b);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00335b', endColorstr='#00335b');
}

.btn.flatDarkBlue:hover, .btn.flatDarkBlue:focus {
    color: #ffffff;
    border: solid 1px #000000;
    background-color: #00335b;
    background: -webkit-gradient(linear, left top, left bottom, from(#000000), to(#000000));
    background: -moz-linear-gradient(top, #000000, #000000);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#000000', endColorstr='#000000');
}

/*flatGray*/
.flatGray {
    color: #0069aa;
    border: solid 1px #ffffff;
    background-color: #E6E6E6;
    background: -webkit-gradient(linear, left top, left bottom, from(#E6E6E6), to(#E6E6E6));
    background: -moz-linear-gradient(top, #E6E6E6, #E6E6E6);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#E6E6E6', endColorstr='#E6E6E6');
}

.btn.flatGray:hover, .btn.flatGray:focus {
    color: #0069aa;
}

.btn.flatGray:active {
    color: #ffffff;
    background-color: #E6E6E6;
    background: -webkit-gradient(linear, left top, left bottom, from(#E6E6E6), to(#E6E6E6));
    background: -moz-linear-gradient(top, #E6E6E6, #E6E6E6);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#E6E6E6', endColorstr='#E6E6E6');
}

.btn.flatGray[disabled], .btn.flatGray[disabled]:hover, .btn.large.flatGray[disabled], .btn.small.flatGray[disabled], .submit-button.btn.flatGray.disabled, .submit-button.btn.flatGray[disabled] {
    cursor: not-allowed;
    color: #aaaaaa;
    background: #e6e6e6;
    border: 1px solid #ffffff;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
/*blue*/
.blue {
    color: #ffffff;
    border: solid 1px #1b365d;
    background-color: #1b365d;
    background: -webkit-gradient(linear, left top, left bottom, from(#27518c), to(#1b365d));
    background: -moz-linear-gradient(top, #27518c, #1b365d);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#27518c', endColorstr='#1b365d');
}

.btn.blue:hover, .btn-large.blue:hover, .btn.blue:focus, .btn.blue:active, .btn-large.blue:active {
    color: #ffffff;
    background-color: #27518c;
    background: -webkit-gradient(linear, left top, left bottom, from(#1b365d), to(#27518c));
    background: -moz-linear-gradient(top, #1b365d, #27518c);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1b365d', endColorstr='#27518c');
}

.btn.blue[disabled], .btn.blue[disabled]:hover, .btn.large.blue[disabled], .btn.small.blue[disabled], .submit-button.btn.blue.disabled, .submit-button.btn.blue[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #1b365d;
    border: 1px solid #1b365d;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
/*orange*/
.orange {
    color: #ffffff;
    border: solid 1px #DE6924;
    background-color: #ed8b00;
    background: -webkit-gradient(linear, left top, left bottom, from(#ed8b00), to(#DE6924));
    background: -moz-linear-gradient(top, #ed8b00, #DE6924);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ed8b00', endColorstr='#DE6924');
}

.btn.orange:hover, .btn-large.orange:hover, .btn.orange:focus, .btn.orange:active, .btn-large.orange:active {
    color: #ffffff;
    background-color: #DE6924;
    background: -webkit-gradient(linear, left top, left bottom, from(#DE6924), to(#ed8b00));
    background: -moz-linear-gradient(top, #DE6924, #ed8b00);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DE6924', endColorstr='#ed8b00');
}

a.btn.orange:hover {
    border-bottom: 1px solid #DE6924;
}

.btn.orange[disabled], .btn.orange[disabled]:hover, .btn.large.orange[disabled], .btn.small.orange[disabled], .submit-button.btn.orange.disabled, .submit-button.btn.orange[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #ed8b00;
    border: 1px solid #DE6924;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.jurisdictionAddButton {
    float: right;
    margin-right: 10px;
}

.deleteAccountButton {
    margin-left: 0px;
}
/*red*/
.red {
    color: #ffffff;
    border: 1px solid #bd362f;
    background: #bd362f;
    background: -webkit-gradient(linear, left top, left bottom, from(#ee5f5b), to(#bd362f));
    background: -moz-linear-gradient(top, #ee5f5b, #bd362f);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ee5f5b', endColorstr='#bd362f');
}

.btn.red:hover, .btn.red:focus, .btn.red:active {
    color: #ffffff;
    background: #ee5f5b;
    background: -webkit-gradient(linear, left top, left bottom, from(#bd362f), to(#ee5f5b));
    background: -moz-linear-gradient(top, #bd362f, #ee5f5b);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#bd362f', endColorstr='#ee5f5b');
}

.btn.red[disabled], .btn[disabled].red:hover, .btn.large.red[disabled], .btn.small.red[disabled] /*.halfRoundBtn.gray:hover,*/ {
    cursor: not-allowed;
    color: #eeeeee;
    background: #ee5f5b;
    border: 1px solid #ee5f5b;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
/*green*/
.green {
    color: #ffffff;
    border: 1px solid #51a351;
    background: #51a351;
    background: -webkit-gradient(linear, left top, left bottom, from(#62c462), to(#51a351));
    background: -moz-linear-gradient(top, #62c462, #51a351);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#62c462', endColorstr='#51a351');
}

.btn.green:hover, .btn.green:focus, .btn.green:active {
    color: #ffffff;
    background: #62c462;
    background: -webkit-gradient(linear, left top, left bottom, from(#51a351), to(#62c462));
    background: -moz-linear-gradient(top, #51a351, #62c462);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#51a351', endColorstr='#62c462');
}

.btn.green[disabled], .btn[disabled].green:hover, .btn.large.green[disabled], .btn.small.green[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #62c462;
    border: 1px solid #62c462;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}
/*black*/
.black {
    color: #ffffff;
    border: 1px solid #555555;
    background: #000000;
    background: -webkit-gradient(linear, left top, left bottom, from(#555555), to(#444444));
    background: -moz-linear-gradient(top, #555555, #444444);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#555555', endColorstr='#444444');
}

.btn.black:hover, .btn.black:focus, .btn.black:active {
    color: #ffffff;
    background: #444444;
    background: -webkit-gradient(linear, left top, left bottom, from(#444444), to(#555555));
    background: -moz-linear-gradient(top, #444444, #555555);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#444444', endColorstr='#555555');
}

.btn.black[disabled], .btn[disabled].black:hover, .btn.large.black[disabled], .btn.small.black[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #444444;
    border: 1px solid #444444;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

/* THUMBNAILS */
a.thumbnail:hover {
    border-color: #005580;
    border-bottom: 1px solid #005580;
    padding-bottom: 4px;
}

#hightlightedProgram h2 {
    margin: 0px 0px 8px;
}

#hightlightedProgram .thumbnails li {
    text-align: center;
    margin-bottom: 24px;
}

#hightlightedProgram .thumbnails h3 {
    padding: 5px;
}

/* MODALS */
.blade .modal-header {
    position: absolute;
    top: 0;
    width: 95%;
}

.modal-header h3 {
    font-size: 20px;
    color: #fff;
}

.modal.facilitySearchModal {
    width: 750px;
    margin-left: -380px;
}

/* FORMS */
.assigningRoles .control-group {
    margin-bottom: -5px;
}

.form-horizontal .control-label {
    font-weight: normal;
    padding-top: 0px;
}

.form-actions {
    background: #e8e8e8;
    border-top: 1px solid #d5d5d5;
    margin-top: 40px;
}

.formDivider {
    border-top: 1px solid #00335b;
    margin-bottom: 16px;
    margin-top: 6px;
}

h2.formHeader {
    margin-left: 15px;
}

.groupedCheckbox .control-group {
    margin-bottom: -5px;
}

.roleGroupControl {
    margin-top: 20px;
}

.form-horizontal.staticInfoForm .control-group {
    margin-bottom: 10px;
}

.controls label, .form-control-static {
    font-weight: bold;
}

    .controls label.checkbox, .controls label.radio {
        font-weight: normal;
    }

.controls .negMarginBottom10 {
    margin-bottom: -10px;
}

.control-group.error .notRequired {
    color: #555555;
    background-color: #ffffff;
    border: 1px solid #cccccc;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border linear 0.2s, box-shadow linear 0.2s;
    -moz-transition: border linear 0.2s, box-shadow linear 0.2s;
    -o-transition: border linear 0.2s, box-shadow linear 0.2s;
    transition: border linear 0.2s, box-shadow linear 0.2s;
}

    .control-group.error .notRequired:focus {
        border-color: rgba(82, 168, 236, 0.8);
        outline: 0;
        outline: thin dotted \9;
        -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
        -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 8px rgba(82, 168, 236, 0.6);
    }

.control-label span.required {
    font-weight: bold;
    color: #b94a48;
}

span.required:before {
    content: ' ';
}

.control-group.error .control-label,
.control-group.error .help-block,
.control-group.error .help-inline,
.control-group.error span.required {
    color: #b94a48;
}

.phoneExtension {
    float: left;
    margin-left: 2px;
    margin-top: 4px;
}

input[disabled].primaryLocation,
input[readonly].primaryLocation {
    background-color: #fff;
}

form#vendorSearchForm {
    margin-bottom: 0px;
}

.regRoles {
    padding-bottom: 8px;
}

/* TABS */
/*sidemenu*/
#sideMenu {
    margin-bottom: 24px;
}

.nav-tabs.nav-stacked > li > a {
    border-left: 0px;
    border-right: 0px;
    border-top: 1px solid #DEE0E4;
    border-bottom: 1px solid #DEE0E4;
    font-family: open_sansregular, sans;
    text-transform: uppercase;
    font-size: 12px;
    color: #768692;
}

    .nav-tabs.nav-stacked > li > a:hover,
    .nav-tabs.nav-stacked > li > a:focus,
    .nav-tabs.nav-stacked > li > a.active {
        color: #1B365D;
        background-color: #DEE0E4;
        background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#dee0e4));
        background: -moz-linear-gradient(top, #ffffff, #dee0e4);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#dee0e4');
    }

.nav-tabs.nav-stacked > li:first-child > a {
    -webkit-border-top-right-radius: 0px;
    -moz-border-radius-topright: 0px;
    border-top-right-radius: 0px;
    -webkit-border-top-left-radius: 0px;
    -moz-border-radius-topleft: 0px;
    border-top-left-radius: 0px;
}

.nav-tabs.nav-stacked li.active a,
.nav-tabs.nav-stacked li.active a:hover {
    cursor: pointer;
    color: #1B365D;
    border-left: 0px;
    border-right: 0px;
    background: #DEE0E4;
    background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(222,224,228,1) 100%);
    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(255,255,255,1)), color-stop(100%,rgba(222,224,228,1)));
    background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
    background: -o-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
    background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
    background: linear-gradient(to bottom, rgba(255,255,255,1) 0%,rgba(222,224,228,1) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#ffffff', endColorstr='#dee0e4',GradientType=0 );
}

#userMgmt.nav-tabs > li > a, #userMgmt.nav-pills > li > a {
    padding-right: 0px;
    padding-left: 20px;
    margin-right: 2px;
    line-height: 14px;
}

.searchTab {
    margin-right: 20px;
}

#userMgmt.nav-tabs > .active > a, #userMgmt.nav-tabs > .active > a:hover {
    border: none;
    border-right: 1px solid #ccc;
    border-left: 1px solid #ccc;
    border-top: 3px solid #ed8b00;
}

.overTitleLabel {
    font-size: 11px;
    font-family: Verdana, Helvetica, Sans-Serif;
    color: #ed8b00;
    text-transform: uppercase;
}

.borderedTabsContainer .tab-content {
    border: 1px solid #eee;
    margin-top: -21px;
    padding: 8px;
}

.nav-tabs.bordered {
    background: #fff;
    border: 1px solid #eee;
}

    .nav-tabs.bordered.header {
        background-color: #eee;
        border-radius: 4px 4px 0px 0px;
    }

        .nav-tabs.bordered.header h4 {
            margin: 0px;
            padding: 6px;
            text-transform: uppercase;
            color: #333;
        }

    .nav-tabs.bordered li a {
        padding: 4px 8px;
        margin-top: -1px;
        border-top: 4px solid transparent;
    }

        .nav-tabs.bordered > .active > a,
        .nav-tabs.bordered > .active > a:hover,
        .nav-tabs.bordered > .active > a:focus,
        .nav-tabs.bordered li a:hover {
            background-color: #fff;
            border-top: 4px solid orange;
            border-bottom: 1px solid transparent;
        }


/* TABLES */
table {
    width: 100%;
}

.table th {
    border-top: none;
    background-color: #eee;
}

.table-hover tbody tr:hover > th {
    background-color: transparent;
}

.table-hover tbody tr:hover > td {
    background-color: #dddddd;
}

.sortedColumn {
    background-color: #f0f8ff;
}

.striped {
    background-color: #f8f8f8;
}

th.noSort:hover {
    cursor: default;
}

.tablesorter-bootstrap .tablesorter-header {
    font-size: 13px;
    font-family: Tahoma, Helvetica, Sans-Serif;
    cursor: pointer;
}

.tablesorter-bootstrap .tablesorter-header-inner {
    float: left;
}

.tablesorter-bootstrap .tablesorter-header, .tablesorter-bootstrap tfoot th, .tablesorter-bootstrap tfoot td {
    padding: 4px;
    margin: 0 0 18px;
    background-color: #eee;
}

.pagesize {
    margin-bottom: 0px;
}

.float-right {
    float: right;
}

.overflow-hidden {
    overflow: hidden;
}

#itemGrid ul.tier {
    list-style: none;
    margin: 0;
}

tr.tablesorter-childRow td {
    padding: 0px;
}

.table-hover tbody tr.tablesorter-childRow:hover > td {
    background-color: transparent;
}

.tablesorter-childRow td ul.nav-tabs {
    margin: 8px 8px 0px 8px;
}

.tablesorter-childRow .tab-content {
    margin: 0px 8px 8px;
    padding: 8px;
    background-color: white;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}

tr.detailsRow td {
    padding: 8px;
}


/* ELEMENTS */
input, a {
    outline: none;
}

.clear {
    clear: both;
}

select, textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
    font-family: Verdana, Helvetica, Sans-Serif;
    font-size: 13px;
    padding: 4px 6px;
}

.input-append select, .input-append textarea, .input-append input[type="text"], .textarea-append textarea {
    font-size:13px;
padding: 4px;
}

/*input[type="radio"][disabled], input[type="checkbox"][disabled]
{
	margin-top: -3px;
}*/

#classOfTrade .dropdown-menu {
    margin-top: -10px;
    width: 100%;
}

.dropdown input[type="checkbox"] {
    margin-left: 0px;
    margin-right: 5px;
}

.select-mini {
    font-size: 11px;
    height: 25px;
    width: 100px;
    padding: 2px;
}

.textarea-append {
    position:relative;
    display:inline-block;
}

.textarea-controls {
    position:absolute;
    top:0px;
    right:0px;
}

label.error {
    color: #b94a48;
    margin-top: -10px;
}

.inputGroup {
    display: inline-block;
    padding: 3px 4px;
    border: 1px solid transparent;
    -moz-border-radius: 7px;
    border-radius: 7px;
    margin-left: 0px;
    width: 98%;
}

.validationSuccess {
    padding: 10px 5px;
    margin-bottom: 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    word-wrap: break-word;
}

#externalForm .validationSuccess {
    margin-left: -19px;
    margin-right: -19px;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
    word-break: break-word;
}

.errorField {
    border-color: #b94a48;
    color: #b94a48;
}

.reportTags {
    display: inline-flex;
}

span.ValidationErrors {
    display: inline-block;
    font-size: 12px;
    color: #D00;
    padding-left: 10px;
    font-style: italic;
}

.validation-summary-errors {
    color: #b94a48;
    background-color: #f2dede;
    border-color: #eed3d7;
    padding: 10px 5px;
    margin-bottom: 20px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    word-wrap: break-word;
}

    .validation-summary-errors ul li {
        word-break: break-word;
    }

#externalForm .validation-summary-errors {
    margin-left: -19px;
    margin-right: -19px;
    -webkit-border-radius: 0px;
    -moz-border-radius: 0px;
    border-radius: 0px;
}

    #externalForm .validation-summary-errors ul {
        margin-left: 20px;
    }

#externalForm .input-validation-error {
    margin-bottom: 0px;
}

.field-validation-error {
    color: #b94a48;
}


/* ALERTS */
.securityAlert {
    padding: 8px 20px 8px 20px;
    -ms-word-wrap: break-word;
    word-wrap: break-word;
}

.alert-info h3 {
    font-family: open_sanssemibold;
    color: #3a87ad;
    margin-bottom: 8px;
}

.alert-info p {
    margin-bottom: 8px;
}

.alertBorder {
    border-bottom: 1px dotted #bbb;
}

/* CALLOUTS */
.callout {
    padding: 10px;
    border-left: 8px solid #ed8b00;
    margin-bottom: 20px;
    background-color: #f3f3f3;
}

    .callout p {
        margin-bottom: 0px;
    }

/* MISC */
.hideDiv {
    display: none;
}

.showDiv {
    display: block;
}

.inputWidth96 {
    width: 96%;
}

.width100 {
    width: 100%;
}

hr.bottom {
    margin-top: 30px;
}

.dottedBorder {
    border-bottom: 1px dotted #cccccc;
    margin-bottom: 10px;
}

#throbber {
    display: none;
    text-align: right;
    margin: -11px 0px -10px;
}

    #throbber img {
        margin-top: 0px;
        margin-bottom: 0px;
    }

.inline {
    display: inline;
}

.inlineBlock {
    display: inline-block;
}

select {
    padding: 5px;
}

legend {
    border-bottom: 0px;
}

.help-block {
    font-size: 12px;
}

.verticalAlignMiddle {
    vertical-align: middle;
}

.verticalAlignTop {
    vertical-align: top;
}

.topBorderBox {
    padding: 10px;
    border-top: 1px solid #E6E6E6;
    margin-bottom: 20px;
}

.hLineDivider {
    border-top: 1px solid white;
    padding: 5px 0px;
    margin-top: 5px;
    margin-left: 0px;
}

.dropDownWidth {
    width: 200px;
    margin-right: 2px;
}

.dropDownPadding {
    padding: 6px 6px 6px 0px;
}

.fullWidth {
    width: 95%;
}

.dso-validation-modal-footer {
    padding: 5px 20px;
    margin-bottom: 5px;
}

.form-group2{
    display: inline-flex;
}

/*
------------------------------------------------------------------------------
------------------------------------------------------------------------------
*/
@media (max-width: 430px) {
    #iconTypeBillOnly {
        float: right;
    }
}

@media (max-width: 480px) {
    .span1.phoneSpan1 {
        display:none;
    }

    .input-prepend input {
        width: 83%;
    }

    .control-group .roles {
        margin-right: 0px;
        margin-left: 0px;
    }

    .form-horizontal .control-group {
        margin-bottom: 0px;
    }

        .form-horizontal .control-group .help-inline {
            margin-bottom: 10px;
        }

    .control-group.rolesPhoneView {
        margin-bottom: 20px;
    }

    #advancedSearch {
        margin-top:10px;
    }

    .advancedSearchLink {
        margin-right:18px;
    }

    .stripeWelcome {
        color:#333;
        text-align:left;
        padding-top:4px;
    }
    a.btn.alphabet {
        padding: 4px 7px;
    }
    .center {
        text-align: center;
    }
}

@media (min-width: 480px) and (max-width: 768px) {
    h3.error404 {
        font-size: 38px;
    }

    a.phoneMenu, a.phoneMenu:hover {
        margin-top: -134px;
    }

   
}

@media (max-width: 767px) {

    
    .col-xs-offset-2 {
        margin-left: 0%
    }

    .reqTypes{
        margin-bottom: 10px;
    }

    #billOnlyFormGroup{
        max-width: none;
    }
    #throbber img {
        padding-right: 10px;
    }

    p.error404 {
        padding: 0px 10px;
    }

    .securityTextBoxPrepend {
        width: 250px;
    }

    #pageslide {
        width: 220px;
    }

    .modal.facilitySearchModal {
        width: auto;
        margin: 0;
    }

    .poBladeMarginTop30 {
        margin-top: 5px;
    }
}

@media (max-width:990px) {
.attestation-modal-footer {
}
}

@media (max-width:1024px) {
    input[disabled].facilitySearch,
    input[readonly].facilitySearch {
        width: 158px;
    }

    #advancedSearch {
        margin-top:10px;
    }

    .advancedSearchArrow {
        left:90%;
    }

    .advancedSearchLink {
        margin-top:0px;
        margin-right:0px;
    }
    #throbber img {
        margin:8px 0px 10px;
    }
}

.table-striped>tbody>tr:nth-child(even) {
background-color: #f3f3f3;
}


.dropdown-menu>li>a:hover, .dropdown-submenu:hover>a, .dropdown-submenu:focus>a {
color: #fff;
text-decoration: none;
background-color: #0081c2;
background-image: -moz-linear-gradient(top,#08c,#0077b3);
background-image: -webkit-gradient(linear,0 0,0 100%,from(#08c),to(#0077b3));
background-image: -webkit-linear-gradient(top,#08c,#0077b3);
background-image: -o-linear-gradient(top,#08c,#0077b3);
background-image: linear-gradient(to bottom,#08c,#0077b3);
background-repeat: repeat-x;
filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc',endColorstr='#ff0077b3',GradientType=0);
}

.dropdown-menu>li>a:focus
{
background-color: transparent;
}

.department-badge {
    padding: 1px 5px;
    margin-bottom: 4px;
}

.department-badge-close {
    margin-left: 3px;
    margin-top: 3px;
    cursor: pointer;
}
