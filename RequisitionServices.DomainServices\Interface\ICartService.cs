﻿using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Cart.Responses;
using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.DomainServices.Interface
{
    public interface ICartService
    {
        Cart GetCart(CartRequest request);
        CartAttributesResponse GetAttributes(CartRequest request);
        CartAttributesResponse GetPOUAttributes(CartRequest request);
        bool CartItemExists(CartItemExistsRequest request);
        AddToCartResponse AddToCart(CartAddItemRequest request);
        AddToCartResponse AddToPOUCart(CartAddItemRequest request);
        void UpdateCartItems(CartUpdateItemsRequest request);
        void UpdateCartPOUItems(CartUpdateItemsRequest request);
        int DeleteCartItem(CartDeleteItemRequest request);
        int DeletePOUCartItem(CartDeleteItemRequest request);
        RequisitionWithDetailsDTO GetRequisition(CartRequest request);
        Item_RFID GetRfidCartAttributes(CartRequest request);

        Cart GetCartMerge(CartRequest request);
        CartAttributesResponse GetAttributesMerge(CartRequest request);
        AddToCartResponse AddToCartMerge(CartAddItemRequest request);
        void UpdateCartItemsMerge(CartUpdateItemsRequest request);
        int DeleteCartItemMerge(CartDeleteItemRequest request);
    }
}