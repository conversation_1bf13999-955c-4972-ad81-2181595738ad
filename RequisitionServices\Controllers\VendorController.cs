﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class VendorController : ApiController
    {
        private IVendorService vendorService;

        public VendorController(IVendorService vendorSvc)
        {
            this.vendorService = vendorSvc;
        }

        /// <summary>
        /// Get list of punch-out vendors for a COID
        /// </summary>
        /// <param name="facility"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<PunchOutVendor> GetPunchOutVendors(Facility facility)
        {
            var punchOutVendors = vendorService.GetPunchoutVendors(facility);

            return punchOutVendors;
        }


        /// <summary>
        /// Get a vendor for a COID by vendor ID
        /// </summary>
        /// <param name="COID"></param>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        [HttpGet]
        public Vendor GetVendorById(string COID, int vendorId)
        {
            var vendor = vendorService.GetVendordByVendorId(COID, vendorId);

            return vendor;
        }
        /// <summary>
        /// Get Vendor Information for a coid by Vendor Id
        /// </summary>
        /// <param name="COID"></param>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        [HttpGet]
        public Vendor GetVendorInformationById(string COID, int vendorId)
        {
            var vendor = vendorService.GetVendorInformationById(COID, vendorId);

            return vendor;
        }

        /// <summary>
        /// Returns the details of a vendor for a COID by vendor ID
        /// </summary>
        /// <param name="COID"></param>
        /// <param name="vendorId"></param>
        /// <returns></returns>
        [HttpGet]
        public VendorDetails GetVendorDetailsById(string COID, int vendorId)
        {
            var vendor = vendorService.GetVendorDetailsByVendorId(COID, vendorId);

            return vendor;
        }

        /// <summary>
        /// Returns header information for all vendors for a COID.
        /// </summary>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpGet]
        public List<VendorHeaderInfo> GetAllVendors(string COID)
        {
            var vendors = vendorService.GetAllVendorsForCoid(COID);

            return vendors;
        }
    }
}
