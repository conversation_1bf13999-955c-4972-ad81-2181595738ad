﻿using RequisitionServices.DomainModel.VPro;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices.Interface
{
    /// <summary>
    /// Provides services for managing VPro Badge Logs.
    /// </summary>
    public interface IVProService
    {
        /// <summary>
        /// Retrieves all badge logs.
        /// </summary>
        /// <returns>A list of RequisitionVProBadgeLog objects.</returns>
        List<RequisitionVProBadgeLog> GetallBadgeLogs();

        /// <summary>
        /// Retrieves a badge log by requisition Id.
        /// </summary>
        /// <param name="requisitionId">The unique identifier for the corresponding Requisition.</param>
        /// <returns>A RequisitionVProBadgeLog object.</returns>
        RequisitionVProBadgeLog GetBadgeLogById(int Id);


        /// <summary>
        /// Deletes a badge log for the specified requisition.
        /// </summary>
        /// <param name="requisitionId">The unique identifier for the correpsonding requisition.</param>
        /// <returns>The deleted RequisitionVProBadgeLog object.</returns>
        RequisitionVProBadgeLog DeleteVProBadgeLog(int Id);

        /// <summary>
        /// Updates an existing badge log.
        /// </summary>
        /// <param name="badgeLog">The updated badge log object.</param>
        /// <returns>The updated RequisitionVProBadgeLog object.</returns>
        RequisitionVProBadgeLog UpdateBadgeLog(RequisitionVProBadgeLog badgeLog);

        /// <summary>
        /// Creates a new badge log.
        /// </summary>
        /// <param name="badgeLog">The badge log object to be created.</param>
        /// <returns>The newly created RequisitionVProBadgeLog object.</returns>
        RequisitionVProBadgeLog CreateVProBadgeLog(RequisitionVProBadgeLog badgeLog);
    }
}
