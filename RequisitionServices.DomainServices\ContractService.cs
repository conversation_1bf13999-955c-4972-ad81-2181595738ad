﻿using log4net;
using RequisitionServices.DomainModel.Contract;
using RequisitionServices.DomainModel.Contracts;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System.Reflection;

namespace RequisitionServices.DomainServices
{
    public class ContractService : IContractService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private IContractRepository contractRepository;
        private ISmartContractService smartContractService;

        public ContractService(ISmartContractService smartContractSvc, IContractRepository contractRepo)
        {
            this.contractRepository = contractRepo;
            this.smartContractService = smartContractSvc;
        }

        public ContractReportResults GetAllContracts(int rowOffset, int pageSize, string sortOrder, string filterText)
        {
            return contractRepository.GetAllContracts(rowOffset, pageSize, sortOrder, filterText);
        }

        public ContractReportResults GetContractsById(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText)
        {
            return contractRepository.GetContractsById(rowOffset, pageSize, sortOrder, filterText, searchText);
        }

        public ContractReportResults GetContractsByVendor(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText)
        {
            return contractRepository.GetContractsByVendor(rowOffset, pageSize, sortOrder, filterText, searchText);
        }

        public ContractDetails GetContractDetails(string userName, string COID, string contractId, string vendorNumber)
        {
            return smartContractService.GetContractDetails(userName, COID, contractId, vendorNumber);
        }

    }
}
