﻿using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Email;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IEmailService
    {
        void SendEmail(EmailRequest request);
        (string rushStockItems, string rushNonStockItems, bool hasStockAndNonStockRushItems) GetItemsForRushEmailTemplate(IEnumerable<RequisitionItemWithDetailsDTO> items, bool isSubmitted);
    }
}
