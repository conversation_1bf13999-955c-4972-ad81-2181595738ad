﻿using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartDepartmentService
    {
        IEnumerable<Department> GetDepartments(string userName, string coid);

        IEnumerable<Department> GetDepartmentsForCache(string userName, string coid);

        Department GetDepartment(string userName, string coid, int deptNumber);
    }
}
