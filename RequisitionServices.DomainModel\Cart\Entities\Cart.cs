﻿using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Enum;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace RequisitionServices.DomainModel.Cart.Entities
{
    public class Cart
    {
        public long Id { get; set; }
        [Required]
        [StringLength(10)]
        public string Coid { get; set; }
        public int DepartmentNumber { get; set; }
        [Required]
        [StringLength(3)]
        public string ParId { get; set; }
        public int? RequisitionId { get; set; }
        private ICollection<CartItem> _items;
        public virtual ICollection<CartItem> Items
        {
            get { return _items ?? (_items = new Collection<CartItem>()); }
            set { _items = value; }
        }
        [Required]
        [StringLength(50)]
        public string Username { get; set; }
        [Required]
        public DateTime CreatedUtc { get; set; }
        [Required]
        public DateTime LastUpdatedUtc { get; set; }
        public int CartTypeId { get; set; }
    }
}
