﻿using System;
using System.ComponentModel.DataAnnotations.Schema;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.DomainModel.Comments
{
    public class UnreadComment
    {
        public long Id { get; set; }

        public int RequisitionId { get; set; }
        [ForeignKey("RequisitionId")]
        public virtual Requisition Requisition { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        public long CommentId { get; set; }
        [ForeignKey("CommentId")]
        public virtual Comment Comment { get; set; }

        public DateTime CreateDateUtc { get; set; }
        public DateTime LastUpdatedUtc { get; set; }
    }
}
