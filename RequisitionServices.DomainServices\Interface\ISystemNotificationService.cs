﻿using RequisitionServices.DomainModel.SystemNotifications;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface ISystemNotificationService
    {
        bool CheckSystemNotificationAuthorization(string userName);

        SystemNotifcationTabInfoDTO GetSystemNotificationTabInfo();

        List<SystemNotificationAdminDTO> SearchNewAdminUsingThreeFour(string searchUserName);

        List<SystemNotificationAdminDTO> SearchNewAdminUsingCOID(string searchString);

        List<SystemNotificationAdminDTO> UpdateAdminsWithAuthorization(List<SystemNotificationAdminDTO> systemNotificationAdmins);

        SystemNotification GetLatestNonExpiredSystemNotification();

        SystemNotification SaveNewSystemNotification(SystemNotification notification);

        void RemoveOldSystemNotifications();
    }
}
