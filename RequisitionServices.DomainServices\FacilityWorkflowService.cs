﻿using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;

namespace RequisitionServices.DomainServices
{
    public class FacilityWorkflowService : IFacilityWorkflowService
    {
        readonly IFacilityWorkflowRepository _facilityWorkflowRepository;
        readonly IUserRepository _userRepository;

        public FacilityWorkflowService(IFacilityWorkflowRepository facilityWorkflowRepository, IUserRepository userRepository)
        {
            _facilityWorkflowRepository = facilityWorkflowRepository;
            _userRepository = userRepository;
        }

        public FacilityWorkflowDTO Get(string coid, WorkflowTypeEnum workflowType)
        {
            IEnumerable<FacilityWorkflowStep> facilityWorkflowSteps = _facilityWorkflowRepository.Get(coid, workflowType);
            
            return new FacilityWorkflowDTO
            {
                Steps = facilityWorkflowSteps,
                Coid = coid,
                WorkflowTypeId = (int)workflowType
            };
        }

        public void Save(SaveFacilityWorkflowDTO request)
        {
            _facilityWorkflowRepository.Save(request);
        }

        public bool AssignDelegateForApprover(int delegateUserId, string username, User user, User delegateUser)
        {
            var userSteps = _facilityWorkflowRepository.GetStepsByApproverUsername(username).Where(x => x.DelegatedByUserId == null).ToList();

            if (!userSteps.Any())
            {
                return false;
            }

            var coids = userSteps.Select(x => x.Coid).Distinct();

            var delegateApprovers = coids.Select(coid => _userRepository.GetApproverByUserId(delegateUser.Id, coid) ?? _userRepository.InsertApprover(new Approver
                {
                    User = delegateUser,
                    UserId = delegateUser.Id,
                    MaxApprovalAmount = 0,
                    CapitalMaxApprovalAmount = 0,
                    IsActive = true,
                    CreatedBy = username,
                    CreateDate = DateTime.Now,
                    COID = coid
                }, null))
                .ToList();

            if (userSteps.Any(x => x.Approver.Delegate == null))
            {
                foreach (var approver in userSteps.Select(x => x.Approver).Distinct())
                {
                    approver.Delegate = delegateApprovers.First(x => x.COID == approver.COID).Id;
                    _userRepository.UpdateApprover(approver, "Assign delegate", username);
                }
            }

            foreach (var step in userSteps)
            {
                _facilityWorkflowRepository.InsertStep(new FacilityWorkflowStep
                {
                    ApproverId = delegateApprovers.First(x => x.COID == step.Coid).Id,
                    DelegatedByUserId = user.Id,
                    WorkflowTypeId = step.WorkflowTypeId,
                    CreatedBy = username,
                    CreateDateUtc = DateTime.UtcNow,
                    Coid = step.Coid
                });
            }

            return true;
        }

        public void DeleteDelegatedSteps(int delegatedByUserId)
        {
            _facilityWorkflowRepository.DeleteDelegatedSteps(delegatedByUserId);
        }
    }
}
