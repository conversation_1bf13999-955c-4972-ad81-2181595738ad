﻿using System;
using System.Data;
using System.Data.SqlClient;
using RequisitionServices.Database;

namespace RequisitionServices.Repositories
{
    public class ConnectionFactory : IConnectionFactory
    {
        string _connectionString;

        // ReSharper disable once SuggestBaseTypeForParameter (This cannot be reduced to base type because it is abstract and not injectable)
        public ConnectionFactory(EProcurementContext context)
        {
            _connectionString = context.Database.Connection.ConnectionString;
        }

        public IDbConnection GetConnection()
        {
#if DEBUG
            var localDatabaseEnvKey = Environment.GetEnvironmentVariable("LOCAL_DATABASE_ENV_KEY");
            if (!string.IsNullOrEmpty(localDatabaseEnvKey))
            {
                var server = Environment.GetEnvironmentVariable(localDatabaseEnvKey, EnvironmentVariableTarget.User);
                if (!string.IsNullOrEmpty(server))
                {
                    var conn = new SqlConnectionStringBuilder(_connectionString) {DataSource = server};
                    _connectionString = conn.ConnectionString;
                }
            }
#endif
            var connection = new SqlConnection(_connectionString);

            if (connection == null)
            {
                throw new InvalidOperationException("Cannot open a null connection.");
            }

            if (string.IsNullOrEmpty(connection.ConnectionString) || string.IsNullOrEmpty(connection.Database))
            {
                throw new InvalidOperationException("Cannot open a connection without specifying a connection string or database.");
            }

            if (connection.State == ConnectionState.Closed)
            {
                connection.Open();
            }

            return connection;
        }
    }
}