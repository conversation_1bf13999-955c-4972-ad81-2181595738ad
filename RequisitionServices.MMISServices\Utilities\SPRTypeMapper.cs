﻿using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.MMISServices.Utilities
{
    public static class SPRTypeMapper
    {
        public static string GetSPRType(bool isRush, RequisitionTypeEnum requsitionType, SPRTypeEnum sprType)
        {
            if(isRush && sprType != SPRTypeEnum.BillOnly)
            {
                return "0"; //Code for RushItem
            }
            if(requsitionType == RequisitionTypeEnum.Capital)
            {
                return "C";
            }
            if (sprType == SPRTypeEnum.BillOnly)
            {
                return "B";
            }
            return "G";
        }
    }
}
