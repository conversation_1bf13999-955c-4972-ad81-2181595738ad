﻿using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class UpdateRequisitionItemStatusResultDTO
    {
        public bool Success
        {
            get
            {
                return !this.ErrorMessages.Any();
            }
        }

        public List<string> ErrorMessages { get; set; }

        public UpdateRequisitionItemStatusResultDTO()
        {
            this.ErrorMessages = new List<string>();
        }
    }
}
