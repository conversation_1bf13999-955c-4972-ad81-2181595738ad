﻿using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Schema;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Utilities;
using RequisitionServices.Utility.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    /// <summary>
    /// Methods for interacting with requisitions
    /// </summary>
    public class RequisitionController : ApiController
    {
        private IRequisitionService requisitionService;
        private IWorkflowService workflowService;
        private IConfigurationService configurationService;
        private ICensorService censorService;
        private static readonly JSchema ReqMessageSchema = ReqRequisitionItemStatusDTO.GetReqSchema();
        private static readonly JSchema SprMessageSchema = SprRequisitionItemStatusDTO.GetSprSchema();
        private readonly ILog Logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public RequisitionController(IRequisitionService requisitionSvc, IWorkflowService workflowSvc, IConfigurationService configurationSvc, ICensorService censorSvc)
        {
            this.requisitionService = requisitionSvc;
            this.workflowService = workflowSvc;
            this.configurationService = configurationSvc;
            this.censorService = censorSvc;
        }

        /// <summary>
        /// Save a requisition
        /// </summary>
        /// <param name="requisition"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionDTO SaveRequisition(RequisitionDTO requisition, string userName)
        {
            
            var requisitionObject = new Requisition(requisition);

            if (requisitionObject.ApprovalStep != null && !requisitionService.CheckIfSaveable(requisitionObject))
            {
                return requisition;
            }
            var returnedReq = requisitionService.SaveRequisition(requisitionObject, userName, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);

            return new RequisitionDTO(returnedReq);
        }

        /// <summary>
        /// Save requisition as an approver
        /// </summary>
        /// <param name="requisition"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionDTO SaveRequisitionAsApprover(RequisitionDTO requisition, string userName)
        {

            var requisitionObject = new Requisition(requisition);
            // makes sure that a rush requisition already submitted to workflow has not auto-escalated past persons step
            if (requisitionObject.ApprovalStep != null && !requisitionService.CheckIfSaveable(requisitionObject))
            {
                return requisition;
            }

            var returnedReq = requisitionService.SaveRequisitionAsApprover(requisitionObject, userName, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);

            return new RequisitionDTO(returnedReq);
        }

        /// <summary>
        /// Submits a requisition
        /// </summary>
        /// <param name="requisition">Requisition Object to submit</param>
        /// <param name="userName">Full username for user performing action (This should match the Domain/Username that PASS has)</param>
        /// <param name="cartId">Used if submitting a Requisition that was created from a Cart</param>
        /// <returns>This methods returns a populated requisition object which contains the updated status of the requisition as well as the parent System Id</returns>
        [HttpPost]
        public RequisitionDTO SubmitRequisition(RequisitionDTO requisition, string userName, long? cartId = null)
        {
            var requisitionObject = new Requisition(requisition);
            
            //Submit
            var returnedReq = requisitionService.SubmitRequisition(requisitionObject, userName, false, cartId);

            //Return DTO
            return new RequisitionDTO(censorService.CensorPrivateRequisitionData(userName, returnedReq));
        }

        /// <summary>
        /// Submits a requisition
        /// </summary>
        /// <param name="requisition">Requisition Object to submit</param>
        /// <param name="userName">Full username for user performing action (This should match the Domain/Username that PASS has)</param>
        /// <param name="cartId">Used if submitting a Requisition that was created from a Cart</param>
        /// <returns>This methods returns a populated requisition object which contains the updated status of the requisition as well as the parent System Id</returns>
        [HttpPost]
        public RequisitionDTO SubmitApproversRequisition(RequisitionDTO requisition, string userName, long? cartId = null)
        {
            var requisitionObject = new Requisition(requisition);

            //TODO: This controller method is not needed. Don't rely on the up-stream system to determine if the user is an approver or not.
            // load it up on submit normally and if they are an approver, do the logic there.

            //Submit
            var returnedReq = requisitionService.SubmitRequisition(requisitionObject, userName, true, cartId);

            //Return DTO
            return new RequisitionDTO(returnedReq);
        }

        /// <summary>
        /// Retrieves a requisition with details by its ID
        /// </summary>
        /// <param name="requisitionId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpGet]
        public RequisitionWithDetailsDTO GetRequisition(int requisitionId, string userName)
        {
            var requisition = this.requisitionService.GetRequisitionWithDetails(requisitionId, userName);

            return requisition != null ? censorService.CensorPrivateRequisitionData(userName, requisition) : requisition;
        }

        [HttpGet]
        public RequisitionWithDetailsDTO GetRequisitionAsApprover(int requisitionId, string userName)
        {
            var requisition = this.requisitionService.GetRequisitionWithDetails(requisitionId, userName);

            return requisition;
        }

        [HttpGet]
        public RequisitionWithDetailsDTO GetRequisitionByCoid(int requisitionId, string coid, string userName)
        {
            var requisition = this.requisitionService.GetRequisitionWithDetails(requisitionId, coid, userName);

            return requisition != null ? censorService.CensorPrivateRequisitionData(userName, requisition) : requisition;
        }

        [HttpPost]
        public RequisitionWithDetailsDTO GetRequisitionForVendorUser(RequisitionForVendorUserRequestDto request)
        {
            var requisition = requisitionService.GetRequisitionForVendorUser(request.RequisitionId, request.Coid, request.Username, request.VendorAffiliations);
            return requisition != null ? censorService.CensorPrivateRequisitionData(request.Username, requisition) : requisition;
        }

        [HttpGet]
        public RequisitionWithDetailsDTO GetRequisitionByLegacyInfo(int requisitionId, string coid, string countryCode, string userName, bool isLegacy)
        {
            var requisition = new RequisitionWithDetailsDTO();
            if (isLegacy)
            {
                requisition = this.requisitionService.GetLegacyRequisition(requisitionId, coid, countryCode, userName);
            }
            else
            {
                requisition = this.requisitionService.GetRequisitionWithDetails(requisitionId, coid, userName);
            }
           

            return requisition != null ? censorService.CensorPrivateRequisitionData(userName, requisition) : requisition;
        }


        /// <summary>
        /// Retrieves a requisition without details by its ID
        /// </summary>
        /// <param name="requisitionId"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpGet]
        public RequisitionDTO GetRequisitionForUpdate(int requisitionId, string userName)
        {
            var requisition = this.requisitionService.GetRequisition(userName, requisitionId);

            return requisition != null ? new RequisitionDTO(censorService.CensorPrivateRequisitionData(userName, requisition)) : null;
        }

        /// <summary>
        /// Updates a requisition status given the values specified in the posted object
        /// </summary>
        /// <param name="requisitionStatusHistory"></param>
        /// <returns></returns>
        [HttpPost]
        public StatusUpdateDTO UpdateRequisitionStatus(RequisitionStatusHistory requisitionStatusHistory)
        {
            return requisitionService.UpdateRequisitionStatus(requisitionStatusHistory);
        }

        /// <summary>
        /// Checks if SMART Legacy GetSprStatus service is available
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="coid"></param>
        /// <returns></returns>
        [HttpGet]
        public bool SmartLegacySubmissionAvailabilityCheck(string userId, string coid)
        {
            return requisitionService.SmartLegacySubmissionAvailabilityCheck(userId, coid);
        }

        /// <summary>
        /// Update requisition item status
        /// </summary>
        [HttpPost]
        public HttpResponseMessage UpdateRequisitionItemStatus()
        {
            try
            {
                string rawMessage = this.Request.Content.ReadAsStringAsync().Result;

#if DEBUG
                Logger.Debug(string.Format("Raw Message: {0}", rawMessage));
#endif

                JObject json = JObject.Parse(rawMessage);
                IList<ValidationError> messages = new List<ValidationError>();
                bool isParMessage = json["ParIdentifier"] != null;
                if (isParMessage && json.IsValid(ReqMessageSchema, out messages))
                {
                    var reqItemStatus = JsonConvert.DeserializeObject<ReqRequisitionItemStatusDTO>(rawMessage);
                    if (reqItemStatus.Id != null && reqItemStatus.Id != 0) requisitionService.UpdateRequisitionStatusToSubmittedIfNeeded(reqItemStatus);
                    UpdateRequisitionItemStatusResultDTO updateStatus = this.requisitionService.UpdateReqRequisitionItemStatus(reqItemStatus);
                    if (!updateStatus.Success) return this.CreateAndLogErrorResponse(GetFormattedErrorMessage(rawMessage, updateStatus.ErrorMessages.GetErrorMessagesString()));
                }
                else if (!isParMessage && json.IsValid(SprMessageSchema, out messages))
                {
                    var sprItemStatus = JsonConvert.DeserializeObject<SprRequisitionItemStatusDTO>(rawMessage);
                    if (sprItemStatus.Id != null && sprItemStatus.Id != 0) requisitionService.UpdateRequisitionStatusToSubmittedIfNeeded(sprItemStatus);
                    UpdateRequisitionItemStatusResultDTO updateStatus = this.requisitionService.UpdateSprRequisitionItemStatus(sprItemStatus);
                    if (!updateStatus.Success) return this.CreateAndLogErrorResponse(GetFormattedErrorMessage(rawMessage, updateStatus.ErrorMessages.GetErrorMessagesString()));
                }
                else
                {
                    return this.CreateAndLogErrorResponse(GetFormattedErrorMessage(rawMessage, string.Format("JSON schema not valid: {0}", messages.GetValidationErrorsString())));
                }
            }
            catch (Exception ex)
            {
                return this.CreateAndLogErrorResponse(ex.ToString());
            }

            return Request.CreateResponse(HttpStatusCode.OK);
        }

        private static string GetFormattedErrorMessage(string rawMessage, string errorMessage)
        {
            return string.Format("MQ Message Error: Raw Message: {0} {1} Error Message: {2}", rawMessage, Environment.NewLine, errorMessage);
        }

        private HttpResponseMessage CreateAndLogErrorResponse(string errorMessage)
        {            
            Logger.Error(errorMessage);
            return Request.CreateErrorResponse(HttpStatusCode.InternalServerError, new HttpError(errorMessage));
        }

        /// <summary>
        /// Get a list of requisitions for a date range and COID
        /// </summary>
        /// <param name="COID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="userName"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public List<RequisitionDTO> GetRequisitions(string COID, DateTime startDate, DateTime endDate, string userName, int departmentId = 0)
        {
            var requisitions = requisitionService.GetRequisitionsByDateRange(COID, startDate, endDate, departmentId, userName);

            var returnList = new List<RequisitionDTO>();
            if (requisitions != null)
            {
                foreach (var requisition in requisitions)
                {
                    returnList.Add(new RequisitionDTO(requisition));
                }
            }

            return returnList;
        }

        /// <summary>
        /// Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="coid"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public List<RequisitionDTO> GetRequisitionsWithItemStatuses(string userName, string coid, DateTime startDate, DateTime endDate, int departmentId = 0)
        {
            var requisitions = requisitionService.GetRequisitionsWithItemStatusesByDateRange(userName, coid, startDate, endDate, departmentId);

            var returnList = new List<RequisitionDTO>();
            if (requisitions != null)
            {
                foreach (var requisition in requisitions)
                {
                    returnList.Add(new RequisitionDTO(requisition));
                }
            }

            return returnList;
        }
        /// <summary>
        /// Retrieves requisition matching Vendor Id or Vendor Name
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionListResultsDTO GetRequisitionsByVendor(RequisitionReportRequestDto request)
        {
            return requisitionService.GetRequisitionsByVendor(request);
        }

        /// <summary>
        /// Get a list of requisitions for Vendor Id or Vendor Name and COID, with item details. Drafts and Templates NOT included.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionReportExportResultsDTO GetRequisitionsByVendorReportExport(RequisitionReportRequestDto request)
        {
            return requisitionService.GetRequisitionsByVendorReportExport(request);
        }

        /// <summary>
        /// Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionListResultsDTO GetRequisitionsForReport(RequisitionReportRequestDto request)
        {
            return requisitionService.GetRequisitionsForReport(request);
        }

        /// <summary>
        /// Get a list of Vendor Bill Only requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionListResultsDTO GetVBORequisitionsForReport(RequisitionReportRequestDto request)
        {
            return requisitionService.GetVBORequisitionsForReport(request);
        }

        /// <summary>
        /// Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionReportExportResultsDTO GetRequisitionsForReportExport(RequisitionReportRequestDto request)
        {
            return requisitionService.GetRequisitionsForReportExport(request);
        }

        /// <summary>
        /// Get a list of requisitions for a date range and COID, with item details. Drafts and Templates NOT included.
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionReportExportResultsDTO GetVBORequisitionsForReportExport(RequisitionReportRequestDto request)
        {
            return requisitionService.GetVBORequisitionsForReportExport(request);
        }

        /// <summary>
        /// Get a paged list of requisitions for an item/catalog/reorder number and COID 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionListResultsDTO GetRequisitionsForReportByItemNumber(RequisitionReportRequestDto request)
        {
            return requisitionService.GetRequisitionsForReportByItemNumber(request);
        }

        /// <summary>
        /// Get a list of requisitions for an item/catalog/reorder number and COID 
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionReportExportResultsDTO GetRequisitionsForReportByItemNumberExport(RequisitionReportRequestDto request)
        {
            return requisitionService.GetRequisitionsForReportByItemNumberExport(request);
        }

        /// <summary>
        /// Retrieves active requisitions and/or templates on the MyRequisitions page for the user
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public RequisitionListMultiResultsDTO GetMyRequisitionsResults(RequisitionListMultiRequestDto request)
        {
            return requisitionService.GetMyRequisitionsResults(request.Username, request.LeftSideRequest, request.RightSideRequest);
        }

        /// <summary>
        /// Retrieves pending approvals and/or approval history on the MyApprovals page for the user
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        [HttpPost]
        public ApprovalListMultiResultsDTO GetMyApprovalsResults(RequisitionListMultiRequestDto request)
        {
            return requisitionService.GetMyApprovalsResults(request.Username, request.LeftSideRequest, request.RightSideRequest);
        }

        /// <summary>
        /// Get a paginated list of upcoming approvals for an approver's Upcoming Approvals widget.
        /// </summary>
        /// <param name="request"></param>
        /// <returns>A list of Approval objects which contain requisition details</returns>
        [HttpPost]
        public ApprovalListResultsDTO GetUpcomingApprovalsForApprover(RequisitionListRequestDto request)
        {
            return requisitionService.GetUpcomingApprovalsForApprover(request);
        }

        /// <summary>
        /// Get a list of history records for a given Requisition
        /// </summary>
        /// <param name="requisitionId"></param>
        /// <returns></returns>
        [HttpGet]
        public List<RequisitionStatusHistoryDTO> GetRequisitionHistory(int requisitionId)
        {
            var requisitionStusHistories = requisitionService.GetRequisitionHistory(requisitionId);

            var returnList = new List<RequisitionStatusHistoryDTO>();
            if (requisitionStusHistories != null)
            {
                foreach (var requisitionStusHistory in requisitionStusHistories)
                {
                    returnList.Add(new RequisitionStatusHistoryDTO(requisitionStusHistory));
                }
            }

            return returnList;
        }

        /// <summary>
        /// Escalate the provided requisition to the provided workflow step
        /// </summary>
        /// <param name="requisitionAdvanceDTO"></param>
        [HttpPost]
        public void AdvanceRequisition(RequisitionAdvanceDTO requisitionAdvanceDTO)
        {
            requisitionService.AdvanceRequisition(requisitionAdvanceDTO);
        }

        /// <summary>
        /// Re-start all requisitions in Pending Approval Status
        /// </summary>
        /// <param name="key"></param>
        /// <param name="userName"></param>
        [HttpGet]
        public void ReInitializeAllWorkflow(string key, string userName)
        {
            if(key == "g34g3432465643y4hg55756776iujbefsfqq4jkwk3why34j4j4jn2qtg")
            {
                workflowService.ReInitializeAllWorkflow(userName);
            }
        }

        /// <summary>
        /// Re-start requisition in Pending Approval Status by Id
        /// </summary>
        /// <param name="key"></param>
        /// <param name="requisitionId"></param>
        /// <param name="userName"></param>
        [HttpGet]
        public void ReInitializeRequisitionInWorkflow(string key, int requisitionId, string userName)
        {
            if (key == "g34g3432465643y4hg55756776iujbefsfqq4jkwk3why34j4j4jn2qtg")
            {
                workflowService.ReInitializeWorkflowByRequisition(requisitionId, userName, true);
            }
        }

        /// <summary>
        /// Return a list of user workflow steps where the user has delegated approval authority
        /// </summary>
        /// <param name="userName"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUser(string userName)
        {
            return workflowService.GetUserWorkflowStepsDelegatedByUserName(userName);
        }

        /// <summary>
        /// Request an ad-hoc review for requisition
        /// </summary>
        /// <param name="adhocReview"></param>
        /// <returns></returns>
        [HttpPost]
        public bool RequestAdhocReview(AdhocReviewDTO adhocReview)
        {
            return requisitionService.RequestAdhocReview(adhocReview);
        }

        /// <summary>
        /// Provide ad hoc review
        /// </summary>
        /// <param name="adhocReview"></param>
        /// <returns></returns>
        [HttpPost]
        public bool ProvideAdhocReview(AdhocReviewDTO adhocReview)
        {
            return requisitionService.ProvideAdhocReview(adhocReview);
        }

        /// <summary>
        /// Get requisition ad hoc reviews
        /// </summary>
        /// <param name="requisitionId"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<AdhocReviewDTO> GetRequisitionAdhocReviews(int requisitionId)
        {
            return requisitionService.GetRequisitionAdhocReviews(requisitionId);
        }

        /// <summary>
        /// IsAdhocReview allowed
        /// </summary>
        /// <param name="adhocReviewId"></param>
        /// <param name="reviewer"></param>
        /// <param name="reqId"></param>
        /// <returns></returns>
        [HttpGet]
        public bool IsAdhocReviewAllowed(int adhocReviewId, string reviewer, int reqId)
        {
            return requisitionService.IsAdhocReviewAllowed(adhocReviewId, reviewer, reqId);
        }

        [HttpPost]
        public bool DeleteAttachment(FileNamesDTO files)
        {
            return requisitionService.DeleteAttachment(files);
        }

        [HttpGet]
        public InFlightQty GetInFlightQuantity(string userName, string coid, int dept, string parClass, string itemId)
        {
            return requisitionService.GetInFlightQuantity(userName, coid, dept, parClass, itemId);
        }

        [HttpPost]
        public LegacyRequisitionReportDTO GetLegacyRequisitionReport(LegacyRequisitionReportRequestDTO request)
        {
            return requisitionService.GetLegacyRequisitionReport(request);
        }

        [HttpGet]
        public LastOrderDetailsDTO GetLastOrderDetails(string coid, string dept, string parClass, string itemId)
        {
            return requisitionService.GetLastOrderDetails(coid, dept, parClass, itemId);
        }

        [HttpPost]
        public SmartItemRequisitionIdDTO GetSmartItemRequisitionIdByPONumber(SmartItemRequisitionIdDTO smartItemRequisitionIdDTO)
        {
            return requisitionService.GetSmartItemRequisitionIdByPONumber(smartItemRequisitionIdDTO);
        }


        /// <summary>
        /// Retrieves a queue of purchasing requisitions for the purchasing report.
        /// </summary>
        /// <remarks>
        /// This endpoint retrieves paginated and filtered purchasing requisitions for generating a report. 
        /// It accepts a JSON object in the request body with the following parameters:
        /// 
        /// **Required Parameters:**
        /// 
        /// * **`Coids`**: (List of string) 
        ///    - The list of COIDs to filter by.
        ///    - Enter comma-separated values without quotes (e.g., `09,332`).
        ///    - Example: `"Coids": ["123", "321"]`
        ///
        /// **Optional Parameters:**
        /// 
        /// * **`advancedFilters`**: (object) 
        ///    - An object containing advanced filter options.
        ///      * **`ReqTypes`**: (List of int) 
        ///          - The list of requisition type IDs to filter by.
        ///          - Enter comma-separated IDs without quotes (e.g., `1,2,3`).
        ///      * **`Vendors`**: (List of int) 
        ///          - The list of vendor IDs to filter by.
        ///          - Enter comma-separated IDs without quotes (e.g., `101,205`).
        ///      * **`Buyers`**: (List of string) 
        ///          - The list of buyer names to filter by.
        ///          - Enter comma-separated names without quotes (e.g., `John Doe, Jane Smith`).
        ///      * **`FilterText`**: (string) 
        ///          - A string value to search certain columns by.
        ///          - Searchable columns include: User First and Last Name, Requisition Status Description, Requisition Submission Type Description, Comments, PO Number, Parent System ID, Original Parent System ID, Location Identifier, and PAR Identifier.
        ///      * **`startDate`**: (Date)
        ///          - The starting date for filtering requisitions by creation date.
        ///          - If not provided, defaults to the minimum date allowed by the database.
        ///      * **`endDate`**: (Date)
        ///          - The ending date for filtering requisitions by creation date.
        ///          - If not provided, defaults to the maximum date allowed by the database.
        /// * **`pageNumber`**: (int, defaults to 1) 
        ///    - The page number for pagination.
        /// * **`pageSize`**: (int, defaults to 25) 
        ///    - The number of records to return per page.
        /// * **`sortColumn`**: (string, defaults to 'RequisitionId') 
        ///    - The column to sort by.
        ///    - Valid values are: `RequisitionId`, `LocationIdentifier`, `VendorNumber`, `VendorName`, `Date`, `RequisitionTypeId`, `FileAttachmentItemId`.
        /// * **`sortType`**: (string, defaults to 'ASC') 
        ///    - The sort direction.
        ///    - Valid values are: `ASC` (Ascending), `DESC` (Descending).
        /// </remarks>
        [HttpPost]
        public PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport([FromBody] PurchasingRequisitionReportParameters reportParameters)
        {
            var results =  requisitionService.GetRequisitionsForPurchasingReport(reportParameters);
            return results;
        }

        /// <summary>
        /// Get a list of filters for the advanced filter for Purchasing with an input list of CoIds.
        /// </summary>
        /// <param name="filterList"></param>
        /// <returns>lists of values</returns>
        [HttpPost]
        public RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList)
        {
            return requisitionService.GetAdvancedFiltersForPurchasingReport(filterList);
        }

        /// <summary>
        /// Handles an HTTP GET request to retrieve a list of requisitions based on the provided purchase order number and COID.
        /// The requisitions are returned in a JSON format.
        /// </summary>
        /// <param name="poNumber">The purchase order number.</param>
        /// <param name="coid">The facility ID.</param>
        /// <returns>A list of <see cref="RequisitionDTO"/> objects that match the specified criteria.</returns>
        [HttpGet]
        public HttpResponseMessage GetRequisitionAndItemsByPONumber(string poNumber, string coid)
        {
            var requisitions = requisitionService.GetRequisitionAndItemsByPONumber(int.Parse(poNumber), coid);
            return requisitions.Count() == 0 ? Request.CreateResponse(HttpStatusCode.NoContent) : Request.CreateResponse(HttpStatusCode.OK, requisitions);
        }
    }
}
