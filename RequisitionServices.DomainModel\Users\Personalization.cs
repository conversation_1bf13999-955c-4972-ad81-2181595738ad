﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Users
{
    public class Personalization
    {
        // Backing field for UserName to ensure it is always lowercase
        private string _userName;
        
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string UserName 
        {
            get => _userName;
            set => _userName = value.ToLower();
        }

        [Required]
        [Column(TypeName = "nvarchar")]
        [StringLength(5)]
        public string FacilityId { get; set; }
        
        public int? DepartmentId { get; set; }

        [Column(TypeName = "nvarchar")]
        [StringLength(10)]
        public string ParId { get; set; }
    }
}
