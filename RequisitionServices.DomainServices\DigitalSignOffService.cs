﻿using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices
{
    public class DigitalSignOffService : IDigitalSignOffService
    {
        private readonly IDigitalSignOffRepository _digitalSignOffRepository;

        public DigitalSignOffService(IDigitalSignOffRepository digitalSignOffRepository) 
        {
            _digitalSignOffRepository = digitalSignOffRepository;
        }
        public async Task<RequisitionDigitalSignOff> GetDigitalSignOff(int requisitionId)
        {
            TaskCompletionSource<RequisitionDigitalSignOff> dso = new TaskCompletionSource<RequisitionDigitalSignOff>();
            await Task.Run(() => 
            {
                RequisitionDigitalSignOff response = _digitalSignOffRepository.GetDigitalSignOff(requisitionId);
                dso.SetResult(response);
            });
        return await dso.Task;
        }

        public async Task<IEnumerable<RequisitionDigitalSignOff>> GetAllRequisitionDigitalSignOffsByRequisitionId(int requisitionId)
        {
            TaskCompletionSource<IEnumerable<RequisitionDigitalSignOff>> dso = new TaskCompletionSource<IEnumerable<RequisitionDigitalSignOff>>();
            await Task.Run(() =>
            {
                IEnumerable<RequisitionDigitalSignOff> response = _digitalSignOffRepository.GetAllRequisitionDigitalSignOffsByRequisitionId(requisitionId);
                dso.SetResult(response);
            });
            return await dso.Task;
        }

        public async Task<RequisitionDigitalSignOff> CreateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff)
        {
            TaskCompletionSource<RequisitionDigitalSignOff> dso = new TaskCompletionSource<RequisitionDigitalSignOff>();    
            await Task.Run(() =>
            {
                RequisitionDigitalSignOff response =  _digitalSignOffRepository.CreateRequisitionDigitalSignOff(requisitionDigitalSignoff);
                dso.SetResult(response);
            });
            return await dso.Task;
        }
        public async Task UpdateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff, int digitalSignOffId)
        {
            await Task.Run(() =>
            {
                _digitalSignOffRepository.UpdateRequisitionDigitalSignOff(requisitionDigitalSignoff, digitalSignOffId);
            });
        }
        public async Task DeleteRequisitionDigitalSignOff(int digitalSignOffId)
        {
            await Task.Run(() =>
            {
                _digitalSignOffRepository.DeleteRequisitionDigitalSignOff(digitalSignOffId);
            });
        }

        public async Task <IEnumerable<DigitalSignOffUser>> GetAllDigitalSignOffUsers()
        {
            TaskCompletionSource<IEnumerable<DigitalSignOffUser>> dso = new TaskCompletionSource<IEnumerable<DigitalSignOffUser>>();
            await Task.Run(() => 
            {
                IEnumerable <DigitalSignOffUser> response = _digitalSignOffRepository.GetAllDigitalSignOffUsers();
                dso.SetResult(response);
            });
            return await dso.Task;
        }

        public async Task <DigitalSignOffUser> GetSingleDigitalSignOffUser(string accountName)
        {
            TaskCompletionSource<DigitalSignOffUser> dso = new TaskCompletionSource<DigitalSignOffUser>();
            await Task.Run(() => 
            {
                DigitalSignOffUser response = _digitalSignOffRepository.GetSingleDigitalSignOffUser(accountName);
                dso.SetResult(response);
            });
            return await dso.Task;
        }

        public async Task <DigitalSignOffUser> CreateDigitalSignOffUser(DigitalSignOffUser request)
        {
            TaskCompletionSource<DigitalSignOffUser> dso = new TaskCompletionSource<DigitalSignOffUser>();

            await Task.Run(() =>
            {
                DigitalSignOffUser response = _digitalSignOffRepository.CreateDigitalSignOffUser(request);
                dso.SetResult(response);
            });
            return await dso.Task;
        }

        public async Task UpdateSingleDigitalSignOffUser(DigitalSignOffUser request)
        {
             await Task.Run(() =>
            {
                _digitalSignOffRepository.UpdateSingleDigitalSignOffUser(request);
            });
        }

        public async Task<ApproverDigitalSignOffDTO> GetApproverDigitalSignOff(int requisitionId)
        {
            TaskCompletionSource<ApproverDigitalSignOffDTO> approverDigitalSignOff = new TaskCompletionSource<ApproverDigitalSignOffDTO>();
            await Task.Run(() =>
            {
                ApproverDigitalSignOffDTO response = _digitalSignOffRepository.GetApproverDigitalSignOff(requisitionId);
                approverDigitalSignOff.SetResult(response);
            });
            return await approverDigitalSignOff.Task;
        }
    }
}
