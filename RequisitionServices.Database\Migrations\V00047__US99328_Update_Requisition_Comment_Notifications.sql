USE [eProcurementQA]
GO

ALTER PROCEDURE [dbo].[usp_GetUnreadComments]
    @username VARCHAR(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit
AS
    BEGIN

	DECLARE @totalRequisitionCount INT
	DECLARE @reqTypeIdTemp TABLE (
		Id INT
	)
	DECLARE @allCommentRequisitions TABLE (
		RowOrder INT,
		SortOrder INT,
		RequisitionId INT,
		RequisitionStatusTypeId INT,
		RequisitionTypeId INT,
		LocationIdentifier VARCHAR(50),
		Comments VARCHAR(255),
		CreatedBy VARCHAR(100),
		RequisitionerFirstName VARCHAR(255),
		RequisitionerLastName VARCHAR(255),
		CreateDate DATETIME,
		CountryCode VARCHAR(3),
		IsMobile BIT,
		OldestCommentDate DATETIME,
		NewestCommentDate DATETIME,
		NewCommentCount INT
	)

	INSERT INTO @reqTypeIdTemp (Id)(SELECT Id FROM @reqTypeIdGroup)

	INSERT INTO @allCommentRequisitions
	(
		RowOrder,
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionTypeId,
		LocationIdentifier,
		Comments,
		CreatedBy,
		RequisitionerFirstName,
		RequisitionerLastName,
		CreateDate,
		CountryCode,
		IsMobile,
		OldestCommentDate,
		NewestCommentDate,
		NewCommentCount
	)
	(
		SELECT
			ROW_NUMBER() OVER(ORDER BY SortOrder,
			CASE @oldestFirst WHEN 0 THEN UnreadComments.NewestUnreadCommentDate END DESC,
			CASE @oldestFirst WHEN 1 THEN UnreadComments.OldestUnreadCommentDate END ASC) AS RowOrder,
			SortOrder,
			allRequisitions.RequisitionId,
			RequisitionStatusTypeId,
			RequisitionTypeId,
			LocationIdentifier,
			Comments,
			CreatedBy,
			RequisitionerFirstName,
			RequisitionerLastName,
			CreateDate,
			CountryCode,
			IsMobile,
			UnreadComments.OldestUnreadCommentDate AS OldestCommentDate,
			UnreadComments.NewestUnreadCommentDate AS NewestCommentDate,
			UnreadComments.TotalUnreadComments AS NewCommentCount
		FROM
		(
			(
				SELECT 
					CASE @statusSorting
					WHEN 1 THEN 2
					ELSE CASE
						WHEN Requisitions.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdTemp) THEN 1
						ELSE 2
						END
					END AS SortOrder,
					requisitions.RequisitionId,
					requisitions.RequisitionStatusTypeId,
					requisitions.RequisitionTypeId,
					requisitions.LocationIdentifier,
					requisitions.Comments,
					requisitions.CreatedBy,
					requisitions.RequisitionerFirstName,
					requisitions.RequisitionerLastName,
					requisitions.CreateDate,
					requisitions.CountryCode,
					requisitions.IsMobile,
					requisitions.RequisitionStatus
				FROM
				(
					SELECT DISTINCT
						WFO.AccountName,
						UWS.COID,
						UWS.WorkflowTypeId
					FROM Users U WITH (NOLOCK)
					INNER JOIN Approvers A WITH (NOLOCK)
						ON U.Id = A.UserId
					INNER JOIN UserWorkflowSteps UWS WITH (NOLOCK)
						ON A.Id = UWS.ApproverId
					INNER JOIN Users WFO WITH (NOLOCK)
						ON UWS.UserId = WFO.Id
					WHERE U.AccountName = @username
					AND WFO.AccountName <> @username
				) AS workflows
				INNER JOIN
				(
					SELECT 
						requisition.RequisitionId,
						requisition.RequisitionStatusTypeId,
						requisition.RequisitionTypeId,
						requisition.LocationIdentifier,
						requisition.Comments,
						requisition.CreatedBy,
						U.FirstName AS RequisitionerFirstName,
						U.LastName AS RequisitionerLastName,
						requisition.CreateDate,
						requisition.CountryCode,
						requisition.IsMobile,
						reqStatus.[Description] AS RequisitionStatus,
						reqWorkflowtypes.WorkflowTypeId
					FROM
					(
						SELECT
							RequisitionId,
							MAX(WorkflowType) AS WorkflowTypeId
						FROM
						(
							SELECT
								R.RequisitionId,
								CASE R.RequisitionTypeId
									WHEN 5 THEN 2 --Capital
									WHEN 6 THEN 3 --Punchout
									WHEN 7 THEN 1 --Rush
									ELSE CASE
										WHEN SPR.RequisitionItemId IS NOT NULL THEN 0 --Standard
										ELSE -1 --For Non-applicaple
										END
									END AS WorkflowType
							FROM Requisitions R WITH (NOLOCK)
							LEFT OUTER JOIN RequisitionItems item WITH (NOLOCK)
								ON R.RequisitionId = item.RequisitionId
							LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
								ON item.Id = SPR.RequisitionItemId
							WHERE R.RequisitionStatusTypeId NOT IN (1, 5) -- Draft, Deleted
						) AS requisition
						GROUP BY requisition.RequisitionId
					) AS reqWorkflowTypes
					INNER JOIN Requisitions requisition WITH (NOLOCK)
						ON reqWorkflowTypes.RequisitionId = requisition.RequisitionId
					INNER JOIN RequisitionStatusTypes reqStatus WITH (NOLOCK)
						ON requisition.RequisitionStatusTypeId = reqStatus.Id
					INNER JOIN Users U WITH (NOLOCK)
						ON requisition.CreatedBy = U.AccountName
				) AS requisitions
					ON workflows.AccountName = requisitions.CreatedBy
					AND workflows.COID = SUBSTRING(requisitions.LocationIdentifier, 1, CHARINDEX('_', requisitions.LocationIdentifier)-1)
					AND workflows.WorkflowTypeId = requisitions.WorkflowTypeId
			)
			UNION
			(
				SELECT
					CASE @statusSorting
						WHEN 1 THEN 2
						ELSE CASE
							WHEN R.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdTemp) THEN 1
							ELSE 2
							END
						END AS SortOrder,
					R.RequisitionId,
					R.RequisitionStatusTypeId,
					R.RequisitionTypeId,
					R.LocationIdentifier,
					R.Comments,
					R.CreatedBy,
					U.FirstName AS RequisitionerFirstName,
					U.LastName AS RequisitionerLastName,
					R.CreateDate,
					R.CountryCode,
					R.IsMobile,
					reqStatus.[Description] AS RequisitionStatus 
				FROM Requisitions R WITH (NOLOCK)
				INNER JOIN RequisitionStatusTypes reqStatus WITH (NOLOCK)
					ON R.RequisitionStatusTypeId = reqStatus.Id
				INNER JOIN Users U WITH (NOLOCK)
					ON R.CreatedBy = U.AccountName
				WHERE R.CreatedBy = @username
				AND R.RequisitionStatusTypeId NOT IN (1, 5) -- Draft, Deleted
			)
		) AS allRequisitions
		INNER JOIN
		(
			SELECT 
				UnreadComments.RequisitionId,
				UnreadComments.TotalUnreadComments AS TotalUnreadComments,
				UnreadComments.OldestUnreadCommentDate AS OldestUnreadCommentDate,
				UnreadComments.NewestUnreadCommentDate AS NewestUnreadCommentDate
			FROM
			(
				SELECT 
					UC.RequisitionId,
					UC.TotalUnreadComments,
					UC.OldestUnreadCommentDate,
					UC.NewestUnreadCommentDate
				FROM 
				(
					SELECT 
						UnreadCommentDates.RequisitionId,
						UnreadCommentDates.UserId,
						UnreadCommentsCount.TotalUnreadComments,
						UnreadCommentDates.OldestUnreadCommentDate,
						UnreadCommentDates.NewestUnreadCommentDate
					FROM 
					(
						SELECT 
							RequisitionId,
							UserId,
							COUNT(*) AS TotalUnreadComments
						FROM UnreadComments
						GROUP BY RequisitionId, UserId
					) AS UnreadCommentsCount
					INNER JOIN
					(
						SELECT
							RequisitionId,
							UserId,
							MIN(CreateDateUtc) AS OldestUnreadCommentDate,
							MAX(CreateDateUtc) AS NewestUnreadCommentDate
						FROM UnreadComments
						GROUP BY RequisitionId, UserId
					) AS UnreadCommentDates
						ON UnreadCommentsCount.RequisitionId = UnreadCommentDates.RequisitionId
						AND UnreadCommentsCount.UserId = UnreadCommentDates.UserId
				) AS UC
				INNER JOIN Users U
					ON UC.UserId = U.Id
				WHERE U.AccountName = @username
			) AS UnreadComments
		) AS UnreadComments
			ON allRequisitions.RequisitionId = UnreadComments.RequisitionId
		WHERE @filterText IS NULL OR
		(allRequisitions.RequisitionId LIKE @filterText + '%'
		OR allRequisitions.CreatedBy LIKE '%' + @filterText + '%'
		OR allRequisitions.RequisitionerFirstName LIKE @filterText + '%'
		OR allRequisitions.RequisitionerLastName LIKE @filterText + '%'
		OR (allRequisitions.RequisitionerFirstName + ' ' + allRequisitions.RequisitionerLastName) LIKE @filterText + '%'
		OR allRequisitions.Comments LIKE '%' + @filterText + '%'
		OR allRequisitions.RequisitionStatus LIKE '%' + @filterText + '%')
	)

	SELECT @totalRequisitionCount = COUNT(*) FROM @allCommentRequisitions
	

	SELECT 
		unreadComments.RequisitionId AS RequisitionId,
		unreadComments.RequisitionStatusTypeId AS RequisitionStatusTypeId,
		unreadComments.RequisitionTypeId AS RequisitionTypeId,
		unreadComments.LocationIdentifier AS LocationIdentifier,
		unreadComments.Comments AS Comments,
		unreadComments.RequisitionerFirstName AS RequisitionerFirstName,
		unreadComments.RequisitionerLastName AS RequisitionerLastName,
		unreadComments.CreateDate AS CreateDate,
		unreadComments.CountryCode AS CountryCode,
		unreadComments.IsMobile AS IsMobile,
		CASE 
			WHEN hasSprItems.RequisitionId IS NULL THEN 0
			ELSE 1 
			END AS HasSPRItems,
		CASE 
			WHEN HasFileAttachment.RequisitionId IS NULL THEN 0
			ELSE 1 
			END AS HasFileAttachment,
		unreadComments.NewCommentCount AS NewCommentCount,
		@totalRequisitionCount AS TotalReqCount
	FROM 
	(
		SELECT * FROM @allCommentRequisitions
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS unreadComments
	LEFT OUTER JOIN 
	(
		SELECT DISTINCT
			RI.RequisitionId
		FROM RequisitionItems RI
		INNER JOIN SPRDetails SPR
			ON RI.Id = SPR.RequisitionItemId
	) AS hasSprItems
		ON unreadComments.RequisitionId = hasSprItems.RequisitionId
	LEFT OUTER JOIN
	(
		SELECT DISTINCT
			FA.RequisitionId
		FROM FileAttachments FA WITH (NOLOCK)
	) AS HasFileAttachment
		ON unreadComments.RequisitionId = HasFileAttachment.RequisitionId
    END

