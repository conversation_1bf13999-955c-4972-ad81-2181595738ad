﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{

    [NotMapped]
    public class RequisitionReportPageRow
    {
        public int RequisitionId { get; set; }
        
        public int RequisitionStatusTypeId { get; set; }

        public string LocationIdentifier { get; set; }

        public string Comments { get; set; }

        public DateTime CreateDate { get; set; }

        public int RequisitionTypeId { get; set; }

        public int? RequisitionItemId { get; set; }

        public string RequisitionItemNumber { get; set; }

        public int? RequisitionItemStatusTypeId { get; set; }

        public string RequisitionItemParentSystemId { get; set; }

        public string RequisitionItemOriginalParentSystemId { get; set; }

        public string RequisitionItemParIdentifier { get; set; }

        public decimal? Discount { get; set; }

        public int? VendorId { get; set; }

        public decimal? UnitCost { get; set; }

        public int? QuantityToOrder { get; set; }

        public int? RequisitionItemPONumber { get; set; }

        public int? RequisitionItemParentItemId { get; set; }

        public int? SprDetailsVendorId { get; set; }

        public string SprDetailsVendorName { get; set; }

        public string SprDetailsPartNumber { get; set; }

        public int? SprDetailsFileAttachment { get; set; }

        public string CreatedById { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

        public long TotalReqCount { get; set; }

        public bool IsMobile { get; set; }

        public bool IsVendor { get; set; }

        public decimal? VboHoldItemConversionUnitCost { get; set; }

        public int RequisitionSubmissionTypeId { get; set; }
    }
}
