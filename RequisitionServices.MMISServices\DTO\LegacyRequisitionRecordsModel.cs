﻿using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.MMISServices.DTO
{
    public class LegacyRequisitionRecordsModel
    {
        public List<LegacyRequisitionRecordModel> RequisitionRecordModels { get; set; }

        public IEnumerable<Requisition> convertToIEnumerableOfRequisitions()
        {
            if (RequisitionRecordModels != null && RequisitionRecordModels.Any())
            {
                var list = new List<Requisition>();
                    
                RequisitionRecordModels.ForEach(x =>
                {
                    var req = new Requisition()
                    {
                        RequisitionId = x.RequisitionId,
                        CreateDate = x.CreateDate,
                        RequisitionStatusTypeId = 13
                    };
                });
                return RequisitionRecordModels.Select(x => new Requisition()
                {
                    RequisitionId = x.RequisitionId,
                    CreateDate = x.CreateDate,
                    RequisitionStatusTypeId = 13
                });
            }
            else
            {
                return new List<Requisition>().AsEnumerable();
            }
        }
    }
}
