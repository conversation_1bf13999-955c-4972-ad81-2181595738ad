USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_VendorUserRequisitionsReportExportGet]    Script Date: 2/25/2025 12:37:48 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



/*
***************************************************************************
Author		: <PERSON>
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by date range export
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Jeremiah King		05/16/2024		Added RequisitionSubmissionTypeId for DSO VBO 
									user reports
Jeremiah King		02/26/2025		Added BadgeLogId and BadgeIn property from the RequisitionVProBadgeLog
									table to return in exported requisition results
***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_VendorUserRequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@maxExportedRequistiionCount int = 1000,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 14 THEN 5
					WHEN 2 THEN 6
					WHEN 4 THEN 7
					ELSE 8 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		LEFT OUTER JOIN RequisitionSubmissionTypes REQST WITH (NOLOCK)
			ON R.RequisitionSubmissionTypeId = REQST.Id
		LEFT OUTER JOIN RequisitionVProBadgeLogs VPRO WITH (NOLOCK)
			ON R.BadgeLogId = VPRO.Id
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR REQST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR R.LocationIdentifier LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		R.RequisitionSubmissionTypeId AS RequisitionSubmissionTypeId,
		R.BadgeLogId AS BadgeLogId,
		VPRO.BadgeIn AS BadgeIn,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT TOP (@maxExportedRequistiionCount)
		*
		FROM @requisitionList
		ORDER BY RowOrder
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN RequisitionSubmissionTypes REQST WITH (NOLOCK)
		ON R.RequisitionSubmissionTypeId = REQST.Id
	LEFT OUTER JOIN RequisitionVProBadgeLogs VPRO WITH (NOLOCK)
		ON R.BadgeLogId = VPRO.Id
	LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F 
		ON RI.Id = F.RequisitionItemId
	
END
GO


