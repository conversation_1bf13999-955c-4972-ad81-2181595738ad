﻿using RequisitionServices.DomainModel.Clinical;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class ClinicalDataController : ApiController
    {
        private IClinicalDataService clinicalDataService;

        public ClinicalDataController(IClinicalDataService cdSvc)
        {
            this.clinicalDataService = cdSvc;
        }

        [HttpGet]
        public IEnumerable<Provider> GetProviders(string userName, string COID)
        {
            return clinicalDataService.GetProviders(userName, COID);
        }

        [HttpGet]
        public IEnumerable<Patient> GetPatients(string userName, string COID)
        {
            return clinicalDataService.GetPatients(userName, COID);
        }

        [HttpGet]
        public Patient GetPatient(string userName, string COID, string patientId)
        {
            return clinicalDataService.GetPatient(userName, COID, patientId);
        }

        [HttpGet]
        public IEnumerable<Patient> SearchPatientsByName(string userName, string COID, string patientName)
        {
            return clinicalDataService.SearchPatientsByName(userName, COID, patientName);
        }
    }
}
