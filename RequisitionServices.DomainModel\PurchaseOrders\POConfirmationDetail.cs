﻿using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.PurchaseOrders
{
    public class POConfirmationDetail
    {
        public int POCCOID { get; set; }
        public int POCNumber { get; set; }
        public int POCLine { get; set; }
        public int POCSequentialNumber { get; set; }
        public string POCVendorPONumber { get; set; }
        public int POCItemNumber { get; set; }
        public DateTime POCDateTime { get; set; }
        public List<string> POConfirmationComments { get; set; }

        public POConfirmationDetail()
        {
            POConfirmationComments = new List<String>();
        }
    }
}