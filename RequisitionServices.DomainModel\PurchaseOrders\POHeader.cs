﻿using System;
using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.DomainModel.PurchaseOrders
{
    public class POHeader
    {
        public int ParentCOID { get; set; }

        public int COID { get; set; }

        public int PONumber { get; set; }

        public int Vendor { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime InvoicedDate { get; set; }

        public DateTime ClosedDate { get; set; }

        public DateTime DateFormatted { get; set; }

        public DateTime DateTransmitted { get; set; }

        public DateTime ConfirmedDate { get; set; }

        public string POStatus { get; set; }

        public int InvoiceNumber { get; set; }

        public Boolean POStatFlag { get; set; }

        public string POType { get; set; }

        public string POMethod { get; set; }

        public string FaxStatus { get; set; }

        public DateTime FaxDate { get; set; }

        public string EDIStatus { get; set; }

        public string EmailStatus { get; set; }

        public DateTime EmailDate { get; set; }

        public decimal Discount { get; set; }

        public int TotalLineItems { get; set; }

        public decimal TotalDollarAmount { get; set; }

        public decimal TotalCreditAmount { get; set; }

        public Address BillAddress { get; set; }

        public Address ShipAddress { get; set; }

        public string SpecialInstruction1 { get; set; }

        public string SpecialInstruction2 { get; set; }

        public string FacilityShipToCode { get; set; }

        public string BuyerID { get; set; }

        public string PatientName { get; set; }

        public string Comment1 { get; set; }

        public string Comment2 { get; set; }

        public string Comment3 { get; set; }

        public decimal TotalTax { get; set; }

        public int Department { get; set; }

        public bool OutstandingFlag { get; set; }

        public bool BackorderFlag { get; set; }

        public bool CreditFlag { get; set; }

        public bool ReturnFlag { get; set; }

        public decimal TotalCreditExpected { get; set; }

        public POInvoice POInvoice { get; set; }
    }
}