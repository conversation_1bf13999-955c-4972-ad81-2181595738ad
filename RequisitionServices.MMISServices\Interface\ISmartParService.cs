﻿using Microsoft.Web.Http;
using RequisitionServices.DomainModel.Items;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartParService
    {
        IEnumerable<Par> GetPars(string username, string coid, int? deptNumber = null);

        IEnumerable<ParItem> GetParItems(string username, string coid, int deptNumber, string parId);

        IEnumerable<ParItem> GetParItemsByItem(string username, string coid, int deptNumber, string itemNumber, ApiVersion version = null);

        Par GetParById(string username, string coid, int deptId, string parId);
    }
}
