﻿using System;
using System.Collections.Specialized;
using System.Linq;
using System.Web;
using log4net;
using Smart.Core.Common;

namespace RequisitionServices.Modules
{
    public class CorrelationIdModule : IHttpModule
    {
        public void Init(HttpApplication context)
        {
            context.BeginRequest += HandleBeginRequest;
        }

        public void Dispose()
        {
        }

        static void HandleBeginRequest(object sender, EventArgs e)
        {
            if (null != HttpContext.Current?.Request.Headers)
            {
                var context = HttpContext.Current;

                if (!ContainsKey(context.Request.Headers, Constants.HttpHeaders.XCorrelationId))
                {
                    context.Request.Headers.Add(Constants.HttpHeaders.XCorrelationId, Guid.NewGuid().ToString());
                }
                else if (context.Request.Headers[Constants.HttpHeaders.XCorrelationId]?.Trim() == string.Empty)
                {
                    context.Request.Headers[Constants.HttpHeaders.XCorrelationId] = Guid.NewGuid().ToString();
                }

                var correlationId = context.Request.Headers[Constants.HttpHeaders.XCorrelationId];

                context.Response.Headers.Add(Constants.HttpHeaders.XCorrelationId, correlationId);
                LogicalThreadContext.Properties[Constants.HttpHeaders.XCorrelationId] = correlationId;
            }
        }
        
        static bool ContainsKey(NameValueCollection collection, string key)
        {
            return collection.Get(key) != null || collection.AllKeys.Contains(key);
        }
    }
}