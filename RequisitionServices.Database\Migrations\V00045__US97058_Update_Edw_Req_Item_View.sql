USE [eProcurementQA]
GO

ALTER VIEW [dbo].[edwRequisitionItems]
AS
SELECT [Id]
      ,[RequisitionId]
      ,[ItemId]
      ,[ParIdentifier]
      ,[RequisitionItemStatusTypeId]
      ,[QuantityToOrder]
      ,[IsRushOrder]
      ,[CreatedBy]
      ,[CreateDate]
      ,[ParentSystemId]
      ,[MainItemId]
      ,[IsFileItem]
      ,[FileItemHasChanged]
      ,[QuantityFulfilled]
      ,[OriginalParentSystemId]
      ,[RequisitionScheduledDate]
      ,[PONumber]
      ,[SmartItemNumber]
      ,[ReOrder]
      ,[PARLocation]
      ,[ItemDescription]
      ,[VendorId]
      ,[VendorName]
      ,[GeneralLedgerCode]
      ,[StockIndicator]
      ,[UOMCode]
      ,[UnitCost]
      ,[TotalCost]
      ,[MinStock]
      ,[MaxStock]
      ,[CatalogNumber]
      ,[Discount]
	  ,CASE WHEN [Discount] IS NULL THEN 0
		ELSE 1 END AS 'Waste'
      ,[ParentRequisitionItemId]
  FROM [dbo].[RequisitionItems]
  GO


GO