﻿using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.DomainModel.Items
{
    public class Item
    {
        public Item()
        {
            AlternateUOMs = new List<AlternateUOM>();
        }

        public static Item ConvertLegacyRequisitionItemToItem(RequisitionItem legacyReqItem)
        {
            if (legacyReqItem != null)
            {
                return new Item()
                {
                    Id = legacyReqItem.ItemId,
                    ReorderNumber = legacyReqItem.ReOrder,
                    ManufacturerCatalogNumber = legacyReqItem.CatalogNumber,
                    Description = legacyReqItem.ItemDescription,
                    UOM = legacyReqItem.UOMCode,
                    PUOM = legacyReqItem.UOMCode, //Test to see which one is showing
                    Vendor = new Vendor()
                    {
                        Id = legacyReqItem.VendorId,
                        Name = legacyReqItem.VendorName
                    },
                    Price = legacyReqItem.UnitCost,
                    IsStock = legacyReqItem.StockIndicator ?? false,
                    ParId = legacyReqItem.ParIdentifier
                };
            }
            else
            {
                return new Item();
            }
        }
        public string Id { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public Vendor Vendor { get; set; }
        public string ReorderNumber { get; set; }
        public string ManufacturerCatalogNumber { get; set; }
        public string Location { get; set; } 
        public string Account { get; set; } 
        public string UOM { get; set; }
        public decimal? Price { get; set; }
        public int Factor { get; set; }
        public string ProcCode { get; set; }
        public int? LeadDays { get; set; }
        public bool IsStock {get; set;}
        public bool IsTempStock { get; set; }
        public bool IsCapitated { get; set; }
        public int? QuantityAvailable { get; set; }
        public int ContractNumber { get; set; }
        public string ComplianceCode { get; set; }

        public int GLSubCode { get; set; }

        public bool IsValidItem { get; set; }

        public string ParId { get; set; }

        public string DistributionPoint { get; set; }

        public bool Chargeable { get; set; }
        public string IUOM { get; set; }
        public string PUOM { get; set; }

        public string ItemType { get; set; }

        public string IUOMUPN { get; set; }

        public string PUOMUPN { get; set; }

        public int LastPO { get; set; }

        public Decimal CostIUOM { get; set; }

        public Decimal CostPUOM { get; set; }

        public int OnorderIUOM { get; set; }

        public int OnorderPUOM { get; set; }

        public string TaxCode { get; set; }

        public string AlgorInPatient { get; set; }

        public string AlgorOutPatient { get; set; }

        public int PurchaseDept { get; set; }

        public bool Labels { get; set; }

        public string PhysicalLoc { get; set; }

        public int ReorderPoint { get; set; }

        public string MfgVendName { get; set; }

        public int MfgVendNumber { get; set; }

        public long ExpGLAccount { get; set; }

        public long InvGLAccount { get; set; }

        public List<AlternateUOM> AlternateUOMs { get; set; }

        public bool IsActive { get; set; }
        public int ORUOMFactor { get; set; }
        public string ORUOM { get; set; }
        public string FactorDisplay { get; set; }
    }
}
