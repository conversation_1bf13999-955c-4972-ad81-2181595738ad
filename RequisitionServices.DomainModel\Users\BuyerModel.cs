﻿namespace RequisitionServices.DomainModel.Users
    {
    /// <summary>
    /// Basic model for a buyer for purchasing advanced filters
    /// </summary>
    public class BuyerModel
        {
        /// <summary>
        /// Gets or sets the buyer ID.
        /// </summary>
        public string BuyerId { get; set; }

        /// <summary>
        /// Gets or sets the buyer name and has a default of null.
        /// This null default is in case the buyer name is null from a past iteration of requisitions
        /// </summary>
        public string BuyerName { get; set; } = null;
        }
    }
