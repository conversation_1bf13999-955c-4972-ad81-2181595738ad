﻿using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices;
using RequisitionServices.Repositories.Interfaces;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class FacilityWorkflowServiceTests
    {
        static Mock<IFacilityWorkflowRepository> _mockFacilityWorkflowRepository;
        static Mock<IUserRepository> _mockUserRepository;

        static FacilityWorkflowService _facilityWorkflowService;

        [ClassInitialize]
        public static void ClassInit(TestContext context)
        {
            _mockFacilityWorkflowRepository = new Mock<IFacilityWorkflowRepository>();
            _mockUserRepository = new Mock<IUserRepository>();
        }

        [TestInitialize]
        public void Initialize()
        {
            _facilityWorkflowService = new FacilityWorkflowService(_mockFacilityWorkflowRepository.Object, _mockUserRepository.Object);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _facilityWorkflowService = null;
            _mockFacilityWorkflowRepository.Reset();
            _mockUserRepository.Reset();
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            _mockFacilityWorkflowRepository = null;
            _mockUserRepository = null;
        }

        [TestMethod]
        public void AssignDelegateForApprover_Returns_False_When_No_Steps_For_Approver()
        {
            _mockFacilityWorkflowRepository.Setup(x => x.GetStepsByApproverUsername(It.IsAny<string>())).Returns(new List<FacilityWorkflowStep>());

            var result = _facilityWorkflowService.AssignDelegateForApprover(0, "a", new User(), new User());

            Assert.IsFalse(result);
        }

        [TestMethod]
        public void AssignDelegateForApprover_Creates_Delegate_Approver()
        {
            var coid = "12345";

            _mockFacilityWorkflowRepository.Setup(x => x.GetStepsByApproverUsername(It.IsAny<string>())).Returns(new List<FacilityWorkflowStep>
            {
                new FacilityWorkflowStep { Coid = coid, Approver = new Approver{ COID = coid }}
            });

            _mockUserRepository.Setup(x => x.GetApproverByUserId(It.IsAny<int>(), It.IsAny<string>())).Returns((Approver)null);

            _mockUserRepository.Setup(x => x.InsertApprover(It.IsAny<Approver>(), It.IsAny<string>())).Returns(new Approver
            {
                COID = coid
            });

            var result = _facilityWorkflowService.AssignDelegateForApprover(0, "a", new User(), new User());

            _mockUserRepository.Verify(x => x.InsertApprover(It.IsAny<Approver>(), It.IsAny<string>()), Times.Once);
        }

        [TestMethod]
        public void AssignDelegateForApprover_Gets_Delegate_Approver()
        {
            var coid = "12345";

            _mockFacilityWorkflowRepository.Setup(x => x.GetStepsByApproverUsername(It.IsAny<string>())).Returns(new List<FacilityWorkflowStep>
            {
                new FacilityWorkflowStep { Coid = coid, Approver = new Approver{ COID = coid }}
            });

            _mockUserRepository.Setup(x => x.GetApproverByUserId(It.IsAny<int>(), It.IsAny<string>())).Returns(new Approver
            {
                COID = coid
            });

            var result = _facilityWorkflowService.AssignDelegateForApprover(0, "a", new User(), new User());

            _mockUserRepository.Verify(x => x.InsertApprover(It.IsAny<Approver>(), It.IsAny<string>()), Times.Never);
        }
    }
}
