﻿using log4net;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Constants;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Email;
using RequisitionServices.Utility.Domain;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Mail;
using System.Reflection;
using System.Text;
using System.Net.Mime;
using System.Configuration;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.MMISServices.Interface;

namespace RequisitionServices.DomainServices
{
    public class EmailService : IEmailService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private string homeAPIEndpoint = ConfigurationManager.AppSettings.Get("HomeAPIUrl");

        private const string getFacilityMethod = "Facility/Get/";

        private readonly ISmartDepartmentService _smartDepartmentService;
        readonly ISmartCOIDService _smartCoidService;

        readonly List<string> _testCoids = !string.IsNullOrWhiteSpace(ConfigurationManager.AppSettings.Get("coidsForQATesting")) ? ConfigurationManager.AppSettings.Get("coidsForQATesting").Split(',').ToList()
                : new List<string>();

        public EmailService(ISmartCOIDService smartCoidService, ISmartDepartmentService smartDepartmentService)
        {
            _smartCoidService = smartCoidService;
            _smartDepartmentService = smartDepartmentService;
        }

        public AlternateView GetEmailImages(string emailBody, string company)
        {
            var imageDictionary = EmailImages.GetImageDictionary(company);
            var altView = AlternateView.CreateAlternateViewFromString(emailBody, Encoding.UTF8, MediaTypeNames.Text.Html);

            imageDictionary.ToList().ForEach(image => {
                altView.LinkedResources.Add(new LinkedResource(image.Value, MediaTypeNames.Image.Jpeg) { ContentId = image.Key });
            });
            return altView;
        }

        public void SendEmail(EmailRequest request)
        {
            if (request.Emails != null && request.Emails.Any())
            {
                var facilityName = "";
                var departmentName = "";
                var requisitionId = "";
                string emailDate, subject;
                var facility = new Facility();
                Department department = null;

                if (request.Requisition != null)
                {
                    var coid = LocationMapper.GetCOID(request.Requisition.LocationIdentifier);
                    var departmentNumber = Int32.Parse(LocationMapper.GetDepartmentId(request.Requisition.LocationIdentifier));
                    facility = this.GetFacility(request.UserName, coid);
                    facilityName = facility == null ? $"COID ({ coid })" : $"{ facility.Name.ToUpperInvariant() } ({ facility.COID })";
                    if (!String.IsNullOrWhiteSpace(request.UserName))
                    {
                        department = _smartDepartmentService.GetDepartment(request.UserName, coid, departmentNumber);
                    }
                    departmentName = department == null ? $"DEPT ({ departmentNumber })" : $"{department.Description?.ToUpperInvariant() } ({department.Id })";
                    emailDate = request.ActionDate.ToString("g", LocationMapper.GetCultureForFormattingUsingSmartCountryCode(request.Requisition.CountryCode));
                    subject = Email.GetSubjectMessage(request.EmailType, request.Requisition.RequisitionId);
                    requisitionId = String.Format("<strong>{0}</strong>", request.Requisition.RequisitionId.ToString());
                }
                else
                {
                    subject = Email.GetSubjectMessage(request.EmailType);
                    emailDate = request.ActionDate.ToString();
                }

                var recommendation = request.Recommendation ? "Approve" : "Deny";
                var isApprovalAmountMet = request.IsApprovalAmountMet ? "Yes" : "No";
                var hasStockAndNonStockRushItems = request.HasStockAndNonStockRushItems ? "Yes" : "No";

                var basePath = GetPath("EmailTemplates/ConsolidatedTemplates/BaseEmailTemplate.html");
                var bodyPath = GetPath(Email.Emails[(int)request.EmailType].BodyTemplate);

                string template = File.OpenText(basePath).ReadToEnd();
                string bodyTemplate = File.OpenText(bodyPath).ReadToEnd();

                template = template.Replace("$emailBody$", bodyTemplate)
                                   .Replace("$emailHeadline$", Email.Emails[(int)request.EmailType].Headline)
                                   .Replace("$bodyMessage$", Email.Emails[(int)request.EmailType].BodyMessage)
                                   .Replace("$viewLink$", request.ViewLink)
                                   .Replace("$senderName$", request.SenderName)
                                   .Replace("$requisitionId$", requisitionId)
                                   .Replace("$actionDate$", emailDate)
                                   .Replace("$vendors$", request.Vendors)
                                   .Replace("$parStockItems$", request.RushStockItems)
                                   .Replace("$rushStockItems$", request.RushStockItems)
                                   .Replace("$rushNonStockItems$", request.RushNonStockItems)
                                   .Replace("$comments$", request.Comments)
                                   .Replace("$recommendation$", recommendation)
                                   .Replace("$isApprovalAmountMet$", isApprovalAmountMet)
                                   .Replace("$facilityName$", facilityName)
                                   .Replace("$departmentName$", departmentName)
                                   .Replace("$hasStockAndNonStock$", hasStockAndNonStockRushItems)
                                   .Replace("$stockDeliveryMethod$", request.StockDeliveryMethod)
                                   .Replace("$nonStockDeliveryMethod$", request.NonStockDeliveryMethod);

                SendEmail(null, request.Emails, subject, template, facility.CompanyName);
            }
        }

        private void SendEmail(string[] toAddresses, string[] bCCAddresses, string subject, string body, string companyName = null, bool highPriority = false)
        {
            log.Info(String.Format("Email Sending: {0}, To: {1}, BCC: {2}", subject, String.Join(";", toAddresses ?? new String[0]), String.Join(";", bCCAddresses ?? new String[0])));

            using (var message = new MailMessage())
            {
                if (toAddresses != null && toAddresses.Any())
                {                   
                    foreach (var toAddress in toAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(toAddress))
                        {
                            message.To.Add(toAddress);
                        }
                    }
                }

                if (bCCAddresses != null && bCCAddresses.Any())
                {
                    foreach (var bCCAddress in bCCAddresses)
                    {
                        if (!string.IsNullOrWhiteSpace(bCCAddress))
                        {
                            message.Bcc.Add(bCCAddress);
                        }
                    }
                }

                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = true;
                message.AlternateViews.Add(this.GetEmailImages(body, companyName)); 

                if (highPriority) message.Priority = MailPriority.High;

                try
                {
                    using (var client = new SmtpClient())
                    {
                        client.Send(message);
                    }
                }
                catch (Exception ex)
                {
                    log.Error(String.Format("Email Sending: {0}, To: {1}, BCC: {2}", subject, String.Join(";", toAddresses ?? new String[0]), String.Join(";", bCCAddresses ?? new String[0])), ex);
                }
            }
        }

        private string GetPath(string template) 
        {
            string codeBase = Assembly.GetExecutingAssembly().CodeBase;
            UriBuilder uri = new UriBuilder(codeBase);
            string path = Uri.UnescapeDataString(uri.Path);
            return Path.Combine(Path.GetDirectoryName(path), template).Replace("bin\\EmailTemplates", "EmailTemplates");
        }

        private Facility GetFacility(string username, string coid)
        {
            if (_testCoids.Any(x => string.Equals(x, coid, StringComparison.InvariantCultureIgnoreCase)))
            {
                var facility = new Facility(_smartCoidService.GetCOID(username, coid))
                {
                    CompanyName = "HCA"
                };
                return facility;
            }

            var facilityDTO = ApiUtility.ExecuteApiGetTo<Facility>(homeAPIEndpoint, getFacilityMethod, new Dictionary<string, string>() { { "cOID", coid } });  
              
            return facilityDTO;
        }

        public (string rushStockItems, string rushNonStockItems, bool hasStockAndNonStockRushItems) GetItemsForRushEmailTemplate(IEnumerable<RequisitionItemWithDetailsDTO> items, bool isSubmitted)
        {
            var rushItems = items.Where(x => x.IsRushOrder == true);

            var stockItems = rushItems.Where(x => x.Item.IsStock);
            var nonStockItems = rushItems.Where(x => !x.Item.IsStock);

            return (
                stockItems.Any() ? FormatRushItemInfo(rushItems.Where(x => x.Item.IsStock), isSubmitted) : String.Empty,
                nonStockItems.Any() ? FormatRushItemInfo(rushItems.Where(x => !x.Item.IsStock), isSubmitted) : String.Empty,
                stockItems.Any() && nonStockItems.Any());
        }

        private string FormatRushItemInfo(IEnumerable<RequisitionItemWithDetailsDTO> rushItems, bool isSubmitted)
        {
            string emailFormattedItems = "<dl>";

            foreach (var rushItem in rushItems)
            {
                emailFormattedItems += "<dt>";
                if (rushItem.SPRDetailDTO == null)
                {
                    emailFormattedItems += $"{ rushItem.ParIdentifier?.ToUpperInvariant() }: ";
                }
                else
                {
                    emailFormattedItems += $"SPR: ";
                }
                emailFormattedItems += $"{ rushItem.Item.Description.ToUpperInvariant() } ({ rushItem.Item.Id })</dt>";
                emailFormattedItems += $"<dd>- { rushItem.Item.Vendor.Name.ToUpperInvariant() } ({ rushItem.Item.Vendor.Id })</dd>";
                if (isSubmitted)
                {
                    emailFormattedItems += $"<dd>- SMART REQ #: { rushItem.ParentSystemId.ToUpperInvariant() }</dd>";
                }
            }

            return emailFormattedItems;
        }

    }
}
