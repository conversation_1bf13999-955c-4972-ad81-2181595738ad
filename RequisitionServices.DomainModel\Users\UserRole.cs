﻿namespace RequisitionServices.DomainModel.Users
{
    public class UserRole
    {
        /// <summary>
        /// Represents the Role Name from NSA.
        /// </summary>
        public string <PERSON>Name { get; set; }

        /// <summary>
        /// Represents the Directory group name for the Role.
        /// </summary>
        public string AdGroupName { get; set; }
    }
    public class UserRoleNew
    {
        /// <summary>
        /// 
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// Represents the Role Name from NSA.
        /// </summary>
        /// 
        public string Name { get; set; }
        /// <summary>
        /// Represents the Directory group name for the Role.
        /// </summary>
        public string UserType { get; set; }

        public string RoleName
        {
            get { return Name; }
            set { Name = value; }
        }
        public string AdGroupName { get; set; }
    }
}
