﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Utility.Domain;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.MMISServices.DTO
{
    public class CapitatedRequisitionModel
    {
        public CapitatedRequisitionModel() { }
        public CapitatedRequisitionModel(Requisition requisition, IEnumerable<RequisitionItem> requisitionItems)
        {
            if (requisition != null)
            {
                var coid = Int32.Parse(LocationMapper.GetCOID(requisition.LocationIdentifier));

                this.Coid = coid;
                this.SmartCountryCode = requisition.CountryCode;
                this.RequisitionId = requisition.RequisitionId;
                this.ParDepartment = Int32.Parse(LocationMapper.GetDepartmentId(requisition.LocationIdentifier));
                this.UserComments = requisition.Comments;
                
                if (requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace)
                {
                    this.BillOnlySwitch = false;
                }
                else
                {
                    this.BillOnlySwitch = true;
                }

                if (requisitionItems != null)
                {
                    //Find main item and add to requisition as main item
                    var mainItem = requisitionItems.First(x => x.MainItemId == null);
                    if(mainItem != null)
                    {
                        this.ItemNumber = int.Parse(mainItem.ItemId);
                        this.MainItemUniqueId = mainItem.Id;
                        if (mainItem.ClinicalUseDetails != null && mainItem.ClinicalUseDetails.Any())
                        {
                            this.DoctorName = mainItem.ClinicalUseDetails.First().Provider;
                            this.PatientId = mainItem.ClinicalUseDetails.First().PatientId;
                            this.PatientName = mainItem.ClinicalUseDetails.First().PatientName;
                            this.ProcedureDate = mainItem.ClinicalUseDetails.First().ProcedureDate ?? DateTime.Now;
                        }
                    }

                    this.ParClass = requisitionItems.First().ParIdentifier;

                    this.RequisitionItems = new List<CapitatedRequisitionItemModel>();
                    foreach (var reqItem in requisitionItems.Where(x => x.MainItemId != null))
                    {
                        var lotNumbers = new List<string>();
                        var serialNumbers = new List<string>();
                        if (reqItem.ClinicalUseDetails != null && reqItem.ClinicalUseDetails.Any())
                        {
                            foreach (var clinicaldetail in reqItem.ClinicalUseDetails)
                            {
                                if (!string.IsNullOrEmpty(clinicaldetail.LotNumber))
                                {
                                    lotNumbers.Add(clinicaldetail.LotNumber);
                                }
                                if (!string.IsNullOrEmpty(clinicaldetail.SerialNumber))
                                {
                                    serialNumbers.Add(clinicaldetail.SerialNumber);
                                }
                            }
                        }
                        this.RequisitionItems.Add(new CapitatedRequisitionItemModel()
                            {
                                Id = reqItem.Id,
                                RequisitionId = reqItem.Id,
                                Coid = coid,
                                ItemNumber = int.Parse(reqItem.ItemId),
                                ItemQuantity = reqItem.QuantityToOrder,
                                LotNumbers = lotNumbers,
                                SerialNumbers = serialNumbers,
                                ChargeCost = reqItem.ClinicalUseDetails?.FirstOrDefault()?.UpchargeCost ?? 0
                        });
                    }
                }
            }
        }

        public int RequisitionId { get; set; }

        public int Coid { get; set; }

        public string SmartCountryCode { get; set; }

        public int ParDepartment { get; set; }

        public string ParClass { get; set; }

        public string DoctorName { get; set; }

        public string PatientId { get; set; }

        public string PatientName { get; set; }

        public DateTime ProcedureDate { get; set; }

        public string UserComments { get; set; }

        public int ItemNumber { get; set; }

        public bool BillOnlySwitch { get; set; }

        public bool ComponentSwitch { get; set; }

        public List<CapitatedRequisitionItemModel> RequisitionItems { get; set; }

        public int MainItemUniqueId { get; set; }
    }
}
