﻿using System;

namespace RequisitionServices.DomainModel.Users
{
    public class Profile
    {
        private string _domain;
        private string _userName;
        
        public string Domain 
        {
            get => _domain;
            set => _domain = value.ToLower();
        }
        public string UserName 
        {
            get => _userName;
            set => _userName = value.ToLower();
        }
        public string UserType { get; set; }
        public string UserTypeName { get; set; }
        public string Title { get; set; }
        public string Department { get; set; }
        public string Coid { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Telephone { get; set; }
        public string Email { get; set; }


        public string FullDomainUserName => $"{this.Domain}/{this.UserName}";

        public string FullName
        {
            get
            {
                return this.FirstName + " " + this.LastName;
            }
        }
    }
}
