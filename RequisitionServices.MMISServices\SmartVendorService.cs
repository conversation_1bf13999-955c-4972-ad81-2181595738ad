﻿using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;

namespace RequisitionServices.MMISServices
{
    public class SmartVendorService : ISmartVendorService
    {
        private const string getVendorByIdMethod = "Vendors/GetVendorById/";
        private const string getVendorHeadersMethod = "Vendors/GetAllVendors/";
        private const string getVendorInformationByIdMethod = "Vendors/GetVendorInformationById/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public Vendor GetVendor(string userName, string coid, int vendorNumber)
        {
            var vendorRecordModel = CallSmartGetVendorById(userName, coid, vendorNumber);
            Vendor vendor = null;
            if (vendorRecordModel != null)
            {
                vendor = vendorRecordModel.MapToVendor();
            }

            return vendor;
        }

        public Vendor GetVendorInformationById(string userName, string coid, int vendorNumber)
        {
            var vendorRecordModel = CallSmartGetVendorInformationById(userName, coid, vendorNumber);
            Vendor vendor = null;
            if (vendorRecordModel != null)
            {
                vendor = vendorRecordModel.MapToVendor();
            }

            return vendor;
        }

        public VendorDetails GetVendorDetails(string userName, string coid, int vendorNumber)
        {
            var vendorRecordModel = CallSmartGetVendorById(userName, coid, vendorNumber);
            VendorDetails vendor = null;
            if (vendorRecordModel != null)
            {
                vendor = vendorRecordModel.MapToVendorDetails();
            }
            return vendor;
        }

        public List<VendorHeaderInfo> GetAllVendorHeaders(string userName, string coid)
        {
            var returnList = new List<VendorHeaderInfo>();

            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            var vendorHeadersModel = ApiUtility.ExecuteApiGetTo<VendorHeadersModel>(endpoint, getVendorHeadersMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });
            foreach (var vHeader in vendorHeadersModel.Vendors)
            {
                returnList.Add(vHeader.MapToVendorHeaderInfo());
            }

            return returnList;
        }

        private VendorRecordModel CallSmartGetVendorById(string userName, string coid, int vendorNumber)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            var vendorRecordModel = ApiUtility.ExecuteApiGetTo<VendorRecordModel>(endpoint, getVendorByIdMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "vendorNumber", vendorNumber.ToString() }
                                                                            });
            return vendorRecordModel;
        }


        private VendorInformationModel CallSmartGetVendorInformationById(string userName, string coid, int vendorNumber)
        {
            SmartInputValidator.CheckUserName(ref userName);
            SmartInputValidator.CheckCoid(coid);

            var vendorInformationModel = ApiUtility.ExecuteApiGetTo<VendorInformationModel>(endpoint, getVendorInformationByIdMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "vendorNumber", vendorNumber.ToString() }
                                                                            });
            if (vendorInformationModel != null)
            {
                if (vendorInformationModel.APvendFlag.ToString() == "Y" && vendorInformationModel.FvendFlag.ToString() == "Y" && vendorInformationModel.VendOnHold.ToString() == "N")
                {
                    return vendorInformationModel;
                }
            }
            return null;
        }

    }
}
