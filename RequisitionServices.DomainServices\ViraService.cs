﻿using log4net;
using RequisitionServices.DomainModel.Vira;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices
{
    /// <summary>
    /// Service class for handling Vira item status operations.
    /// </summary>
    public class ViraService : IViraService
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(ViraService));
        private readonly IViraRepository _viraRepository;

        public ViraService(IViraRepository viraRepository)
        {
            _viraRepository = viraRepository;
        }

        public async Task<ViraItemStatus> GetViraItemStatusRecordById(int requisitionId, int requisitionItemId)
        {
            try
            {
                return await _viraRepository.GetViraItemStatusRecordById(requisitionId, requisitionItemId);
            }
            catch (Exception ex)
            {
                log.Error($"Error getting Vira item status record by ID: {requisitionItemId}", ex);
                throw;
            }
        }

        public async Task<ViraItemStatus> CreateViraItemStatusRecord(ViraItemStatus viraItemStatus)
        {
            try
            {
                return await _viraRepository.CreateViraItemStatusRecord(viraItemStatus);
            }
            catch (Exception ex)
            {
                log.Error("Error creating Vira item status record", ex);
                throw;
            }
        }

        public async Task<string> DeleteViraItemStatusRecord(int requisitionId, int requisitionItemId)
        {
            try
            {
                return await _viraRepository.DeleteViraItemStatusRecord(requisitionId, requisitionItemId);
            }
            catch (Exception ex)
            {
                log.Error($"Error deleting Vira item status record by ID: {requisitionItemId}", ex);
                throw;
            }
        }

        public async Task<string> UpdateViraItemStatusRecord(ViraItemStatus viraItemStatus)
        {
            try
            {
                return await _viraRepository.UpdateViraItemStatusRecord(viraItemStatus);
            }
            catch (Exception ex)
            {
                log.Error("Error updating Vira item status record", ex);
                throw;
            }
        }
    }
}
