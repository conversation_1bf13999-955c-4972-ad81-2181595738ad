﻿using System;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Utilities
{
    public static class ExtensionMethods
    {
        public static bool IsValidItemIdForSMART(this string itemId)
        {
            var valid = false;

            int result = 0;
            if (int.TryParse(itemId, out result))
            {
                if (result > 0)
                {
                    valid = true;
                }
            }

            return valid;
        }
        public static IEnumerable<TSource> DistinctBy<TSource, TKey>(this IEnumerable<TSource> source,
     Func<TSource, TKey> keySelector, IEqualityComparer<TKey> comparer)
        {
            var knownKeys = new HashSet<TKey>(comparer);
            foreach (var element in source)
            {
                if (knownKeys.Add(keySelector(element)))
                {
                    yield return element;
                }
            }
        }

        public static IEnumerable<TSource> DistinctBy<TSource, TKey>(this IEnumerable<TSource> source,
            Func<TSource, TKey> keySelector)
        {
            return source.DistinctBy(keySelector, null);
        }
    }
}
