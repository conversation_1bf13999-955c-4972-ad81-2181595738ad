USE [eProcurementQA]
GO

SET ANSI_NULLS ON
GO

CREATE TABLE [dbo].[ViraItemStatus](
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[RequisitionId] [int] NOT NULL,
    [RequisitionItemId] [int] NOT NULL,
    [PublishStatus] [bit] NOT NULL,
    [RetryCount] [int] NOT NULL,
    [LastRetry] [datetime] NULL,
    [ViraApprovalStatus] [varchar](50) NULL
 CONSTRAINT [PK_ViraItemStatus] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[ViraItemStatus] ADD  DEFAULT ((0)) FOR [RetryCount]
GO

ALTER TABLE [dbo].[ViraItemStatus] ADD  DEFAULT ((0)) FOR [PublishStatus]
GO