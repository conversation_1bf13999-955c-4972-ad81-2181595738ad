using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices.Interface
{
    /// <summary>
    /// Handles services specific to the DigitalSignOff process
    /// </summary>
    public interface IDigitalSignOffService
    {
        /// <summary>
        /// Reads a record from RequisitionDigitalSignOffs by the RequisitionId passed if exists
        /// </summary>
        /// <returns>single RequisitionDigitalSignOff Record</returns>
        Task<RequisitionDigitalSignOff> GetDigitalSignOff(int requisitionId);
        /// <summary>
        /// Reads all records from RequisitionDigitalSignOffs by the RequisitionId passed if exists
        /// </summary>
        /// <returns>single RequisitionDigitalSignOff Record</returns>
        Task<IEnumerable<RequisitionDigitalSignOff>> GetAllRequisitionDigitalSignOffsByRequisitionId(int requisitionId);
        /// <summary>
        /// Creates new record in RequisitionDigitalSignOffs table
        /// </summary>
        /// <returns>all RequisitionDigitalSignOff records by requisition id</returns>
        Task<RequisitionDigitalSignOff> CreateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff);
        /// <summary>
        /// Updates a record columns or columns from RequisitionDigitalSignOffs by the digital sign off Id
        /// </summary>
        /// <returns>updated RequisitionDigitalSignOff Record</returns>
        Task UpdateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff,int digitalSignOffId);
        /// <summary>
        /// Soft deletes record by Id in RequisitionDigitalSignOffs table
        /// </summary>
        /// <returns></returns>
        Task DeleteRequisitionDigitalSignOff(int digitalSignOffId);

        /// <summary>
        /// Reads single record from RequisitionDigitalSignOffs 
        /// </summary>
        /// <returns>single RequisitionDigitalSignOff Record</returns>
        Task <IEnumerable<DigitalSignOffUser>> GetAllDigitalSignOffUsers();

        /// <summary>
        /// Gets singe user record based off 3/4 ID
        /// </summary>
        /// <returns>single GetSingleDigitalSignOffUser Record</returns>
        Task <DigitalSignOffUser> GetSingleDigitalSignOffUser(string accountName);
        /// <summary>
        /// Add Record to DB with data from AD
        /// </summary>
        Task<DigitalSignOffUser> CreateDigitalSignOffUser(DigitalSignOffUser request);
        /// <summary>
        /// Updates record based off of ID thats passed after finding a discrepancy in AD
        /// </summary>
        Task UpdateSingleDigitalSignOffUser(DigitalSignOffUser request);
        /// <summary>
        /// Returns the
        /// </summary>
        /// <returns>single GetSingleDigitalSignOffUser Record</returns>
        Task <ApproverDigitalSignOffDTO> GetApproverDigitalSignOff(int requisitionId);

    }
}