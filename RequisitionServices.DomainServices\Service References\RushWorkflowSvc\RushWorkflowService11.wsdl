<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://local-approvalworkflow.healthtrustpg.com/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="http://local-approvalworkflow.healthtrustpg.com/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://local-approvalworkflow.healthtrustpg.com/Imports">
      <xsd:import schemaLocation="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd1" namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" />
      <xsd:import schemaLocation="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd0" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd2" namespace="http://local-approvalworkflow.healthtrustpg.com/" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IRushApprovalWorkflow_StartRushApproval_InputMessage">
    <wsdl:part xmlns:q1="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" name="Requisition" element="q1:Requisition" />
  </wsdl:message>
  <wsdl:message name="IRushApprovalWorkflow_StartRushApproval_OutputMessage">
    <wsdl:part xmlns:q2="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" name="Requisition" element="q2:Requisition" />
  </wsdl:message>
  <wsdl:message name="IRushApprovalWorkflow_ApproveRushRequisition_InputMessage">
    <wsdl:part name="parameters" element="tns:ApproveRushRequisition" />
  </wsdl:message>
  <wsdl:portType name="IRushApprovalWorkflow">
    <wsdl:operation name="StartRushApproval">
      <wsdl:input wsaw:Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushApproval" message="tns:IRushApprovalWorkflow_StartRushApproval_InputMessage" />
      <wsdl:output wsaw:Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushApprovalResponse" message="tns:IRushApprovalWorkflow_StartRushApproval_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ApproveRushRequisition">
      <wsdl:input wsaw:Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/ApproveRushRequisition" message="tns:IRushApprovalWorkflow_ApproveRushRequisition_InputMessage" />
    </wsdl:operation>
  </wsdl:portType>
</wsdl:definitions>