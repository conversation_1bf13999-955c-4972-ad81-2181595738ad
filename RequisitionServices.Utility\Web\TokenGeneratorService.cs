﻿using log4net;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Net.Http;
using System.Reflection;

namespace RequisitionServices.Utility.Web
{
    public class TokenGeneratorService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        readonly string _smartClinetAuthUrl = ConfigurationManager.AppSettings.Get("SmartClinetAuthUrl");
        readonly string _sso_grant_type = ConfigurationManager.AppSettings.Get("SSO_grant_type");
        readonly string _sso_client_id = ConfigurationManager.AppSettings.Get("SSO_client_id");
        readonly string _sso_client_secret = ConfigurationManager.AppSettings.Get("SSO_client_secret");
        readonly string _sso_scope = ConfigurationManager.AppSettings.Get("SSO_scope");
        readonly string _sso_response_type = ConfigurationManager.AppSettings.Get("SSO_response_type");


        /// <summary>
        /// Executes a HTTP POST call to the specified URL/Action given HTTPContent
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T GetSmartClientAuthToken<T>()
        {
            log.Debug($"Initiating call {_smartClinetAuthUrl}");
            var parameters = new Dictionary<string, string>
                            {
                                { "grant_type", _sso_grant_type },
                                { "client_id", _sso_client_id },
                                { "client_secret", _sso_client_secret },
                                { "scope", _sso_scope },
                                { "response_type", _sso_response_type },
                            };
            var response = ApiUtility.ExecuteApiPostToGetSecurityToken<T>(_smartClinetAuthUrl,
                new FormUrlEncodedContent(parameters));

            return response;
        }
    }
}
