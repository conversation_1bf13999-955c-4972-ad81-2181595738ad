﻿using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IWorkflowService
    {
        void DenyRequisition(DomainModel.Requisitions.Requisition requisition, int denierId);

        void ApproveRequistion(DomainModel.Requisitions.Requisition requisition, int approverId, int approvalStep);

        Requisition StartWorkflow(Requisition requisition, string userName);

        void DeleteAllWorkflow();

        void DeleteRequisitionFromWorkflow(int requisitionId, Guid workflowInstanceId);

        IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserName(string userName);

        IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserId(int userId);

        void ReInitializeAllWorkflow(string userName);

        void ReInitializeWorkflowByRequisition(int requisitionId, string userName, bool updateRequisition = false);

        void ReInitializeWorkflowByRequisition(Requisition requisition, string userName, bool updateRequisition = false);
    }
}
