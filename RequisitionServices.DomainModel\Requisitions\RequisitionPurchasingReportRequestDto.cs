﻿using Newtonsoft.Json.Converters;
using Newtonsoft.Json;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;

namespace RequisitionServices.DomainModel.Requisitions
{
    
    public class PurchasingRequisitionReportParameters
    {
        /// <summary>
        /// The List of coids to filter by (mandatory)
        /// </summary>
        [Required]
        public List<string> Coids { get; set; }

        /// <summary>
        /// The List of optional advanced filters
        /// </summary>
        public RequisitionPurchasingAdvancedFiltersDto AdvancedFiltersDto { get; set; } = new RequisitionPurchasingAdvancedFiltersDto();

        /// <summary>
        /// Page number for pagination (optional, defaults to 1)
        /// </summary>
        public int PageNumber { get; set; } = 1;

        /// <summary>
        /// Number of records to return per page (optional, defaults to 10)
        /// </summary>
        public int PageSize { get; set; } = 25;

        /// <summary>
        /// Column to sort by (optional)
        /// </summary>
        public string SortColumn { get; set; } = "RequisitionID";
        
        /// <summary>
        /// Sort direction (ASC or DESC) (optional, defaults to ASC)
        /// </summary>
        public string SortType { get; set; } = "ASC";

        /// <summary>
        /// field for holding username needed to get parentSystemInfo
        /// </summary>
        public string UserName { get; set; }

    }
}