﻿namespace RequisitionServices.DomainModel.Items
{
    public class FStoreDeptModel
    {
        /// <summary>
        /// Department Number
        /// </summary>
        public int DeptNum { get; set; }

        /// <summary>
        /// Maximum Quantity
        /// </summary>
        public int Max { get; set; }

        /// <summary>
        /// Minimum Quantity
        /// </summary>
        public int Min { get; set; }

        /// <summary>
        /// Reorder Point
        /// </summary>
        public int ReOrderPoint { get; set; }

        /// <summary>
        /// Reorder Point
        /// </summary>
        public long IGLAccount { get; set; }

        /// <summary>
        /// Location
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// Quantity on Hand
        /// </summary>
        public int QOH { get; set; }

        /// <summary>
        /// Quantity on Order
        /// </summary>
        public int QOO { get; set; }

        /// <summary>
        /// Unit of Measure
        /// </summary>
        public string UOM { get; set; }

        /// <summary>
        /// Independent Order Flag
        /// </summary>
        public bool IndOrdFlg { get; set; }

        /// <summary>
        /// Department Name
        /// </summary>
        public string DeptName { get; set; }

    }
}
