﻿using RequisitionServices.DomainModel.Items;
using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class ParItemModel
    {
        public ParItem MapToParItem()
        {
            return new ParItem()
            {
                ParId = this.ParId,
                ItemId = this.ItemNumber,
                MinStock = this.Min,
                MaxStock = this.Max,
                IssueUOM = this.IUOM,
                ParDescription = this.ParDescription,
                ParType = this.ParType,
                GLAccount = this.GLAccount,
                Location = this.Location,
                ParPrice = this.ParPrice,
                ManualOrderFlag = this.ManualOrderFlag
            };
        }

        public string ParId { get; set; }

        public string ParDescription { get; set; }

        public int ParDept { get; set; }

        public int ParItemCoid { get; set; }

        public int ItemNumber { get; set; }

        public int Min { get; set; }

        public int Max { get; set; }

        public string IUOM { get; set; }

        public long GLAccount { get; set; }

        public bool ChargableFlag { get; set; }

        public string Location { get; set; }

        public int Level { get; set; }

        public bool ManualOrderFlag { get; set; }

        public DateTime IssueDate { get; set; }

        public string JustInTimeFlag { get; set; }

        public bool FillKillFlag { get; set; }

        public bool RoundUpFlag { get; set; }

        public bool LotNumberFlag { get; set; }

        public bool SerialNumberFlag { get; set; }

        public int ParType { get; set; }

        public string ItemType { get; set; }

        public bool ConsignmentFlag { get; set; }

        public decimal ParPrice { get; set; }
    }
}
