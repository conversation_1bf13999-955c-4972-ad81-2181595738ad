﻿using RequisitionServices.DomainModel.Users;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IWorkflowRepository
    {
        IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID);

        IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, int workflowTypeId);

        IEnumerable<UserWorkflowStep> GetMultipleUsersWorkflowStepsWithoutDelegates(IEnumerable<string> userNames, string COID);

        IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string userName, int workflowTypeId, string COID, IEnumerable<UserWorkflowStep> userworkflowSteps);

        IEnumerable<UserWorkflowStep> GetApproverWorkflowSteps(string approverUserName);

        IEnumerable<UserWorkflowStep> GetApproverWorkflowSteps(string approverUserName, string COID);

        IEnumerable<UserWorkflowStep> GetApproverWorkflowStepsList(string approverUserName, string COID);
        
        IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserName(string userName);

        IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserId(int userId);

        List<UserWorkflowStep> GetDistinctWorkflowSteps(int userId, int workflowType, string COID);

        Task<List<UserWorkflowStep>> GetDistinctWorkflowStepsAsync(int userId, int workflowType, string coid);
        
        UserWorkflowStep InsertWorkflowStep(UserWorkflowStep userworkflowStep);

        void DeleteDelegatedSteps(int delegatedByUserId);

        void DeleteDelegateSteps(int delegateApproverId);

        void DeleteWorkflowStep(UserWorkflowStep userworkflowStep);

        UserWorkflowStep GetWorkflowStepForStatusHistory(int approverId, int step, int workflowTypeId, string COID);

        UserWorkflowStep GetWorkflowStepForStatusHistory(int approverId, int step, int workflowTypeId, string COID, int userid);

        void SaveBulkApproverJobDetails(BulkApproverJobTracker bulkApproverJobTracker);

        List<BulkApproverJobTracker> GetBulkApproverJobDetails(string userName);
    }
}
