﻿.tableContainer2 {
    margin-left: 60px;
    padding: 10px;
    margin-right: 5px;
}

.countPerPageBar {
    display: flex;
    margin-bottom: 4px;
    padding: 3px;
    margin-right: 2px;
}

.billOnlyReviewRow {
    padding-top: 10px;
}

.bor-button-alignment {
    margin-top: 0;
}

#bill-only-patient-id-input, 
#bill-only-procedure-date-input, 
#bill-only-provider-last-name-input, 
#bill-only-patient-last-name-input {
    margin: 2px 0px 5px 0px;
    max-width: fit-content;
    margin-right: 20px;
}

.billOnlyWidgetHeader {
    background: #f3f3f3;
    border-bottom: 1px solid #ddd;
    padding: 8px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    overflow:visible;
}

.select-sm-bor {
    height: 30px;
    padding: 4px 6px;
    font-size: 13px;
    width: fit-content;
}

.searchErrorMessage {
    color: #ff0000; 
    position: absolute; 
    transform: translate(0px, -13px);
}

.form-group-bor {
    display: inline-block;
    margin-bottom: 0;
}

.borButtonFormGroup {
    align-content: center;
    padding-inline-end: 8em;
}

.glyphicon-calendar-bor {
    color:black;
}

.filter-label {
    color: black;
    font-weight: 700;
    padding-right: 10px;
}

.bor-input-group {
    width: fit-content;
    align-content: center;
}

.bor-filter-group {
    max-width: 22em;
    max-height: 5rem;
}

.bor-procedure-date-filter-group {
    max-width: 18.7em; 
    align-content: center;
    max-height: 3.5em;
    margin-right: 3.3em;
}

.input-group.borid-search{
    display: inline-flex;
    flex-grow: 1; 
    flex-wrap: nowrap;
    max-width: none; 
}

.bor-reqfilter-clear {
    display: flex;
    align-self: center;
    padding-left: 15px;
    padding-bottom: 5px;
    padding-top: 5px;
    padding-right: 15px;
    min-width: 150px;
}

.borButtonFormGroup{
    padding-top: 10px;
}
.clear-all-proc-date {
    position: absolute;
    right: 10px;
    top: 8px;
    transform: translate(-3.2em, -0.1em);
    cursor: pointer;
    font-size: 16px;
    font-weight: 700;
    color: rgb(0 0 0);
    z-index: 10;
}

.borBasicDateSelectorGroup {
    min-width: 11.5em;
    padding-top: 2px;
}

/* filter-dropdown */
.accordion__container {
    background: #eee;
    border: 1px solid #ccc;
    padding: 20px;
}

.accordion__tab {
    background: #aaa;
    color: #fff;
    border-bottom: 1px solid white;
}

.accordion__tab--title {
    background: #888;
    padding: 5px;
    cursor: pointer;
}

.accordion__tab--content {
    align-content: center;
    background: #ededed;
    padding: 10px;
}

.bor-filter-dropdown {
    align-content: center; 
    font-size: 15px; 
    margin-right: 4em; 
    margin-bottom: 0px; 
    width: fit-content;
}

.bor-facility-dropdown-filter {
    display:flex;
    flex-wrap:wrap;    
    margin: 10px;
}

.bor-dropdown-filter-group {
    max-width: 22em;
    align-content: end;
    max-height: 5em;
    margin-right: 6.5em;
}

#facilityButton {
    color: black;
    margin: 20px 0px 0px 0px;
    padding: 5px 40px;
    width: 195px;
}

.borFacilityDropdownFilterInput {
    width: auto;
}

#selectAllFacilities {
    color: black;
}

/* media queries */
@media (min-width:768px) and (max-width:1439px) {
    .clear-button{
        position: absolute;
        right: 10px;
        top: 8px;
        transform: translate(-1.2em, -0.7em);
        cursor: pointer;
        font-size: 16px;
        font-weight: 700;
        color: rgb(0 0 0);
    }

    #facilityButton {
        margin: 20px 0px 0px 0px;
    }
}

@media(min-width: 374px) and (max-width:649px) {
    #billOnlyReviewBladeContainer.shrinkBlade > div {
        width: 27em;
    }


    #billOnlyReviewBladeContainer.shrinkBlade .col-sm-7 {
        width: 58.33333333%;
    }

    .bor-filter-dropdown {
        padding-bottom: 15px;
    }
}

@media(min-width: 374px) and (max-width: 766px) {
    .clear-button {
        position: relative;
        right: 10px;
        top: 8px;
        transform: translate(11.2em, -2.5em);
        cursor: pointer;
        font-size: 16px;
        font-weight: 700;
        color: rgb(0 0 0);
        z-index: 10;
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 8em;
        padding-bottom: 10px;
    }
    
    .col-sm-bor {
        width: auto;
    }

    .dateErrorMessage {
        color: #ff0000;
        position: absolute;
        transform: translate(0px, -17px);
    }
}

@media(min-width:374px) and (max-width: 1160px) {    
    .dateErrorMessage-sm {
        color: #ff0000;
        position: absolute;
        transform: translate(1px, -17px);
    }
}

@media(min-width: 374px) and (max-width: 1024px) {
    .datepicker-label {
        display: block;
        font-size: 15px;
        font-weight: 700;
        padding: 10px 15px 10px 0px;
        padding-inline-end: 8em;
    }
}

@media(min-width: 1160px) and (max-width: 1440px) {
    .dateErrorMessage-md {
        color: #ff0000; 
        position: absolute; 
        transform: translate(210px, -17px);
    }
}

@media(min-width: 650px) and (max-width:904px) {
    #billOnlyReviewBladeContainer.shrinkBlade > div {
        width: 39vw;
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 5em;
        padding-bottom: 10px;
    }

    .col-md-lg-bor {
        display: flex;
        flex-wrap: wrap;
        width: auto;
        row-gap: 10px;
    }

    .filter-group-md {
        display: flex;
        flex-wrap: wrap;
    }

    .bor-sort {
        align-content: center;
    }
}

@media(min-width:905px) and (max-width:991px) {
    #billOnlyReviewBladeContainer.shrinkBlade > div {
        width: 41vw;
    }

    #billOnlyReviewBladeContainer.shrinkBlade .col-sm-7 {
        width: 58.33333333%;
    }

    .form-group2 {
        display: inline-flex;
        margin-top: 15px;
    }

    .col-md-lg-bor {
        display: flex;
        flex-wrap: wrap;
        width: auto;
        row-gap: 10px;
    }

    .filter-group-md {
        display: flex;
        flex-wrap: wrap;
    }

    .bor-sort {
        align-content: center;
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 5em;
        padding-bottom: 10px;
    }
}

@media(min-width:992px) and (max-width:1024px) {
    #billOnlyReviewBladeContainer.shrinkBlade > div {
        width: 42vw;
    }

    #billOnlyReviewBladeContainer.shrinkBlade .col-sm-7 {
        width: 58.33333333%;
        transform: translate(0px, 12.5px);
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 5em;
        padding-bottom: 10px;
    }

    .col-md-lg-bor {
        display: flex;
        width: auto;
        row-gap: 10px;
    }

    .filter-group-md-lg {
        display: flex;
        flex-wrap: wrap;
    }

    .bor-sort {
        align-content: center;
    }
}

@media(min-width: 1025px) and (max-width:1440px) {
    .datepicker-label {
        display: inline-block;
        font-size: 15px;
        font-weight: 700;
        padding: 10px 15px 10px 0px;
        padding-inline-end: 2em;
    }

    .col-md-lg-bor {
        display: flex;
        width: auto;
        row-gap: 10px;
    }

    .bor-sort {
        align-content: center;
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 5em;
        padding-bottom: 10px;
    }

    .filter-group-md-lg {
        display: flex;
        flex-wrap: wrap;
    }
}

@media(min-width:1025px) and (max-width:1600px) {
    #billOnlyReviewBladeContainer.shrinkBlade > div {
        width: 43vw;
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 8em;
    }

    .filter-group-md-lg {
        display: flex;
        flex-wrap: wrap;
    }
}

@media(min-width:1440px) {
    #billOnlyReviewBladeContainer.slide .col-sm-billOnly-desktop {
        float:right;
        padding-top: 3.8px;
    }

    #billOnlyReviewBladeContainer.shrinkBlade .col-sm-billOnly-desktop {
        padding-top: 3.8px;
    }

    .datepicker-label {
        display: inline-block;
        font-size: 15px;
        font-weight: 700;
        padding: 10px 15px 10px 0px;
        padding-inline-end: 8em;
    }

    .col-lg-bor {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-around;
        width: auto;
        row-gap: 10px;
    }

    .bor-sort {
        align-content: center;
    }

    .dateErrorMessage {
        color: #ff0000;
        position: absolute;
        transform: translate(210px, -17px);
    }

    .clear-button{
        position: absolute;
        right: 10px;
        top: 8px;
        transform: translate(-1.2em, -0.7em);
        cursor: pointer;
        font-size: 16px;
        font-weight: 700;
        color: rgb(0 0 0);
    }
}

@media (min-width:1601px) {
    #billOnlyReviewBladeContainer.shrinkBlade > div {
        width: 45vw;
    }

    .filter-group-lg {
        display: flex;
        justify-content: space-around;
    }

    .bor-sort {
        align-content: center;
    }

    .borDateSelectorGroup {
        min-width: 11.5em;
        padding-inline-end: 8em;
    }
}
@media (min-width:1551px) and (max-width:1577px) {
    #rightSideSummary {
        transform: translate(1100px, -305px);
    }
}

@media (min-width:992px) and (max-width:994px) {
    #rightSideSummary {
        transform: translateX(110px);
    }
}