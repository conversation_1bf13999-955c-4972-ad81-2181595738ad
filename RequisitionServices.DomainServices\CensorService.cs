﻿using log4net;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Reflection;
using RequisitionServices.DomainModel.Enum;
using System.Linq;

namespace RequisitionServices.DomainServices
{
    public class CensorService : ICensorService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        
        public CensorService()
        {
            //Currently doesn't need any services/repos to be initialized
        }

        public Requisition CensorPrivateRequisitionData(string userName, Requisition requisition)
        {
            int[] reqTypes = { (int)RequisitionTypeEnum.BillOnly, (int)RequisitionTypeEnum.BillAndReplace, (int)RequisitionTypeEnum.CapitatedBillAndReplace, (int)RequisitionTypeEnum.CapitatedBillOnly };

            // IMPORTANT: BO/BR requisitions need to be cleaned of patient and provider details before being exposed to a user. See Rally ticket US30432 for further details
            if (!(requisition.CreatedBy.ToLower() == userName.ToLower()) && reqTypes.Contains(requisition.RequisitionTypeId))
            {
                CensorPrivateDataInClinicalDetails(requisition.RequisitionItems);
            }

            return requisition;
        }

        public RequisitionWithDetailsDTO CensorPrivateRequisitionData(string userName, RequisitionWithDetailsDTO requisition)
        {
            int[] reqTypes = { (int)RequisitionTypeEnum.BillOnly, (int)RequisitionTypeEnum.BillAndReplace, (int)RequisitionTypeEnum.CapitatedBillAndReplace, (int)RequisitionTypeEnum.CapitatedBillOnly };

            // IMPORTANT: BO/BR requisitions need to be cleaned of patient and provider details before being exposed to a user. See Rally ticket US30432 for further details
            if (requisition.CreatedBy != null && requisition.CreatedBy.ToLower() != userName.ToLower() && reqTypes.Contains(requisition.RequisitionTypeId))
            {
                CensorPrivateDataInClinicalDetails(requisition.RequisitionItems);
            }

            return requisition;
        }

        public IEnumerable<Requisition> CensorPrivateRequisitionData(string userName, IEnumerable<Requisition> requisitions)
        {
            foreach (Requisition req in requisitions)
            {
                CensorPrivateRequisitionData(userName, req);
            }
            return requisitions;
        }

        public IEnumerable<RequisitionWithDetailsDTO> CensorPrivateRequisitionData(string userName, IEnumerable<RequisitionWithDetailsDTO> requisitions)
        {
            foreach (RequisitionWithDetailsDTO req in requisitions)
            {
                CensorPrivateRequisitionData(userName, req);
            }
            return requisitions;
        }

        private void CensorPrivateDataInClinicalDetails(ICollection<RequisitionItem> requisitionItems)
        {
            if (requisitionItems != null)
            {
                foreach (var item in requisitionItems)
                {
                    foreach (var clinicalDetail in item.ClinicalUseDetails)
                    {
                        clinicalDetail.Provider = "#######";
                        clinicalDetail.PatientId = "#######";
                        clinicalDetail.PatientName = "#######";
                    }
                }
            }
        }

        private void CensorPrivateDataInClinicalDetails(ICollection<RequisitionItemWithDetailsDTO> requisitionItems)
        {
            if (requisitionItems != null)
            {
                foreach (var item in requisitionItems)
                {
                    item.Provider = "#######";
                    item.PatientId = "#######";
                    item.PatientName = "#######";
                }
            }
        }
    }
}
