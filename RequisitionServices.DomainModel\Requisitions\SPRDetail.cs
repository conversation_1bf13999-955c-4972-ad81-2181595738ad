﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Vendors;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.ObjectModel;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class SPRDetail
    {
        public SPRDetail() {
        }
        public SPRDetail(SPRDetailDTO sprDetailDTO, int requisitionItemId)
        {
            if (sprDetailDTO != null)
            {
                this.RequisitionItemId = requisitionItemId;

                this.ItemDescription = sprDetailDTO.ItemDescription;
                this.HasApproverChangedEstimatedPrice = sprDetailDTO.HasApproverChangedEstimatedPrice;
                this.EstimatedPrice = sprDetailDTO.EstimatedPrice;
                this.PartNumber = sprDetailDTO.PartNumber;
                this.GeneralLedgerCode = sprDetailDTO.GeneralLedgerCode;
                this.AcquisitionType = sprDetailDTO.AcquisitionType;
                this.EquipmentType = sprDetailDTO.EquipmentType;
                this.DeliveryMethodTypeId = sprDetailDTO.DeliveryMethodTypeId;

                this.AdditionalInformation = sprDetailDTO.AdditionalInformation;

                this.BudgetNumber = sprDetailDTO.BudgetNumber;
                this.IsTradeIn = sprDetailDTO.IsTradeIn;
                this.TradeInValue = sprDetailDTO.TradeInValue;

                if (sprDetailDTO.ShipToAddress != null)
                {
                    this.ShipToAddressId = sprDetailDTO.ShipToAddress.ExternalSystemId;
                }

                if (sprDetailDTO.Vendor != null)
                {
                    this.VendorId = sprDetailDTO.Vendor.Id;
                    this.VendorName = sprDetailDTO.Vendor.Name;
                    this.Vendor = sprDetailDTO.Vendor;
                }

                if (sprDetailDTO.UOM != null)
                {
                    this.UOMCode = sprDetailDTO.UOM.Code;
                }

                if (sprDetailDTO.FileAttachments != null)
                {
                    this.FileAttachments = new List<FileAttachment>();
                    foreach (var file in sprDetailDTO.FileAttachments)
                    {
                        this.FileAttachments.Add(new FileAttachment(file));
                    }
                }

                this.SPRTypeId = sprDetailDTO.SPRTypeId;
                this.ParIdentifier = sprDetailDTO.ParIdentifier;
                this.IsAddToParRequest = sprDetailDTO.IsAddToParRequest;

                this.RejectCode = sprDetailDTO.RejectCode;
                this.RejectionComments = sprDetailDTO.RejectionComments;

                this.UOMHasChanged = sprDetailDTO.UOMHasChanged; 
                this.DescriptionHasChanged = sprDetailDTO.DescriptionHasChanged;
                this.EstimatedPriceHasChanged = sprDetailDTO.EstimatedPriceHasChanged;
                this.VendorHasChanged = sprDetailDTO.VendorHasChanged;
                this.PartNumberHasChanged = sprDetailDTO.PartNumberHasChanged;

            }
        }

        //Foreign key to ReqItem?
        [Key, ForeignKey("RequisitionItem")]
        public int RequisitionItemId { get; set; }
        
        public RequisitionItem RequisitionItem { get; set; }

        [Required]
        [StringLength(255)]
        public string ItemDescription { get; set; }
                
        [Required]
        [StringLength(50)]
        public string UOMCode { get; set; }
        
        [NotMapped]
        public bool HasApproverChangedEstimatedPrice { get; set; }
        public decimal? EstimatedPrice { get; set; }

        public int VendorId { get; set; }

        [StringLength(32)]
        public string VendorName { get; set;}

        [NotMapped]
        private Vendor _vendor { get; set; }

        [NotMapped]
        public virtual Vendor Vendor {
            get
            {
                if (_vendor == null)
                {
                    return new Vendor() { Id = this.VendorId, Name = this.VendorName };
                }
                return _vendor;
            }
            set
            {
                this.VendorId = value.Id;
                this.VendorName = value.Name;
                _vendor = value;
                
            }
        }

        [StringLength(50)]
        public string PartNumber { get; set; }

        public int? ShipToAddressId { get; set; }
        [NotMapped]
        public Address ShipToAddress { get; set; }
        
        [StringLength(12)]
        public string GeneralLedgerCode { get; set; }
                
        public int DeliveryMethodTypeId { get; set; }
        [ForeignKey("DeliveryMethodTypeId")]
        public virtual DeliveryMethodType DeliveryMethodType { get; set; }

        [StringLength(255)]
        public string AdditionalInformation { get; set; }

        private ICollection<FileAttachment> _fileAttachments;
        public virtual ICollection<FileAttachment> FileAttachments
        {
            get { return _fileAttachments ?? (_fileAttachments = new Collection<FileAttachment>()); }
            set { _fileAttachments = value; }
        }

        [StringLength(50)]
        public string BudgetNumber { get; set; }
        
        public bool IsTradeIn { get; set; }
        
        public decimal? TradeInValue { get; set; }

        public int SPRTypeId { get; set; }
        [ForeignKey("SPRTypeId")]
        public virtual SPRType SPRType { get; set; }

        [StringLength(50)]
        public string ParIdentifier { get; set; }

        public bool IsAddToParRequest { get; set; }

        [StringLength(2)]
        public string RejectCode { get; set; }

        [StringLength(140)]
        public string RejectionComments { get; set; }

        public bool UOMHasChanged { get; set; }

        public bool DescriptionHasChanged { get; set; }

        public bool EstimatedPriceHasChanged { get; set; }

        public bool VendorHasChanged { get; set; }

        public bool PartNumberHasChanged { get; set; }

        public string AcquisitionType { get; set; }
        public string EquipmentType { get; set; }

    }
}
