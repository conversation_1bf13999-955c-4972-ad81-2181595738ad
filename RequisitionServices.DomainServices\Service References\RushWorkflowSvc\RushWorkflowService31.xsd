<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://local-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx?xsd=xsd0" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
  <xs:complexType name="Requisition">
    <xs:sequence>
      <xs:element minOccurs="0" name="Approvers" nillable="true" type="tns:ArrayOfRequisitionApprover" />
      <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CreateDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ErrorMessage" nillable="true" type="tns:ErrorMessage" />
      <xs:element minOccurs="0" name="LocationIdentifier" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RequiresApproval" type="xs:boolean" />
      <xs:element minOccurs="0" name="RequisitionId" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionItems" nillable="true" type="tns:ArrayOfRequisitionItem" />
      <xs:element minOccurs="0" name="RequisitionStatusHistories" nillable="true" type="tns:ArrayOfRequisitionStatusHistory" />
      <xs:element minOccurs="0" name="RequisitionStatusTypeId" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionTypeId" type="xs:int" />
      <xs:element minOccurs="0" name="WorkflowInstanceId" type="ser:guid" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Requisition" nillable="true" type="tns:Requisition" />
  <xs:complexType name="ArrayOfRequisitionApprover">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RequisitionApprover" nillable="true" type="tns:RequisitionApprover" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRequisitionApprover" nillable="true" type="tns:ArrayOfRequisitionApprover" />
  <xs:complexType name="RequisitionApprover">
    <xs:sequence>
      <xs:element minOccurs="0" name="ApprovalLevel" type="xs:int" />
      <xs:element minOccurs="0" name="ApprovalLimit" type="xs:decimal" />
      <xs:element minOccurs="0" name="ApproverEmail" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ApproverId" type="xs:int" />
      <xs:element minOccurs="0" name="HasDecided" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsCurrentApprover" type="xs:boolean" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RequisitionApprover" nillable="true" type="tns:RequisitionApprover" />
  <xs:complexType name="ErrorMessage">
    <xs:sequence>
      <xs:element minOccurs="0" name="ExceptionType" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ErrorMessage" nillable="true" type="tns:ErrorMessage" />
  <xs:complexType name="ArrayOfRequisitionItem">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RequisitionItem" nillable="true" type="tns:RequisitionItem" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRequisitionItem" nillable="true" type="tns:ArrayOfRequisitionItem" />
  <xs:complexType name="RequisitionItem">
    <xs:sequence>
      <xs:element minOccurs="0" name="ClinicalUseDetails" nillable="true" type="tns:ArrayOfClinicalUseDetail" />
      <xs:element minOccurs="0" name="CreateDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="IsApproved" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsRushOrder" type="xs:boolean" />
      <xs:element minOccurs="0" name="IsStatusChange" type="xs:boolean" />
      <xs:element minOccurs="0" name="ItemId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="MainItemId" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="OriginalParentSystemId" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="PONumber" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="ParIdentifier" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ParentSystemId" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="QuantityFulfilled" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="QuantityToOrder" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionId" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionItemStatusHistories" nillable="true" type="tns:ArrayOfRequisitionItemStatusHistory" />
      <xs:element minOccurs="0" name="RequisitionItemStatusTypeId" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionScheduledDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="SPRDetail" nillable="true" type="tns:SPRDetail" />
      <xs:element minOccurs="0" name="TrackerIndex" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RequisitionItem" nillable="true" type="tns:RequisitionItem" />
  <xs:complexType name="ArrayOfClinicalUseDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ClinicalUseDetail" nillable="true" type="tns:ClinicalUseDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfClinicalUseDetail" nillable="true" type="tns:ArrayOfClinicalUseDetail" />
  <xs:complexType name="ClinicalUseDetail">
    <xs:sequence>
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="LotNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PatientId" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ProcedureDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Provider" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RequisitionItemId" type="xs:int" />
      <xs:element minOccurs="0" name="SerialNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="UpchargeCost" nillable="true" type="xs:decimal" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ClinicalUseDetail" nillable="true" type="tns:ClinicalUseDetail" />
  <xs:complexType name="ArrayOfRequisitionItemStatusHistory">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RequisitionItemStatusHistory" nillable="true" type="tns:RequisitionItemStatusHistory" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRequisitionItemStatusHistory" nillable="true" type="tns:ArrayOfRequisitionItemStatusHistory" />
  <xs:complexType name="RequisitionItemStatusHistory">
    <xs:sequence>
      <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CreateDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionItemId" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionItemStatusTypeId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RequisitionItemStatusHistory" nillable="true" type="tns:RequisitionItemStatusHistory" />
  <xs:complexType name="SPRDetail">
    <xs:sequence>
      <xs:element minOccurs="0" name="AdditionalInformation" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="BudgetNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="DeliveryMethodTypeId" type="xs:int" />
      <xs:element minOccurs="0" name="EstimatedPrice" nillable="true" type="xs:decimal" />
      <xs:element minOccurs="0" name="FileName" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="FilePath" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="GeneralLedgerCode" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ItemDescription" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="ParIdentifier" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="PartNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="RequisitionItemId" type="xs:int" />
      <xs:element minOccurs="0" name="SPRTypeId" type="xs:int" />
      <xs:element minOccurs="0" name="ShipToAddressId" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="TradeInNumber" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TradeInValue" nillable="true" type="xs:decimal" />
      <xs:element minOccurs="0" name="UOMCode" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="VendorId" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="SPRDetail" nillable="true" type="tns:SPRDetail" />
  <xs:complexType name="ArrayOfRequisitionStatusHistory">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RequisitionStatusHistory" nillable="true" type="tns:RequisitionStatusHistory" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRequisitionStatusHistory" nillable="true" type="tns:ArrayOfRequisitionStatusHistory" />
  <xs:complexType name="RequisitionStatusHistory">
    <xs:sequence>
      <xs:element minOccurs="0" name="Comments" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="CreateDate" type="xs:dateTime" />
      <xs:element minOccurs="0" name="CreatedBy" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionId" type="xs:int" />
      <xs:element minOccurs="0" name="RequisitionStatusTypeId" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RequisitionStatusHistory" nillable="true" type="tns:RequisitionStatusHistory" />
</xs:schema>