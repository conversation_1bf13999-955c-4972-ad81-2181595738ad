﻿using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using System.Configuration;
using RequisitionServices.Utility.WebAPI;
using System.Reflection;
using log4net;


namespace RequisitionServices.MMISServices
{
    public class SmartRequisitionInquiryService : ISmartRequisitionInquiryService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private const string getRequisitionReport = "RequisitionInquiry/GetRequisitionReport/";
        private const string getRequisitionById = "RequisitionInquiry/GetRequisitionById/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public Requisition GetRequisitionById(string userName, int reqId, string coid, string countryCode)
        {
            Requisition req = new Requisition();

            var requisitionResponse = ApiUtility.ExecuteApiGetTo<LegacyRequisitionItemsModel>(endpoint, getRequisitionById, new Dictionary<string, string>()
            {
                { "userId", userName },
                { "coid", coid.ToString() },
                { "reqId", reqId.ToString() }
            }); 
            if( requisitionResponse != null && requisitionResponse.LegacyRequisitionItemModels.Any()) 
            {
                req = requisitionResponse.MapToRequisition(reqId, coid, countryCode);
            }

            return req;
        }

        public LegacyRequisitionReportDTO GetRequisitionCollectionByCreateDate(LegacyRequisitionReportRequestDTO request)
        {
            var report = new LegacyRequisitionReportDTO(request);
            var userName = request.UserName;
            var coid = request.COID;

            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                request.UserName = userName;
                SmartInputValidator.CheckCoid(coid);

                LegacyRequisitionReportRequestModel smartRequest = new LegacyRequisitionReportRequestModel(request);

                var legacyRequisitionReportModel = ApiUtility.ExecuteApiPostWithContentTo<LegacyRequisitionReportModel>(endpoint, getRequisitionReport, null, smartRequest);

                report = legacyRequisitionReportModel.MapToLegacyRequisitionReportDTO();

            }
            catch(Exception ex)
            {
                log.Error("Exception calling method GetRequisitionCollectionByCreateDate", ex);
                throw;
            }

            return report;
        }
    }
}
