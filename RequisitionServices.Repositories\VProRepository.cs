﻿using RequisitionServices.Database;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RequisitionServices.DomainModel.VPro;
using RequisitionServices.Repositories.Interfaces;
using log4net;
using RequisitionServices.DomainModel.Vira;

namespace RequisitionServices.Repositories
{
    /// <summary>
    /// Implements IVProRepository for managing RequisitionVProBadgeLog data.
    /// </summary>
    public class VProRepository : IVProRepository
    {
        private readonly ILog log = LogManager.GetLogger(typeof(VProRepository));

        private readonly EProcurementContext _context;

        public VProRepository(EProcurementContext context)
        {
            _context = context;
        }

        public List<RequisitionVProBadgeLog>  GetallBadgeLogs()
        {
           return _context.RequisitionVProBadgeLogs.ToList();
        }

        public RequisitionVProBadgeLog GetBadgeLogById(int Id)
        {
            return _context.RequisitionVProBadgeLogs
                .Where(x => x.Id == Id)
                .OrderByDescending(x => x.Id) 
                .FirstOrDefault();
        }

        public RequisitionVProBadgeLog UpdateBadgeLog(RequisitionVProBadgeLog badgeLog)
        {
            var BadgeLog = GetBadgeLogById(badgeLog.Id); // Fetch latest record

            if (BadgeLog != null)
            {
                BadgeLog.BadgeInStatusId = badgeLog.BadgeInStatusId; // Update field(s)
                _context.SaveChanges();
            }

            return BadgeLog;

        }

        public RequisitionVProBadgeLog DeleteVProBadgeLog(int Id)
        {

            var BadgeLog = GetBadgeLogById(Id);

            if (BadgeLog != null)
            {
                _context.RequisitionVProBadgeLogs.Remove(BadgeLog);
                _context.SaveChanges();
            }

            return BadgeLog;
        }

        public RequisitionVProBadgeLog CreateVProBadgeLog(RequisitionVProBadgeLog badgeLog)
        {
            _context.RequisitionVProBadgeLogs.Add(badgeLog);
            _context.SaveChanges();


            return badgeLog;
        }
    }
}
