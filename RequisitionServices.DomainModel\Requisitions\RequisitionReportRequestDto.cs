﻿using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionReportRequestDto : RequisitionListRequestDto
    {
        public RequisitionReportRequestDto()
        {
            DepartmentIds = new List<int>();
        }

        public string Coid { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string SearchText { get; set; }
        public List<int> DepartmentIds { get; set; }
        public List<int> VendorAffiliations { get; set; }
    }
}