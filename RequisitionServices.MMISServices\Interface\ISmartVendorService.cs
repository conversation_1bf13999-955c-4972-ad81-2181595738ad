﻿using RequisitionServices.DomainModel.Vendors;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartVendorService
    {
        Vendor GetVendor(string userName, string coid, int vendorNumber);

        List<VendorHeaderInfo> GetAllVendorHeaders(string userName, string coid);

        VendorDetails GetVendorDetails(string userName, string coid, int vendorNumber);

        Vendor GetVendorInformationById(string userName, string coid, int vendorNumber);
    }
}
