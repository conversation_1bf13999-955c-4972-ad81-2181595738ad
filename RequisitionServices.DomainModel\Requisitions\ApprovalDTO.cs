﻿namespace RequisitionServices.DomainModel.Requisitions
{
    public class ApprovalDTO
    {
        public ApprovalDTO(Requisition requisitionDTO, AdhocReview adhocReviewDTO, bool pendingReviewsExist, bool fromDB)
        {
            this.RequisitionDTO = new RequisitionDTO(requisitionDTO, fromDB);
            this.AdhocReviewDTO = new AdhocReviewDTO(adhocReviewDTO);
            this.PendingReviewsExist = pendingReviewsExist;
        }public ApprovalDTO(Requisition requisitionDTO, AdhocReview adhocReviewDTO, bool pendingReviewsExist)
        {
            this.RequisitionDTO = new RequisitionDTO(requisitionDTO);
            this.AdhocReviewDTO = new AdhocReviewDTO(adhocReviewDTO);
            this.PendingReviewsExist = pendingReviewsExist;
        }
        public ApprovalDTO(Requisition requisitionDTO, AdhocReview adhocReviewDTO, RequisitionStatusHistory historyItem, bool pendingReviewsExist)
        {
            this.RequisitionDTO = new RequisitionDTO(requisitionDTO);
            this.AdhocReviewDTO = new AdhocReviewDTO(adhocReviewDTO);
            this.HistoryItem = new RequisitionStatusHistoryDTO(historyItem);
            this.PendingReviewsExist = pendingReviewsExist;
        }

        public RequisitionDTO RequisitionDTO { get; set; }
        public AdhocReviewDTO AdhocReviewDTO { get; set; }
        public RequisitionStatusHistoryDTO HistoryItem { get; set; }
        public bool PendingReviewsExist { get; set; }
    }
}
