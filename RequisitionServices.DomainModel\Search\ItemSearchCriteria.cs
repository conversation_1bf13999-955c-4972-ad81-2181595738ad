﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Search
{
    public class ItemSearchCriteria
    {
        public string COID { get; set; }

        public string MainSearchText { get; set; }

        public int PageSize { get; set; }

        public int StartAtRecord { get; set; }

        public string SortByField { get; set; }

        public string VendorId { get; set; }

        public string CountryCode { get; set; }

        public List<FilterCriteria> FilterCriteria { get; set; }

        public string UserName { get; set; }

        public string Department { get; set; }

        public string ParId { get; set; }
    }
}
