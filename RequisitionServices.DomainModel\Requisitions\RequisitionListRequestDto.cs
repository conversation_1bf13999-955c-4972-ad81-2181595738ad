﻿using RequisitionServices.DomainModel.Enum;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionListRequestDto
    {
        public int RowOffset { get; set; }
        public int PageSize { get; set; }
        public RequisitionSortOrder SortOrder { get; set; }
        public string FilterText { get; set; }
        public bool IsExcludeVboChecked { get; set; }
        public List<string> FacilitiesMatchedOnFilter { get; set; }
        public List<string> DepartmentsMatchedOnFilter { get; set; }
        public string Username { get; set; }
    }
}