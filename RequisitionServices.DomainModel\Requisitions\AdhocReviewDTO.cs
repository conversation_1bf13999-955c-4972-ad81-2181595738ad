﻿using System;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class AdhocReviewDTO
    {
        public AdhocReviewDTO()
        {

        }

        public AdhocReviewDTO(AdhocReview adhocReview)
        {
            if (adhocReview != null)
            {
                this.Id = adhocReview.Id;
                this.RequisitionId = adhocReview.RequisitionId;
                this.RequesterRequisitionStatusHistoryId = adhocReview.RequesterRequisitionStatusHistoryId;
                this.ReviewerRequisitionStatusHistoryId = adhocReview.ReviewerRequisitionStatusHistoryId;
                this.Requester = adhocReview.Requester;
                this.RequesterComments = adhocReview.RequesterComments;
                this.CreateDate = adhocReview.CreateDate;
                this.Reviewer = adhocReview.Reviewer;
                this.ReviewerComments = adhocReview.ReviewerComments;
                this.Recommended = adhocReview.Recommended;
                this.ReviewDate = adhocReview.ReviewDate;
            }
        }

        public int Id { get; set; }

        public int RequisitionId { get; set; }

        public int RequesterRequisitionStatusHistoryId { get; set; }

        public int? ReviewerRequisitionStatusHistoryId { get; set; }

        public string Requester { get; set; }

        public string RequesterName { get; set; }

        public string RequesterComments { get; set; }

        public DateTime CreateDate { get; set; }

        public string Reviewer { get; set; }

        public string ReviewerName { get; set; }

        public string ReviewerComments { get; set; }

        public bool? Recommended { get; set; }

        public DateTime? ReviewDate { get; set; }

    }
}
