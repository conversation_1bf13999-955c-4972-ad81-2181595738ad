
--Insert ParTypeId =3 for empty Bin
INSERT INTO dbo.PARType(id, [name]) values(3,'Empty Bin')
-- EmptyBinHistory Table
Create TABLE [dbo].[EmptyBinHistory](
	[Id] [bigint] IDENTITY(1,1) NOT NULL,
	[COID] [nvarchar](5) NOT NULL,
	[DepartmentNumber] [int] NOT NULL,
	[PARId] [nvarchar](3) NOT NULL,
	[ItemNumber] [int] NOT NULL,
	[SubmittedDateTime] [datetime] NOT NULL,
	[Username] [nvarchar](50)
 CONSTRAINT [PK__EmptyBinHistory] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

--------------------------------------------------------------------------------
-- Indexing EmptyBinHistory

CREATE NONCLUSTERED INDEX [FK__EmptyBinHistory__COID_DepartmentNumber_PARId_ItemNumber] ON [dbo].[EmptyBinHistory]
(
	[COID] ASC,
	[DepartmentNumber] ASC,
	[PARId] ASC,
	[ItemNumber] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
