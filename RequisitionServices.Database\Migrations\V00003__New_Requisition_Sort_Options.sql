﻿USE [EProcurementQA]
GO

CREATE TYPE [dbo].[IdTemplate] AS TABLE(
	Id INT NOT NULL
)
GO


SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsGet
Purpose     : Returns a paginated list of requisitions for the MyRequisitions page.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 10-26-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/26/2017      Script created
Peter Hurlburt		10/27/2017      Submitted for deployment 21
Peter Hurlburt		11/01/2017		Added WITH (NOLOCK) statements
Peter Hurlburt		11/02/2017		Changing ALTER to CREATE, adding new script headers
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
									Submitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column 
Colin Glasco		08/18/2020		Adding new Sort types: Oldest, BO/BR, Mobile

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[QuantityToOrder] AS [RequisitionItemQuantityToOrder],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		CASE
			WHEN @mobileReqs = 1 
			THEN 
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs 
					THEN 1
					ELSE 2 
				END
			ELSE 
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup) 
					THEN 1
					ELSE 2 
				END
		END AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
		[Requisition].[CreatedBy] = @userName
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%')
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE WHEN @oldestFirst = 0 THEN [Req].[CreateDate] END DESC,
	CASE WHEN @oldestFirst = 1 THEN [Req].[CreateDate] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_PendingApprovalsGet
Purpose     : Returns a paginated list of requisitions and ad hoc reviews for the MyApprovals page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 11-14-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/14/2017      Script created
Peter Hurlburt		11/29/2017      Submitted for deployment 24
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_PendingApprovalsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[RequisitionLocationIdentifier] AS [RequisitionLocationIdentifier],
[DistinctRequisitions].[RequisitionComments] AS [RequisitionComments],
[DistinctRequisitions].[RequisitionCreateDate] AS [RequisitionCreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[PendingReviewsExist] AS [PendingReviewsExist],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[QuantityToOrder] AS [RequisitionItemQuantityToOrder],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemSprDetails].[EstimatedPrice] AS [SprDetailsEstimatedPrice],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[RequisitionerId] AS [RequisitionerId],
[DistinctRequisitions].[RequisitionerFirstName] AS [RequisitionerFirstName],
[DistinctRequisitions].[RequisitionerLastName] AS [RequisitionerLastName],
[DistinctRequisitions].[ReviewId] AS [ReviewId],
[DistinctRequisitions].[RequesterId] AS [RequesterId],
[DistinctRequisitions].[RequesterFirstName] AS [RequesterFirstName],
[DistinctRequisitions].[RequesterLastName] AS [RequesterLastName],
[DistinctRequisitions].[RequesterComments] AS [RequesterComments],
[DistinctRequisitions].[RequestCreateDate] AS [RequestCreateDate],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
[DistinctRequisitions].[CountryCode] AS [CountryCode]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN (CASE
		WHEN [Req].[ReviewId] IS NULL THEN 1
        ELSE 0 END) END), [ReqTypeGroupingOrder], 
		CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC,
		CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC) as [rowNumber],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[RequisitionLocationIdentifier] AS [RequisitionLocationIdentifier],
	[Req].[RequisitionComments] AS [RequisitionComments],
	[Req].[RequisitionCreateDate] AS [RequisitionCreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[PendingReviewsExist] AS [PendingReviewsExist],
	[Req].[RequisitionerId] AS [RequisitionerId],
	[Req].[RequisitionerFirstName] AS [RequisitionerFirstName],
	[Req].[RequisitionerLastName] AS [RequisitionerLastName],
	[Req].[ReviewId] AS [ReviewId],
	[Req].[RequesterId] AS [RequesterId],
	[Req].[RequesterFirstName] AS [RequesterFirstName],
	[Req].[RequesterLastName] AS [RequesterLastName],
	[Req].[RequesterComments] AS [RequesterComments],
	[Req].[RequestCreateDate] AS [RequestCreateDate],
	[Req].[DateForSorting] AS [DateForSorting],
	[Req].[CountryCode] AS [CountryCode],
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	(CASE @statusSorting WHEN 1 THEN (CASE
		WHEN [Req].[ReviewId] IS NULL THEN 1
        ELSE 0 END) END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC, [Req].[ReviewId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC, [Req].[ReviewId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		(
		SELECT
		CASE 
			WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup) 
			THEN 1 
			ELSE 2
		END AS [ReqTypeGroupingOrder],
		(CASE
		WHEN EXISTS (
			SELECT 1
			FROM [dbo].[AdhocReviews] AS [Reviews] WITH (NOLOCK)
			WHERE [Reviews].[RequisitionId] = [Requisition].[RequisitionId]
			AND [Reviews].[Recommended] IS NULL)
		THEN CAST (1 AS BIT)
		ELSE CAST (0 AS BIT) END) AS [PendingReviewsExist],
		[ReqStatus].[Description] AS [StatusForFilter],
		[Requisition].[CreateDate] AS [DateForSorting],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [RequisitionLocationIdentifier],
		[Requisition].[Comments] AS [RequisitionComments],
		[Requisition].[CreateDate] AS [RequisitionCreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[CountryCode] AS [CountryCode],
		[Requisitioner].[AccountName] AS [RequisitionerId],
		[Requisitioner].[FirstName] AS [RequisitionerFirstName],
		[Requisitioner].[LastName] AS [RequisitionerLastName],
		NULL AS [ReviewId],
		NULL AS [RequesterId],
		NULL AS [RequesterFirstName],
		NULL AS [RequesterLastName],
		NULL AS [RequesterComments],
		NULL AS [RequestCreateDate]
		FROM
		[dbo].[Users] AS [User] WITH (NOLOCK)
		INNER JOIN [dbo].[Approvers] AS [Approver] WITH (NOLOCK) ON [User].[Id] = [Approver].[UserId]
		INNER JOIN [dbo].[UserWorkflowSteps] AS [WorkflowStep] WITH (NOLOCK) ON [Approver].[Id] = [WorkflowStep].[ApproverId]
		INNER JOIN [dbo].[Users] AS [Requisitioner] WITH (NOLOCK) ON [Requisitioner].[Id] = [WorkflowStep].[UserId]
		INNER JOIN [dbo].[Requisitions] AS [Requisition] WITH (NOLOCK) ON [Requisition].[CreatedBy] = [Requisitioner].[AccountName]
																	AND [WorkflowStep].[Step] = [Requisition].[ApprovalStep]
																	AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = [WorkflowStep].[COID]
																	AND (([Requisition].[RequisitionTypeId] IN (0, 1, 2, 3, 4) AND [WorkflowStep].[WorkflowTypeId] = 0)
																		OR ([Requisition].[RequisitionTypeId] = 7 AND [WorkflowStep].[WorkflowTypeId] = 1)
																		OR ([Requisition].[RequisitionTypeId] = 5 AND [WorkflowStep].[WorkflowTypeId] = 2)
																		OR ([Requisition].[RequisitionTypeId] = 6 AND [WorkflowStep].[WorkflowTypeId] = 3)
																	)
		INNER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [ReqStatus].[Id] = [Requisition].[RequisitionStatusTypeId]
		WHERE
		[User].[AccountName] = @userName
		AND 2 = [Requisition].[RequisitionStatusTypeId]
		)
		UNION
		(
		SELECT
		(CASE
		WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup) THEN 1
		ELSE 2 END) AS [ReqTypeGroupingOrder],
		CAST (0 AS BIT) AS [PendingReviewsExist],
		'Pending Review' AS [StatusForFilter],
		[AdhocReview].[CreateDate] AS [DateForSorting],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS RequisitionLocationIdentifier,
		[Requisition].[Comments] AS [RequisitionComments],
		[Requisition].[CreateDate] AS [RequisitionCreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[CountryCode] AS [CountryCode],
		[Requisition].[CreatedBy] AS [RequisitionerId],		
		[RequisitionCreator].[FirstName] AS [RequisitionerFirstName],
		[RequisitionCreator].[LastName] AS [RequisitionerLastName],
		[AdhocReview].[Id] AS [ReviewId],
		[AdhocReview].[Requester] AS [RequesterId],
		[ReviewRequester].[FirstName] AS [RequesterFirstName],
		[ReviewRequester].[LastName] AS [RequesterLastName],
		[AdhocReview].[RequesterComments] AS [RequesterComments],
		[AdhocReview].[CreateDate] AS [RequestCreateDate]
		FROM
		[dbo].[AdhocReviews] AS [AdHocReview] WITH (NOLOCK)
		INNER JOIN [dbo].[Requisitions] AS [Requisition] WITH (NOLOCK) ON [Requisition].[RequisitionId] = [AdHocReview].[RequisitionId]
		INNER JOIN [dbo].[Users] AS [ReviewRequester] WITH (NOLOCK) ON [ReviewRequester].[AccountName] = [AdHocReview].[Requester]
		INNER JOIN [dbo].[Users] AS [RequisitionCreator] WITH (NOLOCK) ON [RequisitionCreator].[AccountName] = [Requisition].[CreatedBy]
		WHERE
		[AdhocReview].[Reviewer] = @userName
		AND [AdHocReview].[Recommended] IS NULL
		AND [Requisition].[RequisitionStatusTypeId] IN (0, 2, 3, 4, 8, 9, 10, 11)
		)
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR [Req].[RequisitionerId] LIKE '%' + @filterText + '%'
	OR ([Req].[RequisitionerFirstName] + ' ' + [Req].[RequisitionerLastName]) LIKE '%' + @filterText + '%'
	OR [Req].[RequesterId] LIKE '%' + @filterText + '%'
	OR ([Req].[RequesterFirstName] + ' ' + [Req].[RequesterLastName]) LIKE '%' + @filterText + '%'
	OR [Req].[RequisitionComments] LIKE '%' + @filterText + '%'
	OR [Req].[RequesterComments] LIKE '%' + @filterText + '%'
	OR [Req].[StatusForFilter] LIKE '%' + @filterText + '%')
	GROUP BY
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[RequisitionLocationIdentifier],
	[Req].[RequisitionComments],
	[Req].[RequisitionCreateDate],
	[Req].[RequisitionTypeId],
	[Req].[PendingReviewsExist],
	[Req].[RequisitionerId],
	[Req].[RequisitionerFirstName],
	[Req].[RequisitionerLastName],
	[Req].[ReviewId],
	[Req].[RequesterId],
	[Req].[RequesterFirstName],
	[Req].[RequesterLastName],
	[Req].[RequesterComments],
	[Req].[RequestCreateDate],
	[Req].[DateForSorting],
	[Req].[ReqTypeGroupingOrder],
	[Req].[CountryCode]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC,
	CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_ApprovalHistoryGet
Purpose     : Returns a paginated list of requisition history items for the MyApprovals page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 11-20-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Peter Hurlburt		11/20/2017		Script created
Peter Hurlburt		11/28/2017		Submitted for deployment 24
Peter Hurlburt		12/15/2017		Removed an overly-verbose union statement
									Submitted for deployment 24
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_ApprovalHistoryGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[HistoryItemId] AS [HistoryItemId],
[DistinctRequisitions].[HistoryItemCreateDate] AS [HistoryItemCreateDate],
[DistinctRequisitions].[HistoryItemStatusTypeId] AS [HistoryItemStatusTypeId],
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[RequisitionLocationIdentifier] AS [RequisitionLocationIdentifier],
[DistinctRequisitions].[RequisitionComments] AS [RequisitionComments],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[PendingReviewsExist] AS [PendingReviewsExist],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[QuantityToOrder] AS [RequisitionItemQuantityToOrder],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemSprDetails].[EstimatedPrice] AS [SprDetailsEstimatedPrice],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[RequisitionerId] AS [RequisitionerId],
[DistinctRequisitions].[RequisitionerFirstName] AS [RequisitionerFirstName],
[DistinctRequisitions].[RequisitionerLastName] AS [RequisitionerLastName],
[DistinctRequisitions].[RequesterId] AS [RequesterId],
[DistinctRequisitions].[RequesterFirstName] AS [RequesterFirstName],
[DistinctRequisitions].[RequesterLastName] AS [RequesterLastName],
[DistinctRequisitions].[RequesterComments] AS [RequesterComments],
[DistinctRequisitions].[ReviewerRecommended] AS [ReviewerRecommended],
[DistinctRequisitions].[ReviewerComments] AS [ReviewerComments],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN (CASE [Req].[HistoryItemStatusTypeId]
        WHEN 6 THEN 3
        WHEN 3 THEN 2
        ELSE [Req].[ReviewerRecommended] END) END) DESC, [ReqTypeGroupingOrder], 
		CASE @oldestFirst WHEN 0 THEN [HistoryItemCreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [HistoryItemCreateDate] END ASC) as [rowNumber],
	[Req].[HistoryItemId] AS [HistoryItemId],
	[Req].[HistoryItemCreateDate] AS [HistoryItemCreateDate],
	[Req].[HistoryItemStatusTypeId] AS [HistoryItemStatusTypeId],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[RequisitionLocationIdentifier] AS [RequisitionLocationIdentifier],
	[Req].[RequisitionComments] AS [RequisitionComments],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[PendingReviewsExist] AS [PendingReviewsExist],
	[Req].[RequisitionerId] AS [RequisitionerId],
	[Req].[RequisitionerFirstName] AS [RequisitionerFirstName],
	[Req].[RequisitionerLastName] AS [RequisitionerLastName],
	[Req].[RequesterId] AS [RequesterId],
	[Req].[RequesterFirstName] AS [RequesterFirstName],
	[Req].[RequesterLastName] AS [RequesterLastName],
	[Req].[RequesterComments] AS [RequesterComments],
	[Req].[ReviewerRecommended] AS [ReviewerRecommended],
	[Req].[ReviewerComments] AS [ReviewerComments],
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	(CASE @statusSorting WHEN 1 THEN (CASE [Req].[HistoryItemStatusTypeId]
        WHEN 6 THEN 3
        WHEN 3 THEN 2
        ELSE [Req].[ReviewerRecommended] END) END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[HistoryItemId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[HistoryItemId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
			THEN 1
			ELSE 2
		END) AS [ReqTypeGroupingOrder],
		(CASE
		WHEN EXISTS (
			SELECT 1
			FROM [dbo].[AdhocReviews] AS [Reviews] WITH (NOLOCK)
			WHERE [Reviews].[RequisitionId] = [Requisition].[RequisitionId]
			AND [Reviews].[Recommended] IS NULL)
		THEN CAST (1 AS BIT)
		ELSE CAST (0 AS BIT) END) AS [PendingReviewsExist],
		(CASE [HistoryItem].[RequisitionStatusTypeId]
		WHEN 10 THEN (CASE [AdHocReview].[Recommended]
					 WHEN 1 THEN 'Recommend Approve'
					 ELSE 'Recommend Deny' END)
		ELSE (SELECT [HistoryStatus].[Description]
			 FROM [dbo].[RequisitionStatusTypes] AS [HistoryStatus] WITH (NOLOCK)
			 WHERE [HistoryStatus].[Id] = [HistoryItem].[RequisitionStatusTypeId]) END) AS [StatusForFilter],
		[HistoryItem].[Id] AS [HistoryItemId],
		[HistoryItem].[CreateDate] AS [HistoryItemCreateDate],
		[HistoryItem].[RequisitionStatusTypeId] AS [HistoryItemStatusTypeId],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [RequisitionLocationIdentifier],
		[Requisition].[Comments] AS [RequisitionComments],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[CreatedBy] AS [RequisitionerId],
		[RequisitionCreator].[FirstName] AS [RequisitionerFirstName],
		[RequisitionCreator].[LastName] AS [RequisitionerLastName],
		[AdHocReview].[Requester] AS [RequesterId],
		[ReviewRequester].[FirstName] AS [RequesterFirstName],
		[ReviewRequester].[LastName] AS [RequesterLastName],
		[AdHocReview].[RequesterComments] AS [RequesterComments],
		[AdHocReview].[Recommended] AS [ReviewerRecommended],
		[AdHocReview].[ReviewerComments] AS [ReviewerComments]
		FROM
		[dbo].[RequisitionStatusHistories] AS [HistoryItem] WITH (NOLOCK)
		INNER JOIN [dbo].[Requisitions] AS [Requisition] WITH (NOLOCK) ON [Requisition].[RequisitionId] = [HistoryItem].[RequisitionId]
		INNER JOIN [dbo].[Users] AS [RequisitionCreator] WITH (NOLOCK) ON [RequisitionCreator].[AccountName] = [Requisition].[CreatedBy]
		LEFT OUTER JOIN [dbo].[AdhocReviews] AS [AdHocReview] WITH (NOLOCK) ON [AdHocReview].[ReviewerRequisitionStatusHistoryId] = [HistoryItem].[Id]
		LEFT OUTER JOIN [dbo].[Users] AS [ReviewRequester] WITH (NOLOCK) ON [ReviewRequester].[AccountName] = [AdHocReview].[Requester]
		WHERE
		[HistoryItem].[CreatedBy] = @userName
		AND [HistoryItem].[RequisitionStatusTypeId] IN (3, 6, 10)
		AND [HistoryItem].[CreateDate] >= (SELECT Max([LatestDraftStatus].[CreateDate])
										FROM [dbo].[RequisitionStatusHistories] AS [LatestDraftStatus] WITH (NOLOCK)
										WHERE [LatestDraftStatus].[RequisitionId] = [HistoryItem].[RequisitionId]
										AND [LatestDraftStatus].[RequisitionStatusTypeId] = 1)
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR [Req].[RequisitionerId] LIKE '%' + @filterText + '%'
	OR ([Req].[RequisitionerFirstName] + ' ' + [Req].[RequisitionerLastName]) LIKE '%' + @filterText + '%'
	OR [Req].[RequesterId] LIKE '%' + @filterText + '%'
	OR ([Req].[RequesterFirstName] + ' ' + [Req].[RequesterLastName]) LIKE '%' + @filterText + '%'
	OR [Req].[RequisitionComments] LIKE '%' + @filterText + '%'
	OR [Req].[RequesterComments] LIKE '%' + @filterText + '%'
	OR [Req].[ReviewerComments] LIKE '%' + @filterText + '%'
	OR [Req].[StatusForFilter] LIKE '%' + @filterText + '%')
	GROUP BY
	[Req].[HistoryItemId],
	[Req].[HistoryItemCreateDate],
	[Req].[HistoryItemStatusTypeId],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[RequisitionLocationIdentifier],
	[Req].[RequisitionComments],
	[Req].[RequisitionTypeId],
	[Req].[PendingReviewsExist],
	[Req].[RequisitionerId],
	[Req].[RequisitionerFirstName],
	[Req].[RequisitionerLastName],
	[Req].[RequesterId],
	[Req].[RequesterFirstName],
	[Req].[RequesterLastName],
	[Req].[RequesterComments],
	[Req].[ReviewerRecommended],
	[Req].[ReviewerComments],
	[Req].[ReqTypeGroupingOrder]
	ORDER BY
	[ConditionalStatusSorting] DESC, [ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [HistoryItemCreateDate] END DESC,
	CASE @oldestFirst WHEN 1 THEN [HistoryItemCreateDate] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].[rowNumber]
OPTION (RECOMPILE)

                END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_UpcomingApprovalsGet
Purpose     : Returns a paginated list of requisitions and ad hoc reviews for the MyApprovals page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 12-14-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		12/14/2017      Script created
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_UpcomingApprovalsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@oldestFirst bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[RequisitionComments] AS [RequisitionComments],
[DistinctRequisitions].[RequisitionCreateDate] AS [RequisitionCreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[PendingReviewsExist] AS [PendingReviewsExist],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[RequisitionerId] AS [RequisitionerId],
[DistinctRequisitions].[RequisitionerFirstName] AS [RequisitionerFirstName],
[DistinctRequisitions].[RequisitionerLastName] AS [RequisitionerLastName],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY [ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC,
	CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC) as [rowNumber],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[RequisitionComments] AS [RequisitionComments],
	[Req].[RequisitionCreateDate] AS [RequisitionCreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[PendingReviewsExist] AS [PendingReviewsExist],
	[Req].[RequisitionerId] AS [RequisitionerId],
	[Req].[RequisitionerFirstName] AS [RequisitionerFirstName],
	[Req].[RequisitionerLastName] AS [RequisitionerLastName],
	[Req].[DateForSorting] AS [DateForSorting],
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
			THEN 1
			ELSE 2
		END) AS [ReqTypeGroupingOrder],
		(CASE
		WHEN EXISTS (
			SELECT 1
			FROM [dbo].[AdhocReviews] AS [Reviews] WITH (NOLOCK)
			WHERE [Reviews].[RequisitionId] = [Requisition].[RequisitionId]
			AND [Reviews].[Recommended] IS NULL)
		THEN CAST (1 AS BIT)
		ELSE CAST (0 AS BIT) END) AS [PendingReviewsExist],
		[ReqStatus].[Description] AS [StatusForFilter],
		[Requisition].[CreateDate] AS [DateForSorting],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[Comments] AS [RequisitionComments],
		[Requisition].[CreateDate] AS [RequisitionCreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisitioner].[AccountName] AS [RequisitionerId],
		[Requisitioner].[FirstName] AS [RequisitionerFirstName],
		[Requisitioner].[LastName] AS [RequisitionerLastName]
		FROM
		[dbo].[Users] AS [User] WITH (NOLOCK)
		INNER JOIN [dbo].[Approvers] AS [Approver] WITH (NOLOCK) ON [Approver].[UserId] = [User].[Id]
		INNER JOIN [dbo].[UserWorkflowSteps] AS [WorkflowStep] WITH (NOLOCK) ON  [WorkflowStep].[ApproverId] = [Approver].[Id]
		INNER JOIN [dbo].[Users] AS [Requisitioner] WITH (NOLOCK) ON [Requisitioner].[Id] = [WorkflowStep].[UserId]
		INNER JOIN [dbo].[Requisitions] AS [Requisition] WITH (NOLOCK) ON [Requisition].[CreatedBy] = [Requisitioner].[AccountName]
																	   AND [WorkflowStep].[Step] > [Requisition].[ApprovalStep]
																	   AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = [WorkflowStep].[COID]
																	   AND (([Requisition].[RequisitionTypeId] IN (0, 1, 2, 3, 4) AND [WorkflowStep].[WorkflowTypeId] = 0)
																		OR ([Requisition].[RequisitionTypeId] = 7 AND [WorkflowStep].[WorkflowTypeId] = 1)
																		OR ([Requisition].[RequisitionTypeId] = 5 AND [WorkflowStep].[WorkflowTypeId] = 2)
																		OR ([Requisition].[RequisitionTypeId] = 6 AND [WorkflowStep].[WorkflowTypeId] = 3)
																	   )
																	   AND NOT EXISTS
																	   (SELECT
																	   1
																	   FROM
																	   [dbo].[UserWorkflowSteps] AS [ExistingPendingApproval] WITH (NOLOCK)
																	   WHERE
																	   [ExistingPendingApproval].[ApproverId] = [Approver].[Id]
																	   AND [ExistingPendingApproval].[UserId] = [WorkflowStep].[UserId]
																	   AND [ExistingPendingApproval].[COID] = [WorkflowStep].[COID]
																	   AND [ExistingPendingApproval].[WorkflowTypeId] = [WorkflowStep].[WorkflowTypeId]
																	   AND [ExistingPendingApproval].[Step] = [Requisition].[ApprovalStep]
																	   )
		INNER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [ReqStatus].[Id] = [Requisition].[RequisitionStatusTypeId]
		WHERE
		[User].[AccountName] = @userName
		AND [Requisition].[RequisitionStatusTypeId] = 2
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR [Req].[RequisitionerId] LIKE '%' + @filterText + '%'
	OR ([Req].[RequisitionerFirstName] + ' ' + [Req].[RequisitionerLastName]) LIKE '%' + @filterText + '%'
	OR [Req].[RequisitionComments] LIKE '%' + @filterText + '%'
	OR [Req].[StatusForFilter] LIKE '%' + @filterText + '%')
	GROUP BY
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[RequisitionComments],
	[Req].[RequisitionCreateDate],
	[Req].[RequisitionTypeId],
	[Req].[PendingReviewsExist],
	[Req].[RequisitionerId],
	[Req].[RequisitionerFirstName],
	[Req].[RequisitionerLastName],
	[Req].[DateForSorting],
	[Req].[ReqTypeGroupingOrder]
	ORDER BY
	[ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC,
	CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 10-30-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/30/2017		Script created
Peter Hurlburt		11/03/2017		Version 1.0 submitted for deployment 22
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
Peter Hurlburt		11/09/2017		Changed date filters to DateTime objects from Date
Peter Hurlburt		11/13/2017		Removed Deleted requisitions from result set
									Resubmitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Jonathan Moosekian	08/19/2020		Adding multi-department selection
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Change @departmentIds to use general IdTemplate Table Type

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSsytemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId]
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN @mobileReqs = 1
			THEN 
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
		1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]
        AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid  
		AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
		AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%')
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportExportGet
Purpose     : Returns a non-paginated list of requisitions for the Requisition Report export button.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 11-02-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/02/2017		Script created
Peter Hurlburt		11/03/2017		Version 1.0 submitted for deployment 22
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
Peter Hurlburt		11/09/2017		Changed date filters to DateTime objects from Date
Peter Hurlburt		11/13/2017		Removed Deleted requisitions from result set
									Resubmitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Jonathan Moosekian	08/13/2020		Adding IsMobile column
Jonathan Moosekian	08/19/2020		Adding multi-department selection
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Change @departmentIds to use general IdTemplate Table Type

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
                BEGIN

SELECT
[Req].[RequisitionId]				AS [RequisitionId],
[Req].[CreatedBy]					AS [RequisitionerId],
[Req].[RequisitionTypeId]			AS [RequisitionTypeId],
[Req].[CreateDate]					AS [RequisitionCreateDate],
[Req].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
[Req].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[Req].[IsMobile]					AS [RequisitionIsMobile],
[User].[FirstName]					AS [RequisitionerFirstName],
[User].[LastName]					AS [RequisitionerLastName],
[ReqItem].[Id]						AS [RequisitionItemId],
[ReqItem].[ItemId]					AS [RequisitionItemNumber],
[ReqItem].[UOMCode]					AS [RequisitionItemUomCode],
[ReqItem].[PONumber]				AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]				AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]				AS [RequisitionItemVendorId],
[ReqItem].[VendorName]				AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]			AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]			AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]					AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]			AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]	AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]			AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]			AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]		AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[ReqItem].[Discount]				AS [Discount],
[SprDetails].[UOMCode]				AS [SprDetailsUomCode],
[SprDetails].[VendorId]				AS [SprDetailsVendorId],
[SprDetails].[VendorName]			AS [SprDetailsVendorName],
[SprDetails].[PartNumber]			AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]		AS [SprDetailsDescription],
[SprDetails].[TradeInValue]			AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]			AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]	AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]		AS [SprDetailsEstimatedUnitPrice],
(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
    WHEN 7 THEN 1 
    WHEN 6 THEN 2
    WHEN 12 THEN 3
    WHEN 1 THEN 4
    WHEN 2 THEN 5
    WHEN 4 THEN 6
    ELSE 7
    END END)						AS [ConditionalStatusSorting]
FROM
(
	SELECT
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
	END)										AS [ReqTypeGroupingOrder],
	[Requisition].[RequisitionId]				AS [RequisitionId],
	[Requisition].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Requisition].[LocationIdentifier]			AS [LocationIdentifier],
	[Requisition].[IsMobile]					AS [IsMobile],
	[Requisition].[Comments]					AS [Comments],
	[Requisition].[CreatedBy]					AS [CreatedBy],
	[Requisition].[CreateDate]					AS [CreateDate],
	[Requisition].[RequisitionTypeId]			AS [RequisitionTypeId]
	FROM [dbo].[Requisitions]					AS [Requisition] WITH (NOLOCK)
	WHERE
	1 <> [Requisition].[RequisitionStatusTypeId]
	AND 5 <> [Requisition].[RequisitionStatusTypeId]
	AND 8 <> [Requisition].[RequisitionStatusTypeId]
	AND 12 <> [Requisition].[RequisitionStatusTypeId]
    AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
	AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
	AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate
) AS [Req]
LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
WHERE
@filterText IS NULL OR
([Req].[RequisitionId] LIKE '%' + @filterText + '%'
OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
OR [Req].[Comments] LIKE '%' + @filterText + '%'
OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%')
ORDER BY
[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
OPTION (RECOMPILE)

END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report item number search.
Used By     : SMART Procurement team
Author      : Cassie Martinez
Created     : 02-05-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Cassie Martinez		02/05/2018		Script created
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].IsMobile AS [IsMobile],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		[Item].[ItemId],
		[Item].[ReOrder],
		[Item].[CatalogNumber],
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDetail ON SPRDetail.RequisitionItemId = [Item].Id
		WHERE
		[ItemId] = @searchText
		OR [ReOrder] = @searchText
		OR [CatalogNumber] = @searchText
		OR [SPRDetail].PartNumber = @searchText
	) AS [Item]
	LEFT OUTER JOIN (SELECT
		(CASE 
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] As [IsMobile]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
	) AS [Req] ON [Item].[RequisitionId] = [Req].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	(@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [Item].[ItemId] LIKE '%' + @filterText + '%'
	OR [Item].[ReOrder] LIKE '%' + @filterText + '%'
	OR [Item].[CatalogNumber] LIKE '%' + @filterText + '%'))
	AND substring([Req].[LocationIdentifier],0,(CHARINDEX('_',[Req].[LocationIdentifier]))) = @coid
	AND 1 <> [Req].[RequisitionStatusTypeId]
	AND 5 <> [Req].[RequisitionStatusTypeId]
	AND 8 <> [Req].[RequisitionStatusTypeId]
	AND 12 <> [Req].[RequisitionStatusTypeId]
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberExportGet
Purpose     : Returns a non-paginated list of requisitions for the Requisition Report export button on the item number search.
Used By     : SMART Procurement team
Author      : Cassie Martinez
Created     : 02-07-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Cassie Martinez		02/07/2018		Script created
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberExportGet]
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
                BEGIN

SELECT
[PopulatedReq].[RequisitionId]				AS [RequisitionId],
[PopulatedReq].[CreatedBy]					AS [RequisitionerId],
[PopulatedReq].[RequisitionTypeId]			AS [RequisitionTypeId],
[PopulatedReq].[CreateDate]					AS [RequisitionCreateDate],
[PopulatedReq].[RequisitionStatusTypeId]	AS [RequisitionStatusTypeId],
[PopulatedReq].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[PopulatedReq].[FirstName]					AS [RequisitionerFirstName],
[PopulatedReq].[LastName]					AS [RequisitionerLastName],
[PopulatedReq].[IsMobile]					AS [RequisitionIsMobile],
[ReqItem].[Id]								AS [RequisitionItemId],
[ReqItem].[ItemId]							AS [RequisitionItemNumber],
[ReqItem].[UOMCode]							AS [RequisitionItemUomCode],
[ReqItem].[PONumber]						AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]						AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]						AS [RequisitionItemVendorId],
[ReqItem].[VendorName]						AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]					AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]					AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]							AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]					AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]			AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]					AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]					AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]				AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId]			AS [RequisitionItemParentItemId],
[ReqItem].[Discount]						AS [Discount],
[SprDetails].[UOMCode]						AS [SprDetailsUomCode],
[SprDetails].[VendorId]						AS [SprDetailsVendorId],
[SprDetails].[VendorName]					AS [SprDetailsVendorName],
[SprDetails].[PartNumber]					AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]				AS [SprDetailsDescription],
[SprDetails].[TradeInValue]					AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]					AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]			AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]				AS [SprDetailsEstimatedUnitPrice]
FROM
(
	SELECT DISTINCT
	(CASE 
		WHEN @mobileReqs = 1
		THEN 
			CASE
				WHEN [MatchingReq].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [MatchingReq].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2 
			END
	END) AS [ReqTypeGroupingOrder],
	[MatchingReq].[RequisitionId],
	[MatchingReq].[CreatedBy],
	[MatchingReq].[RequisitionTypeId],
	[MatchingReq].[CreateDate],
	[MatchingReq].[RequisitionStatusTypeId],
	[MatchingReq].[LocationIdentifier],
	[MatchingReq].[IsMobile],
	[User].[FirstName],
	[User].[LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
	FROM
	(
		SELECT DISTINCT
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] ON [SprDetail].[RequisitionItemId] = [Item].[Id]
		WHERE
		[Item].[ItemId] = @searchText
		OR [Item].[ReOrder] = @searchText
		OR [Item].[CatalogNumber] = @searchText
		OR [SprDetail].[PartNumber] = @searchText
	) AS [MatchingItem]
	LEFT OUTER JOIN [dbo].[Requisitions] AS [MatchingReq] WITH (NOLOCK) ON [MatchingItem].[RequisitionId] = [MatchingReq].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [MatchingReq].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [MatchingReqItem] WITH (NOLOCK) ON [MatchingReq].[RequisitionId] = [MatchingReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [MatchingReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [MatchingReq].[CreatedBy] = [User].[AccountName]
	WHERE
	substring([MatchingReq].[LocationIdentifier],0,(CHARINDEX('_',[MatchingReq].[LocationIdentifier]))) = @coid
	AND (@filterText IS NULL OR
	([MatchingReq].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [MatchingReq].[Comments] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ItemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ReOrder] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[CatalogNumber] LIKE '%' + @filterText + '%'))
	AND 1 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 5 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 8 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 12 <> [MatchingReq].[RequisitionStatusTypeId]
) AS [PopulatedReq]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [PopulatedReq].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
ORDER BY
[ConditionalStatusSorting], [PopulatedReq].[ReqTypeGroupingOrder], 
CASE @oldestFirst WHEN 0 THEN [PopulatedReq].[CreateDate] END DESC,
CASE @oldestFirst WHEN 1 THEN [PopulatedReq].[CreateDate] END ASC
OPTION (RECOMPILE)

END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsVendorReportGet
Purpose     : Returns a paginated list of requisitions for the Vendor Id or Vendor Name in Requisition Report.
Used By     : SMART Procurement team
Author      : Vani Vasanthan
Created     : 02-06-2018
Usage       : Executed by the Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsVendorReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		INNER JOIN [dbo].[RequisitionItems] AS [ReqItemGroup1] ON [Requisition].RequisitionId = [ReqItemGroup1].RequisitionId
	    LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDETAIL ON SPRDETAIL.RequisitionItemId = [ReqItemGroup1].Id
		WHERE
		1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]		
		AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
		AND (@VendorId IS NULL OR Cast([ReqItemGroup1].[VendorId] as VARCHAR(32)) = @VendorId OR Cast(SPRDETAIL.[VendorId] as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR [ReqItemGroup1].[VendorName] = @VendorName OR SPRDETAIL.[VendorName] = @VendorName)

	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%')
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsVendorReportExportGet
Purpose     : Returns a non-paginated list of requisitions for the Vendor Search Requisition Report export button.
Used By     : SMART Procurement team
Author      : Vani Vasanthan
Created     : 02-06-2018
Usage       : Executed by the Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Add IsMobile column

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsVendorReportExportGet]
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit
AS
BEGIN

SELECT
[PopulatedReq].[RequisitionId]				AS [RequisitionId],
[PopulatedReq].[CreatedBy]					AS [RequisitionerId],
[PopulatedReq].[RequisitionTypeId]			AS [RequisitionTypeId],
[PopulatedReq].[CreateDate]					AS [RequisitionCreateDate],
[PopulatedReq].[RequisitionStatusTypeId]	AS [RequisitionStatusTypeId],
[PopulatedReq].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[PopulatedReq].[FirstName]					AS [RequisitionerFirstName],
[PopulatedReq].[LastName]					AS [RequisitionerLastName],
[PopulatedReq].[IsMobile]					AS [RequisitionIsMobile],
[ReqItem].[Id]								AS [RequisitionItemId],
[ReqItem].[ItemId]							AS [RequisitionItemNumber],
[ReqItem].[UOMCode]							AS [RequisitionItemUomCode],
[ReqItem].[PONumber]						AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]						AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]						AS [RequisitionItemVendorId],
[ReqItem].[VendorName]						AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]					AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]					AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]							AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]					AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]			AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]					AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]					AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]				AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId]			AS [RequisitionItemParentItemId],
[ReqItem].[Discount]						AS [Discount],
[SprDetails].[UOMCode]						AS [SprDetailsUomCode],
[SprDetails].[VendorId]						AS [SprDetailsVendorId],
[SprDetails].[VendorName]					AS [SprDetailsVendorName],
[SprDetails].[PartNumber]					AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]				AS [SprDetailsDescription],
[SprDetails].[TradeInValue]					AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]					AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]			AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]				AS [SprDetailsEstimatedUnitPrice]
FROM
(
	SELECT DISTINCT
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [MatchingVendorReq].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [MatchingVendorReq].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END
	END) AS [ReqTypeGroupingOrder],
	[MatchingVendorReq].[RequisitionId],
	[MatchingVendorReq].[CreatedBy],
	[MatchingVendorReq].[RequisitionTypeId],
	[MatchingVendorReq].[CreateDate],
	[MatchingVendorReq].[RequisitionStatusTypeId],
	[MatchingVendorReq].[LocationIdentifier],
	[MatchingVendorReq].[IsMobile],
	[User].[FirstName],
	[User].[LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId]
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
	FROM
	(
		SELECT DISTINCT
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] ON [SprDetail].[RequisitionItemId] = [Item].[Id]
		WHERE
		(@VendorId IS NULL OR Cast([Item].[VendorId] as VARCHAR(32)) = @VendorId OR Cast([SprDetail].[VendorId] as VARCHAR(32)) = @VendorId)
		AND (@VendorName IS NULL OR [Item].[VendorName] = @VendorName OR [SprDetail].[VendorName] = @VendorName)
	) AS [MatchingVendorItem]
	LEFT OUTER JOIN [dbo].[Requisitions] AS [MatchingVendorReq] ON [MatchingVendorItem].[RequisitionId] = [MatchingVendorReq].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [MatchingVendorReqItems] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionId] = [MatchingVendorReqItems].[RequisitionId]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] WITH (NOLOCK) ON [MatchingVendorReqItems].[Id] = [SprDetail].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [MatchingVendorReqItems].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [MatchingVendorReq].[CreatedBy] = [User].[AccountName]
	WHERE
	substring([MatchingVendorReq].[LocationIdentifier],0,(CHARINDEX('_',[MatchingVendorReq].[LocationIdentifier]))) = @coid
	AND (@filterText IS NULL OR
	([MatchingVendorReq].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%')
	OR [MatchingVendorReq].[Comments] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[PONumber] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[OriginalParentSystemId] LIKE '%' + @filterText + '%')
	AND 1 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 5 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 8 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 12 <> [MatchingVendorReq].[RequisitionStatusTypeId]
) AS [PopulatedReq]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [PopulatedReq].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
ORDER BY
[ConditionalStatusSorting], [PopulatedReq].[ReqTypeGroupingOrder], 
CASE @oldestFirst WHEN 0 THEN [PopulatedReq].[CreateDate] END DESC,
CASE @oldestFirst WHEN 1 THEN [PopulatedReq].[CreateDate] END ASC
OPTION (RECOMPILE)

END
GO

DROP TYPE [dbo].[DepartmentIdTemplate]
GO
