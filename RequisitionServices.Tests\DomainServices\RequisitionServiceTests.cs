﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;
using Moq;
using RequisitionServices.DomainServices.Interface;
using System.Linq;
using RequisitionServices.Tests.Helper;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Repositories;
using RequisitionServices.DomainServices;
using RequisitionServices.Database;
using System.Data.Entity;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class RequisitionServiceTests
    {

        private static Mock<IRequisitionService> mockIRequisitionService;
        private static Mock<IRequisitionRepository> mockIRequisitionRepository;
        private static Mock<IRequisitionStatusRepository> mockrequisitionStatusRepository;
        private static Mock<IAdhocReviewRepository> mockadhocReviewRepository;
        private static Mock<IUserService> mockuserService;
        private static Mock<IWorkflowService> mockworkflowService;
        private static Mock<IWorkflowRepository> mockworkflowRepository;
        private static Mock<ITypeRepository> mocktypeRepository;
        private static Mock<IEmailService> mockemailService;
        private static Mock<IConfigurationRepository> mockconfigurationRepository;
        private static Mock<IItemService> mockItemService;
        private static Mock<IParService> mockparService;
        private static Mock<IVendorService> mockvendorSvc;
        private static Mock<IConfigurationService> mockconfigurationService;
        private static Mock<ISmartIINItemService> mocksmartIINItemService;
        private static Mock<ICensorService> mockcensorService;
        private static Mock<ICartRepository> mockCartRepository;

        private static Mock<ISmartVendorService> mocksmartVendorSvc;
        private static Mock<ISmartCOIDService> mocksmartCoidSvc;
        private static Mock<ISmartParItemsService> mocksmartParItemsSvc;
        private static Mock<ISmartRequisitionService> mocksmartRequisitionService;
        private static Mock<ISmartRequisitionInquiryService> mocksmartRequisitionInquiryService;
        private static Mock<IFacilityWorkflowService> mockFacilityWorkflowService;
        private static Mock<IDigitalSignOffRepository> mockDigitalSignOffsRepository;
        private static Mock<IVProService> mockVProService;
        private static RequisitionService mockRequisitionService;
        private static Mock<EProcurementContext> mockEproContext;
        private static RequisitionRepository mockRequisitionRepository;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            mockIRequisitionService = new Mock<IRequisitionService>();
            mockIRequisitionRepository = new Mock<IRequisitionRepository>();
            mockrequisitionStatusRepository = new Mock<IRequisitionStatusRepository>();
            mockadhocReviewRepository = new Mock<IAdhocReviewRepository>();
            mockuserService = new Mock<IUserService>();
            mockworkflowService = new Mock<IWorkflowService>();
            mockworkflowRepository = new Mock<IWorkflowRepository>();
            mocktypeRepository = new Mock<ITypeRepository>();
            mockemailService = new Mock<IEmailService>();
            mockconfigurationRepository = new Mock<IConfigurationRepository>();
            mockItemService = new Mock<IItemService>();
            mockparService = new Mock<IParService>();
            mockvendorSvc = new Mock<IVendorService>();
            mockconfigurationService = new Mock<IConfigurationService>();
            mocksmartIINItemService = new Mock<ISmartIINItemService>();
            mockcensorService = new Mock<ICensorService>();
            mockCartRepository = new Mock<ICartRepository>();

            mocksmartVendorSvc = new Mock<ISmartVendorService>();
            mocksmartCoidSvc = new Mock<ISmartCOIDService>();
            mocksmartParItemsSvc = new Mock<ISmartParItemsService>();
            mocksmartRequisitionService = new Mock<ISmartRequisitionService>();
            mocksmartRequisitionInquiryService = new Mock<ISmartRequisitionInquiryService>();
            mockFacilityWorkflowService = new Mock<IFacilityWorkflowService>();
            mockDigitalSignOffsRepository = new Mock<IDigitalSignOffRepository>();
            mockVProService = new Mock<IVProService>();

            mockRequisitionService = new RequisitionService(mockIRequisitionRepository.Object, mockrequisitionStatusRepository.Object, mockadhocReviewRepository.Object, mockworkflowRepository.Object, mockuserService.Object, mocksmartRequisitionService.Object, mockworkflowService.Object, mocktypeRepository.Object, mockemailService.Object, mockconfigurationRepository.Object, mockItemService.Object, mockparService.Object, mockvendorSvc.Object, mockconfigurationService.Object, mocksmartIINItemService.Object, mockcensorService.Object, mocksmartRequisitionInquiryService.Object, mockCartRepository.Object, mockFacilityWorkflowService.Object, mockDigitalSignOffsRepository.Object, mockVProService.Object);

            mockEproContext = new Mock<EProcurementContext>();
            mockRequisitionRepository = new RequisitionRepository(mockEproContext.Object);
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            mockIRequisitionService = null;
            mockIRequisitionRepository = null;
            mockrequisitionStatusRepository = null;
            mockadhocReviewRepository = null;
            mockuserService = null;
            mockworkflowService = null;
            mockworkflowRepository = null;
            mocktypeRepository = null;
            mockemailService = null;
            mockconfigurationRepository = null;
            mockItemService = null;
            mockparService = null;
            mockvendorSvc = null;
            mockconfigurationService = null;

            mocksmartVendorSvc = null;
            mocksmartCoidSvc = null;
            mocksmartParItemsSvc = null;
            mocksmartRequisitionService = null;

            mockRequisitionService = null;

            mockEproContext = null;
            mockRequisitionRepository = null;
        }

        [TestMethod]
        public void TestReqSvcUpdatePONumberIsNull()
        {
            int rowsUpdated = 1;
            mockIRequisitionRepository.Setup(x => x.UpdateRequisitionItemPONumber(It.IsAny<RequisitionItem>())).Returns(rowsUpdated);
            var response = mockRequisitionService.UpdatePONumber(TestData.ReqItemDBPONull, TestData.ReqItemSmart);
            Assert.IsTrue(response, "DB Updated successfully with PO Number: " + TestData.ReqItemSmart.PONumber);
        }

        [TestMethod]
        public void TestReqSvcUpdatePONumberNotNull()
        {
            int rowsUpdated = 0;
            mockIRequisitionRepository.Setup(x => x.UpdateRequisitionItemPONumber(It.IsAny<RequisitionItem>())).Returns(rowsUpdated);
            var response = mockRequisitionService.UpdatePONumber(TestData.ReqItemDBPONotNull, TestData.ReqItemSmart);
            Assert.IsFalse(response, "DB Not Updated Since PO Number: " + TestData.ReqItemSmart.PONumber + " already exists");
        }

        [TestMethod]
        public void TestReqRepoUpdatePONumber()
        {
            int rowsAffected = 1;

            var reqItems = new List<RequisitionItem> {
                new RequisitionItem { Id = 10, PONumber=12345 }, new RequisitionItem { Id = 20, PONumber=67890 } }.AsQueryable();

            var mockSet = new Mock<DbSet<RequisitionItem>>();
            mockSet.As<IQueryable<RequisitionItem>>().Setup(x => x.Provider).Returns(reqItems.Provider);
            mockSet.As<IQueryable<RequisitionItem>>().Setup(x => x.Expression).Returns(reqItems.Expression);
            mockSet.As<IQueryable<RequisitionItem>>().Setup(x => x.ElementType).Returns(reqItems.ElementType);
            mockSet.As<IQueryable<RequisitionItem>>().Setup(x => x.GetEnumerator()).Returns(reqItems.GetEnumerator());

            mockEproContext.Setup(x => x.RequisitionItems).Returns(mockSet.Object);

            mockEproContext.Setup(x => x.SaveChanges()).Returns(rowsAffected);

            var response = mockRequisitionRepository.UpdateRequisitionItemPONumber(TestData.ReqItemDBPONotNull);

            Assert.IsTrue(response > 0,"DB updated successfully with PO Number: "+ TestData.ReqItemDBPONotNull.PONumber + " for Requisition Id: " + TestData.ReqItemDBPONotNull.Id);

        }
    }
}
