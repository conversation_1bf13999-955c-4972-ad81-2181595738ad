﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.DomainModel.Vira;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class ViraItemStatusServiceTests
    {
        private static Mock<IViraRepository> mockViraRepository;
        private static Mock<ViraService> mockViraService; 

        [ClassInitialize()]
        public static void ClassInit(TestContext context)
        {
            mockViraRepository = new Mock<IViraRepository>();
            mockViraService = new Mock<ViraService>();
        }

        [TestInitialize()]
        public void Initialize()
        {
            mockViraService = new Mock<ViraService>(mockViraRepository.Object);
        }

        [TestCleanup()]
        public void Cleanup()
        {
            mockViraService = null;
        }

        [ClassCleanup()]
        public static void ClassCleanup()
        {
            mockViraRepository = null;
        }

        [TestMethod]
        public void GetViraItemStatusRecordById_ShouldReturnViraItemStatusRecord()
        {
            // Arrange
            var requisitionId = 1;
            var requisitionItemId = 1;
            var viraItemStatus = new ViraItemStatus
            {
                RequisitionId = requisitionId,
                RequisitionItemId = requisitionItemId,
                PublishStatus = false,
                RetryCount = 0,
                LastRetry = DateTime.Now,
                ViraApprovalStatus = "Approved"
            };

            mockViraRepository.Setup(x => x.GetViraItemStatusRecordById(requisitionId, requisitionItemId)).ReturnsAsync(viraItemStatus);
            var response = mockViraService.Object.GetViraItemStatusRecordById(requisitionId, requisitionItemId).Result;

            var setupData = new ViraItemStatus
            {
                RequisitionId = response.RequisitionId,
                RequisitionItemId = response.RequisitionItemId,
                PublishStatus = response.PublishStatus,
                RetryCount = response.RetryCount,
                LastRetry = response.LastRetry,
                ViraApprovalStatus = response.ViraApprovalStatus
            };

            Assert.AreEqual(setupData.RequisitionId, viraItemStatus.RequisitionId);
            Assert.AreEqual(setupData.RequisitionItemId, viraItemStatus.RequisitionItemId);
            Assert.AreEqual(setupData.PublishStatus, viraItemStatus.PublishStatus);
            Assert.AreEqual(setupData.RetryCount, viraItemStatus.RetryCount);
            Assert.AreEqual(setupData.LastRetry, viraItemStatus.LastRetry);
            Assert.AreEqual(setupData.ViraApprovalStatus, viraItemStatus.ViraApprovalStatus);
        }

        [TestMethod]
        public void CreateViraItemStatusRecord_ShouldReturnViraItemStatusRecord()
        {
            // Arrange
            var viraItemStatus = new ViraItemStatus
            {
                RequisitionId = 1,
                RequisitionItemId = 1,
                PublishStatus = false,
                RetryCount = 0,
                LastRetry = DateTime.Now,
                ViraApprovalStatus = "Approved"
            };

            mockViraRepository.Setup(x => x.CreateViraItemStatusRecord(viraItemStatus)).ReturnsAsync(viraItemStatus);
            var response = mockViraService.Object.CreateViraItemStatusRecord(viraItemStatus).Result;

            var setupData = new ViraItemStatus
            {
                RequisitionId = response.RequisitionId,
                RequisitionItemId = response.RequisitionItemId,
                PublishStatus = response.PublishStatus,
                RetryCount = response.RetryCount,
                LastRetry = response.LastRetry,
                ViraApprovalStatus = response.ViraApprovalStatus
            };

            Assert.AreEqual(setupData.RequisitionId, viraItemStatus.RequisitionId);
            Assert.AreEqual(setupData.RequisitionItemId, viraItemStatus.RequisitionItemId);
            Assert.AreEqual(setupData.PublishStatus, viraItemStatus.PublishStatus);
            Assert.AreEqual(setupData.RetryCount, viraItemStatus.RetryCount);
            Assert.AreEqual(setupData.LastRetry, viraItemStatus.LastRetry);
            Assert.AreEqual(setupData.ViraApprovalStatus, viraItemStatus.ViraApprovalStatus);
        }

        [TestMethod]
        public void DeleteViraItemStatusRecord_ShouldReturnDeleted()
        {
            // Arrange
            var requisitionId = 1;
            var requisitionItemId = 1;
            var viraItemStatus = new ViraItemStatus
            {
                RequisitionId = requisitionId,
                RequisitionItemId = requisitionItemId,
                PublishStatus = false,
                RetryCount = 0,
                LastRetry = DateTime.Now,
                ViraApprovalStatus = "Approved"
            };

            mockViraRepository.Setup(x => x.DeleteViraItemStatusRecord(requisitionId, requisitionItemId)).ReturnsAsync("Deleted");
            var response = mockViraService.Object.DeleteViraItemStatusRecord(requisitionId, requisitionItemId).Result;

            Assert.AreEqual(response, "Deleted");
        }

        [TestMethod]
        public void UpdateViraItemStatusRecord_ShouldReturnUpdated()
        {
            // Arrange
            var viraItemStatus = new ViraItemStatus
            {
                RequisitionId = 1,
                RequisitionItemId = 1,
                PublishStatus = false,
                RetryCount = 0,
                LastRetry = DateTime.Now,
                ViraApprovalStatus = "Approved"
            };

            mockViraRepository.Setup(x => x.UpdateViraItemStatusRecord(viraItemStatus)).ReturnsAsync("Updated");
            var response = mockViraService.Object.UpdateViraItemStatusRecord(viraItemStatus).Result;

            Assert.AreEqual(response, "Updated");
        }
    }
}
