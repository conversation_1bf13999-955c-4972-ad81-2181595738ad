﻿using System.Collections.Generic;
using System.Web.Http;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;

namespace RequisitionServices.Controllers
{
    [RoutePrefix("Comment")]
    public class CommentController : ApiController
    {
        readonly ICommentService _commentService;

        public CommentController(ICommentService commentService)
        {
            _commentService = commentService;
        }

        [Route("")]
        [HttpGet]
        public CommentsResponse Get(int requisitionId, string username)
        {
            return _commentService.Get(requisitionId, username);
        }

        [Route("")]
        [HttpPost]
        public List<CommentDTO> Add(AddCommentDTO request)
        {
            return _commentService.Add(request);
        }

        [Route("Notifications")]
        [HttpGet]
        public CommentNotificationRequisitionsDTO GetNotifications(string username, int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string filterText = null)
        {
            return _commentService.GetNotifications(username, rowOffset, pageSize, sortOrder, filterText);
        }
    }
}