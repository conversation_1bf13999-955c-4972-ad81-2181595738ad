﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class FileAttachment
    {
        public FileAttachment() { }

        public FileAttachment(FileAttachmentDTO fileAttachment)
        {
            this.Id = fileAttachment.Id;
            this.RequisitionId = fileAttachment.RequisitionId;
            this.RequisitionItemId = fileAttachment.RequisitionItemId;
            this.FileName = fileAttachment.FileName;
            this.FilePath = fileAttachment.FilePath;
            this.CreatedBy = fileAttachment.CreatedBy;
            this.CreateDate = fileAttachment.CreateDate;
        }

        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        public int RequisitionItemId { get; set; }

        [ForeignKey("RequisitionItemId")]
        public virtual SPRDetail SPRDetail { get; set; }

        public int RequisitionId { get; set; }

        [NotMapped]
        [StringLength(255)]
        public string FilePath { get; set; }

        [StringLength(300)]
        public string FileName { get; set; }

        public string CreatedBy { get; set; }

        public DateTime CreateDate { get; set; }


    }
}
