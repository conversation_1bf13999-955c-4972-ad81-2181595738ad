﻿using log4net;
using RequisitionServices.DomainModel.ItemInfo;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;
using RequisitionServices.MMISServices.Utilities;

namespace RequisitionServices.MMISServices
{
    public class SmartItemInfoService : ISmartItemInfoService
    {
        private const string getItemInfoByItemIdMethod = "ItemInfo/GetItemInfoById/";
        private const string getItemInfoByReorderNbrIdMethod = "ItemInfo/GetItemInfoByReorderNbr/";
        private readonly string _endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");
        private readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);


        public List<ItemInfoModel> GetItemInfoById(string userName, string coid, string itemNum)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                return ApiUtility.ExecuteApiGetTo<List<ItemInfoModel>>(_endpoint, getItemInfoByItemIdMethod, new Dictionary<string, string>()
                {
                    { "userId", userName },
                    { "coid", coid },
                    { "ItemNum", itemNum },
                    { "CoidType", "" }
                });
            }
            catch (Exception ex)
            {
                _log.Error("Exception calling method GetItemInfoById", ex);
                throw;
            }
        }

        public List<ItemInfoModel> GetItemInfoByReorderNbr(string userName, string coid, string reorderNum)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref userName);

                return ApiUtility.ExecuteApiGetTo<List<ItemInfoModel>>(_endpoint, getItemInfoByReorderNbrIdMethod, new Dictionary<string, string>()
                {
                    { "userId", userName },
                    { "coid", coid },
                    { "Reorder", reorderNum },
                    { "CoidType", "" }
                });
            }
            catch (Exception ex)
            {
                _log.Error("Exception calling method GetItemInfoByReorderNbr", ex);
                throw;
            }
        }
    }
}
