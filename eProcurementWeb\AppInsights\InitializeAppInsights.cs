﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Configuration;
using Microsoft.ApplicationInsights.Extensibility;
using System.Web.Configuration;

namespace eProcurementWeb.AppInsights
{
    public static class InitializeAppInsights
    {
        public static void _InitializeAppInsights()
        {
            string AppInsightEnabledConfig = ConfigurationManager.AppSettings.Get("AppInsightsEnabled");
            bool appInsightsEnabled;
            if (Boolean.TryParse(AppInsightEnabledConfig, out appInsightsEnabled))
            {
                if (appInsightsEnabled)
                {
                    Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration.Active.InstrumentationKey =
                    WebConfigurationManager.AppSettings["ikey"];

                    //Enable masking of PHI data.
                    var builder = TelemetryConfiguration.Active.TelemetryProcessorChainBuilder;
                    builder.Use((next) => new RequestFilter(next));
                    builder.Build();
                }
                else
                {
                    TelemetryConfiguration.Active.DisableTelemetry = true;
                }
            }
        }
    }
}