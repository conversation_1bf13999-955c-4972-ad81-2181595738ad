﻿using System;

namespace RequisitionServices.DomainModel.Contract
{
    public class ContractDetails
    {
        public int ContractId { get; set; }

        public string Status { get; set; }

        public string ContractType { get; set; }

        public int VendorId { get; set; }

        public string ManufacturerVendorName { get; set; }

        public DateTime EffectiveDate { get; set; }

        public DateTime ExpirationDate { get; set; }

        public string ContractClass { get; set; }

        public string ContractClassDescription { get; set; }

        public bool IsActive { get; set; }

        public DateTime AgreementDate { get; set; }

        public DateTime ThroughDate { get; set; }

        public DateTime LastRevisionDate { get; set; }

        public bool HasRebate { get; set; }

        public int OrderVendorId { get; set; }

        public decimal MinOrderAmount { get; set; }

        public int MinOrderQuantity { get; set; }

        public string PayTerms { get; set; }

        public string FreightOnBoard { get; set; }

        public string DiscountAppliedTo { get; set; }

        public int NumberOfRenewals { get; set; }

        public bool HasMarkup { get; set; }
    }
}
