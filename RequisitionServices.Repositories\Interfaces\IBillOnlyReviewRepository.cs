﻿using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RequisitionServices.Repositories
{
    public interface IBillOnlyReviewRepository
    {
        /// <summary>
        /// Retrieves a list of BillOnlyReview requisitions based on the specified parameters.
        /// </summary>
        /// <param name="request">The parameters for the requisitions.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the list of BillOnlyReview requisitions.</returns>
        IQueryable<BillOnlyReviewDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request);

        /// <summary>
        /// Retrieves a list of BillOnlyReview requisitions based on the specified parameters.
        /// </summary>
        /// <param name="request">The request parameters for the requisitions are a list of RequisitionIds and the UserName</param>
        /// <returns>An asynchronous task result containing the list of BillOnlyReviewRequisition requisitions.</returns>
        Task<List<BillOnlyReviewRequisition>> GetRequisitionsDetailsForBORPrint(BillOnlyReviewPrintRequest request);
    }
}