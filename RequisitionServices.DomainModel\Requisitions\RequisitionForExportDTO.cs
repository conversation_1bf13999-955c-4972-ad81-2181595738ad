﻿using RequisitionServices.DomainModel.VPro;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionForExportDTO : RequisitionDTO
    {
        public RequisitionForExportDTO(RequisitionForExport requisition) : base(requisition) {
            this.CreatedByFullName = requisition.CreatedByFullName;
            this.BadgeInStatusId = requisition.BadgeInStatusId;
        }

        public string CreatedByFullName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the badge in exists.
        /// </summary>
        public int? BadgeInStatusId { get; set; }
    }
}
