html {
    height: 100%;
}

body {
    min-height: 100%;
    position: relative;
    font-family: Verdana, Helvetica, Sans-Serif;
    color: #333;
    font-size: 13px;
    overflow-x: hidden;
}

main {
    position: relative;
    padding-top: 94px;
    padding-bottom: 35px;
    overflow-x: hidden;
}

.ng-pageslide {
    background: #eee;
}

/************HEADER************/
header {
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#headerMenu .nav-pills {
    margin: 23px 0;
}

#headerMenu .nav-pills .dropdown-menu {
    z-index: 1300;
}

#headerMenu .nav-pills li ul li a:hover {
    border-bottom: 1px solid transparent;
}

.logo img{
    height: 70px;
    /*Image rendering so quality is not lost on downscale*/
    image-rendering: -webkit-optimize-contrast;
}

/*FOOTER*/
footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    text-align: right;
    padding: 10px 0 10px 10px;

    background: #1B365D;
    background: -webkit-gradient(linear, left top, left bottom, from(#27518C), to(#1B365D));
    background: -moz-linear-gradient(top, #27518C, #1B365D);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#27518C', endColorstr='#1B365D');
}

footer a {
    color: #DEE0E4;
}

footer a:hover {
    color: #BFDAEE;
    text-decoration: underline;
    border-bottom: 1px transparent;
}

footer p {
    margin-bottom: 0px;
}

.copyright {
    font-size: 11px;
    color: #FFF;
}

.backToTop {
    position: fixed;
    right: 10px;
    margin-top: -20px;
    top: 50%;
    text-decoration: none;
    color: #fff;
    background-color: #ccc;
    font-size: 12px;
    padding: 5px;
    display: none;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    -khtml-border-radius: 4px;
    border-radius: 4px;
}

    .backToTop:hover {
        padding: 5px;
        background-color: #DE6924;
    }

/***********LEFT NAVIGATION************/
#menuleft>div {
    z-index: 500;
    position: fixed;
    left: 0;
    top: 94px;
}

#menuleft.slide>div {
    width: 50%;
}

#menuleft.hideMenu>div {
    width: 120px;
}

#mainMenuHeader {
    height: 32px;
    background: #1b365d;
}

#mainMenuHeader img {
    margin: 4px;
}

#mainMenu {
    border-right: 2px solid #ccc;
    background-color: #e3e3e3;
    margin-top: 32px;
    bottom: 0;
}

.slide, .hideMenu {
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}

    .hideMenu ul li a button {
        margin-left: 55px;
        position: absolute;
        margin-top: 7px;
        z-index: 11;
    }

ul.menuList {
    list-style-type: none;
    margin-left: -40px;
}

.menuList > li.menuListItem {
    width: 45.5%;
    float: left;
    margin: 10px;
    padding: 10px;
    background: #fafafa;
    border-radius: 4px;
}

.menuList li.menuListItem:hover,
.menuList li.menuListItem:active,
.menuList li.menuListItem.active {
    background-color: #1b365d;
}

    .menuList li.menuListItem a:hover,
    .menuList li.menuListItem a:active,
    .menuList li.menuListItem.active a {
        text-decoration: none;
    }

    .menuList li.menuListItem h1,
    .menuList li.menuListItem:hover h1,
    .menuList li.menuListItem:active h1,
    .menuList li.menuListItem.active h1 {
        margin: 10px;
    }

        .menuList li.menuListItem h1 span,
        .menuList li.menuListItem:hover h1 span,
        .menuList li.menuListItem:active h1 span,
        .menuList li.menuListItem.active h1 span {
            font-size: 16px;
        }

    .menuList li.menuListItem:hover h1,
    .menuList li.menuListItem:active h1,
    .menuList li.menuListItem.active h1,
    .menuList li.menuListItem.hover .menuItemContent ul li,
    .menuList li.menuListItem:active .menuItemContent ul li,
    .menuList li.menuListItem.active .menuItemContent ul li {
        color: #fff;
    }

.menuList li.menuListItem p {
    color: #1b365d;
    margin: 8px 4px;
    text-align: center;
    font-size: 36px;
}

.menuList li.menuListItem:hover p,
.menuList li.menuListItem:active p,
.menuList li.menuListItem.active p {
    color: #fff;
    margin: 8px 4px;
    text-align: center;
    font-size: 36px;
}

.menuList.slide > li {
    float: none;
    width: 98px;
    padding: 4px;
}

.menuItemHeader h1 {
    margin: 10px;
    color: #333;
}

.menuItemHeader p {
    margin: 8px 4px;
    text-align: center;
    color: #333;
}

.menuItemContent {
    text-align: center;
}

    .menuItemContent ul li {
        padding: 15px 0;
        margin: 0 10px 1em;
        vertical-align: top;
        text-decoration: none;
        color: #1b365d;
    }

.menuList li.menuListItem:hover .menuItemContent ul li,
.menuList li.menuListItem:active .menuItemContent ul li,
.menuList li.menuListItem.active .menuItemContent ul li {
    color: #fff;
}

.menuItemContent .menuSubItemText {
    text-transform: uppercase;
    margin-top: 0;
    padding-top: 0;
}

.menuItemContent .menuSubItemValue {
    font-size: 28px;
    margin-bottom: -5px;
    padding-bottom: 0;
}

.large-glyphicon {
    font-size: 50px;
}

.menuNotifications {
    display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    background-color: #a94442;
    border-radius: 10px;
    border: none;
    margin-left: -20px;
}

    .menuNotifications:focus {
        outline: none;
    }


.nav-list .nav-header {
    font-weight: bold;
}

/************MAIN CONTENT************/
.mainViewItem .mainView-header {
    height: 100%;
    width: 50px;
    background-color: #eee;
    position: fixed;
    font-family: open_sansregular;
    z-index: 700;
}

.mainView-title {
    color: #1b365d;
    white-space: nowrap;
    -webkit-transform: rotate(-90deg);
    -webkit-transform-origin: left top 0;
    -moz-transform: rotate(-90deg);
    -moz-transform-origin: left top 0;
    -o-transform: rotate(-90deg);
    -o-transform-origin: left top 0;
    -ms-transform: rotate(-90deg);
    -ms-transform-origin: left top 0;
    transform: rotate(-90deg);
    transform-origin: left top 0;
    direction: rtl;
    margin-top: 75px;
    margin-left: 3px;
    font-size: 30px;
    text-transform: uppercase;
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

.dashboardsAndReports{
    margin-top: 30px;
    margin-bottom: 30px;
    min-height: 630px;
}

.adminDashboard{
    margin-top: 30px;
    min-height: 630px;
}
.reqStatus span {
    vertical-align: middle;
}

.reqStatus .label {
    padding: 4px 6px;
    font-size: 14px;
}

.dsoReqTypeLabel {
    font-size: 14px;
    color: white;
    padding: 4px 6px;
    background-color: #1b365d;
    vertical-align: middle;
    border-radius: 0.35rem;
    text-transform:none;
}

.dsoValidationModalWindow{
    touch-action: none;
}

.mainView-content {
    float: left;
    margin-left: 50px;
    width: 91.1%;
}

#contentFromMenu {
    position: relative;
    left: 120px;
    padding-right: 120px;
    width: 100%;
}

#contentFromMenu.hideMenu {
    left: 50%;
}

.summaryInfo {
    background-color: #f3f3f3;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.reviewInfo {
    background-color: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #ddd;
    margin-bottom: 10px;
}

.dsoCartMessage {
    text-align: center;
    font-size: 16px;
    padding-bottom: 18px;
}

#dsoCartMessageStyle {
    color: #a87d1e; 
    background-color: #facf70; 
    padding: 0.5rem 20rem;
}

#contentFromMenu.slide .hotKeyContainer {
    border-top: 1px solid #ddd;
    bottom: 0;
    position: relative;
}

.hotKeyContainer p {
    margin-left: 10px;
}

#contentFromMenu.hideMenu .sidebarRight {
    display: none;
}

.sidebarRight {
    position: absolute;
    padding: 10px;
    bottom: 45px;
    right: 10px;
    background-color: #f3f3f3;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.reqKey {
    margin-top: 5px;
    margin-bottom: 10px;
}

.form-actions {
    padding: 19px 20px 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.text-overflow-ellipsis-multiline {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: inherit;
}

.text-overflow-ellipsis-single-line {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/************BLADE MODAL************/
.modal-open .blade.modal,
.modal-open .bladeSmall.modal {
    overflow-y: hidden;
}

.blade.modal .modal-dialog {
    width: auto;
    top: 0;
    right: 0;
    margin-top: 0;
    margin-left: 0;
    height: 100%;
    float: right;
}

.blade.modal .modal-content {
    border: 0;
    border-radius: 0;
    height: 100%;
    max-width: 900px;
}

.bladeSmall.modal .modal-dialog {
    width: auto;
    float: right;
    right: 0;
    margin-top: 0;
    height: 100%;
    display: flex;
}

.bladeSmall.modal .modal-content {
    border-radius: 0;
    width: fit-content;
    height: 100%;
}

.modal-backdrop {
    height: 100%;
}

.modal-content-container {
    height: 100%;
    padding-top: 61px;
    padding-bottom: 60px;
    padding-left: 50px;
}

.modal-content-container-no-header {
    height: 100%;
    padding-bottom: 60px;
    padding-left: 50px;
}

.modal-content-container>form,
.modal-content-container-no-header>form {
    height: 100%;
}

.modal-body {
    height: 100%;
    overflow-y: auto;
}

.modal-body-note {
    position: relative;
    padding: 15px;
    font-size: 13px;
}

.modal-body-inputs {
    position: relative;
    padding: 18px 10px 15px 30px;
    font-size: 13px;
}

.modal-body-attestation {
    padding: 10px 10px 15px 30px;
}

.modal-body-note hr {
    margin-top: 0px;
}

.modal-footer {
    background-color: #f8f8f8;
}

.modal-footer-approval-workflow {
    background-color: #f8f8f8;
    width: 100%;
}
.blade .modalTitleContainer, .bladeSmall .modalTitleContainer {
    float: left;
    height: 100%;
    width: 50px;
    background-color: #eee;
    position: absolute;
    font-family: open_sansregular;
}

.blade .modalTitle, .bladeSmall .modalTitle {
    text-transform: uppercase;
    color: #1b365d;
    white-space: nowrap;
    -webkit-transform: rotate(-90deg);
    -webkit-transform-origin: left top 0;
    -moz-transform: rotate(-90deg);
    -moz-transform-origin: left top 0;
    -o-transform: rotate(-90deg);
    -o-transform-origin: left top 0;
    -ms-transform: rotate(-90deg);
    -ms-transform-origin: left top 0;
    transform: rotate(-90deg);
    transform-origin: left top 0;
    direction: rtl;
    margin-top: 75px;
    margin-left: 2px;
    font-size: 30px;
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

.bladeSmall .modal-body .form-group .dropdown-menu {
    max-height: 270px;
}

/************Requisition DETAILS************/
.tableContainer h1 {
    margin: 0 0 5px 0;
    padding: 0;
}

.tableContainer, .detailsContainer {
    padding: 15px;
    margin-left: 60px;
}

.tableContainer {
    margin-bottom: 40px;
}
.dropdownHeader .dropdown-menu {
    margin-top: -8px;
}

.requisitionDetails input[type="text"],
.requisitionDetails input[type="type"],
.requisitionDetails input[type="number"],
.st-selected input {
    padding: 2px 3px;
}

/*NOTE: Adding !important to max-height crashes IE8 == */
.requisitionDetailScroll .scroll-content {
    max-height: 660px;
}

#contentFromMenu.hideMenu .summaryArea {
    display: none;
}

.summaryArea {
    background-color: #f3f3f3;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin-top: 15px;
    height: 86%;
    padding: 10px;
}

.summaryArea h2 {
    margin-top: 0;
    margin-bottom: 0;
}

.summaryArea label {
    font-weight: normal;
}

.summaryArea .adhocReviewButtons {
    width: 100%;
    margin-top: 10px;
}

.label {
    font-size: 11px;
    padding: 2px 4px;
}

.workflowStepArea {
    background-color: #f3f3f3;
    border-radius: 4px;
    border: 1px solid #ddd;
    margin-top: 15px;
    padding: 10px;
    width: 100%;
    margin-left: -15px;
}

    .workflowStepArea h2 {
        margin-top: 0;
        margin-bottom: 0;
    }

.userWorkflows h1 {
    margin-top:10px;
}

.item-list-container .pipe-delineated:not(:empty) ~ .pipe-delineated:not(:empty) {
    margin-left: 8px;
    padding-left: 10px;
    border-left: 1px solid #333;
}

.item-list-container {
    overflow-y: auto;
    margin:0px;
    overflow-x: hidden;
}

.list-item-maxwidth {max-width: 99%;}

.currency-symbol {
    position: absolute;
    padding: 5.4px 5.5px 5.5px 3.5px;
}
/******SEARCH & FILTERS******/
p.showingResults {
    margin-top: 0;
}

#contract-blade-affiliated-items-list li:last-of-type {
    border-bottom: 0;
    margin-bottom: 0;
    padding-bottom: 0;
}

.searchResult {
    border-bottom: 1px dotted #ddd;
    margin-bottom: 15px;
    padding-bottom: 10px;
}

    .searchResult h3 {
        text-transform: none;
        margin-top: -2px;
        font-weight: bold;
    }

    .searchResult h5 {
        font-weight: bold;
    }

    .searchResult input[type="number"] {
        padding: 2px 3px;
        margin-top: -3px;
        margin-bottom: 10px;
    }


#itemSearchResults .scrollbar-outer {
    max-height: 600px;
}

.filterContainer {
    margin-bottom: 20px;
}

    .filterContainer h2 {
        margin-top: 0;
        padding-bottom: 10px;
    }

    .filterContainer h5 {
        font-weight: bold;
    }

.filterSummary {
    background-color: #eee;
    padding: 5px 10px 8px;
    margin-bottom: 10px;
    border-radius: 4px;
}

    .filterSummary h4 {
        font-weight: bold;
        padding: 0;
        margin: 0;
        line-height: 20px;
    }

    .filterSummary a.text-danger:hover {
        border-bottom: 1px solid transparent;
    }

#filter {
    color: #333;
}

.resultHeaderBackground {
    background-color: #ffffff;
    padding: 5px 0;
    width: 100%;
}

.matchingSearchResult, .nonExactMatchingSearchResultLabel {
    background-color: #fffddd;
    border-radius: 4px;
    border: 1px dashed #ffcf22;
    border-bottom: 1px dashed #ffcf22 !important;
    padding-top: 10px;
    margin-bottom: 15px;
}

/*********PAGINATION********/
.pagination {
    margin: 5px 0 !important;
}

.pagination > li > a, .pagination > li > span {
    padding: 5px 10px;
    color: #333;
    background: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#e6e6e6));
    background: -moz-linear-gradient(top, #ffffff, #e6e6e6);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#e6e6e6');
}

    .pagination > li > a:focus,
    .pagination > li > a:hover,
    .pagination > li > span:focus,
    .pagination > li > span:hover {
        color: #333;
        background: #eee;
        background: -webkit-gradient(linear, left top, left bottom, from(#e6e6e6), to(#ffffff));
        background: -moz-linear-gradient(top, #e6e6e6, #ffffff);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e6e6e6', endColorstr='#ffffff');
    }

.pagination > .disabled > a,
.pagination > .disabled > a:focus,
.pagination > .disabled > a:hover,
.pagination > .disabled > span,
.pagination > .disabled > span:focus,
.pagination > .disabled > span:hover {
    color: #777;
    border-color: #ddd;
    background: #eee;
}


.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:focus,
.pagination > .active > a:hover,
.pagination > .active > span:focus,
.pagination > .active > span:hover {
    color: #fff;
    border-color: #1b365d;
    background: #1b365d;
    background: -webkit-gradient(linear, left top, left bottom, from(#27518c), to(#1b365d));
    background: -moz-linear-gradient(top, #27518c, #1b365d);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#27518c', endColorstr='#1b365d');
}



/* ALPHABET */
a.btn.alphabet, .btn.alphabet {
    padding: 5px 10px;
    color: #333;
}

    a.btn.alphabet:hover, .btn.alphabet:hover {
        border-bottom: 1px solid #dddddd;
    }

    a.btn.alphabet.blue, .btn.alphabet.blue {
        padding: 5px 10px;
        border-bottom: solid 1px #1b365d;
        color: #fff;
    }

        a.btn.alphabet.blue:hover, .btn.alphabet.blue:hover {
            border-bottom: solid 1px #1b365d;
        }

/********ALERTS**********/
.topAlert {
    text-align: center;
    top: 0;
    z-index: 100000;
    position: fixed;
}

.topAlertModal {
    text-align: center;
    width: 96%;
    top: .5%;
    z-index: 100000;
    position: relative;
    left: 13px;
}

.topAlertParModal {
    font-weight: bold !important;
    color: #000000 !important;
    text-align: center;
    top: 0;
    z-index: 100000;
    position: fixed;
    right: 23%;
}

.approvalAlertCenter {
    left:22%;
}

/************BUTTONS************/
.btn.dropdown-toggle:focus {
    color: #333;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px;
}

.gray {
    color: #fff;
    border: solid 1px #768692;
    background: #768692;
    background: -webkit-gradient(linear, left top, left bottom, from(#9caab4), to(#768692));
    background: -moz-linear-gradient(top, #9caab4, #768692);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#9caab4', endColorstr='#768692');
}

.btn.gray:hover, .btn-large.gray:hover, .btn.gray:focus, .btn.gray:active, .btn-large.gray:active {
    color: #ffffff;
    background-color: #768692;
    background: -webkit-gradient(linear, left top, left bottom, from(#768692), to(#9caab4));
    background: -moz-linear-gradient(top, #768692, #9caab4);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#768692', endColorstr='#9caab4');
}

.btn.gray[disabled], .btn.gray[disabled]:hover, .btn.large.gray[disabled], .btn.small.gray[disabled], .submit-button.btn.gray.disabled, .submit-button.btn.gray[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #768692;
    border: 1px solid #768692;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.lightBlue {
    color: #ffffff;
    border: solid 1px #2f96b4;
    background-color: #49afcd;
    background: -webkit-gradient(linear, left top, left bottom, from(#5bc0de), to(#2f96b4));
    background: -moz-linear-gradient(top, #5bc0de, #2f96b4);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#5bc0de', endColorstr='#2f96b4');
}

.btn.lightBlue:hover, .btn.lightBlue:active {
    color: #ffffff;
    background-color: #2f96b4;
    background: -webkit-gradient(linear, left top, left bottom, from(#2f96b4), to(#5bc0de));
    background: -moz-linear-gradient(top, #2f96b4, #5bc0de);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#2f96b4', endColorstr='#5bc0de');
}

.btn.lightBlue[disabled], .btn.lightBlue[disabled]:hover, .btn.large.lightBlue[disabled], .btn.small.lightBlue[disabled], .submit-button.btn.lightBlue.disabled, .submit-button.btn.lightBlue[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #5bc0de;
    border: 1px solid #2f96b4;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

/*black*/
.black {
    color: #ffffff;
    border: 1px solid #555555;
    background: #000000;
    background: -webkit-gradient(linear, left top, left bottom, from(#555555), to(#444444));
    background: -moz-linear-gradient(top, #555555, #444444);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#555555', endColorstr='#444444');
}

.btn.black:hover, .btn.black:focus, .btn.black:active {
    color: #ffffff;
    background: #444444;
    background: -webkit-gradient(linear, left top, left bottom, from(#444444), to(#555555));
    background: -moz-linear-gradient(top, #444444, #555555);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#444444', endColorstr='#555555');
}

.btn.black[disabled], .btn[disabled].black:hover, .btn.large.black[disabled], .btn.small.black[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #444444;
    border: 1px solid #444444;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

/*purple*/
.purple {
    color: #ffffff;
    border: 1px solid #330033;
    background: #470047;
    background: -webkit-gradient(linear, left top, left bottom, from(#660066), to(#330033));
    background: -moz-linear-gradient(top, #660066, #330033);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#660066', endColorstr='#330033');
}

.btn.purple:hover, .btn.purple:focus, .btn.purple:active {
    color: #ffffff;
    background: #660066;
    background: -webkit-gradient(linear, left top, left bottom, from(#330033), to(#660066));
    background: -moz-linear-gradient(top, #330033, #660066);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#330033', endColorstr='#660066');
}

.btn.purple[disabled], .btn[disabled].purple:hover, .btn.large.purple[disabled], .btn.small.purple[disabled] {
    cursor: not-allowed;
    color: #eeeeee;
    background: #660066;
    border: 1px solid #660066;
    background-image: none;
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    filter: alpha(opacity=65);
    -khtml-opacity: 0.65;
    -moz-opacity: 0.65;
    opacity: 0.65;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.btn-xs, .btn-xs:hover, .btn-xs:focus {
    padding: 1px 5px;
    font-size: 12px;
    line-height: 1.5;
    border-radius: 3px;
}

.input-group-btn .btn.iconButton, .input-group-btn .btn.iconButton:hover {
    margin-left: 0;
}

.iconButton, .iconButton:hover, .iconButton:focus {
    padding: 4px 10px;
}

.btnPlainDropdownContent {
    max-width: 100%;
    overflow-x: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
    padding-right: 9px;
}

.btnPlainSelect select {
    background: transparent;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-appearance: none;
    padding: 4px 1px;
    width: 100%;
}

    .btnPlainSelect select:focus {
        text-align: left;
        font-size: 13px;
        padding: 4px 1px;
        border: 1px solid #ccc;
        border-color: #66afe9;
        outline: 0;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
    }

.btnPlainSelect {
    text-align: left;
    font-size: 13px;
    background: #fff url(/Content/images/selectArrow.png) no-repeat right;
}

.btnPlainDropDown,
.btnPlainDropDown:hover,
.btnPlainDropDown:active,
.btnPlainDropDown.form-control,
.btnPlainDropDown.form-control:hover,
.btnPlainDropDown.form-control:active {
    text-align: left;
    font-size: 13px;
    padding: 5px 6px;
    border: 1px solid #ccc;
    background: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#ffffff));
    background: -moz-linear-gradient(top, #ffffff, #ffffff);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#ffffff');
}

    .btnPlainDropDown:focus {
        text-align: left;
        font-size: 13px;
        padding: 5px 6px;
        border: 1px solid #ccc;
        background: #ffffff;
        background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#ffffff));
        background: -moz-linear-gradient(top, #ffffff, #ffffff);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#ffffff');
        border-color: #66afe9;
        outline: 0;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
    }

.dropdown-sm {
    font-size: 12px;
}

    .dropdown-sm > li > a {
        padding: 3px 15px;
    }

.dateSelector .input-group-btn .btn {
    padding: 6px 16px;
}

.showHand:hover {
    cursor: pointer;
}

.control-label small {
    font-weight: normal;
}

.control-label-checkbox {
    color: #768692;
    -webkit-touch-callout: none; /* iOS Safari */
    -webkit-user-select: none; /* Chrome/Safari/Opera */
    -khtml-user-select: none; /* Konqueror */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently not supported by any browser */
}

.control-label-checkbox input[type=checkbox] {
    vertical-align: middle;
    margin: 0 !important;
}

.position-relative {
    position: relative;
}

.bottom-3{
    bottom: 3px;
}

.position-right { /* may require a .position-relative strategically placed in a parent element */
    position: absolute;
    right: 0;
}

.applyToAllDisabled {
    cursor: default;
}

.applyToAllDisabled label {
    cursor: default;
    font-weight: normal;
}

.has-error .form-control.btnPlainDropDown {
    border-color: #a94442 !important;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
}

    .has-error .form-control.btnPlainDropDown:active,
    .has-error .form-control.btnPlainDropDown:focus {
        border-color: #843534 !important;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483;
        outline: none;
    }

.highlight-red {
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
}

/****** HIGHLIGHTS ******/

input[type=number].number-no-spin::-webkit-inner-spin-button,
input[type=number].number-no-spin::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.has-changed.dropdown .sprFileItemHasChangedIcon {
  top: 28px;
  padding-right: 35px;
}

.has-changed .sprFileItemHasChangedIcon {
  top: 28px;
  padding-right: 25px;
}

.has-changed {
    position: relative;
}

.has-changed .form-control {
    padding-right: 29.5px;
}

.has-changed.dropdown .btnPlainDropdownContent {
    padding-right: 28px;
}

.has-changed .btnPlainDropDown.form-control {
    font-size: 13px;
    padding: 5px 6px;
    border: 1px solid #ccc;
    background: #ffffff;
    background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#ffffff));
    background: -moz-linear-gradient(top, #ffffff, #ffffff);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#ffffff');
}

/*********WIDGETS*********/
.widgetContainer {
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
}

.widgetHeader {
    background: #f3f3f3;
    border-bottom: 1px solid #ddd;
    padding: 8px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    overflow: hidden;
}

    .widgetHeader h3 {
        text-transform: uppercase;
        color: #333;
        margin: 0;
        font-weight: bold;
    }

.widgetHeaderEmpty{
    border-bottom: 0;
    padding-bottom: 20px;
    margin-bottom: 10px;
}

#userReportWidget .scrollbar-outer {
    max-height: 550px;
}

#editUserWidget .scrollbar-outer {
    max-height: 500px;
    margin-bottom: 20px;
}

#mytemplates-template-widget-body,
#myrequisitions-requisition-widget-body,
#pending-approvals-requisition-widget-body,
#approval-history-requisition-widget-body
.scrollbar-outer {
    height: 500px;
}

.widgetBody .scrollbar-outer {
    max-height: 500px;
    overflow-y: auto;
}

.widgetBody400 {
    min-height: 400px;
}

.widgetBodyPadded {
    padding: 15px 0px;
    overflow: hidden;
    min-height: 350px;
}

div.widgetBodyPadded > div > table > tbody > tr > td > input, div.widgetBodyPadded > div > table > tbody > tr > td > button {
    height: 28px;
}

div.widgetBodyPadded > div > textarea {
    min-height: 200px;
}

.widgetBodyItemSearch{
    padding-top: 10px;
    padding-left:10px;
}

.widgetBodyContent {
    padding: 8px 0;
    border-bottom: 1px dotted #ddd;
    display: flex;
}

.widgetBodyContent .col-lg-1,
.widgetBodyContent .col-md-1,
.widgetBodyContent .col-sm-1,
.widgetBodyContent .col-xs-2 {
    padding-left: 0;
}
.wrapWidgetDetail a {
    font-size: 14px;
}
.wrapWidgetDetail p {
    margin-bottom: 0;
}

.widgetDetail {
    float: left;
    width: max-content;
}

.widgetDetail a {
    font-size: 14px;
}

.widgetDetail p {
    margin-bottom: 0;
}

.widgetDate {
    vertical-align: middle;
    text-align: right;
    float: right;
}

.widgetDateMonth {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #768692;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: -7px;
}

.widgetDateDay {
    display: block;
    font-size: 24px;
    color: #00335b;
    text-align: center;
}

.widgetDateYear {
    display: block;
    font-size: 10px;
    font-weight: 600;
    color: #aaa;
    text-align: center;
    margin-top: -6px;
}

.archiveDate {
    background: #f5f5f5;
    width: 45px;
    padding: 4px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    vertical-align: top;
    margin-right: 8px;
}

.archiveDateMonth {
    display: block;
    font-size: 12px;
    font-weight: 600;
    color: #768692;
    text-transform: uppercase;
    text-align: center;
    margin-bottom: -8px;
}

.archiveDateDay {
    display: block;
    font-size: 24px;
    color: #00335b;
    text-align: center;
    margin-bottom: -5px;
}

.archiveDateYear {
    display: block;
    font-size: 10px;
    font-weight: 600;
    color: #aaa;
    text-align: center;
    margin-top: -6px;
}

.workflowSetup {
    max-height: 400px;
}

.userDetailsWorkflow{
    max-height: 360px;
}

.availableApprovers .scrollbar-outer {
    min-height: 400px;
    max-height: 500px;
}

.availableReviewers .scrollbar-outer {
    min-height: 400px;
    max-height: 500px;
}

.modal-body .availableApprovers .scrollbar-outer {
    min-height: 400px;
    max-height: 700px;
}

.modal-body .availableReviewers .scrollbar-outer {
    min-height: 400px;
    max-height: 700px;
}

.widgetHeader .filterCheckboxSingleton {
    margin-left: 10px;
    margin-top: 0;
    vertical-align: middle;
}

#editUserModal .scrollbar-outer.scroll-content {
    height: 500px;
}

.orStepRow~.orStepRow .col-lg-3:first-child {
    visibility: hidden
}

.finalStepRow~.finalStepRow .col-lg-3:first-child {
    visibility: hidden;
}

.finalRushStepRow~.finalRushStepRow .col-lg-3:first-child {
    visibility: hidden;
}

.vendorShipAndOrderInfo > div {
    margin-bottom: 5px;
}

.vendorLogistics > div {
    margin-bottom: 5px;
}

/********CARD********/
.itemCardContainer .scrollbar-outer {
    margin-bottom: 35px;
    max-height: 565px;
}

    .itemCardContainer .scrollbar-outer > .scroll-scrollx_visible.scroll-content {
        /*display: none !important;*/
        overflow-x: hidden !important;
    }

.modal-body .itemCardContainer .scrollbar-dynamic {
    max-height: 555px;
}

.reqItemCardContainer {
    max-width:99%;
}

.cardResult {
    border-left: 3px solid #e6e6e6;
    padding-left: 8px;
    margin-bottom: 12px;
}

.cardResult p {
    margin-bottom: 0;
}

.cardResult p.miniLabel {
    font-size: 10px;
    padding: 8px;
}

.cardResult ul {
    padding: 5px 0;
}

.cardResult ul li {
    margin-bottom: 0;
}

.cardResult h3 {
    display: inline-block;
}

.cardResult h3 a {
    color: #1B365D;
    font-size: 15px;
}

.cardResult .btn-group {
    margin: 4px;
}

.cardResultMatch {
    border-color: #F2665E !important;
}

.cardResultHeader {
    padding-right: 10px;
}

.cardResultFooter {
    background-color: #f5f5f5;
    margin-left: -8px;
    bottom: 0;
    border-top: 1px solid #eee;
    padding: 4px 8px;
    min-height: 40px;
}

#reqCardFooterText{
    display: block;
}

.cardResultFooterMatch {
    background-color: #f6cfcf !important;
}

.cardResult a.statusTooltip:hover {
    border-bottom: 1px solid transparent;
}

.itemEntryWidget {
    margin-bottom: 10px;
}

    .itemEntryWidget .widgetBody {
        background-color: #f3f3f3;
        padding-top: 5px;
    }

    .itemEntryWidget .form-group {
        margin-bottom: 0;
    }

.tooltipIndicator {
    border-bottom: 1px dotted #999;
}

.dRTypesHeader{
    margin-left: 20px;
}

.levelInfo {
    color: #333;
    padding: 7px 10px;
    text-align: center;
    border-right: 3px solid #e6e6e6;
    background-color: #fff;
    margin-right: -12px;
    margin-top: -4px;
    margin-bottom: -4px;
}

    .levelInfo p {
        font-size: 8px;
        margin: 0 0 -14px;
        padding: 0;
        padding-bottom: 2px;
    }

    .levelInfo h1 {
        padding: 0;
        margin: 0;
        margin-top: 0;
        line-height: 24px;
    }

.componentItem {
    margin-left: 40px;
}

.cardResult.componentItem {
    border-left: 3px solid #bac2ce;
}

.componentItem .cardResultFooter {
    background-color: #d1d6de;
    border-top: 1px solid #bac2ce;
}

.approverCardContainer.scrollbar-outer {
    max-height: 600px;
}

.approverCardContainer .cardResult .cardResultFooter {
    position: absolute;
    width: 92%;
}

/******LABELS & BOTTOM BORDER COLORS******/
.countIndicator {
    padding: 0 3px;
    color: #fff;
    -webkit-border-top-left-radius: 2px;
    -webkit-border-top-right-radius: 2px;
    -moz-border-radius-topleft: 2px;
    -moz-border-radius-topright: 2px;
    border-top-left-radius: 2px;
    border-top-right-radius: 2px;
}

.count-offWhite {
    color: #333;
    background-color: #dedede;
}

.label-lightOrange {
    background-color: #f89406;
    text-transform: none;
}

.label-orange {
    background-color: #DE6924;
    text-transform: none;
}

.label-lightGreen {
    background-color: #76A276;
    text-transform: none;
}

.label-olive {
    background-color: #516d51;
    text-transform: none;
}

.label-fuschia {
    background-color: #AA0078;
    text-transform: none;
}

.label-darkRed {
    background-color: #990000;
    text-transform: none;
}

.label-red {
    background-color: #e50000;
    text-transform: none;
}

.label-brown {
    background-color: #7a5230;
    text-transform: none;
}

.label-purple {
    background-color: #660066;
    text-transform: none;
}

.label-lavender {
    background-color: #B378D3;
    text-transform: none;
}

.label-brightTeal {
    background-color: #57c4c2;
    text-transform: none;
}

.label-lightBlue {
    background-color: #7BAFD4;
    text-transform: none;
}

.label-offWhite {
    color: #333;
    background-color: #dedede;
    border: 1px solid #ccc;
    padding: 1px 3px;
    text-transform: none;
}

.label-white {
    color: #333;
    background-color: #fefefe;
    text-transform: none;
}

.label-charcoalGray {
    background-color: #777;
    text-transform: none;
}

.label-gray {
    background-color: #999;
    text-transform: none;
}

.label-lightGray {
    background-color: #bbbbbb;
    text-transform: none;
}

.label-black {
    background-color: #000;
    text-transform: none;
}

.label-legacy {
    background-color: #000;
    color: #faef6c;
    text-transform: uppercase;
    font-weight: bold;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12), 0 1px 1px -2px rgba(0, 0, 0, 0.2);
    transition: box-shadow 0.28s cubic-bezier(0.4, 0, 0.2, 1);
}

.label-blue {
    background-color: #0000FF;
    text-transform: none;
}
.label-yellow {
    background-color: #DBC913;
    text-transform: none;
}

.smallBorderBottom {
    border-bottom: 2px solid transparent;
}

.borderOrange {
    border-color: #DE6924;
}

.borderLightOrange {
    border-color: #f89406;
}

#item-search-modal-text-box{
    width: max-content;
}

.borderOlive {
    border-color: #516d51;
}

.borderLightGreen {
    border-color: #76A276;
}

.borderDarkRed {
    border-color: #990000;
}

.borderRed {
    border-color: #e50000;
}

.borderBrown {
    border-color: #7a5230;
}

.borderFuschia {
    border-color: #AA0078;
}

.borderPurple {
    border-color: #660066;
}

.borderLavender {
    border-color: #B378D3;
}

.borderBrightTeal {
    border-color: #57c4c2;
}

.borderLightBlue {
    border-color: #7BAFD4;
}

.borderDarkBlue {
    border-color: #1b365d;
}

.borderOffWhite {
    border-color: #ddd;
}

.borderGray {
    border-color: #999;
}

.borderBlack {
    border-color: #000;
}

.borderBlue {
    border-color: #0000FF;
}
.borderYellow {
    border-color: #DBC913;
}

.borderCharcoalGray {
    border-color: #777;
}

.textOrange {
    color: #DE6924;
}

.textGreen {
    color: #468847;
}


.textCharcoalGray {
    color: #777;
}

/*DATEPICKER*/
.dateSelector ul.dropdown-menu {
    padding: 6px;
}

    .dateSelector ul.dropdown-menu li table:focus {
        outline: none;
    }

    .dateSelector ul.dropdown-menu li table thead tr th button.btn,
    .dateSelector ul.dropdown-menu li table tbody tr td button.btn {
        background: #ffffff;
        background: -webkit-gradient(linear, left top, left bottom, from(#ffffff), to(#ffffff));
        background: -moz-linear-gradient(top, #ffffff, #ffffff);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#ffffff');
        border-radius: 4px;
        border: 0;
        padding: 8px 16px;
    }

    .dateSelector ul.dropdown-menu li table tr td button.btn:hover {
        background: #e6e6e6;
        background: -webkit-gradient(linear, left top, left bottom, from(#e6e6e6), to(#e6e6e6));
        background: -moz-linear-gradient(top, #e6e6e6, #e6e6e6);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#e6e6e6', endColorstr='#e6e6e6');
    }

    .dateSelector ul.dropdown-menu li table tbody tr td button.btn.active {
        background: #1b365d;
        background: -webkit-gradient(linear, left top, left bottom, from(#1b365d), to(#1b365d));
        background: -moz-linear-gradient(top, #1b365d, #1b365d);
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1b365d', endColorstr='#1b365d');
    }

        .dateSelector ul.dropdown-menu li table tbody tr td button.btn.active,
        .dateSelector ul.dropdown-menu li table tbody tr td button.btn.active .text-info {
            color: #fff;
            font-weight: bold;
        }

    .dateSelector ul.dropdown-menu li table tbody tr td button.btn .text-info {
        color: #DE6924;
        font-weight: bold;
    }

    .dateSelector ul.dropdown-menu li .btn-group button.btn,
    .dateSelector ul.dropdown-menu li button.btn {
        color: #000;
    }

/************CHARTS***********/
.largeChart {
    width: 98%;
    height: 285px;
    position: relative;
}

.textInDonut{
    position: absolute;
    text-align: center;
    vertical-align: middle;
    top: 45%;
    left: 34%;
    font-size: 9px;
}

    .textInDonut h4 {
        font-weight: bold;
        padding: 0;
        margin: 0;
    }

.dashboardContainer .scrollbar-outer {
    min-height: 630px;
}

.dashboardDetailContainer .scrollbar-outer {
    max-height: 630px;
}

.dashboardContainer .scroll-element.scroll-x.scroll-scrollx_visible,
.dashboardDetailContainer .scroll-element.scroll-x.scroll-scrollx_visible {
    display: none;
}

.dashboardContainer .widgetHeader a {
    display: block;
    border-bottom: 1px solid transparent;
}

    .dashboardContainer .widgetHeader a:hover {
        border-bottom: 0;
    }

.dashboardContainer .widgetHeader .glyphicon {
    color: #333;
}

.reportsGroupBtns li {
    padding-right: 3px;
}


/************OTHER************/

.control-checkbox-vbo-approval input[type=checkbox] {
    display: inline;
    position: relative;
    vertical-align: middle;
    margin-bottom: 6.5px;
}
.overlay.show {
    top: 0;
    left: 50%;
    right: 0;
    bottom: 0;
    z-index: 99;
    position: fixed;
}

.aboveOverlay {
    z-index: 100;
    position: relative;
}

.callout {
    margin-bottom: 0;
    border-width: 5px;
    border-radius: 4px;
}

dt {
    font-weight: normal;
}

dd {
    font-weight: bold;
}

.popover {
    width: 150%;
    z-index: 11000;
}

.popover-content {
    word-break: break-word;
}

.siteThrobberContainer {
    position: fixed;
    left: 50%;
    top: 50%;
    margin-left: -64px; /* -1 * image width / 2 */
    margin-top: -64px; /* -1 * image height / 2 */
    display: block;
    z-index: 99999;
}

.siteThrobberOverlay {
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    z-index: 3000;
    position: fixed;
    background-color: #000;
    opacity: .5;
    filter: alpha(opacity=50);
}

.throbberImgContainer {
    background-color: #ddd;
    border-radius: 4px;
    padding: 10px;
}

.gold {
    background-color: #F0D002;
    color: #333;
    padding: 3px 4px;
    border-radius: 4px;
}

.form-control {
    font-size: 12px;
    padding: 4px;
}

.has-error .form-control.filterInput {
    border: 1px solid #ccc;
}

.has-error .form-control.form-control:focus {
    border-color: #66afe9;
    outline: 0;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,.6);
}

.bold {
    font-weight: bold;
}

.dropdown-menu {
    max-height: 465px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 900;
}

    .dropdown-menu > li > a {
        border-bottom: 1px solid transparent;
        padding: 3px 8px;
    }

        .dropdown-menu > li > a:hover,
        .dropdown-submenu:hover > a
        {
            color: #fff;
            text-decoration: none;
            background-color: #1b365d;
            background-image: -moz-linear-gradient(top,#27518c,#1b365d);
            background-image: -webkit-gradient(linear,0 0,0 100%,from(#27518c),to(#1b365d));
            background-image: -webkit-linear-gradient(top,#27518c,#1b365d);
            background-image: -o-linear-gradient(top,#27518c,#1b365d);
            background-image: linear-gradient(to bottom,#27518c,#1b365d);
            background-repeat: repeat-x;
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff27518c',endColorstr='#ff1b365d',GradientType=0);
        }

    .dropdown-menu > .active > a,
    .dropdown-menu > .active > a:hover {
        background-color: #1b365d;
    }

    .dropdown-menu > li > a:focus,
    .dropdown-menu > .active > a:focus,
    .dropdown-submenu:focus > a
    {
        background-color: transparent;
    }


.dropdownNoResults {
    z-index: 990;
    position: absolute;
    min-width: 160px;
    padding: 5px 10px;
    margin: 2px 0 0;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.dropdownRight {
    right: 0;
    left: auto;
}

.dropdown-menu.facilityShip > li > a {
    border-top: 1px solid #dddddd;
}

.select-sm {
    height: 30px;
    padding: 4px 6px;
    font-size: 13px;
}

.input-xs {
    height: 25px;
    margin-bottom: 3px;
}

.requiredLabelIndicator {
    font-size: 11px;
    color: #a94442;
    font-weight: bold;
}

.ng-invalid {
    border-color: #a94442;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075);
}

    .ng-invalid:focus {
        border-color: #843534;
        -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483;
        box-shadow: inset 0 1px 1px rgba(0,0,0,.075),0 0 6px #ce8483;
    }

form.ng-invalid {
    -webkit-box-shadow: none;
    box-shadow: none;
}

.rushIcon {
    background-image: url(/Content/images/icons/timeline.png);
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-bottom: -2px;
    margin-right: -2px;
}

.billOnlyIcon {
    background-image: url(/Content/images/icons/card_back.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.capitatedIcon {
    background-image: url(/Content/images/icons/text_list_bullets.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-bottom: -2px;
}

.capitalIcon {
    background-image: url(/Content/images/icons/coins_add.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.pharmacyIcon {
    background-image: url(/Content/images/icons/pill.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.sprIcon {
    background-image: url(/Content/images/icons/flag_blue.png);
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-bottom: -2px;
}

.sprFileItemHasChangedIcon {
    background-image: url(/Content/images/icons/flag_orange.png);
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-bottom: -2px;
}

.attachIcon {
    background-image: url(/Content/images/icons/attach.png);
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-bottom: -2px;
}

.delegateIcon {
    background-image: url(/Content/images/icons/user_go.png);
    background-repeat: no-repeat;
    height: 16px;
    width: 16px;
    display: inline-block;
    margin-bottom: -2px;
}

.punchOutIcon {
    background-image: url(/Content/images/icons/application_go.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.reviewsPendingIcon {
    background-image: url(/Content/images/icons/eye.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.mobileIcon {
    background-image: url(/Content/images/icons/phone.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.vendorIcon {
    background-image: url(/Content/images/icons/vendor.png);
    background-repeat: no-repeat;
    padding: 0 8px;
    margin-right: 4px;
}

.billType.active {
    color: #ffffff;
    background-color: #27518c;
    background: -webkit-gradient(linear, left top, left bottom, from(#1b365d), to(#27518c));
    background: -moz-linear-gradient(top, #1b365d, #27518c);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#1b365d', endColorstr='#27518c');
}

    .billType.active[disabled] {
        cursor: not-allowed;
        color: #eeeeee;
        background: #1b365d;
        border: 1px solid #1b365d;
        background-image: none;
        filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
        filter: alpha(opacity=65);
        -khtml-opacity: 0.65;
        -moz-opacity: 0.65;
        opacity: 0.65;
        -webkit-box-shadow: none;
        -moz-box-shadow: none;
        box-shadow: none;
    }

.text-muted {
    color: #999;
}

.alert {
    padding: 8px 14px;
}

.alert-none {
    color: #768692;
    background-color: #f3f3f3;
    border-color: #ccc;
}

.showWhenPrint {
    display: none;
}

.commentWrap {
  word-wrap: break-word;
  word-break: break-word;
}

#requisitionComments{
    max-width: 300px;
    display: block;
}

input[type=number] {
    -moz-appearance: textfield;
    font-size: 12px;
}

.iconWidth {
    width:20px;
}

.uppercase {
    text-transform:uppercase;
}

.fontSizeOverride {
    font-size: inherit !important;
}

.slide-hidden-default {
    max-height: 0;
    overflow: hidden;
}

#parSelectAlert.slide-open {
    max-height: 600px;
}

.threeFourInputContainer {
    display: flex;
    width: 100%;
    padding-bottom: 0.5rem;
}

.titleInput {
    padding: 0px 5px 0px 5px;
    width: 100%;
}

.threeFourInput {
    padding: 0px 5px 0px 0px;
    width: 92%;
}

input::-ms-clear, input::-ms-reveal {
    display: none;
}

#eyeToggler{
    position:absolute;
    float: right;
    transform: translate(-2.5rem, 2.7rem);
    cursor: pointer; 
    pointer-events: all;
}

.threeFourErrorMessage {
    color: #a94442;
    position: absolute;
    float: right;
    font-size: 10px ;
    transform: translate(0.5rem,-3rem);
    width:45%;
}

.dso-attestation-modal-body {
    height: 102%;
    width: 95% !important;
    overflow-y: auto;
    position: relative;
}

.dso-modal-title {
    margin: 0;
    line-height: 1.42857143;
    padding-left: 15px;
}

.firstNameInput {
    padding: 0px 5px 0px 0px;
}

.lastNameInput {
    padding: 0px 5px 0px 0px;
}

.nameInput {
    display: flex;
    width: 100%;
}

.dsoUserInfoForm, .dsoSignatureCanvas {
    margin: 2em;
}

.removeSignatureGlyphicon {
    float: right;
    transform: translate(-150%, 200%);
    padding: 0.2rem;
}

.signaturePadCanvas {
    border: 1px solid #D3D3D3;
    border-radius: 5px;
    width: 300px;
    height: 200px;
    cursor: pointer;
}

.dropdown-message {
    display: none;
    position: fixed;
    top: 0;
    width: 100%;
    color: black;
    text-align: center;
    z-index: 1000;
}

.dropdown-message button {
    background-color: transparent;
    border: none;
    color:black;
    margin-left: 20px;
    cursor: pointer;
}

.message-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-top: solid 1px #e6e6e624;
}

.message-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.message-text {
    flex-grow: 1;
    text-align: center;
}

.message-dismiss {
    margin-left: auto;
}

/*VENDOR DETAILS BLADE*/
.VendorBladeHyperlink, .ContractBladeHyperlink {
    font-weight: bold;
}

.vendorBlade .vendorMainSection, .contractBlade .contractMainSection {
    border-top: 1px solid #ddd;
    /*padding-top: 10px;*/
    margin-top: 10px;
    margin-bottom: 30px;
}

.contractBlade .contractMainSection, .contractBlade .contractMidSection,
#VendorHeaderColumn1 div, #VendorHeaderColumn2 div, #VendorHeaderColumn3 div,
#ItemDetailsColumn1 div, #ItemDetailsColumn2 div, #ItemDetailsColumn3 div,
.vendorMainSection .col-md-4 div {
    margin-bottom: 5px;
}

.contractBlade .contractMainSection {
    border-top: 1px solid #ddd;
}

.contractBlade .contractEndSection {
    margin-bottom: 30px;
}

.vendorBlade .vendorAddressLabel {
    background-color: #f3f3f3;
    border-radius: 4px;
    border: 1px dotted #ddd;
    height: 86%;
    padding: 5px 10px;
    overflow: hidden;
    min-width: 270px;
    margin-right: 15px;
}

.vendorBlade .vendorAddressLabel > span {
    float: left;
}

.vendorBlade .vendorAddressLabel div {
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
}

.vendorBlade .header, .contractBlade .header {
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 25px;
    font-weight: bold;

    color: #1B365D;
    line-height: 28px;
    font-family: open_sansregular;
    text-rendering: auto;
}

.vendorBlade .fieldPair {
    display: flex;
}

.vendorBlade .fieldPair>span:first-of-type {
    white-space: nowrap;
}

    .vendorBlade .fieldPair>span:first-of-type:after {
        content: "\00A0";
    }

/*ITEM DETAILS BLADE*/
.ExchangeApproverBlade .header,
.ItemDetailsBlade .header {
    margin-top: 15px;
    margin-bottom: 15px;
    font-size: 25px;
    font-weight: bold;
    color: #1B365D;
    line-height: 28px;
    font-family: open_sansregular;
    text-rendering: auto;
}

.ItemDetailsBlade .ItemMainSection{
    border-top: 1px solid #ddd;
    /*padding-top: 10px;*/
    margin-top: 10px;
    margin-bottom: 30px;
}

.ItemDetailsBlade .fieldPair{
    display: flex;
}

/* PURCHASE ORDER DETAILS BLADE */
.purchaseOrderBlade .purchaseOrderMainSection {
    border-top: 1px solid #ddd;
    padding-top: 10px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.purchaseOrderBlade .poChangeList {
    overflow: hidden;
    max-height:250px;
}

.purchaseOrderBlade .poChangeList.poChangeListHide {
    max-height: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
    border-top-width: 0;
    border-top-color: #fff;
}

.purchaseOrderBlade .headerRow {
    margin-left: -5px;
    margin-right: -15px;
}

.top-zero{
    top:0px;
}

.top-align {
    vertical-align: top;
}

.noDots-ul {
    list-style-type: none;
}

.purchaseOrderBlade .purchaseOrderAddressLabel {
    background-color: #f3f3f3;
    border-radius: 4px;
    border: 1px dotted #ddd;
    height: 86%;
    padding: 5px 10px;
    overflow: hidden;
    min-width: 270px;
}

.purchaseOrderBlade .purchaseOrderAddressLabel > span {
    float: left;
}

.purchaseOrderBlade .purchaseOrderAddressLabel div {
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
}

.purchaseOrderBlade .dynamicFieldPair{
    display: flex;
}

.purchaseOrderBlade .dynamicFieldPair > span{
    color: #888;
    white-space: nowrap;
}

.purchaseOrderBlade .dynamicFieldPair > span:after{
    content: "\00A0";
}

.purchaseOrderBlade .fieldHead{
    color: #888;
    white-space: nowrap;
}

.purchaseOrderBlade .fieldHead > span:first-of-type{
    content: "\00A0";
}

.purchaseOrderBlade .fieldWrap{
    flex-wrap: wrap;
}

.purchaseOrderBlade .overflowXScroll{
    overflow-x: auto;
}

.purchaseOrderBlade .scroll-wrapper > .scroll-content{
    overflow-x:hidden !important;
}

.purchaseOrderBlade .fieldPair{
    display: flex;
}

.purchaseOrderBlade .fieldPair > span:first-of-type {
    color: #888;
    white-space: nowrap;
}

.purchaseOrderBlade .fieldPair > span:first-of-type:after {
    content: "\00A0";
}

.purchaseOrderBlade .purchaseOrderHistory {
border-bottom: 1px solid #ddd;
margin-bottom: 4px;
}

.purchaseOrderBlade .purchaseOrderHistory:last-child {
    border-bottom: none;
    margin-bottom: 1px;
}

.purchaseOrderBlade .purchaseOrderLineItem {
    border-top: 1px solid #ddd;
    margin-bottom: 4px;
}

.purchaseOrderBlade .purchaseOrderLineItem small {
    color: #888;
}

.purchaseOrderBlade .purchaseOrderLineItemSubMenu {
    border-top: 1px dotted #ddd;
    margin-top: 4px;
}

.purchaseOrderBlade .grayedOut{
    color: #888;
    font-weight: normal;
}

.purchaseOrderBlade .fakeJqueryScrollbar {
    max-height: 310px;
    overflow-y: auto;
}

.purchaseOrderBlade {
    max-height: 100%;
}

.purchaseOrderBlade .fakeJqueryScrollbar::-webkit-scrollbar {
    width: 12px;
}

.purchaseOrderBlade .fakeJqueryScrollbar::-webkit-scrollbar-thumb {
    border-radius: 12px;
    border: 2px solid rgba(0,0,0,0);
    background-clip: padding-box;
    background-color: #d9d9d9;
}

.purchaseOrderBlade .fakeJqueryScrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #c2c2c2;
}

.purchaseOrderBlade .confirmationDetailsComment {
    margin-bottom: 5px;
    padding: 5px;
    background-color: #f3f3f3;
    border: 1px solid #dddddd;
}

.purchaseOrderBlade .fakeJqueryScrollbar::-webkit-scrollbar-track {
    border-radius: 12px;
    border: 2px solid rgba(0,0,0,0);
    background-clip: padding-box;
    background-color: #eeeeee;
}

.col-xs-display-none {
    display: none;
}

.form-group-comments-modal {
    padding: 10px;
}

.commentModal{
    height: 100%; 
    padding-bottom: 150px; 
    max-width: 500px;
    min-width: 500px;
}

#rightSideSummary{
    width: fit-content;
}

#columnTwo{
    max-width: fit-content;
    display: contents;
}

#columnThree {
    float: right;
}

#approvalRequestBlade{
    display: flex;
    margin-right: 0px;
}

#upcomingApprovalsColOne {
    width: auto;
}

#smartReqId{
    margin-top: 5px;
    position: absolute;
    float: right;
}

#approvalTextBox{
    width: auto;
}   

#itemSearchFooter{
    min-width: 500px;
}

.adminBodyContent{
    display: flow-root;
    margin-bottom: 16px;
}

.userWorkFlowSteps{
    padding-bottom: 15px;
    border-bottom: 1px solid transparent;
}

#searchBarGroup{
    display: block;
}

#approvalRequestBladeSlim{
    min-width: 850px; 
    max-width: 850px;
}

#vboWorkFlow{
    display: block;
    overflow: hidden;
}

#myApprovalSum {
    display: block;
    min-width: auto;
    overflow: hidden;
}

.bulkStatusDetail{
    width: auto;
}

.userDetailsModal{
    display: block;
    overflow: hidden;
    padding: 8px 0;
    border-bottom: 1px dotted #ddd;
}

@media (min-width: 768px) {
    
    .widgetDetail {
        float: left;
        width: inherit;
    }
    
    .col-sm-padding-left-0 {
        padding-left: 0;
    }

    .col-sm-display-visible {
        display: inherit;
    }
    .modal-footer-small {
        text-align: right;
        border-top: 1px solid #e5e5e5;
    }

    .widgetBodyContent {
        display: flex
    }
}

    /* MEDIA QUERIES */
@media (max-width: 480px) {
    .phoneNoPullRight {
        float: none !important;
    }

    .widgetBodyContent {
        display: flex;
    }

    #widgetBodySlim{
        display: flow-root;
    }

    .center-sm {
        text-align: center;
        width: 100%;
    }
    .stripeWelcome {
        color: #fff;
        padding: 0;
        text-align: right;
    }

    .summaryArea.fix {
        position: relative;
        margin: 10px 20px 20px 75px;
    }

    .workflowStepArea {
        width: 81%;
        margin: 15px 15px 20px 66px;
    }

    #menuleft.slide .menuList > li.menuListItem {
        width: 94.6%;
        padding: 4px;
    }

    .menuList > li.menuListItem h1 {
        font-size: 16px;
    }

    .large-glyphicon {
        font-size: 30px;
    }

    .blade.modal .modal-dialog, .bladeSmall.modal .modal-dialog {
        display: contents;
    }

    .blade.modal .modal-content, .bladeSmall.modal .modal-content {
        width: auto;
    }

    #approvalWorkflowModal{
        width: auto; 
    }

    #approvalWorkflow{
        width: auto !important; 
        height: inherit;
        display: block;
    }

    .detailsContainer .scrollbar-outer > .scroll-element.scroll-x,
    .detailsContainer .scrollbar-outer > .scroll-element.scroll-y {
        display: none;
    }

    .dropdown-menu {
        max-height: 250px;
        max-width: 310px;
        margin-left: -10px;
    }

    .widgetBody .scrollbar-outer {
        max-height: 100% !important;
    }

    .widgetHeader label {
        margin-top: 5px;
    }

    .itemSearchContainer {
        margin-right: 10px;
    }

    #itemSearchResults .scrollbar-outer {
        max-height: 100%;
    }

    .dashboardContainer .topWidget {
        margin-top: 30px;
    }

    .largeChart {
        width: 100%;
    }

    .textInDonut {
        top: 50px;
        left: 26px;
    }

    .reqKey {
        margin-top: 10px;
    }

    .approverCardContainer.scrollbar-outer {
        max-height: 100%;
    }

    .approverCardContainer .cardResult .cardResultFooter {
        width: 100%;
    }

    .modal-body .availableApprovers .scrollbar-outer {
        max-height: 100%;
    }

    .modal-body .availableReviewers .scrollbar-outer {
        max-height: auto;
    }

    #contentFromMenu.slide .hotKeyContainer {
        display: none;
    }

    .col-lg-22 {
        max-width: 115px;
        padding-right: 5px;
        padding-left: 10px;
    }

    .widgetBody {
        overflow-y: auto;
        display: flex;
    }

    #widgetBodyContentSlim{
        display: grid;
    }

    .col-xs-22 {
        max-width: 80px;
        padding-right: 5px;
        padding-left: 5px;
    }

    .col-xs-23 {
        max-width: 60px;
        padding-left: 15px;
        padding-right: 5px;
    }

    .pull-right-btns {
        float: right;
        padding-right: 50px !important;
    }

    .modal-content-container-no-header-sm {
        height: 100%;
        padding-bottom: 90px;
        padding-left: 50px;
    }

    .modal-footer-sm {
        text-align: right;
        border-top: 1px solid #e5e5e5;
    }

    .pulldown-item-sort {
        max-width: 150px;
    }

    .dateSelector ul.dropdown-menu li table thead tr th button.btn,
    .dateSelector ul.dropdown-menu li table tbody tr td button.btn {
        padding: 8px 10px;
    }

    .modal-footer {
        background-color: #f8f8f8;
        margin-bottom: 5px;
    }

    .bladeSmall .modal-body .form-group .dropdown-menu {
        max-height: 270px;
        max-width: 290px;
    }

    #columnOne{
        width: 50%;
    }

    #bulkEditApprover{
        display: flow-root;
    }
}
    /****************************************/

@media (max-width: 600px) {
    main {
        padding-top: 163px;
    }

    #menuleft>div {
        top: 163px;
    }

    .pageHeader-inner {
        height: 145px;
    }
    .modal-body {
        height: 106%;
    }

    #itemSearchFooter{
        min-width: auto;
    }

    #smartReqId{
        display: contents;
        position: relative;
        float: right;
    }
    
}

@media(max-width:769px) {

    #menuleft.hideMenu #mainMenu {
        display: none;
    }

    #menuleft #mainMenuHeader {
        width: 100%;
    }

    #contentFromMenu {
        padding-right: 0;
    }

    #contentFromMenu.slide {
        left: 0;
    }

    .tableContainer, .detailsContainer {
        margin-top: 32px;
        margin-bottom: 0;
    }

    .col-lg-6 {
        width: 100%;
    }

    #columnTwo{
        max-width: fit-content;
        display: block;
    }

    #columnThree{
        float: right;
    }

    .col-sm-7 {
        max-width: 150px;
    }

    col-sm-5 {
        max-width: 300px;
    }

    .col-sm-13 {
        max-width: 55vw;
        padding-right: 0px;
        padding-top: 18px;
    }

    .widgetContainer-sm {
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 10px;
        overflow-y: auto;
        height: 500px;
    }

    .modal-body {
        height: 100%;
    }

    .modal-footer-comments {
        padding: 5px;
    }

    .widgetDetail {
        width: auto;
    }
      
}

@media (width: 820px) {

    #rightSideSummary{
        width: 100%;
    }
    .modal-content-container-par {
        height: 100%;
    }
    .modal-body {
        overflow-y: auto;
    }
    .modal-body-bill-info, .modal-body-approval-req {
        height: 100% !important;
        overflow-y: auto;
    }
    .modal-body-item-details {
        width: 94%;
    }
    .modal-footer {
        align-content: flex-end;
        width: auto;
    }
    .modal-content-container-no-header {
        height: 102%;
    }
    .modal-body-comments {
        height: 100% !important;
    }
    .modal-footer-comments {
        padding: 5px;
    }
    .modal-body-vendor-blade {
        height: 107%;
        width: 100%;
    }
    .modal-footer-vendor-details {
        width: 100%;
    }
    .modal-footer-approval-request {
        padding: 5px;
    }
    .modal-body-approval-workflow {
        overflow-y: auto;
    }
}

@media(max-width:991px) {

    #approvalRequestBlade {
        display: contents;
    }

    .reqTypes{
        margin-top: 25px;
    }

    #menuleft #mainMenu {
        overflow-y: auto;
    }
    .modal-content-container-no-header {
        height: inherit;
        padding-bottom: 60px;
        padding-left: 50px;
    }

    #rightSideSummary{
        width: 100%;
    }
}

@media (min-width: 430px) and (max-width:768px) {
    #columnOne{
        display:flex
    }

    #reqHistoryBlade{
        display: grid;
    }

    #approvalFormGroup{
        display: contents;
    }

}

/****************************************/
@media (max-width:600px) {
    .poNumberContainer {
        right: 0;
        position: absolute;
        max-width: max-content;
   }
}

@media (min-width: 374px) and (max-width:767px) {
    .commentModal{
        max-width: 95%;
        min-width: auto;
    }

    #contentFromMenu.slide .hotKeyContainer {
        display: none;
    }

    #approvalRequestBlade {
        display: inline-block;
    }

    .phoneNoPullRight {
        float: none !important;
        width: inherit;
    }

    .summaryArea.fix {
        position: relative;
        margin: 0 15px 20px 55px;
    }

    .workflowStepArea {
        width: 83%;
        margin: 15px 15px 20px 66px;
    }

    .itemCardContainer .scrollbar-outer {
        max-height: 100% !important;
        margin-bottom: 0;
    }

    .submitButton.btn-block {
        display: inline;
        width: auto;
        vertical-align: top;
    }

    .detailsContainer .scrollbar-outer > .scroll-element.scroll-x,
    .detailsContainer .scrollbar-outer > .scroll-element.scroll-y {
        display: none;
    }

    #menuleft.slide .menuList > li.menuListItem {
        width: 94.6%;
        padding: 4px;
    }

    .menuList > li.menuListItem h1 {
        font-size: 16px;
    }

    .large-glyphicon {
        font-size: 30px;
    }

    .dropdown-menu {
        max-height: 465px;
    }

    .widgetBody .scrollbar-outer {
        max-height: 100% !important;
    }

    .widgetHeader label {
        margin-top: 5px;
    }

    .blade.modal .modal-dialog{
        width: auto;
    }
    
    .bladeSmall.modal .modal-dialog {
    }

    #bladeCss{
        width: 100%;
    }

    .modal-open .blade.modal, .modal-open .bladeSmall.modal {
        width: fit-content;
    }

    #approvalWorkflow{
        width: auto !important; 
    }

    #itemSearchResults .scrollbar-outer {
        max-height: 450px;
    }

    .dashboardContainer .topWidget {
        margin-top: 30px;
    }

    .approverCardContainer.scrollbar-outer, .modal-body .availableApprovers .scrollbar-outer {
        max-height: 100%;
    }

    .approverCardContainer .cardResult .cardResultFooter {
        width: 100%;
    }

    .modal-body .availableReviewers .scrollbar-outer {
        max-height: auto;
        width: min-content;
    }
    .modal-content-container {
        height: 100%;
        left:10%;
        padding-top: 61px;
        padding-bottom: 60px;
        padding-left: 50px;
    }
    .modal-content-container-no-header {
        width: auto;
        margin: inherit;
        height: inherit;
    }
    #reqHistorySum{
        display: contents;
    }
    #searchBarGroup {
        display: contents;
    }
    .modal-body {
        height: inherit;
        width: auto;
        position: relative;
        padding-top: 25px;
    }
    .blade-modal-body-sm {
        height: 102%;
        overflow-y: auto;
        position: relative;
        padding: 55px 15px 15px 15px;
    }
    .attestation-modal-body {
        height: 102%;
        width: 95% !important;
        overflow-y: auto;
        position: relative;
        padding: 55px 10px 15px 30px;
    }
    .modal-body-bill-info, .modal-body-approval-req {
        height: 100%;
        overflow-y: auto;
        position: relative;
        padding-top: 55px;
    }
    .detailsContainer {
        width: auto;
    }
    .modal-body-approval-workflow {
        width: 95% !important;
        overflow-y: auto;
        position: relative;
        padding: 15px;
        padding-top: 55px;
    }

    .modal-footer-approval-workflow {
        padding: 5px;
        width: 94%;
    }

    .modal-body-item-details, .modal-body-vendor-details {
        width: 95% !important;
        overflow-y: auto;
        position: relative;
        padding: 15px;
        padding-top: 55px;
    }

    .modal-footer-small {
        text-align: right;
        border-top: 1px solid #e5e5e5;
    }

    .modal-body-approval-request {
        height: 100%;
        width: 95% !important;
        overflow-y: auto;
        position: relative;
        padding: 15px 0px;
        padding-top: 55px;
    }

    .modal-content-container-no-header-dso {
        height: 100%;
        padding-bottom: 60px;
        padding-left: 50px;
        width: inherit;
    }

    .modal-footer-dso-small {
        border-top: 1px solid #e5e5e5;
    }

    .threeFourErrorMessage {
        color: #a94442;
        position: absolute;
        float: right;
        font-size: 10px;
        transform: translate(0.5rem,-3rem);
        width: 45%;
    }

    #approvalRequestBladeSlim{
        min-width: fit-content;
        max-width: fit-content;
    }
}

@media (min-width: 768px) and (max-width: 991px) {

    #contentFromMenu.slide .hotKeyContainer {
        display: none;
    }

    .summaryArea.fix {
        position: relative;
        margin: 15px 15px 20px 66px;
        width: 90%
    }

    .workflowStepArea {
        width: 87.3%;
        margin: 15px 15px 20px 66px;
    }

    .submitButton.btn-block {
        display: inline;
        width: auto;
    }

    .itemCardContainer .scrollbar-outer {
        max-height: 100% !important;
        margin-bottom: 0;
    }

    .detailsContainer .scrollbar-outer > .scroll-element.scroll-x,
    .detailsContainer .scrollbar-outer > .scroll-element.scroll-y {
        display: none;
    }

    #menuleft.slide .menuList > li.menuListItem {
        width: 94.6%;
    }

    .widgetBody .scrollbar-outer {
        max-height: 100% !important;
        overflow-y: auto;
    }

    .widgetHeader label {
        margin-top: 5px;
    }

    .blade.modal .modal-dialog{
    }

    .bladeSmall.modal .modal-dialog {
    }

    .blade.modal .modal-content, .bladeSmall.modal .modal-content {
    }

    #itemSearchResults .scrollbar-outer {
        max-height: 570px;
    }

    .approverCardContainer.scrollbar-outer {
        max-height: 100%;
    }

    .approverCardContainer .cardResult .cardResultFooter {
        width: 100%;
    }

    .modal-body .availableApprovers .scrollbar-outer, .modal-body .availableReviewers .scrollbar-outer {
        max-height: auto;
        display: contents;
    }

    .col-sm-5 {
        max-width: 300px !important;
    }

    .col-sm-6 {
        max-width: 300px !important;
    }

    .textInDonut {
        left: 33%;
    }
    .modal-content-container-no-header {
        padding-bottom: 60px;
        padding-left: 50px;
    }
    .modal-content-container-no-header-dso {
        height: 100%;
    }
    .modal-content-container {
        height: 100%;
    }
    .modal-body {
        overflow: auto;
    }
    .modal-body-bill-info, .modal-body-approval-req {
        height: 100% !important;
        overflow-y: auto;
    }
    .modal-body-comments {
        height: 100%;
        width: 94%;
    }
    .modal-footer-comments {
    }
    .modal-body-approval-workflow {
        width: 100%;
        padding: 5px;
    }
    .modal-footer-approval-workflow {
        padding: 5px 22px;
        width: 94%;
    }
    .modal-body-item-details {
        width: 100%;
    }
    .col-sm-12 {
        width: 100%;
    }
    .col-sm-10 {
        width: 100%;
    }
    .currency-symbol {
        margin: 18.5px -31.5px;
        position: absolute;
        padding: 5.5px 5.5px 5.5px 3.5px;
    }
    .modal-footer{
        padding: 5px 10px 5px 0px;
    }
    .row{
        margin: auto;
    }
    #dashboardReqs{
        margin: none;
    }
    .modal-content-container-no-header > form {
    }
    .modal-footer-ipad {
        padding-right: 53px;
    }
    .modal-footer-small {
        text-align: right;
        border-top: 1px solid #e5e5e5;
    }
    #vendorDetailsBlade{
        height: inherit;
        width: fit-content;
    }
    #approvalTextBox {
        width: 100%;
    }
    
    #approvalRequestBladeSlim{
        min-width: 750px; 
        max-width: 750px;
    }

    #smartReqId{
        display: contents;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {

    #myApprovalSum {
        display: block;
        min-width: auto;
    }

    .menuList.hideMenu > li.menuListItem {
        padding: 0;
        width: 46.7%;
    }

    .menuList > li.menuListItem {
        margin: 5px;
    }

    .menuItemContent .menuSubItemValue {
        font-size: 24px;
    }

    .menuItemContent ul li {
        margin: 0 5px 1em;
        font-size: 11px;
    }

    .large-glyphicon {
        font-size: 30px;
    }

    .detailsContainer {
        margin-right: -16px;
        padding-right: 0;
    }

    .summaryArea {
        height: 82.5%;
        margin-right: 5px;
    }

    .workflowStepArea {
        width: 131%;
        margin-left: 0;
    }

    .itemCardContainer .scrollbar-outer {
        max-height: 100%;
    }

    .widgetBody .scrollbar-outer{
        max-height: 100% !important;
    }

    .blade.modal .modal-dialog {
        margin-right: 0;
    }

    .blade.modal .modal-content {
    }

    .bladeSmall.modal .modal-content {
    }

    .bladeSmall.modal .modal-dialog {
        width: auto;
    }

    .bladeSmall .modal-body .form-group .dropdown-menu {
        max-height: 225px;
    }

    #itemSearchResults .scrollbar-outer {
        max-height: 500px;
    }

    .modal-body .itemCardContainer .scrollbar-dynamic {
        max-height: 390px;
    }

    .dashboardContainer .scrollbar-outer {
        max-height: 630px;
    }

    .approverCardContainer.scrollbar-outer {
        max-height: 100%;
    }

    .largeChart {
        width: 90%;
    }

    .textInDonut {
        left: 32%;
    }

    .modal-body .availableApprovers .scrollbar-outer {
        max-height:100%;
    }

    .modal-body .availableReviewers .scrollbar-outer {
        max-height:100%;
    }

        .hotKeyContainer {
            display:none;
        }
        .currency-symbol {
            margin: 18.5px -31.5px;
            position: absolute;
            padding: 5.5px 5.5px 5.5px 3.5px;
        }
        .modal-content-container-no-header-dso {
            height: 100%;
        }

        #smartReqId{
            display: contents;
        }
    }

@media (min-width: 1200px) and (max-width: 1550px) {
    .bladeSmall.modal .modal-content {
    }

    .bladeSmall.modal .modal-dialog {
    }

    #rightSideSummary {
        width: min-content;
        min-width: 25%;
    }
}

@media (min-width: 1200px) and (max-width:1679px) {
    #contentFromMenu.slide .hotKeyContainer {
        bottom: auto;
        position: relative;
    }

    .summaryArea.fix {
        position: relative;
        height: 100%;
        margin-left: -15px;
        margin-right: 15px;
    }

    #menuleft.slide .menuList > li.menuListItem {
        max-width: 46.6%;
    }

    .detailsContainer {
        padding-right: 0;
    }

    .modal-body .availableApprovers .scrollbar-outer {
        max-height: 100%;
    }

    .modal-body .availableReviewers .scrollbar-outer {
        max-height: 100%;
    }

    .itemCardContainer .scrollbar-outer {
        max-height:100%;
    }

        .approverCardContainer.scrollbar-outer {
            max-height:100%;
        }
    }

@media (min-width: 1680px) {
    #contentFromMenu.slide .hotKeyContainer {
        bottom: auto;
        position: relative;
    }

    .summaryArea.fix {
        position: relative;
        height: 100%;
        margin-left: -15px;
        margin-right: 15px;
    }

    #menuleft.slide .menuList > li.menuListItem {
        width: 47.6%;
    }

        .detailsContainer {
            padding-right: 0;
        }
    }

.tooltip-inner {
    white-space:pre;
    max-width:none;
}

.tooltipOverrideOverride .tooltip-inner {
    white-space: normal;
    max-width: 200px;
}

/* Style the tab content */

#userReportWidget .scrollbar-outer {
    max-height: 550px;
}

.userDetailsLabel {
    font-size: 10px;
}

.widgetHeader .checkboxFilterSingleton {
    margin-left: 10px;
    margin-top: 0;
    vertical-align: middle;
}

#userReportWidget .scrollbar-outer {
    max-height: 550px;
}

.userDetailsLabel {
    font-size: 10px;
}

.widgetHeader .checkboxFilterSingleton {
    margin-left: 10px;
    margin-top: 0;
    vertical-align: middle;
}

.failedValidation a{
    color: #a94442 !important;
    font-weight: bold;
}

.nav-tabs>li.disabled>a {
    background-color: #ddd !important;
    cursor: not-allowed !important;
}
.label-lightRed {
    background-color: #d40000; /*maroon*/
    text-transform: none;
}
.label-rgbGreen {
    background-color: rgb(0, 170, 0); /*#008000*/
    text-transform: none;
}
.label-darkGreen {
    background-color: forestgreen; /*#008000*/
    text-transform: none;
}

.myRequisitionTotalLoadInfo {font-size: 13px;}

.textWrap {
    word-wrap: break-word;
}

.timepicker-container {
    max-width: 205px;
}

.timepicker-container > table {
    width: 69%;
}

.timepicker-container > table > tbody > tr:nth-child(2) > td:nth-child(2) {
    text-align: center;
    width: 50px;
}

.systemMessageMini {
    margin-top: 15px;
    margin-left: 12px;
    margin-bottom: 0px;
    /*border: 2px solid;*/
    padding: 15px;
}

.systemMessageIcon {
    font-size: 1.5em;
}

.userWorkFlowSteps{
    display: inherit;
}

.characterCount{
    float: right;
    color: #768692;
}

#autoSub-label {
    font-family: Helvetica, sans-serif;
    font-weight: bold;
}

.mobileWrap {
    word-wrap: break-word;
    word-break: break-word;
}