﻿using System;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using log4net;
using System.Reflection;

namespace RequisitionServices.AppInsights
{
    public class RequestFilter : ITelemetryProcessor
    {
        private ITelemetryProcessor Next { get; set; }
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public RequestFilter(ITelemetryProcessor next)
        {
            this.Next = next;
        }

        public void Process(ITelemetry item)
        {
            var request = item as RequestTelemetry;
            var dependency = item as DependencyTelemetry;

            if (request != null)
            {
               this.ProcessItemRequest(ref request);
               this.Next.Process(item);
            }
            else if(dependency!=null)
            {
                this.ProcessItemRequest(ref dependency);
                this.Next.Process(item);
            }
            else { this.Next.Process(item); }
        }

        public void ProcessItemRequest(ref RequestTelemetry request)
        {
            if (Uri.IsWellFormedUriString(request.Url.ToString(), UriKind.Absolute))
            {
                var uri = new Uri(request.Url.ToString());
                this.MaskURI(ref uri);
                request.Url = uri;
            }
        }

        public void ProcessItemRequest(ref DependencyTelemetry dependency)
        {
            if (Uri.IsWellFormedUriString(dependency.Data, UriKind.Absolute))
            {
                var uri = new Uri(dependency.Data);
                this.MaskURI(ref uri);
                dependency.Data = uri.OriginalString;
            }
        }

        private Uri MaskURI(ref Uri uri)
        {
            uri = new Uri(uri.GetLeftPart(UriPartial.Path));

            return uri;
        }
    }
}