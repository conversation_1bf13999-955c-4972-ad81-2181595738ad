﻿using RequisitionServices.DomainModel.UserAlertMessage;
using RequisitionServices.DomainServices.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using System.Web.Http.Description;

namespace RequisitionServices.Controllers
{
    public class UserAlertMessageController : ApiController
    {
        private readonly string failure = "Failed";
        private readonly string notFound = "Id Not Found";
        private readonly string badRequestForCreate = "Failed to create message";

        readonly IUserAlertMessageService _userAlertMessageService;

        public UserAlertMessageController(IUserAlertMessageService userAlertMessageService)
        {
            _userAlertMessageService = userAlertMessageService;
        }

        [HttpGet]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<HttpResponseMessage> GetAllMessages()
        {
            try
            {
                var result = await _userAlertMessageService.GetAllMessages();
                if (result.Any())
                {
                    return Request.CreateResponse(HttpStatusCode.OK, result);
                }
                return Request.CreateResponse(HttpStatusCode.NoContent);
            }
            catch (Exception ex)
            {
                return Request.CreateResponse(HttpStatusCode.InternalServerError, ex);
            }
        }

        [HttpGet]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<HttpResponseMessage> GetMessageById(int messageId)
        {
            if (messageId <= 0)
            {
                return Request.CreateResponse(HttpStatusCode.BadRequest);
            }
          
            try
            {
                var result = await _userAlertMessageService.GetMessageById(messageId);
                return Request.CreateResponse(HttpStatusCode.OK, result);
            }
            catch(KeyNotFoundException)
            {
                return Request.CreateResponse(HttpStatusCode.NotFound, badRequestForCreate);
            }
            catch(Exception ex)
            {
                return Request.CreateResponse(HttpStatusCode.InternalServerError, ex);
            }
        }

        [HttpPost]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<HttpResponseMessage> CreateMessage(UserAlertMessageRequest messageRequest)
        {
            var result = await _userAlertMessageService.CreateMessage(messageRequest);
            return result != null
                ? Request.CreateResponse(HttpStatusCode.Created, result)
                : Request.CreateResponse(HttpStatusCode.InternalServerError, failure);
        }

        [HttpPatch]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<HttpResponseMessage> UpdateMessage(UserAlertMessageRequest messageRequest)
        {
            try
            {
                var result = await _userAlertMessageService.UpdateMessage(messageRequest);
                return result != null
                    ? Request.CreateResponse(HttpStatusCode.OK, result)
                    : Request.CreateResponse(HttpStatusCode.NotFound, notFound);
            }
            catch
            {
                return Request.CreateResponse(HttpStatusCode.InternalServerError, failure);
            }
        }

        [HttpDelete]
        [ApiExplorerSettings(IgnoreApi = true)]
        public async Task<HttpResponseMessage> DeleteMessage(int messageId)
        {
            try
            {
                await _userAlertMessageService.DeleteMessage(messageId);
                return Request.CreateResponse(HttpStatusCode.OK);
            }
            catch(KeyNotFoundException knf)
            {
                return Request.CreateResponse(HttpStatusCode.NotFound, knf);
            }
            catch
            {
                return Request.CreateResponse(HttpStatusCode.InternalServerError, failure);
            }
        }
    }
}