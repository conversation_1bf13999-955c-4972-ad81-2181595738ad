﻿using log4net;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.Utility.LegacyConnector
{
    /// <summary>
    /// <Userstory>US119491</Userstory>
    /// <para>OAuth token generator class for legacy connector API</para>
    /// </summary>
    public class LegacyConnectorTokenGenerator
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        readonly string _TokenEndpoint = ConfigurationManager.AppSettings.Get("LegacyConnectorTokenUrl");
        readonly string _TokenUsername = ConfigurationManager.AppSettings.Get("LegacyConnectorTokenUsername");
        readonly string _TokenPassword = ConfigurationManager.AppSettings.Get("LegacyConnectorTokenPassword");
        readonly string _TokenGrantType = ConfigurationManager.AppSettings.Get("LegacyConnectorTokenGrant_type");


        /// <summary>
        /// Executes a HTTP GET call to the apigee cloud Token URL to get the OAuth token
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <returns></returns>
        public T GetLegacyConnectorClientAuthToken<T>()
        {
            log.Debug($"Initiating call {_TokenEndpoint}");
            var parameters = new Dictionary<string, string>
                            {
                                {"grant_type",_TokenGrantType}
                            };

            var response = LegacyConnectorUtility.ExecuteTokenApiGetTo<T>(_TokenEndpoint, _TokenUsername, _TokenPassword,parameters);

            return response;
        }
    }
}
