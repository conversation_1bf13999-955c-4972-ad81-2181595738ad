﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RequisitionServices.DomainServices.RushWorkflowSvc {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Requisition", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class Requisition : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionApprover[] ApproversField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CommentsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime CreateDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CreatedByField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.ErrorMessage ErrorMessageField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LocationIdentifierField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool RequiresApprovalField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionItem[] RequisitionItemsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionStatusHistory[] RequisitionStatusHistoriesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionStatusTypeIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionTypeIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Guid WorkflowInstanceIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionApprover[] Approvers {
            get {
                return this.ApproversField;
            }
            set {
                if ((object.ReferenceEquals(this.ApproversField, value) != true)) {
                    this.ApproversField = value;
                    this.RaisePropertyChanged("Approvers");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Comments {
            get {
                return this.CommentsField;
            }
            set {
                if ((object.ReferenceEquals(this.CommentsField, value) != true)) {
                    this.CommentsField = value;
                    this.RaisePropertyChanged("Comments");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreateDate {
            get {
                return this.CreateDateField;
            }
            set {
                if ((this.CreateDateField.Equals(value) != true)) {
                    this.CreateDateField = value;
                    this.RaisePropertyChanged("CreateDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedBy {
            get {
                return this.CreatedByField;
            }
            set {
                if ((object.ReferenceEquals(this.CreatedByField, value) != true)) {
                    this.CreatedByField = value;
                    this.RaisePropertyChanged("CreatedBy");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.ErrorMessage ErrorMessage {
            get {
                return this.ErrorMessageField;
            }
            set {
                if ((object.ReferenceEquals(this.ErrorMessageField, value) != true)) {
                    this.ErrorMessageField = value;
                    this.RaisePropertyChanged("ErrorMessage");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LocationIdentifier {
            get {
                return this.LocationIdentifierField;
            }
            set {
                if ((object.ReferenceEquals(this.LocationIdentifierField, value) != true)) {
                    this.LocationIdentifierField = value;
                    this.RaisePropertyChanged("LocationIdentifier");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool RequiresApproval {
            get {
                return this.RequiresApprovalField;
            }
            set {
                if ((this.RequiresApprovalField.Equals(value) != true)) {
                    this.RequiresApprovalField = value;
                    this.RaisePropertyChanged("RequiresApproval");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionId {
            get {
                return this.RequisitionIdField;
            }
            set {
                if ((this.RequisitionIdField.Equals(value) != true)) {
                    this.RequisitionIdField = value;
                    this.RaisePropertyChanged("RequisitionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionItem[] RequisitionItems {
            get {
                return this.RequisitionItemsField;
            }
            set {
                if ((object.ReferenceEquals(this.RequisitionItemsField, value) != true)) {
                    this.RequisitionItemsField = value;
                    this.RaisePropertyChanged("RequisitionItems");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionStatusHistory[] RequisitionStatusHistories {
            get {
                return this.RequisitionStatusHistoriesField;
            }
            set {
                if ((object.ReferenceEquals(this.RequisitionStatusHistoriesField, value) != true)) {
                    this.RequisitionStatusHistoriesField = value;
                    this.RaisePropertyChanged("RequisitionStatusHistories");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionStatusTypeId {
            get {
                return this.RequisitionStatusTypeIdField;
            }
            set {
                if ((this.RequisitionStatusTypeIdField.Equals(value) != true)) {
                    this.RequisitionStatusTypeIdField = value;
                    this.RaisePropertyChanged("RequisitionStatusTypeId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionTypeId {
            get {
                return this.RequisitionTypeIdField;
            }
            set {
                if ((this.RequisitionTypeIdField.Equals(value) != true)) {
                    this.RequisitionTypeIdField = value;
                    this.RaisePropertyChanged("RequisitionTypeId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Guid WorkflowInstanceId {
            get {
                return this.WorkflowInstanceIdField;
            }
            set {
                if ((this.WorkflowInstanceIdField.Equals(value) != true)) {
                    this.WorkflowInstanceIdField = value;
                    this.RaisePropertyChanged("WorkflowInstanceId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ErrorMessage", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class ErrorMessage : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ExceptionTypeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string MessageField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ExceptionType {
            get {
                return this.ExceptionTypeField;
            }
            set {
                if ((object.ReferenceEquals(this.ExceptionTypeField, value) != true)) {
                    this.ExceptionTypeField = value;
                    this.RaisePropertyChanged("ExceptionType");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Message {
            get {
                return this.MessageField;
            }
            set {
                if ((object.ReferenceEquals(this.MessageField, value) != true)) {
                    this.MessageField = value;
                    this.RaisePropertyChanged("Message");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RequisitionApprover", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class RequisitionApprover : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int ApprovalLevelField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private decimal ApprovalLimitField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ApproverEmailField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int ApproverIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool HasDecidedField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool IsCurrentApproverField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ApprovalLevel {
            get {
                return this.ApprovalLevelField;
            }
            set {
                if ((this.ApprovalLevelField.Equals(value) != true)) {
                    this.ApprovalLevelField = value;
                    this.RaisePropertyChanged("ApprovalLevel");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public decimal ApprovalLimit {
            get {
                return this.ApprovalLimitField;
            }
            set {
                if ((this.ApprovalLimitField.Equals(value) != true)) {
                    this.ApprovalLimitField = value;
                    this.RaisePropertyChanged("ApprovalLimit");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ApproverEmail {
            get {
                return this.ApproverEmailField;
            }
            set {
                if ((object.ReferenceEquals(this.ApproverEmailField, value) != true)) {
                    this.ApproverEmailField = value;
                    this.RaisePropertyChanged("ApproverEmail");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int ApproverId {
            get {
                return this.ApproverIdField;
            }
            set {
                if ((this.ApproverIdField.Equals(value) != true)) {
                    this.ApproverIdField = value;
                    this.RaisePropertyChanged("ApproverId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool HasDecided {
            get {
                return this.HasDecidedField;
            }
            set {
                if ((this.HasDecidedField.Equals(value) != true)) {
                    this.HasDecidedField = value;
                    this.RaisePropertyChanged("HasDecided");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsCurrentApprover {
            get {
                return this.IsCurrentApproverField;
            }
            set {
                if ((this.IsCurrentApproverField.Equals(value) != true)) {
                    this.IsCurrentApproverField = value;
                    this.RaisePropertyChanged("IsCurrentApprover");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RequisitionItem", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class RequisitionItem : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.ClinicalUseDetail[] ClinicalUseDetailsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime CreateDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CreatedByField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool IsApprovedField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool IsRushOrderField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool IsStatusChangeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ItemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> MainItemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> OriginalParentSystemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> PONumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ParIdentifierField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> ParentSystemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> QuantityFulfilledField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int QuantityToOrderField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionItemStatusHistory[] RequisitionItemStatusHistoriesField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionItemStatusTypeIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<System.DateTime> RequisitionScheduledDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private RequisitionServices.DomainServices.RushWorkflowSvc.SPRDetail SPRDetailField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> TrackerIndexField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.ClinicalUseDetail[] ClinicalUseDetails {
            get {
                return this.ClinicalUseDetailsField;
            }
            set {
                if ((object.ReferenceEquals(this.ClinicalUseDetailsField, value) != true)) {
                    this.ClinicalUseDetailsField = value;
                    this.RaisePropertyChanged("ClinicalUseDetails");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreateDate {
            get {
                return this.CreateDateField;
            }
            set {
                if ((this.CreateDateField.Equals(value) != true)) {
                    this.CreateDateField = value;
                    this.RaisePropertyChanged("CreateDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedBy {
            get {
                return this.CreatedByField;
            }
            set {
                if ((object.ReferenceEquals(this.CreatedByField, value) != true)) {
                    this.CreatedByField = value;
                    this.RaisePropertyChanged("CreatedBy");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id {
            get {
                return this.IdField;
            }
            set {
                if ((this.IdField.Equals(value) != true)) {
                    this.IdField = value;
                    this.RaisePropertyChanged("Id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsApproved {
            get {
                return this.IsApprovedField;
            }
            set {
                if ((this.IsApprovedField.Equals(value) != true)) {
                    this.IsApprovedField = value;
                    this.RaisePropertyChanged("IsApproved");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsRushOrder {
            get {
                return this.IsRushOrderField;
            }
            set {
                if ((this.IsRushOrderField.Equals(value) != true)) {
                    this.IsRushOrderField = value;
                    this.RaisePropertyChanged("IsRushOrder");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool IsStatusChange {
            get {
                return this.IsStatusChangeField;
            }
            set {
                if ((this.IsStatusChangeField.Equals(value) != true)) {
                    this.IsStatusChangeField = value;
                    this.RaisePropertyChanged("IsStatusChange");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ItemId {
            get {
                return this.ItemIdField;
            }
            set {
                if ((object.ReferenceEquals(this.ItemIdField, value) != true)) {
                    this.ItemIdField = value;
                    this.RaisePropertyChanged("ItemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> MainItemId {
            get {
                return this.MainItemIdField;
            }
            set {
                if ((this.MainItemIdField.Equals(value) != true)) {
                    this.MainItemIdField = value;
                    this.RaisePropertyChanged("MainItemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> OriginalParentSystemId {
            get {
                return this.OriginalParentSystemIdField;
            }
            set {
                if ((this.OriginalParentSystemIdField.Equals(value) != true)) {
                    this.OriginalParentSystemIdField = value;
                    this.RaisePropertyChanged("OriginalParentSystemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> PONumber {
            get {
                return this.PONumberField;
            }
            set {
                if ((this.PONumberField.Equals(value) != true)) {
                    this.PONumberField = value;
                    this.RaisePropertyChanged("PONumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParIdentifier {
            get {
                return this.ParIdentifierField;
            }
            set {
                if ((object.ReferenceEquals(this.ParIdentifierField, value) != true)) {
                    this.ParIdentifierField = value;
                    this.RaisePropertyChanged("ParIdentifier");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ParentSystemId {
            get {
                return this.ParentSystemIdField;
            }
            set {
                if ((this.ParentSystemIdField.Equals(value) != true)) {
                    this.ParentSystemIdField = value;
                    this.RaisePropertyChanged("ParentSystemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> QuantityFulfilled {
            get {
                return this.QuantityFulfilledField;
            }
            set {
                if ((this.QuantityFulfilledField.Equals(value) != true)) {
                    this.QuantityFulfilledField = value;
                    this.RaisePropertyChanged("QuantityFulfilled");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int QuantityToOrder {
            get {
                return this.QuantityToOrderField;
            }
            set {
                if ((this.QuantityToOrderField.Equals(value) != true)) {
                    this.QuantityToOrderField = value;
                    this.RaisePropertyChanged("QuantityToOrder");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionId {
            get {
                return this.RequisitionIdField;
            }
            set {
                if ((this.RequisitionIdField.Equals(value) != true)) {
                    this.RequisitionIdField = value;
                    this.RaisePropertyChanged("RequisitionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.RequisitionItemStatusHistory[] RequisitionItemStatusHistories {
            get {
                return this.RequisitionItemStatusHistoriesField;
            }
            set {
                if ((object.ReferenceEquals(this.RequisitionItemStatusHistoriesField, value) != true)) {
                    this.RequisitionItemStatusHistoriesField = value;
                    this.RaisePropertyChanged("RequisitionItemStatusHistories");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionItemStatusTypeId {
            get {
                return this.RequisitionItemStatusTypeIdField;
            }
            set {
                if ((this.RequisitionItemStatusTypeIdField.Equals(value) != true)) {
                    this.RequisitionItemStatusTypeIdField = value;
                    this.RaisePropertyChanged("RequisitionItemStatusTypeId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> RequisitionScheduledDate {
            get {
                return this.RequisitionScheduledDateField;
            }
            set {
                if ((this.RequisitionScheduledDateField.Equals(value) != true)) {
                    this.RequisitionScheduledDateField = value;
                    this.RaisePropertyChanged("RequisitionScheduledDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public RequisitionServices.DomainServices.RushWorkflowSvc.SPRDetail SPRDetail {
            get {
                return this.SPRDetailField;
            }
            set {
                if ((object.ReferenceEquals(this.SPRDetailField, value) != true)) {
                    this.SPRDetailField = value;
                    this.RaisePropertyChanged("SPRDetail");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> TrackerIndex {
            get {
                return this.TrackerIndexField;
            }
            set {
                if ((this.TrackerIndexField.Equals(value) != true)) {
                    this.TrackerIndexField = value;
                    this.RaisePropertyChanged("TrackerIndex");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RequisitionStatusHistory", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class RequisitionStatusHistory : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CommentsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime CreateDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CreatedByField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionStatusTypeIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Comments {
            get {
                return this.CommentsField;
            }
            set {
                if ((object.ReferenceEquals(this.CommentsField, value) != true)) {
                    this.CommentsField = value;
                    this.RaisePropertyChanged("Comments");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreateDate {
            get {
                return this.CreateDateField;
            }
            set {
                if ((this.CreateDateField.Equals(value) != true)) {
                    this.CreateDateField = value;
                    this.RaisePropertyChanged("CreateDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedBy {
            get {
                return this.CreatedByField;
            }
            set {
                if ((object.ReferenceEquals(this.CreatedByField, value) != true)) {
                    this.CreatedByField = value;
                    this.RaisePropertyChanged("CreatedBy");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id {
            get {
                return this.IdField;
            }
            set {
                if ((this.IdField.Equals(value) != true)) {
                    this.IdField = value;
                    this.RaisePropertyChanged("Id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionId {
            get {
                return this.RequisitionIdField;
            }
            set {
                if ((this.RequisitionIdField.Equals(value) != true)) {
                    this.RequisitionIdField = value;
                    this.RaisePropertyChanged("RequisitionId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionStatusTypeId {
            get {
                return this.RequisitionStatusTypeIdField;
            }
            set {
                if ((this.RequisitionStatusTypeIdField.Equals(value) != true)) {
                    this.RequisitionStatusTypeIdField = value;
                    this.RaisePropertyChanged("RequisitionStatusTypeId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SPRDetail", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class SPRDetail : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string AdditionalInformationField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string BudgetNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int DeliveryMethodTypeIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<decimal> EstimatedPriceField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string FileNameField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string FilePathField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string GeneralLedgerCodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ItemDescriptionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ParIdentifierField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string PartNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionItemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int SPRTypeIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> ShipToAddressIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string TradeInNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<decimal> TradeInValueField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string UOMCodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> VendorIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string AdditionalInformation {
            get {
                return this.AdditionalInformationField;
            }
            set {
                if ((object.ReferenceEquals(this.AdditionalInformationField, value) != true)) {
                    this.AdditionalInformationField = value;
                    this.RaisePropertyChanged("AdditionalInformation");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string BudgetNumber {
            get {
                return this.BudgetNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.BudgetNumberField, value) != true)) {
                    this.BudgetNumberField = value;
                    this.RaisePropertyChanged("BudgetNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int DeliveryMethodTypeId {
            get {
                return this.DeliveryMethodTypeIdField;
            }
            set {
                if ((this.DeliveryMethodTypeIdField.Equals(value) != true)) {
                    this.DeliveryMethodTypeIdField = value;
                    this.RaisePropertyChanged("DeliveryMethodTypeId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> EstimatedPrice {
            get {
                return this.EstimatedPriceField;
            }
            set {
                if ((this.EstimatedPriceField.Equals(value) != true)) {
                    this.EstimatedPriceField = value;
                    this.RaisePropertyChanged("EstimatedPrice");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FileName {
            get {
                return this.FileNameField;
            }
            set {
                if ((object.ReferenceEquals(this.FileNameField, value) != true)) {
                    this.FileNameField = value;
                    this.RaisePropertyChanged("FileName");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string FilePath {
            get {
                return this.FilePathField;
            }
            set {
                if ((object.ReferenceEquals(this.FilePathField, value) != true)) {
                    this.FilePathField = value;
                    this.RaisePropertyChanged("FilePath");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string GeneralLedgerCode {
            get {
                return this.GeneralLedgerCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.GeneralLedgerCodeField, value) != true)) {
                    this.GeneralLedgerCodeField = value;
                    this.RaisePropertyChanged("GeneralLedgerCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ItemDescription {
            get {
                return this.ItemDescriptionField;
            }
            set {
                if ((object.ReferenceEquals(this.ItemDescriptionField, value) != true)) {
                    this.ItemDescriptionField = value;
                    this.RaisePropertyChanged("ItemDescription");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string ParIdentifier {
            get {
                return this.ParIdentifierField;
            }
            set {
                if ((object.ReferenceEquals(this.ParIdentifierField, value) != true)) {
                    this.ParIdentifierField = value;
                    this.RaisePropertyChanged("ParIdentifier");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PartNumber {
            get {
                return this.PartNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.PartNumberField, value) != true)) {
                    this.PartNumberField = value;
                    this.RaisePropertyChanged("PartNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionItemId {
            get {
                return this.RequisitionItemIdField;
            }
            set {
                if ((this.RequisitionItemIdField.Equals(value) != true)) {
                    this.RequisitionItemIdField = value;
                    this.RaisePropertyChanged("RequisitionItemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int SPRTypeId {
            get {
                return this.SPRTypeIdField;
            }
            set {
                if ((this.SPRTypeIdField.Equals(value) != true)) {
                    this.SPRTypeIdField = value;
                    this.RaisePropertyChanged("SPRTypeId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> ShipToAddressId {
            get {
                return this.ShipToAddressIdField;
            }
            set {
                if ((this.ShipToAddressIdField.Equals(value) != true)) {
                    this.ShipToAddressIdField = value;
                    this.RaisePropertyChanged("ShipToAddressId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TradeInNumber {
            get {
                return this.TradeInNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.TradeInNumberField, value) != true)) {
                    this.TradeInNumberField = value;
                    this.RaisePropertyChanged("TradeInNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> TradeInValue {
            get {
                return this.TradeInValueField;
            }
            set {
                if ((this.TradeInValueField.Equals(value) != true)) {
                    this.TradeInValueField = value;
                    this.RaisePropertyChanged("TradeInValue");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string UOMCode {
            get {
                return this.UOMCodeField;
            }
            set {
                if ((object.ReferenceEquals(this.UOMCodeField, value) != true)) {
                    this.UOMCodeField = value;
                    this.RaisePropertyChanged("UOMCode");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> VendorId {
            get {
                return this.VendorIdField;
            }
            set {
                if ((this.VendorIdField.Equals(value) != true)) {
                    this.VendorIdField = value;
                    this.RaisePropertyChanged("VendorId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="ClinicalUseDetail", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class ClinicalUseDetail : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LotNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string PatientIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<System.DateTime> ProcedureDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string ProviderField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionItemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string SerialNumberField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<decimal> UpchargeCostField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id {
            get {
                return this.IdField;
            }
            set {
                if ((this.IdField.Equals(value) != true)) {
                    this.IdField = value;
                    this.RaisePropertyChanged("Id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string LotNumber {
            get {
                return this.LotNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.LotNumberField, value) != true)) {
                    this.LotNumberField = value;
                    this.RaisePropertyChanged("LotNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PatientId {
            get {
                return this.PatientIdField;
            }
            set {
                if ((object.ReferenceEquals(this.PatientIdField, value) != true)) {
                    this.PatientIdField = value;
                    this.RaisePropertyChanged("PatientId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> ProcedureDate {
            get {
                return this.ProcedureDateField;
            }
            set {
                if ((this.ProcedureDateField.Equals(value) != true)) {
                    this.ProcedureDateField = value;
                    this.RaisePropertyChanged("ProcedureDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Provider {
            get {
                return this.ProviderField;
            }
            set {
                if ((object.ReferenceEquals(this.ProviderField, value) != true)) {
                    this.ProviderField = value;
                    this.RaisePropertyChanged("Provider");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionItemId {
            get {
                return this.RequisitionItemIdField;
            }
            set {
                if ((this.RequisitionItemIdField.Equals(value) != true)) {
                    this.RequisitionItemIdField = value;
                    this.RaisePropertyChanged("RequisitionItemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string SerialNumber {
            get {
                return this.SerialNumberField;
            }
            set {
                if ((object.ReferenceEquals(this.SerialNumberField, value) != true)) {
                    this.SerialNumberField = value;
                    this.RaisePropertyChanged("SerialNumber");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<decimal> UpchargeCost {
            get {
                return this.UpchargeCostField;
            }
            set {
                if ((this.UpchargeCostField.Equals(value) != true)) {
                    this.UpchargeCostField = value;
                    this.RaisePropertyChanged("UpchargeCost");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "4.0.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RequisitionItemStatusHistory", Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow")]
    [System.SerializableAttribute()]
    public partial class RequisitionItemStatusHistory : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CommentsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime CreateDateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CreatedByField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionItemIdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int RequisitionItemStatusTypeIdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Comments {
            get {
                return this.CommentsField;
            }
            set {
                if ((object.ReferenceEquals(this.CommentsField, value) != true)) {
                    this.CommentsField = value;
                    this.RaisePropertyChanged("Comments");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime CreateDate {
            get {
                return this.CreateDateField;
            }
            set {
                if ((this.CreateDateField.Equals(value) != true)) {
                    this.CreateDateField = value;
                    this.RaisePropertyChanged("CreateDate");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CreatedBy {
            get {
                return this.CreatedByField;
            }
            set {
                if ((object.ReferenceEquals(this.CreatedByField, value) != true)) {
                    this.CreatedByField = value;
                    this.RaisePropertyChanged("CreatedBy");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id {
            get {
                return this.IdField;
            }
            set {
                if ((this.IdField.Equals(value) != true)) {
                    this.IdField = value;
                    this.RaisePropertyChanged("Id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionItemId {
            get {
                return this.RequisitionItemIdField;
            }
            set {
                if ((this.RequisitionItemIdField.Equals(value) != true)) {
                    this.RequisitionItemIdField = value;
                    this.RaisePropertyChanged("RequisitionItemId");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int RequisitionItemStatusTypeId {
            get {
                return this.RequisitionItemStatusTypeIdField;
            }
            set {
                if ((this.RequisitionItemStatusTypeIdField.Equals(value) != true)) {
                    this.RequisitionItemStatusTypeIdField = value;
                    this.RaisePropertyChanged("RequisitionItemStatusTypeId");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.ServiceContractAttribute(Namespace="http://local-approvalworkflow.healthtrustpg.com/", ConfigurationName="RushWorkflowSvc.IRushApprovalWorkflow")]
    public interface IRushApprovalWorkflow {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushAp" +
            "proval", ReplyAction="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushAp" +
            "provalResponse")]
        RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalResponse StartRushApproval(RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalRequest request);
        
        // CODEGEN: Generating message contract since the operation has multiple return values.
        [System.ServiceModel.OperationContractAttribute(Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushAp" +
            "proval", ReplyAction="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/StartRushAp" +
            "provalResponse")]
        System.Threading.Tasks.Task<RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalResponse> StartRushApprovalAsync(RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalRequest request);
        
        [System.ServiceModel.OperationContractAttribute(IsOneWay=true, Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/ApproveRush" +
            "Requisition")]
        void ApproveRushRequisition(RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition);
        
        [System.ServiceModel.OperationContractAttribute(IsOneWay=true, Action="http://local-approvalworkflow.healthtrustpg.com/IRushApprovalWorkflow/ApproveRush" +
            "Requisition")]
        System.Threading.Tasks.Task ApproveRushRequisitionAsync(RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition);
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class StartRushApprovalRequest {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow", Order=0)]
        public RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition;
        
        public StartRushApprovalRequest() {
        }
        
        public StartRushApprovalRequest(RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition) {
            this.Requisition = Requisition;
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.MessageContractAttribute(IsWrapped=false)]
    public partial class StartRushApprovalResponse {
        
        [System.ServiceModel.MessageBodyMemberAttribute(Namespace="http://schemas.datacontract.org/2004/07/ApprovalWorkflow", Order=0)]
        public RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition;
        
        public StartRushApprovalResponse() {
        }
        
        public StartRushApprovalResponse(RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition) {
            this.Requisition = Requisition;
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public interface IRushApprovalWorkflowChannel : RequisitionServices.DomainServices.RushWorkflowSvc.IRushApprovalWorkflow, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public partial class RushApprovalWorkflowClient : System.ServiceModel.ClientBase<RequisitionServices.DomainServices.RushWorkflowSvc.IRushApprovalWorkflow>, RequisitionServices.DomainServices.RushWorkflowSvc.IRushApprovalWorkflow {
        
        public RushApprovalWorkflowClient() {
        }
        
        public RushApprovalWorkflowClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public RushApprovalWorkflowClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RushApprovalWorkflowClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public RushApprovalWorkflowClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalResponse RequisitionServices.DomainServices.RushWorkflowSvc.IRushApprovalWorkflow.StartRushApproval(RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalRequest request) {
            return base.Channel.StartRushApproval(request);
        }
        
        public void StartRushApproval(ref RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition) {
            RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalRequest inValue = new RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalRequest();
            inValue.Requisition = Requisition;
            RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalResponse retVal = ((RequisitionServices.DomainServices.RushWorkflowSvc.IRushApprovalWorkflow)(this)).StartRushApproval(inValue);
            Requisition = retVal.Requisition;
        }
        
        public System.Threading.Tasks.Task<RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalResponse> StartRushApprovalAsync(RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalRequest request) {
            return base.Channel.StartRushApprovalAsync(request);
        }
        
        public void ApproveRushRequisition(RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition) {
            base.Channel.ApproveRushRequisition(Requisition);
        }
        
        public System.Threading.Tasks.Task ApproveRushRequisitionAsync(RequisitionServices.DomainServices.RushWorkflowSvc.Requisition Requisition) {
            return base.Channel.ApproveRushRequisitionAsync(Requisition);
        }
    }
}
