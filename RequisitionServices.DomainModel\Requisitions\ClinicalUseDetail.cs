﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class ClinicalUseDetail
    {
        public int Id { get; set; }

        public int RequisitionItemId { get; set; }
        [ForeignKey("RequisitionItemId")]
        public virtual RequisitionItem RequisitionItem { get; set; }
        [NotMapped]
        public bool HasLotNumberChanged { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [NotMapped]
        public bool HasSerialNumberChanged { get; set; }
        [StringLength(100)]
        public string SerialNumber { get; set; }

        [StringLength(200)]
        public string Provider { get; set; }

        [StringLength(50)]
        public string PatientId { get; set; }

        [StringLength(50)]
        public string PatientName { get; set; }

        public DateTime? ProcedureDate { get; set; }

        public decimal? UpchargeCost { get; set; }
        [NotMapped]
        public ChangeStatus ChangeStatus { get; set; }

        public bool ProviderHasChanged { get; set; }

        public bool PatientIdHasChanged { get; set; }

        public bool PatientNameHasChanged { get; set; }

        public bool ProcedureDateHasChanged { get; set; }

    }
}
