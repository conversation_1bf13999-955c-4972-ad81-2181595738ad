﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class AdhocReview
    {
        public AdhocReview()
        {
        }

        public AdhocReview(AdhocReviewDTO adhocReview)
        {
            if (adhocReview  == null)
            {
                throw new ArgumentNullException("adhocReview");
            }

            this.Id = adhocReview.Id;
            this.RequisitionId = adhocReview.RequisitionId;
            this.RequesterRequisitionStatusHistoryId = adhocReview.RequesterRequisitionStatusHistoryId;
            this.ReviewerRequisitionStatusHistoryId = adhocReview.ReviewerRequisitionStatusHistoryId;
            this.Requester = adhocReview.Requester;
            this.RequesterComments = adhocReview.RequesterComments;
            this.CreateDate = adhocReview.CreateDate;
            this.Reviewer = adhocReview.Reviewer;
            this.ReviewerComments = adhocReview.ReviewerComments;
            this.Recommended = adhocReview.Recommended;
            this.ReviewDate = adhocReview.ReviewDate;
        }

        public int Id { get; set; }

        public int RequisitionId { get; set; }

        [ForeignKey("RequisitionId")]
        public virtual Requisition Requisition { get; set; }

        public int RequesterRequisitionStatusHistoryId { get; set; }

        [ForeignKey("RequesterRequisitionStatusHistoryId")]
        public virtual RequisitionStatusHistory RequesterRequisitionStatusHistory { get; set; }

        public string Requester { get; set; }

        [StringLength(255)]
        public string RequesterComments { get; set; }

        public DateTime CreateDate { get; set; }

        public int? ReviewerRequisitionStatusHistoryId { get; set; }

        [ForeignKey("ReviewerRequisitionStatusHistoryId")]
        public virtual RequisitionStatusHistory ReviewerRequisitionStatusHistory { get; set; }

        public string Reviewer { get; set; }

        [StringLength(255)]
        public string ReviewerComments { get; set; }

        public bool? Recommended { get; set; }

        public DateTime? ReviewDate { get; set; }
    }
}
