﻿using System;
using RequisitionServices.DomainModel.Items;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class VboHoldItemConversionDto
    {
        public VboHoldItemConversionDto() { }

        public VboHoldItemConversionDto(VboHoldItemConversion conversion)
        {
            RequisitionItemId = conversion.RequisitionItemId;
            SmartItemNumber = conversion.SmartItemNumber;
            UnitCost = conversion.UnitCost;
            ItemDetails = conversion.ItemDetails;
            CreatedBy = conversion.CreatedBy;
            CreateDate = conversion.CreateDate;
        }

        public int RequisitionItemId { get; set; }
        public int SmartItemNumber { get; set; }
        public decimal UnitCost { get; set; }
        public ParItem ItemDetails { get; set; }
        public string CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }
    }
}
