﻿using RequisitionServices.DomainModel.Items;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class ParItemDetailsModel
    {
        public ParItemDetails MapToParItemDetails()
        {
            return new ParItemDetails()
            {
               ParId= this.ParId,
               ParDept= this.ParDept,
               ItemNumber = this.ItemNumber,
               Min = this.Min,
               Max=this.Max,
               IUOM = this.IUOM,
               GLAccount = this.GLAccount,
               ChargableFlag = this.ChargableFlag,
               Location = this.Location,
               ItemType = this.ItemType,
               ConsignmentFlag = this.ConsignmentFlag,
               PARType = this.PARType,
               ParDescription = this.ParDescription,
               ItemDescription = this.ItemDescription,
               UnitPrice = this.UnitPrice,
               PurchVendorNumber = this.PurchVendorNumber,
               PurchVendorName = this.PurchVendorName,
               StockFlag = this.StockFlag,
               IsCapitated = this.IsCapitated,
               TempStock = this.TempStock,
               ComplianceCode = this.ComplianceCode,
               DistributionPoint = this.DistributionPoint,
               ReorderNumber = this.ReorderNumber,
               ContractNumber = this.ContractNumber,
               ProcedureCode = this.ProcedureCode,
               QuantityAvailable = this.QuantityAvailable,
               IGLAccount = this.IGLAccount,
               CatalogNum = this.CatalogNum,
               PUOM = this.PUOM,
               Factor = this.Factor
            };
        }

        public string ParId { get; set; }

        public int ParDept { get; set; }

        public int ItemNumber { get; set; }

        public int Min { get; set; }

        public int Max { get; set; }

        public string IUOM { get; set; }

        public long GLAccount { get; set; }

        public bool ChargableFlag { get; set; }

        public string Location { get; set; }

        public string ItemType { get; set; }

        public bool ConsignmentFlag { get; set; }

        public int PARType { get; set; }

        public string ParDescription { get; set; }

        public string ItemDescription { get; set; }

        public decimal UnitPrice { get; set; }

        public int PurchVendorNumber { get; set; }

        public string PurchVendorName { get; set; }

        public bool StockFlag { get; set; }

        public bool IsCapitated { get; set; }

        public bool TempStock { get; set; }

        public string ComplianceCode { get; set; }

        public string DistributionPoint { get; set; }

        public string ReorderNumber { get; set; }

        public int ContractNumber { get; set; }

        public string ProcedureCode { get; set; }

        public int QuantityAvailable { get; set; }

        public long IGLAccount { get; set; }

        public string CatalogNum { get; set; }

        public string PUOM { get; set; }

        public int Factor { get; set; }
        public string FactorDisplay { get; set; }


        public List<ParItem> CreatePARItemDetailsModel(List<ParItemDetails> parItemDetails)
        {
            return parItemDetails.ConvertAll(x => new ParItem
            {
                ParId = x.ParId,
                ItemId = x.ItemNumber,
                MinStock = x.Min,
                MaxStock = x.Max, 
                ParPrice = x.UnitPrice,
                IssueUOM = x.IUOM,
                Item = new Item
                {
                    Id = x.ItemNumber.ToString(),
                    Name = x.ItemDescription,
                    Description = x.ItemDescription,
                    Vendor = new DomainModel.Vendors.Vendor
                    {
                        Id = x.PurchVendorNumber,
                        Name = x.PurchVendorName
                    },
                    ReorderNumber = x.ReorderNumber,
                    Location = x.Location,
                    UOM = x.IUOM,
                    IUOM = x.IUOM,
                    PUOM = x.PUOM,
                    Factor = x.Factor,
                    ManufacturerCatalogNumber = x.CatalogNum,
                    Price = x.UnitPrice,
                    ProcCode = x.ProcedureCode,
                    IsStock = (x.StockFlag || string.Compare(x.DistributionPoint, "F", true) == 0),
                    IsTempStock = x.TempStock,
                    IsCapitated = x.IsCapitated,
                    QuantityAvailable = x.QuantityAvailable,
                    ContractNumber = x.ContractNumber,
                    ComplianceCode = x.ComplianceCode,
                    IsValidItem = true,
                    ParId = x.ParId,
                    DistributionPoint = x.DistributionPoint,
                    Chargeable = x.ChargableFlag
                },
                ParDescription = x.ParDescription,
                ParType = x.PARType,
                GLAccount = x.GLAccount,
                IGLAccount = x.IGLAccount,
                Location = x.Location
            });

        }
    }
}
