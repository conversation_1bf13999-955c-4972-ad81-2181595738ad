﻿using System;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Email
{
    public class EmailRequest
    {
        //Not all these variables are used -- depends on email/request type
        public string UserName { get; set; }
        public EmailType EmailType { get; set; }
        public string [] Emails { get; set; }
        public string ViewLink { get; set; }
        public string SenderName { get; set; }
        public Requisition Requisition { get; set; }
        public DateTime ActionDate { get; set; }
        public string Vendors { get; set; }
        public string RushStockItems { get; set; }
        public string RushNonStockItems { get; set; }
        public string Comments { get; set; }
        public bool Recommendation { get; set; }
        public bool IsApprovalAmountMet { get; set; }
        public bool HighPriority { get; set; }
        public bool HasStockAndNonStockRushItems { get; set; }
        public string StockDeliveryMethod { get; set; }
        public string NonStockDeliveryMethod { get; set; }

    }
}
