USE [eProcurementQA]
GO

/****** Object:  View [dbo].[edwRequisitions]    Script Date: 4/11/2024 5:55:54 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

ALTER VIEW [dbo].[edwRequisitions]
AS
     SELECT R.[RequisitionId], 
            R.[RequisitionStatusTypeId], 
            R.[RequisitionTypeId], 
            SUBSTRING(R.[LocationIdentifier], 0, (CHARINDEX('_', R.[LocationIdentifier]))) AS 'COID', 
            SUBSTRING(R.[LocationIdentifier], (CHARINDEX('_', R.[LocationIdentifier])+1), (LEN(R.[LocationIdentifier])-CHARINDEX('_', R.[LocationIdentifier]))) AS 'DepartmentId', 
            SUBSTRING(R.[CreatedBy], 0, (CHARINDEX('/', R.[CreatedBy]))) AS 'Domain', 
            SUBSTRING(R.[CreatedBy], (CHARINDEX('/', R.[CreatedBy])+1), (LEN(R.[CreatedBy])-CHARINDEX('_', R.[CreatedBy]))) AS 'UserName', 
            R.[CreateDate], 
            R.[Comments], 
            R.[ApprovalStep], 
            R.[ApprovedAmount], 
            R.[WorkflowInstanceId], 
            R.[RequisitionParClass], 
            R.[CountryCode], 
            R.[IsMobile], 
			R.[IsVendor],
			R.[RequisitionSubmissionTypeId],
            RSH.LastUpdated
     FROM [dbo].[Requisitions] AS R
          JOIN
     (
         SELECT H.RequisitionId, 
                MAX(H.[CreateDate]) AS LastUpdated
         FROM [dbo].[RequisitionStatusHistories] H
         GROUP BY H.RequisitionId
     ) AS RSH ON RSH.RequisitionId = R.RequisitionId;
GO


