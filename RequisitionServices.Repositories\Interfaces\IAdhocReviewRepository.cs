﻿using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IAdhocReviewRepository
    {
        void InsertAdhocReview(AdhocReview adhocReview);

        IEnumerable<AdhocReview> GetRequisitionAdhocReviews(int requisitionId);

        bool IsAdhocReviewAllowed(int adhocReviewId, string reviewerName, int reqId);

        void UpdateAdhocReview(AdhocReview entity);

        string GetRequester(int adhocReviewId);
    }
}
