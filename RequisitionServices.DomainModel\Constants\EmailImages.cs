﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace RequisitionServices.DomainModel.Constants
{
    public static class EmailImages
    {
        public static string CurrentDirectory = GetPath("EmailTemplates/Images");

        public static Dictionary<string, string> CommonImages = new Dictionary<string, string>()
        {
            { "SMARTLogo", CurrentDirectory + "/SMARTProcurement.png" },
            { "HealthTrustLogo", CurrentDirectory + "/HealthTrustLogo.png" },
            { "Space", CurrentDirectory + "/Space.gif" },
            { "Top", CurrentDirectory + "/Top.gif" },
            { "Divider", CurrentDirectory + "/Divider.gif" },
            { "Bottom", CurrentDirectory + "/Bottom.gif" }
        };

        public static Dictionary<string, string> GetImageDictionary(string companyName)
        {
            var imageDictionary = new Dictionary<string, string>(CommonImages);

            if (companyName == null)
            {
                imageDictionary.Add("CompanyLogo", CurrentDirectory + "/NoLogo.png");
            }
            else
            {
                var company = companyName.ToUpper();

                if (company == Company.HCA)
                {
                    imageDictionary.Add("CompanyLogo", CurrentDirectory + "/HCALogo.png");
                }
                else if (company == Company.HCAUK)
                {
                    imageDictionary.Add("CompanyLogo", CurrentDirectory + "/HCAUK_HealthcareLogo.png");
                }
                else if (company.IndexOf(Company.Lifepoint) != -1)
                {
                    imageDictionary.Add("CompanyLogo", CurrentDirectory + "/LifepointHealthLogo.jpg");
                }
                else
                {
                    imageDictionary.Add("CompanyLogo", CurrentDirectory + "/NoLogo.png");
                }
            }

            return imageDictionary;

        }
        private static string GetPath(string template) 
        {
            string codeBase = Assembly.GetExecutingAssembly().CodeBase;

            UriBuilder uri = new UriBuilder(codeBase);

            string path = Uri.UnescapeDataString(uri.Path);

            return Path.Combine(Path.GetDirectoryName(path), template).Replace("bin\\EmailTemplates", "EmailTemplates");
        }
    }
}
