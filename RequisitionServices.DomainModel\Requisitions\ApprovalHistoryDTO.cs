﻿using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    [NotMapped]
    public class ApprovalHistoryDTO
    {
        public ApprovalHistoryDTO(Requisition RequisitionDTO, AdhocReview AdhocReviewDTO, RequisitionStatusHistory HistoryItem, bool PendingReviewsExist)
        {
            this.RequisitionDTO = new RequisitionDTO(RequisitionDTO);
            this.AdhocReviewDTO = new AdhocReviewDTO(AdhocReviewDTO);
            this.HistoryItem = new RequisitionStatusHistoryDTO(HistoryItem);
            this.PendingReviewsExist = PendingReviewsExist;
        }
        public RequisitionDTO RequisitionDTO { get; set; }
        public AdhocReviewDTO AdhocReviewDTO { get; set; }
        public RequisitionStatusHistoryDTO HistoryItem { get; set; }
        public bool PendingReviewsExist { get; set; }
    }
}
