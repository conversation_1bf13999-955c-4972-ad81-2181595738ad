﻿using Newtonsoft.Json;
using Newtonsoft.Json.Schema;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class SprRequisitionItemStatusDTO : BaseRequisitionItemStatusDTO
    {
        /// <summary>
        /// Vendor Id
        /// </summary>
        [JsonProperty("VendorId", Required = Required.Always)]
        public int VendorId { get; set; }

        /// <summary>
        /// Part Number
        /// </summary>
        [JsonProperty("PartNumber", Required = Required.Always)]
        public string PartNumber { get; set; }

        /// <summary>
        /// Reject Code
        /// </summary>
        [JsonProperty("RejectCode", Required = Required.AllowNull)]
        public string RejectCode { get; set; }

        /// <summary>
        /// Reject Comments
        /// </summary>
        [JsonProperty("RejectComments", Required = Required.AllowNull)]
        public string RejectComments { get; set; }

        public override RequisitionItemStatusTypeEnum GetRequisitionItemStatusType()
        {
            RequisitionItemStatusTypeEnum itemStatusType = RequisitionItemStatusTypeEnum.Unknown;

            switch (this.RequisitionItemStatusTypeId.ToLower())
            {
                case "0":
                    itemStatusType = RequisitionItemStatusTypeEnum.Processing;
                    break;
                case "1":
                    itemStatusType  = RequisitionItemStatusTypeEnum.POInProcess;
                    break;
                case "2":
                    itemStatusType = RequisitionItemStatusTypeEnum.POCreated;
                    break;
                case "9":
                    itemStatusType = RequisitionItemStatusTypeEnum.Rejected;
                    break;
                default:
                    itemStatusType = RequisitionItemStatusTypeEnum.Unknown;
                    break;
            }

            return itemStatusType;
        }

        public override void UpdateRequisitionItemStatus(RequisitionItem reqItem)
        {
            RequisitionItemStatusTypeEnum itemStatusType = this.GetRequisitionItemStatusType();
            reqItem.RequisitionItemStatusTypeId = (int)itemStatusType;

            switch (itemStatusType)
            {
                case RequisitionItemStatusTypeEnum.Rejected:
                    reqItem.SPRDetail.RejectCode = this.RejectCode;
                    reqItem.SPRDetail.RejectionComments = this.RejectComments;
                    break;
            }

            SetupCommonItems(reqItem);        
        }

        public static JSchema GetSprSchema()
        {
            return GenerateSchemaForClass(typeof(SprRequisitionItemStatusDTO));
        }
    }
}
