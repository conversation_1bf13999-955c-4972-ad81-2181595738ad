﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Users
{
    public class UserWorkflowStep
    {
        private string _createdBy;
        
        public int Id { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public User User { get; set; }

        public string COID { get; set; }

        public int Step { get; set; }

        public int ApproverId { get; set; }
        [ForeignKey("ApproverId")]
        public Approver Approver { get; set; }
        
        public bool IsFinalStep { get; set; }
                
        public bool IsFinalRushStep { get; set; }

        public int? DelegatedByUserId { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy 
        {
            get => _createdBy;
            set => _createdBy = value.ToLower();
        }
        public DateTime CreateDate { get; set; }

        public int WorkflowTypeId { get; set; }
        [ForeignKey("WorkflowTypeId")]
        public virtual WorkflowType WorkflowType { get; set; }

        public UserWorkflowStep CreateDeepCopy(UserWorkflowStep workflow)
        {
            UserWorkflowStep copy = (UserWorkflowStep)MemberwiseClone();
            copy.Approver = workflow.Approver;
            copy.ApproverId = workflow.ApproverId;
            copy.Step = workflow.Step;
            copy.User = workflow.User;
            copy.UserId = workflow.UserId;
            copy.WorkflowType = workflow.WorkflowType;
            copy.WorkflowTypeId = workflow.WorkflowTypeId;
            copy.CreateDate = workflow.CreateDate;
            copy.CreatedBy = workflow.CreatedBy;
            copy.DelegatedByUserId = workflow.DelegatedByUserId;
            copy.Id = workflow.Id;
            copy.IsFinalRushStep = workflow.IsFinalRushStep;
            copy.IsFinalStep = workflow.IsFinalStep;
            return copy;
        }
        
    }
}
