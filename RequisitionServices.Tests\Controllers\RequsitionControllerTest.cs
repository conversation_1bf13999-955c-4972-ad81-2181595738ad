﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.Controllers;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Tests.Helper;
using RequisitionServices.Utility.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;

namespace RequisitionServices.Tests.Controllers
{
    [TestClass]
    public class RequsitionControllerTest
    {
        static Mock<IRequisitionService> _mockRequisitionService;
        static Mock<IWorkflowService> _mockWorkflowService;
        static Mock<IConfigurationService> _mockConfigurationService;
        static Mock<ICensorService> _mockCensorService;
        static RequisitionController _mockRequisitionController;
        static Mock<ISmartVendorService> _mockSmartVendorService;

        const string Dept = "611";
        const string ParIdentifier = "R40";

        static readonly string _userName = TestData.TestUser1.Username;
        static readonly string _coid = TestData.TestUser1.PrimaryCOID;
        static readonly string _locationIdentifier = $"{_coid}_{Dept}";

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {   
            _mockRequisitionService = new Mock<IRequisitionService>();
            _mockWorkflowService = new Mock<IWorkflowService>();
            _mockConfigurationService = new Mock<IConfigurationService>();
            _mockSmartVendorService = new Mock<ISmartVendorService>();
            _mockCensorService = new Mock<ICensorService>();
            _mockRequisitionController = new RequisitionController(_mockRequisitionService.Object, _mockWorkflowService.Object, _mockConfigurationService.Object, _mockCensorService.Object);
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            _mockRequisitionController = null;
            _mockWorkflowService = null;
            _mockRequisitionService = null;
        }

        static Requisition GetRequisition(List<RequisitionItem> requisitionItems)
        {
            var requisition = new Requisition
            {
                RequisitionStatusTypeId = 1,
                LocationIdentifier = _locationIdentifier,
                Comments = "Comments Test",
                CreatedBy = _userName,
                CreateDate = DateTime.Now,
                RequisitionItems = requisitionItems
            };

            return requisition;
        }

        static List<RequisitionItem> GetRequisitionItems()
        {
            return
            new List<RequisitionItem>
            {
                    new RequisitionItem { ItemId = "1234", ParIdentifier = ParIdentifier, RequisitionItemStatusTypeId = 1, QuantityToOrder = 3, IsRushOrder = false, CreateDate = DateTime.Now, CreatedBy = _userName, ReOrder="123",PARLocation="LAB",ItemDescription="Test Item1",VendorId=900700,VendorName="Cardinal",GeneralLedgerCode="10000",StockIndicator=true,UOMCode="EA",UnitCost=10,TotalCost=20,MinStock=1,MaxStock=10},
                    new RequisitionItem { ItemId = "2234", ParIdentifier = ParIdentifier, RequisitionItemStatusTypeId = 1, QuantityToOrder = 2, IsRushOrder = false, CreateDate = DateTime.Now, CreatedBy = _userName,ReOrder="456",PARLocation="DESK",ItemDescription="Test Item2",VendorId=900700,VendorName="Cardinal",GeneralLedgerCode="10000",StockIndicator=false,UOMCode="PK",UnitCost=10,TotalCost=20,MinStock=1,MaxStock=10},
                    new RequisitionItem { ItemId = "3234", ParIdentifier = ParIdentifier, RequisitionItemStatusTypeId = 1, QuantityToOrder = 1, IsRushOrder = false, CreateDate = DateTime.Now, CreatedBy = _userName,ReOrder="789",PARLocation="DRAWER",ItemDescription="Test Item3",VendorId=900700,VendorName="Cardinal",GeneralLedgerCode="10000",StockIndicator=true,UOMCode="BX",UnitCost=10,TotalCost=20,MinStock=1,MaxStock=10}
            };
        }

        [TestMethod]
        [TestCategory("IntegrationTests")]
        public void TestSaveRequisition()
        {   
            var requisition = GetRequisition(GetRequisitionItems());
            var requisitionDto = new RequisitionDTO(requisition);
            //Insert 
            var submittdReq = RequisitionControllerHelper.SaveRequisition(requisitionDto, TestData.TestUser1.Username);

            Assert.IsNotNull(submittdReq);
            Assert.IsTrue(submittdReq.RequisitionId > 0);
            
            //Update
            submittdReq.LocationIdentifier = _locationIdentifier;

            var updatedReq = RequisitionControllerHelper.SaveRequisition(submittdReq, TestData.TestUser1.Username);

            Assert.IsNotNull(updatedReq);
            Assert.IsTrue(updatedReq.RequisitionId > 0);

            var lookupReq = RequisitionControllerHelper.GetRequisition(updatedReq.RequisitionId, TestData.TestUser1.Username);

            Assert.IsNotNull(lookupReq);
        }

        [TestMethod]
        public void TestReqUpdateRequisitionItemStatusSuccess()
        {
            _mockRequisitionService.Setup(x => x.UpdateReqRequisitionItemStatus(It.IsAny<ReqRequisitionItemStatusDTO>())).Returns(new UpdateRequisitionItemStatusResultDTO());
            var messageId = Guid.NewGuid().ToString();
            
            var reqMessage = $@"{{
               'MessageId': '{messageId}',
               'COID': '{_coid}',
               'Dept': '{Dept}',
               'ParIdentifier': '{ParIdentifier}',
               'ParentSystemId': '16076825',
               'OriginalParentSystemId': '0',
               'ItemId': '817352',
               'Id': 2,
               'QuantityToOrder': 1,
               'QuantityFulfilled': 1,
               'RequisitionItemStatusTypeId': 's',
               'RequisitionScheduledDate': '2016-12-30T10:00:00.000Z',
               'PONumber': null,
               'Sequence': 1,
               'CreateDateTime': '2016-12-12T19:15:25.010Z',
               'UTCOffset': -6
            }}";

            _mockRequisitionController.Request = new HttpRequestMessage();
            _mockRequisitionController.Request.Content = new StringContent(reqMessage);
            var response = _mockRequisitionController.UpdateRequisitionItemStatus();
            Assert.AreEqual(HttpStatusCode.OK, response.StatusCode, "Status should be the same");
            _mockRequisitionService.Verify(x => x.UpdateReqRequisitionItemStatus(It.IsAny<ReqRequisitionItemStatusDTO>()), "Expecting on receiving a REQ message, Req status should be updated.");
        }

        [TestMethod]
        public void TestSprUpdateRequisitionItemStatusSuccess()
        {
            _mockRequisitionService.Setup(x => x.UpdateSprRequisitionItemStatus(It.IsAny<SprRequisitionItemStatusDTO>())).Returns(new UpdateRequisitionItemStatusResultDTO());
            var messageId = Guid.NewGuid().ToString();
            var sprMessage = $@"{{
               'MessageId': '{messageId}',
               'COID': '{_coid}',
               'Dept': '{Dept}',
               'ParentSystemId': 'EPS16G000006',
               'VendorId': 929968,
               'PartNumber': 'GB90X',
               'Id': null,
               'QuantityToOrder': 1,   
               'RequisitionItemStatusTypeId': '1',   
               'RejectCode': null,
               'RejectComments': null,
               'PONumber': null,   
               'CreateDateTime': '2016-12-15T09:40:30.700Z',
               'UTCOffset': -6  
            }}";
            _mockRequisitionController.Request = new HttpRequestMessage();
            _mockRequisitionController.Request.Content = new StringContent(sprMessage);
            var response = _mockRequisitionController.UpdateRequisitionItemStatus();
            Assert.AreEqual(HttpStatusCode.OK, response.StatusCode, "Status should be the same");
            _mockRequisitionService.Verify(x => x.UpdateSprRequisitionItemStatus(It.IsAny<SprRequisitionItemStatusDTO>()), "Expecting on receiving an SPR message, Spr status should be updated.");
        }

        [TestMethod]
        [TestCategory("IntegrationTests")]
        [Ignore] // TOOD: needs to be moved to intergration tests under QA control: https://rally1.rallydev.com/#/27210487384ud/detail/userstory/7f6b0fdd-9e7d-44b7-804f-9413ad7ec7f6
        public void TestReqMessageSaveSubmitAndReceiveMessage()
        {            
            IEnumerable<ParItem> items = ParControllerHelper.GetParItems(TestData.TestUser1.Username, _coid, Dept, ParIdentifier);
            ParItem firstItem = items.First();
            var requisitionItem = new RequisitionItem { ItemId = firstItem.ItemId.ToString(), ParIdentifier = ParIdentifier, RequisitionItemStatusTypeId = 1, QuantityToOrder = 1, IsRushOrder = false, CreateDate = DateTime.Now, CreatedBy = _userName };
            var parRequisition = GetRequisition(new List<RequisitionItem> { requisitionItem });
            var parRequisitionDto = new RequisitionDTO(parRequisition);            
            var savedRequisition = RequisitionControllerHelper.SaveRequisition(parRequisitionDto, TestData.TestUser1.Username);            
            var submittedRequisition = RequisitionControllerHelper.SubmitApproversRequisition(savedRequisition, TestData.TestUser1.Username);

            Assert.IsTrue(submittedRequisition.RequisitionItems[0].ParentSystemId.Length > 0, "Submitted Requisition should have a SMART requisition number");
            Assert.AreEqual(RequisitionItemStatusTypeEnum.Processing, (RequisitionItemStatusTypeEnum)submittedRequisition.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be the same");

            var reqMessage = new ReqRequisitionItemStatusDTO
            {
                MessageId = Guid.NewGuid().ToString(),
                COID = LocationMapper.GetCOID(submittedRequisition.LocationIdentifier),
                Dept = LocationMapper.GetDepartmentId(submittedRequisition.LocationIdentifier),
                ParIdentifier = submittedRequisition.RequisitionItems[0].ParIdentifier,
                ParentSystemId = submittedRequisition.RequisitionItems[0].ParentSystemId,
                OriginalParentSystemId = null,
                ItemId = submittedRequisition.RequisitionItems[0].ItemId,
                Id = submittedRequisition.RequisitionItems[0].Id,
                QuantityToOrder = submittedRequisition.RequisitionItems[0].QuantityToOrder,
                QuantityFulfilled = submittedRequisition.RequisitionItems[0].QuantityFulfilled.HasValue ? submittedRequisition.RequisitionItems[0].QuantityFulfilled.Value : 0,
                RequisitionItemStatusTypeId = "i", // PO In Process
                RequisitionScheduledDate = null,
                PONumber = null,
                Sequence  = 1,
                CreateDateTime = DateTime.Now,
                UTCOffset = -6
            };
            
            RequisitionControllerHelper.UpdateRequisitionItemStatus(reqMessage);
            
            var retrievedAgain = RequisitionControllerHelper.GetRequisition(submittedRequisition.RequisitionId, TestData.TestUser1.Username);

            Assert.AreEqual(RequisitionItemStatusTypeEnum.POInProcess, (RequisitionItemStatusTypeEnum)retrievedAgain.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be updated now");
        }

        [TestMethod]
        [TestCategory("IntegrationTests")]
        [Ignore] // TOOD: needs to be moved to intergration tests under QA control: https://rally1.rallydev.com/#/27210487384ud/detail/userstory/7f6b0fdd-9e7d-44b7-804f-9413ad7ec7f6
        public void TestAddedToPreviousStatus()
        {
            IEnumerable<ParItem> items = ParControllerHelper.GetParItems(TestData.TestUser1.Username, _coid, Dept, ParIdentifier);
            ParItem firstItem = items.First();
            var requisitionItem = new RequisitionItem { ItemId = firstItem.ItemId.ToString(), ParIdentifier = ParIdentifier, RequisitionItemStatusTypeId = 1, QuantityToOrder = 1, IsRushOrder = false, CreateDate = DateTime.Now, CreatedBy = _userName };
            var parRequisition = GetRequisition(new List<RequisitionItem> { requisitionItem });
            var parRequisitionDto = new RequisitionDTO(parRequisition);
            var savedRequisition = RequisitionControllerHelper.SaveRequisition(parRequisitionDto, TestData.TestUser1.Username);
            var submittedRequisition1 = RequisitionControllerHelper.SubmitApproversRequisition(savedRequisition, TestData.TestUser1.Username);

            Assert.IsTrue(submittedRequisition1.RequisitionItems[0].ParentSystemId.Length > 0, "Submitted Requisition should have a SMART requisition number");
            Assert.AreEqual(RequisitionItemStatusTypeEnum.Processing, (RequisitionItemStatusTypeEnum)submittedRequisition1.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be the same");

            var reqMessage = new ReqRequisitionItemStatusDTO
            {
                MessageId = Guid.NewGuid().ToString(),
                COID = LocationMapper.GetCOID(submittedRequisition1.LocationIdentifier),
                Dept = LocationMapper.GetDepartmentId(submittedRequisition1.LocationIdentifier),
                ParIdentifier = submittedRequisition1.RequisitionItems[0].ParIdentifier,
                ParentSystemId = submittedRequisition1.RequisitionItems[0].ParentSystemId,
                OriginalParentSystemId = null,
                ItemId = submittedRequisition1.RequisitionItems[0].ItemId,
                Id = submittedRequisition1.RequisitionItems[0].Id,
                QuantityToOrder = submittedRequisition1.RequisitionItems[0].QuantityToOrder,
                QuantityFulfilled = submittedRequisition1.RequisitionItems[0].QuantityFulfilled.HasValue ? submittedRequisition1.RequisitionItems[0].QuantityFulfilled.Value : 0,
                RequisitionItemStatusTypeId = "c", // PO Created
                RequisitionScheduledDate = null,
                PONumber = null,
                Sequence = 1,
                CreateDateTime = DateTime.Now,
                UTCOffset = -6
            };

            RequisitionControllerHelper.UpdateRequisitionItemStatus(reqMessage);

            var retrievedAgain = RequisitionControllerHelper.GetRequisition(submittedRequisition1.RequisitionId, TestData.TestUser1.Username);

            Assert.AreEqual(RequisitionItemStatusTypeEnum.Scheduled, (RequisitionItemStatusTypeEnum)retrievedAgain.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be updated now");

            var parRequisitionDto2 = new RequisitionDTO(GetRequisition(new List<RequisitionItem> { requisitionItem }));
            var savedRequisition2 = RequisitionControllerHelper.SaveRequisition(parRequisitionDto2, TestData.TestUser1.Username);
            var submittedRequisition2 = RequisitionControllerHelper.SubmitApproversRequisition(savedRequisition2, TestData.TestUser1.Username);

            Assert.IsTrue(submittedRequisition2.RequisitionItems[0].ParentSystemId.Length > 0, "Submitted Requisition should have a SMART requisition number");
            Assert.AreEqual(RequisitionItemStatusTypeEnum.Processing, (RequisitionItemStatusTypeEnum)submittedRequisition2.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be the same");

            var reqMessage2 = new ReqRequisitionItemStatusDTO
            {
                MessageId = Guid.NewGuid().ToString(),
                COID = LocationMapper.GetCOID(submittedRequisition2.LocationIdentifier),
                Dept = LocationMapper.GetDepartmentId(submittedRequisition2.LocationIdentifier),
                ParIdentifier = submittedRequisition2.RequisitionItems[0].ParIdentifier,
                ParentSystemId = submittedRequisition2.RequisitionItems[0].ParentSystemId,
                OriginalParentSystemId = null,
                ItemId = submittedRequisition2.RequisitionItems[0].ItemId,
                Id = submittedRequisition2.RequisitionItems[0].Id,
                QuantityToOrder = submittedRequisition2.RequisitionItems[0].QuantityToOrder,
                QuantityFulfilled = submittedRequisition2.RequisitionItems[0].QuantityFulfilled.HasValue ? submittedRequisition2.RequisitionItems[0].QuantityFulfilled.Value : 0,
                RequisitionItemStatusTypeId = "i", // PO Created
                RequisitionScheduledDate = null,
                PONumber = null,
                Sequence = 1,
                CreateDateTime = DateTime.Now,
                UTCOffset = -6
            };

            RequisitionControllerHelper.UpdateRequisitionItemStatus(reqMessage2);

            var retrievedAgain2 = RequisitionControllerHelper.GetRequisition(submittedRequisition2.RequisitionId, TestData.TestUser1.Username);

            Assert.AreEqual(RequisitionItemStatusTypeEnum.POInProcess, (RequisitionItemStatusTypeEnum)retrievedAgain2.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be updated now");

        }

        [TestMethod]
        [TestCategory("IntegrationTests")]
        [Ignore] // TOOD: needs to be moved to intergration tests under QA control: https://rally1.rallydev.com/#/27210487384ud/detail/userstory/7f6b0fdd-9e7d-44b7-804f-9413ad7ec7f6
        public void TestRequisitionScheduledStatus()
        {
            IEnumerable<ParItem> items = ParControllerHelper.GetParItems(TestData.TestUser1.Username, _coid, Dept, ParIdentifier);
            ParItem firstItem = items.First();
            var requisitionItem = new RequisitionItem { ItemId = firstItem.ItemId.ToString(), ParIdentifier = ParIdentifier, RequisitionItemStatusTypeId = 1, QuantityToOrder = 1, IsRushOrder = false, CreateDate = DateTime.Now, CreatedBy = _userName };
            var parRequisition = GetRequisition(new List<RequisitionItem> { requisitionItem });
            var parRequisitionDto = new RequisitionDTO(parRequisition);
            var savedRequisition = RequisitionControllerHelper.SaveRequisition(parRequisitionDto, TestData.TestUser1.Username);
            var submittedRequisition1 = RequisitionControllerHelper.SubmitApproversRequisition(savedRequisition, TestData.TestUser1.Username);

            Assert.IsTrue(submittedRequisition1.RequisitionItems[0].ParentSystemId.Length > 0, "Submitted Requisition should have a SMART requisition number");
            Assert.AreEqual(RequisitionItemStatusTypeEnum.Processing, (RequisitionItemStatusTypeEnum)submittedRequisition1.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be the same");

            var reqMessage = new ReqRequisitionItemStatusDTO
            {
                MessageId = Guid.NewGuid().ToString(),
                COID = LocationMapper.GetCOID(submittedRequisition1.LocationIdentifier),
                Dept = LocationMapper.GetDepartmentId(submittedRequisition1.LocationIdentifier),
                ParIdentifier = submittedRequisition1.RequisitionItems[0].ParIdentifier,
                ParentSystemId = submittedRequisition1.RequisitionItems[0].ParentSystemId,
                OriginalParentSystemId = null,
                ItemId = submittedRequisition1.RequisitionItems[0].ItemId,
                Id = submittedRequisition1.RequisitionItems[0].Id,
                QuantityToOrder = submittedRequisition1.RequisitionItems[0].QuantityToOrder,
                QuantityFulfilled = submittedRequisition1.RequisitionItems[0].QuantityFulfilled.HasValue ? submittedRequisition1.RequisitionItems[0].QuantityFulfilled.Value : 0,
                RequisitionItemStatusTypeId = "t", // Requisition Scheduled
                RequisitionScheduledDate = DateTime.Now.AddHours(2),
                PONumber = null,
                Sequence = 1,
                CreateDateTime = DateTime.Now,
                UTCOffset = -6
            };

            RequisitionControllerHelper.UpdateRequisitionItemStatus(reqMessage);

            var retrievedAgain = RequisitionControllerHelper.GetRequisition(submittedRequisition1.RequisitionId, TestData.TestUser1.Username);

            Assert.AreEqual(RequisitionItemStatusTypeEnum.Scheduled, (RequisitionItemStatusTypeEnum)retrievedAgain.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be updated now");


            var parRequisitionDto2 = new RequisitionDTO(GetRequisition(new List<RequisitionItem> { requisitionItem }));
            var savedRequisition2 = RequisitionControllerHelper.SaveRequisition(parRequisitionDto2, TestData.TestUser1.Username);
            var submittedRequisition2 = RequisitionControllerHelper.SubmitApproversRequisition(savedRequisition2, TestData.TestUser1.Username);

            Assert.IsTrue(submittedRequisition2.RequisitionItems[0].ParentSystemId.Length > 0, "Submitted Requisition should have a SMART requisition number");
            Assert.AreEqual(RequisitionItemStatusTypeEnum.Processing, (RequisitionItemStatusTypeEnum)submittedRequisition2.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be the same");

            if (submittedRequisition2.RequisitionItems[0].ParentSystemId != submittedRequisition1.RequisitionItems[0].ParentSystemId)
            {
                Assert.Inconclusive("Separate requisitions were created. Test cannot proceed");
                return;
            }

            var REQMessage2 = new ReqRequisitionItemStatusDTO
            {
                MessageId = Guid.NewGuid().ToString(),
                COID = LocationMapper.GetCOID(submittedRequisition2.LocationIdentifier),
                Dept = LocationMapper.GetDepartmentId(submittedRequisition1.LocationIdentifier),
                ParIdentifier = submittedRequisition2.RequisitionItems[0].ParIdentifier,
                ParentSystemId = submittedRequisition2.RequisitionItems[0].ParentSystemId,
                OriginalParentSystemId = submittedRequisition1.RequisitionItems[0].ParentSystemId,
                ItemId = submittedRequisition2.RequisitionItems[0].ItemId,
                Id = null,
                QuantityToOrder = submittedRequisition2.RequisitionItems[0].QuantityToOrder,
                QuantityFulfilled = submittedRequisition2.RequisitionItems[0].QuantityFulfilled.HasValue ? submittedRequisition1.RequisitionItems[0].QuantityFulfilled.Value : 0,
                RequisitionItemStatusTypeId = "w", // Need To send to WH
                RequisitionScheduledDate = null,
                PONumber = null,
                Sequence = 1,
                CreateDateTime = DateTime.Now,
                UTCOffset = -6
            };

            var status2 = RequisitionControllerHelper.UpdateRequisitionItemStatus(REQMessage2);

            var retrievedAgain1 = RequisitionControllerHelper.GetRequisition(submittedRequisition1.RequisitionId, TestData.TestUser1.Username);
            var retrievedAgain2 = RequisitionControllerHelper.GetRequisition(submittedRequisition2.RequisitionId, TestData.TestUser1.Username);

            Assert.AreEqual(RequisitionItemStatusTypeEnum.NeedToSendToWH, (RequisitionItemStatusTypeEnum)retrievedAgain1.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be updated and same");
            Assert.AreEqual(RequisitionItemStatusTypeEnum.NeedToSendToWH, (RequisitionItemStatusTypeEnum)retrievedAgain2.RequisitionItems[0].RequisitionItemStatusTypeId, "Status should be updated and same");
        }

        [TestMethod]
        public void TestFetchingAllVendorInfo()
        {
            //Create Requisition Item Object with SPR Details
            var reqItem = new RequisitionItem
            {
                Id = 1,
                RequisitionId = 2,
                ItemId = "10ABC",
                CreatedBy = TestData.TestUser1.Username,
                CreateDate = DateTime.Now,
                RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Valid,
                SPRDetail = new SPRDetail
                {
                    RequisitionItemId = 201,
                    ItemDescription = "Test Item",
                    UOMCode = "EA",
                    EstimatedPrice = 15,
                    VendorId = 900700,
                    VendorName = "CARDINAL MEDICAL HEALTH",
                    Vendor = new DomainModel.Vendors.Vendor { Id = 900700, Name = "CARDINAL MEDICAL HEALTH" },
                    PartNumber = "abc123",
                    GeneralLedgerCode = "610277",
                    DeliveryMethodTypeId = 0,
                    ParIdentifier = "F34"
                }
            };

            var vendorHeaderInfoList = new List<DomainModel.Vendors.VendorHeaderInfo>();
            vendorHeaderInfoList.Add(new DomainModel.Vendors.VendorHeaderInfo {Coid=9391,Id= 900700,IsPOOnly=false,Name= "CARDINAL HEALTH MEDICAL PRODUCTS",StandardNationalVendorNumber= 900700, Status="A" });
            //Mock call to SMART GetAllVendorHeaders Vendor Information
            _mockSmartVendorService.Setup(x => x.GetAllVendorHeaders(It.IsAny<string>(), It.IsAny<string>())).Returns(vendorHeaderInfoList);

            var returnDatafromSmart = _mockSmartVendorService.Object.GetAllVendorHeaders(TestData.TestUser1.Username,"09391");

            var vendorInfo = returnDatafromSmart.ConvertAll(x => new DomainModel.Vendors.Vendor { Id = x.Id, Name = x.Name }).FirstOrDefault();

            //Assign the return value from Mock method to Requisition Item Vendor
            reqItem.SPRDetail.Vendor = vendorInfo;

            //Assert if the values are assigned to the Requisition Item as expected.
            Assert.AreEqual(reqItem.SPRDetail.Vendor.Id, 900700);
            Assert.AreEqual(reqItem.SPRDetail.Vendor.Name, "CARDINAL HEALTH MEDICAL PRODUCTS");
        }
    }
}
