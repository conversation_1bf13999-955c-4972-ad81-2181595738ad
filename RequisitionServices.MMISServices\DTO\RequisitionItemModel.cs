﻿using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class RequisitionItemModel
    {
        public RequisitionItemModel()
        {
            this.BillReplaceType = "";
        }

        public int Id { get; set; }
        public int RequisitionId { get; set; }
        public int Coid { get; set; }
        public int ParDepartment { get; set; }
        public string ParClass { get; set; }
        public int ItemQuantity { get; set; }
        public DateTime RequestDate { get; set; }
        public int ItemNumber { get; set; }
        public bool ReqRush { get; set; }
        
        public string SPRType { get; set; }
        public string RequisitionerName { get; set; }
        public string ReorderNumber { get; set; }
        public string VendorName { get; set; }
        public int VendorNumber { get; set; }
        public string ItemDescription { get; set; }
        public string UnitOfMeasure { get; set; }
        public decimal ItemPrice { get; set; }
        public long GLAccountNumber { get; set; }
        public string ShipToFacilityName { get; set; }
        public string ShipToAddress { get; set; }
        public string ShipToCity { get; set; }
        public string ShipToState { get; set; }
        public string ShipToZip { get; set; }
        public string ShippingInstructions { get; set; }
        public string SpecialInstructions { get; set; }
        public string BudgetNumber { get; set; }
        public decimal TradeIn { get; set; }
        public string Justification { get; set; }
        public bool AttachmentFlag { get; set; }
        public int ShipToCode { get; set; }
        public string BillReplaceType { get; set; }

        public bool IsWastePar { get; set; }
    }
}
