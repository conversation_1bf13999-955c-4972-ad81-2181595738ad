﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionListResultsDTO
    {
        public RequisitionListResultsDTO(List<RequisitionDTO> requisitions, long totalCount)
        {
            this.Requisitions = requisitions;
            this.TotalCount = totalCount;
        }

        public List<RequisitionDTO> Requisitions { get; set; }

        public long TotalCount { get; set; }
    }
}
