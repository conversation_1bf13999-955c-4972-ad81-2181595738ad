﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Items
{
    /// <summary>
    /// IIN Item Record
    /// </summary>
    public class IINItemRecordModel
    {
        /// <summary>
        /// Item Number
        /// </summary>
        public int ItemNumber { get; set; }

        /// <summary>
        /// Re Order Number
        /// </summary>
        public string ReorderNumber { get; set; }

        /// <summary>
        /// Item Description 1
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Item Description 2
        /// </summary>
        public string Description2 { get; set; }

        /// <summary>
        /// Item Long Description
        /// </summary>
        public string LongDescription { get; set; }

        /// <summary>
        /// Item Type
        /// </summary>
        public string ItemType { get; set; }

        /// <summary>
        /// Vendor Number
        /// </summary>
        public int VendorNumber { get; set; }

        /// <summary>
        /// Issue Unit of Measure
        /// </summary>
        public string IUOM { get; set; }

        /// <summary>
        /// Issue Unit of Measure Universal Product Number
        /// </summary>
        public string IUOMUPN { get; set; }

        /// <summary>
        /// Last PO
        /// </summary>
        public int LastPO { get; set; }

        /// <summary>
        /// Cost Issue Unit of Measure
        /// </summary>
        public decimal CostIUOM { get; set; }

        /// <summary>
        /// On Order Issue Unit of Measure
        /// </summary>
        public int OnOrderIUOM { get; set; }

        /// <summary>
        /// Quantity on Hand
        /// </summary>
        public int QOH { get; set; }

        /// <summary>
        /// On Order Purchase Unit of Measure
        /// </summary>
        public int OnOrderPUOM { get; set; }

        /// <summary>
        /// Purchase Unit of Measure
        /// </summary>
        public string PUOM { get; set; }

        /// <summary>
        /// Purchase Unit of Measure Universal Product Number
        /// </summary>
        public string PUOMUPN { get; set; }

        /// <summary>
        /// Cost Purchase Unit of Measure
        /// </summary>
        public decimal CostPUOM { get; set; }

        /// <summary>
        /// Tax Code
        /// </summary>
        public string TaxCode { get; set; }

        /// <summary>
        /// Factor
        /// </summary>
        public int Factor { get; set; }

        /// <summary>
        /// Minimum Quantity
        /// </summary>
        public int MinQuantity { get; set; }

        /// <summary>
        /// Maximum Quantity
        /// </summary>
        public int MaxQuantity { get; set; }

        /// <summary>
        /// Chargeable flag
        /// </summary>
        public bool Chargeable { get; set; }

        /// <summary>
        /// Expense GL Account
        /// </summary>
        public long ExpGLAccount { get; set; }

        /// <summary>
        /// Invoide GL Account
        /// </summary>
        public long InvGLAccount { get; set; }

        /// <summary>
        /// Procedure Code
        /// </summary>
        public string ProcCode { get; set; }

        /// <summary>
        /// In Patient Algorithm
        /// </summary>
        public string AlgorInPatient { get; set; }

        /// <summary>
        /// Out Patient Algorithm
        /// </summary>
        public string AlgorOutPatient { get; set; }

        /// <summary>
        /// Purchasing Department
        /// </summary>
        public int PurchaseDept { get; set; }

        /// <summary>
        /// Labels
        /// </summary>
        public bool Labels { get; set; }

        /// <summary>
        /// Physical Location
        /// </summary>
        public string PhysicalLoc { get; set; }

        /// <summary>
        /// Distribution Point
        /// </summary>
        public string DistPoint { get; set; }

        /// <summary>
        /// Compliance Code
        /// </summary>
        public string ComplianceCode { get; set; }

        /// <summary>
        /// Re Order Point
        /// </summary>
        public int ReorderPt { get; set; }

        /// <summary>
        /// Manufacturing Vendor Number
        /// </summary>
        public int MfgVendNumber { get; set; }

        /// <summary>
        /// Manufacturing Vendor Name
        /// </summary>
        public string MfgVendName { get; set; }

        /// <summary>
        /// Manufacturing Catalog Number
        /// </summary>
        public string MfgCatNumber { get; set; }

        /// <summary>
        /// Purchasing Vendor Name
        /// </summary>
        public string PurchVendName { get; set; }

        /// <summary>
        /// Contract Number
        /// </summary>
        public int ContrNbr { get; set; }

        /// <summary>
        /// Stock
        /// </summary>
        public string Stock { get; set; }

        /// <summary>
        /// Temporary Stock
        /// </summary>
        public string TempStock { get; set; }

        /// <summary>
        /// Facility Store
        /// </summary>
        public List<FStoreDeptModel> FStore { get; set; }
    }
}
