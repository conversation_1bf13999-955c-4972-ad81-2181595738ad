﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IFacilityWorkflowRepository
    {
        IEnumerable<FacilityWorkflowStep> Get(string coid, WorkflowTypeEnum workflowType);
        void Save(SaveFacilityWorkflowDTO workflow);
        void InsertStep(FacilityWorkflowStep step);
        void DeleteDelegatedSteps(int delegatedByUserId);
        List<FacilityWorkflowStep> GetStepsByApproverUsername(string username);
    }
}
