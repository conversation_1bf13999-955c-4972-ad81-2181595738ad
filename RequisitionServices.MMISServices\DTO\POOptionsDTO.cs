﻿using System;
using RequisitionServices.DomainModel.PurchaseOrders;

namespace RequisitionServices.MMISServices.DTO
{
    public class POOptionsDTO
    {
        public int PONumber { get; set; }

        public string Status { get; set; }

        public string VendorName { get; set; }

        public string POType { get; set; }

        public int VendorNumber { get; set; }

        public DateTime CreateDate { get; set; }

        public POOptions MapToPOList()
        {
            return new POOptions()
            {
                PONumber = this.PONumber,
                Status = this.Status,
                VendorName = this.VendorName,
                VendorNumber = this.VendorNumber,
                CreateDate = this.CreateDate,
                POType = this.POType
            };
        }
    }
}
