USE [eProcurementQA]
GO
/****** Object:  StoredProcedure [dbo].[usp_RequisitionsReportByItemNumberGet]    Script Date: 5/16/2024 3:32:02 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report item number search.
Used By     : SMART Procurement team
Author      : <PERSON> Martinez
Created     : 02-05-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
<PERSON> Martinez		02/05/2018		Script created
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

Eric Simmons		05-20-2024		Adding RequisitionSubmissionTypeId to create DSO tag
									and use for filtering for Approval History

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS

BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].IsMobile AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[DistinctRequisitions].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC) as rowNumber,
		
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[Req].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		[Item].[ItemId],
		[Item].[ReOrder],
		[Item].[CatalogNumber],
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDetail ON SPRDetail.RequisitionItemId = [Item].Id
		WHERE
		[ItemId] = @searchText
		OR [ReOrder] = @searchText
		OR [CatalogNumber] = @searchText
		OR [SPRDetail].PartNumber = @searchText
	) AS [Item]
	LEFT OUTER JOIN (SELECT
		(CASE 
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] As [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor],
		[Requisition].[RequisitionSubmissionTypeId]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
	) AS [Req] ON [Item].[RequisitionId] = [Req].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionSubmissionTypes] AS [ReqSubmissionTypes] WITH (NOLOCK) ON [Req].[RequisitionSubmissionTypeId] = [ReqSubmissionTypes].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	(@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [Item].[ItemId] LIKE '%' + @filterText + '%'
	OR [Item].[ReOrder] LIKE '%' + @filterText + '%'
	OR [Item].[CatalogNumber] LIKE '%' + @filterText + '%'
	OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)))
	AND substring([Req].[LocationIdentifier],0,(CHARINDEX('_',[Req].[LocationIdentifier]))) = @coid
	AND 1 <> [Req].[RequisitionStatusTypeId]
	AND 5 <> [Req].[RequisitionStatusTypeId]
	AND 8 <> [Req].[RequisitionStatusTypeId]
	AND 12 <> [Req].[RequisitionStatusTypeId]
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[Req].[RequisitionSubmissionTypeId],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
	
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [AllReqItems].[Id] = [VboHoldItemConversion].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END