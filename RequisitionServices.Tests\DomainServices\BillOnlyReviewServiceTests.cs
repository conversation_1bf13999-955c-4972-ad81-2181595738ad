﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class BillOnlyReviewServiceTests
    {
        private static Mock<IBillOnlyReviewService> mockIBillOnlyReviewService;
        private static Mock<IRequisitionService> mockRequisitionService;
        private static Mock<IBillOnlyReviewRepository> mockBillOnlyReviewRepository;
        private static Mock<ISmartItemService> mockSmartItemService;
        private static Mock<IParService> mockParService;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            mockIBillOnlyReviewService = new Mock<IBillOnlyReviewService>();
            mockRequisitionService = new Mock<IRequisitionService>();
            mockBillOnlyReviewRepository = new Mock<IBillOnlyReviewRepository>();
            mockSmartItemService = new Mock<ISmartItemService>();
            mockParService = new Mock<IParService>();
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            mockIBillOnlyReviewService = null;
            mockRequisitionService = null;
            mockBillOnlyReviewRepository = null;
            mockSmartItemService = null;
            mockParService = null;
        }

        [TestMethod]
        public void TestGetRequisitionsDetailsForBORPrint()
        {
            // Arrange
            var request = new BillOnlyReviewPrintRequest
            {
                RequisitionIds = new List<int> { 139503 },
                UserName = "test"
            };
            List<BillOnlyReviewRequisition> requisitions = new List<BillOnlyReviewRequisition>();
            var req = new BillOnlyReviewRequisition();
            var reqList = new List<BillOnlyReviewRequisitionWithDetailsDTO>();
            var reqDetails = new BillOnlyReviewRequisitionWithDetailsDTO();
            var reqWithDetailsDTO = new RequisitionWithDetailsDTO();
            var userName = "test";
            var billOnlyReviewService = new BillOnlyReviewService(mockBillOnlyReviewRepository.Object, mockRequisitionService.Object, mockSmartItemService.Object, mockParService.Object);
            mockBillOnlyReviewRepository.Setup(x => x.GetRequisitionsDetailsForBORPrint(request)).ReturnsAsync(requisitions);
            mockRequisitionService.Setup(x => x.GetWithDetailsDTO(req, userName)).Returns(reqWithDetailsDTO);
            reqList.Add(reqDetails);
            // Act
            var result = billOnlyReviewService.GetRequisitionsDetailsForBORPrint(request).Result;

            // Assert
            Assert.IsNotNull(result);
            //Assert.AreEqual(reqList.Count, result.Count);
        }
    }
}
