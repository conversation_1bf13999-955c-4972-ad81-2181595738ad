﻿using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IConfigurationRepository
    {
        IEnumerable<FacilityNotification> GetFacilityNotifications(string cOID);
        FacilityNotification GetGetFacilityNotification(int id);
        FacilityNotification InsertFacilityNotification(FacilityNotification facilityNotification);
        void UpdateFacilityNotification(FacilityNotification facilityNotification, string userName);
        void DeleteFacilityNotification(int id);

        IEnumerable<FacilityNotificationType> GetFacilityNotificationTypes();
        FacilityNotificationType GetGetFacilityNotificationType(int id);
    }
}
