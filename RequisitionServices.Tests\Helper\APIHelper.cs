﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Net.Http;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.Tests.Helper
{
    public static class GlobalConfig
    {
        public static readonly string TestUserName = null;
        public static readonly string TestPassword = null;

        public static readonly string APIURL = System.Configuration.ConfigurationManager.AppSettings["APIURL"];
        static GlobalConfig()
        {
#if SANDBOX
            TestUserName = TestData.TestUser1.Username;
            TestPassword = TestData.TestUser1.Password;
#endif       
        }
    }

    public static class ParControllerHelper
    {
        public static IEnumerable<ParItem> GetParItems(string userName, string COID, string Dept, string ParIdentifier)
        {
            return ApiUtility.ExecuteApiGetTo<IEnumerable<ParItem>>(GlobalConfig.APIURL, "Par/GetParItems/", new Dictionary<string, string> { { "userName", Uri.EscapeUriString(userName) }, { "COID", COID }, { "departmentId", Dept }, { "parId", ParIdentifier } }, GlobalConfig.TestUserName, GlobalConfig.TestPassword);
        }
    }

    public static class UserControllerHelper
    {
        public static IEnumerable<Location> GetLocations(string userName)
        {
            return ApiUtility.ExecuteApiGetTo<IEnumerable<Location>>(GlobalConfig.APIURL, "User/GetLocations/", new Dictionary<string, string> { { "userName", Uri.EscapeUriString(userName) } }, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }

        public static IEnumerable<Approver> UpdateApprovers(ApproverUpdateDTO updateDTO)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<IEnumerable<Approver>>(GlobalConfig.APIURL, "User/UpdateApprovers/", null, updateDTO, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }

        public static IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, WorkflowTypeEnum workflowType)
        {
            return ApiUtility.ExecuteApiGetTo<IEnumerable<UserWorkflowStep>>(GlobalConfig.APIURL, "User/GetUserWorkflowSteps/", new Dictionary<string, string> { { "userName", Uri.EscapeUriString(userName) }, { "COID", COID }, { "workflowTypeId", ((int)workflowType).ToString() } }, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }

        public static IEnumerable<UserWorkflowDTO> SaveWorkflows(string updater, SaveWorkflowsDTO saveworkflowDTO)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<IEnumerable<UserWorkflowDTO>>(GlobalConfig.APIURL, "User/SaveWorkflows/", new Dictionary<string, string> { { "updater", Uri.EscapeUriString(updater) } }, saveworkflowDTO, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }
    }

    public static class RequisitionControllerHelper
    {
        public static RequisitionDTO SaveRequisition(RequisitionDTO requisitionDTO, string userName)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<RequisitionDTO>(GlobalConfig.APIURL, "Requisition/SaveRequisition/", new Dictionary<string, string> { { "userName", Uri.EscapeUriString(userName) } }, requisitionDTO, GlobalConfig.TestUserName, GlobalConfig.TestPassword);
        }

        public static RequisitionDTO SubmitApproversRequisition(RequisitionDTO requisitionDTO, string userName)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<RequisitionDTO>(GlobalConfig.APIURL, "Requisition/SubmitApproversRequisition/", new Dictionary<string, string> { { "userName", Uri.EscapeUriString(userName) } }, requisitionDTO, GlobalConfig.TestUserName, GlobalConfig.TestPassword);
        }

        public static RequisitionDTO GetRequisition(int requisitionId, string userName)
        {
            return ApiUtility.ExecuteApiGetTo<RequisitionDTO>(GlobalConfig.APIURL, "Requisition/GetRequisition/", new Dictionary<string, string> { { "requisitionId", requisitionId.ToString() }, { "userName", Uri.EscapeUriString(userName) } }, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }

        public static HttpResponseMessage UpdateRequisitionItemStatus(BaseRequisitionItemStatusDTO message)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<HttpResponseMessage>(GlobalConfig.APIURL, "Requisition/UpdateRequisitionItemStatus/", null, message, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }

        public static StatusUpdateDTO UpdateRequisitionStatus(RequisitionStatusHistory requisitionStatusHistory)
        {
            return ApiUtility.ExecuteApiPostWithContentTo<StatusUpdateDTO>(System.Configuration.ConfigurationManager.AppSettings["APIURL"], "Requisition/UpdateRequisitionStatus/", null, requisitionStatusHistory, username: GlobalConfig.TestUserName, password: GlobalConfig.TestPassword);
        }
    }
}
