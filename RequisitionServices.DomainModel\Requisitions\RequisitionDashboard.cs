﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{

    [NotMapped]
    public class RequisitionDashboard
    {
        public int RequisitionId { get; set; }

        public int RequisitionStatusTypeId { get; set; }

        public int RequisitionTypeId { get; set; }

        public int? RequisitionItemId { get; set; }

        public string RequisitionItemNumber { get; set; }

        public string RequisitionItemParentSystemId { get; set; }

        public string RequisitionItemOriginalParentSystemId { get; set; }

        public int? RequisitionItemParentItemId { get; set; }

        public DateTime CreateDate { get; set; }

        public string SprDetailsPartNumber { get; set; }
    }
}
