﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionStatusHistory
    {
        public int Id { get; set; }

        public int RequisitionId { get; set; }

        [ForeignKey("RequisitionId")]
        public virtual Requisition Requisition { get; set; }

        public int RequisitionStatusTypeId { get; set; }

        [ForeignKey("RequisitionStatusTypeId")]
        public virtual RequisitionStatusType RequisitionStatusType { get; set; }

        public string Comments { get; set; }

        public decimal ApprovedAmount { get; set; }

        public int? ApprovalStep { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }

        public DateTime CreateDate { get; set; }

        public bool IsCERReviewer { get; set; }

        public int? DelegatedByApproverId { get; set; }

        [ForeignKey("DelegatedByApproverId")]
        public virtual RequisitionServices.DomainModel.Users.Approver ApproverId { get; set; }

    }
}
