﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.Utility.Domain;
using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class GLAccountModel
    {
        public int Coid { get; set; }

        public string SmartCountryCode { get; set; }

        public string AccountNumber { get; set; }

        public string Description { get; set; }

        public GLAccount MapToGLAccount()
        {
            var glAccount = new GLAccount()
            {
                COID = Coid.ToString(),
                AccountNumber = AccountNumber,
                AccountNumberString = AccountNumber.ToString().PadLeft(LocationMapper.GetGeneralLedgerLength(SmartCountryCode), '0'),
                Description = Description
            };

            var lengthDifference = LocationMapper.GetGeneralLedgerLength(SmartCountryCode) - LocationMapper.GetCostCodeLength(SmartCountryCode);

            glAccount.CostCode = Convert.ToInt32(AccountNumber.Substring(0, lengthDifference));

            return glAccount;
        }
    }
}
