﻿using System;
using RequisitionServices.DomainModel.PurchaseOrders;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class POVendorDTO
    {
        public int VendorNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int Coid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Address1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Address2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string City { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string County { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Country { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ZipCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WorkPhone { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string WorkExtension { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HomePhone { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FaxNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int TermsCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Terms { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShippingMethod { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string FreightOnBoard { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int NumberOfDaysInOrderCycle { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ContactName { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int StandardNationalVendorNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool EdiFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string EdiId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string JitFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string JitId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FaxFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool EmailFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal OrderFillRatio { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool SendFlag867 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FreightOnBoardOverrideFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string Status { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FillKillFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string BuyerId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int AccountsPayable { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string OrderDay { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string CustomerId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string SupplierId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int AddressId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ParentVendor { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int ParentCoid { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal MarkUpPercent { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public List<string> SpecialOption { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool HealthIndustryNumberFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string HealthIndustryNumber { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool UniversalProductNumberFLAG { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool MinorityFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool FreightFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromCity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromCounty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromState { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipFromZip { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool ShipInCityFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceCity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceCounty { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceState { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string ShipOrderAcceptanceZipCode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public bool ShipOrderAcceptanceInCityFlag { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int MinimumDollar { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int MinimumQuantity { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int YearToDateDollar { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int YearToDatePo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearToDateDollar { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearToDatePo { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearYear { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int NumberOfDeliveries { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public int LastYearNumberOfDeliveries { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public DateTime DateIncrement { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public decimal AverageLine { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxNumber1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxNumber2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxNumber3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxIdentifier1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxIdentifier2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateFaxIdentifier3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailAddress1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailAddress2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailAddress3 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailIdentifier1 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailIdentifier2 { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public string AlternateEmailIdentifier3 { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public OrderTimesDTO OrderTimes { get; set; }
        public POVendor MapToPOVendor()
        {
            return new POVendor()
            {
                AccountsPayable = this.AccountsPayable,
                AddressId = this.AddressId,
                AlternateEmailAddress1 = this.AlternateEmailAddress1,
                AlternateEmailAddress2 = this.AlternateEmailAddress2,
                AlternateEmailAddress3 = this.AlternateEmailAddress3,
                AlternateEmailIdentifier1 = this.AlternateEmailIdentifier1,
                AlternateEmailIdentifier2 = this.AlternateEmailIdentifier2,
                AlternateEmailIdentifier3 = this.AlternateEmailIdentifier3,
                AlternateFaxIdentifier1 = this.AlternateFaxIdentifier1,
                AlternateFaxIdentifier2 = this.AlternateFaxIdentifier2,
                AlternateFaxIdentifier3 = this.AlternateFaxIdentifier3,
                AlternateFaxNumber1 = this.AlternateFaxNumber1,
                AlternateFaxNumber2 = this.AlternateFaxNumber2,
                AlternateFaxNumber3 = this.AlternateFaxNumber3,
                AverageLine = this.AverageLine,
                BuyerId = this.BuyerId,
                ContactName = this.ContactName,
                CustomerId = this.CustomerId,
                DateIncrement = this.DateIncrement,
                EdiFlag = this.EdiFlag,
                EdiId = this.EdiId,
                EmailFlag = this.EmailFlag,
                FaxFlag = this.FaxFlag,
                FaxNumber = this.FaxNumber,
                FillKillFlag = this.FillKillFlag,
                FreightFlag = this.FreightFlag,
                FreightOnBoard = this.FreightOnBoard,
                FreightOnBoardOverrideFlag = this.FreightOnBoardOverrideFlag,
                HomePhone = this.HomePhone,
                HealthIndustryNumber = this.HealthIndustryNumber,
                HealthIndustryNumberFlag = this.HealthIndustryNumberFlag,
                JitFlag = this.JitFlag,
                JitId = this.JitId,
                LastYearNumberOfDeliveries = this.LastYearNumberOfDeliveries,
                LastYearToDateDollar = this.LastYearToDateDollar,
                LastYearToDatePo = this.LastYearToDatePo,
                LastYearYear = this.LastYearYear,
                MarkUpPercent = this.MarkUpPercent,
                MinimumDollar = this.MinimumDollar,
                MinimumQuantity = this.MinimumQuantity,
                MinorityFlag = this.MinorityFlag,
                NumberOfDaysInOrderCycle = this.NumberOfDaysInOrderCycle,
                NumberOfDeliveries = this.NumberOfDeliveries,
                OrderDay = this.OrderDay,
                OrderFillRatio = this.OrderFillRatio,
                ParentCoid = this.ParentCoid,
                ParentVendor = this.ParentVendor,
                SendFlag867 = this.SendFlag867,
                ShipFromCity = this.ShipFromCity,
                ShipFromCounty = this.ShipFromCounty,
                ShipFromState = this.ShipFromState,
                ShipFromZip = this.ShipFromZip,
                ShipInCityFlag = this.ShipInCityFlag,
                ShipOrderAcceptanceCity = this.ShipOrderAcceptanceCity,
                ShipOrderAcceptanceCounty = this.ShipOrderAcceptanceCounty,
                ShipOrderAcceptanceInCityFlag = this.ShipOrderAcceptanceInCityFlag,
                ShipOrderAcceptanceState = this.ShipOrderAcceptanceState,
                ShipOrderAcceptanceZipCode = this.ShipOrderAcceptanceZipCode,
                ShippingMethod = this.ShippingMethod,
                SpecialOption = this.SpecialOption,
                StandardNationalVendorNumber = this.StandardNationalVendorNumber,
                Status = this.Status,
                SupplierId = this.SupplierId,
                Terms = this.Terms,
                TermsCode = this.TermsCode,
                UniversalProductNumberFLAG = this.UniversalProductNumberFLAG,
                VendorAddress = new DomainModel.Locations.Address()
                {
                    Address1 = this.Address1,
                    Address2 = this.Address2,
                    AddressName = this.VendorName,
                    City = this.City,
                    COID = this.Coid.ToString(),
                    State = this.State,
                    Zip = this.ZipCode
                },
                VendorNumber = this.VendorNumber,
                WorkExtension = this.WorkExtension,
                WorkPhone = this.WorkPhone,
                YearToDateDollar = this.YearToDateDollar,
                YearToDatePo = this.YearToDatePo,
                OrderTimes = new DomainModel.Vendors.OrderTimes()
                {
                    MondayTimes = this.OrderTimes.MondayTimes,
                    TuesdayTimes = this.OrderTimes.TuesdayTimes,
                    WednesdayTimes = this.OrderTimes.WednesdayTimes,
                    ThursdayTimes = this.OrderTimes.ThursdayTimes,
                    FridayTimes = this.OrderTimes.FridayTimes,
                    SaturdayTimes = this.OrderTimes.SaturdayTimes,
                    SundayTimes = this.OrderTimes.SundayTimes
                }
            };
        }
    }
}
