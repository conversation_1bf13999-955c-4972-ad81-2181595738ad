﻿using System;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;

namespace eProcurementWeb.AppInsights
{
    public class RequestFilter : ITelemetryProcessor
    {
        ITelemetryProcessor Next { get; set; }

        public RequestFilter(ITelemetryProcessor next)
        {
            Next = next;
        }

        public void Process(ITelemetry item)
        {
            var request = item as RequestTelemetry;
            var dependency = item as DependencyTelemetry;

            if (request != null)
            {
                ProcessItemRequest(ref request);
                Next.Process(item);
            }
            else if (dependency != null)
            {
                ProcessItemRequest(ref dependency);
                Next.Process(item);
            }
            else { Next.Process(item); }
        }

        public void ProcessItemRequest(ref RequestTelemetry request)
        {
            if (Uri.IsWellFormedUriString(request.Url.ToString(), UriKind.Absolute))
            {
                var uri = new Uri(request.Url.ToString());
                MaskURI(ref uri);
                request.Url = uri;
            }
        }

        public void ProcessItemRequest(ref DependencyTelemetry dependency)
        {
            if (Uri.IsWellFormedUriString(dependency.Data, UriKind.Absolute))
            {
                var uri = new Uri(dependency.Data);
                MaskURI(ref uri);
                dependency.Data = uri.OriginalString;
            }
        }

        void MaskURI(ref Uri uri)
        {
            uri = new Uri(uri.GetLeftPart(UriPartial.Path));
        }
    }
}