﻿using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Cart.Responses;
using RequisitionServices.DomainModel.Cart.Entities;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface ICartRepository
    {
        Cart AddCart(CartRequest request);
        Cart AddPOUCart(CartRequest request);
        Cart GetCart(CartRequest request);
        Cart GetPOUCart(CartRequest request);
        void CorrelateCartAndRequisition(long cartId, int requisitionId);
        void Delete(long cartId);
        AddToCartResponse AddToCart(Cart cart, CartItem item);
        void UpdateCartItems(CartUpdateItemsRequest request);
        void UpdateCartPOUItems(CartUpdateItemsRequest request);
        int DeleteCartItem(CartDeleteItemRequest request);
        int DeletePOUCartItem(CartDeleteItemRequest request);
        Item_RFID GetRfidCartAttributes(CartRequest request);

        Cart AddCartMerge(CartRequest request);
        Cart GetCartMerge(CartRequest request);
        void UpdateCartItemsMerge(CartUpdateItemsRequest request);
        int DeleteCartItemMerge(CartDeleteItemRequest request);
    }
}