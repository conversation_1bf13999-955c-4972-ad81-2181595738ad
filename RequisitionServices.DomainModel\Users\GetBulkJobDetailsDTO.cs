﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Users
{
    public class GetBulkJobDetailsDTO
    {
        public GetBulkJobDetailsDTO(List<BulkApproverJobTracker> bulkApproverJob, long totalCount)
        {
            this.BulkApproverJob = bulkApproverJob;
            this.TotalCount = totalCount;
        }
        public List<BulkApproverJobTracker> BulkApproverJob { get; set; }
        public long TotalCount { get; set; }
    }
}
