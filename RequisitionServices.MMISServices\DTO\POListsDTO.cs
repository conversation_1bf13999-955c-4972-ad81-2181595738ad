﻿using System;
using RequisitionServices.DomainModel.PurchaseOrders;

namespace RequisitionServices.MMISServices.DTO
{
    public class POListsDTO
    {
        public int PONumber { get; set; }

        public int COID { get; set; }

        public string Status { get; set; }

        public string VendorName { get; set; }

        public int VendorNumber { get; set; }

        public DateTime CreateDate { get; set; }

        public bool Outstanding { get; set; }

        public string POComment1 { get; set; }

        public string POComment2 { get; set; }

        public string POComment3 { get; set; }

        public string POType { get; set; }

        public string SpecialInstruction2 { get; set; }

        public bool BackorderFlag { get; set; }

        public bool CreditFlag { get; set; }

        public bool ReturnFlag { get; set; }

        public POLists MapToPOList()
        {
            return new POLists()
            {
                PONumber = this.PONumber,
                COID = this.COID,
                Status = this.Status,
                VendorName = this.VendorName,
                VendorNumber = this.VendorNumber,
                CreateDate = this.CreateDate,
                Outstanding = this.Outstanding,
                POComment1 = this.POComment1,
                POComment2 = this.POComment2,
                POComment3 = this.POComment3,
                SpecialInstruction2 = this.SpecialInstruction2,
                BackorderFlag = this.BackorderFlag,
                CreditFlag = this.CreditFlag,
                ReturnFlag = this.ReturnFlag,
                POType = this.POType
            };
        }
    }
}
