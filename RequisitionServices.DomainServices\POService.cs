﻿using log4net;
using RequisitionServices.DomainModel.PurchaseOrders;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Web.Http;
using RequisitionServices.Utility;

namespace RequisitionServices.DomainServices
{
    public class POService : IPOService
    {
        private ISmartPOService smartPOService;
        private IRequisitionRepository requisitionRepository;
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public POService(ISmartPOService smartPOSvc, IRequisitionRepository requisitionRepo)
        {
            this.smartPOService = smartPOSvc;
            this.requisitionRepository = requisitionRepo;
        }

        public PODetails GetDetailsByPO(string userName, string COID, string PONumber, string stockIndicator, ApiVersion version = null)
        {
            try
            {
                return smartPOService.GetDetailsByPO(userName, COID, PONumber, stockIndicator, version);
            }
            catch (BadRequestException ex)
            {

                log.Error($"Method: GetDetailsByPO Parameters: userName:  { userName}, COID  {COID}, PONumber  { PONumber}, StockIndicator:  {stockIndicator}", ex);
                if (version?.MajorVersion > 1)
                {
                    throw;
                }
                return new PODetails();
            }
            catch (Exception ex)
            {
                log.Error($"Method: GetDetailsByPO Parameters: userName:  { userName}, COID  {COID}, PONumber  { PONumber}, StockIndicator:  {stockIndicator}", ex);
                return new PODetails();
            }
        }

        public List<POConfirmationDetail> GetPOConfirmationDetails(string userName, string COID, string PONumber, string lineNumber)
        {
            try
            {
                return smartPOService.GetPOConfirmationDetails(userName, COID, PONumber, lineNumber).ToList();
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0} Parameters: {1}, {2}, {3}, {4}", "GetPOConfirmationDetails", "userName: " + userName, "COID: " + COID, "PONumber: " + PONumber, "LineNumber: " + lineNumber), ex);
                return null;
            }

        }

        public List<POLists> GetDetailsByDateRange(string userName, string COID, DateTime startDate, DateTime endDate, int department)
        {
            try
            {
                return smartPOService.GetDetailsByDateRange(userName, COID, startDate, endDate, department).ToList();
            }
            catch (Exception ex)
            {
                log.Error(ex);
                return null;
            }
        }


        public List<POOptions> GetPoByOptions(string userName, string COID, DateTime startDate, DateTime endDate, string poType, int department, string reorderNumber)
        {
            try
            {
                return smartPOService.GetPoByOptions(userName, COID, startDate, endDate, poType, department, reorderNumber).ToList();
            }
            catch (Exception ex)
            {
                log.Error(ex);
                return null;
            }
        }


        public List<POLists> GetPoByProjectNumber(string userName, string COID, string projectNumber)
        {
            var returnList = new List<POLists>();
            var poList = requisitionRepository.GetPoNumbersByProjectNumberAndCoid(COID, projectNumber);

            returnList = smartPOService.GetPoHeaderInfoByPoNumbers(userName, COID, poList).ToList();

            return returnList;
        }


        public List<POHistory> GetHistoryByPO(string userName, string COID, string PONumber)
        {
            try
            {
                return smartPOService.GetHistoryByPO(userName, COID, PONumber).ToList();
            }
            catch (Exception ex)
            {
                log.Error(ex);
                return null;
            }
        }

    }
}
