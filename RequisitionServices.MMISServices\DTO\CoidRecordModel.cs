﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.Utility.Domain;

namespace RequisitionServices.MMISServices.DTO
{
    public class CoidRecordModel
    {
        public int Coid { get; set; }

        public string CoidType { get; set; }

        public int ParentCoid { get; set; }

        public string ParentCoidName { get; set; }

        public string CoidName { get; set; }

        public string CoidHostName { get; set; }

        public string CoidBoxName { get; set; }

        public bool IsDefault { get; set; }

        public bool MQEnabled { get; set; }

        public int PortNumber { get; set; }

        public string CoidCompanyCode { get; set; }

        public string SmartCountryCode { get; set; }

        public Location MapToLocation()
        {
            return new Location()
            {
                Id = this.Coid == 0 ? null : this.Coid.ToString().PadLeft(LocationMapper.GetCOIDCharacterLength(this.SmartCountryCode), '0'),
                Description = this.CoidName,
                ParentId = this.ParentCoid.ToString(),
                ParentDescription = this.ParentCoidName,
                IsDefault = this.IsDefault,
                ParentSystem = ParentSystemType.SMART,
                MQEnabled = this.MQEnabled,
                CompanyCode = this.CoidCompanyCode,
                SmartCountryCode = this.SmartCountryCode
            };
        }
    }
}
