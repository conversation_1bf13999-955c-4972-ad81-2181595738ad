﻿using AutoMapper;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.Utility.Date;

namespace RequisitionServices.DomainServices
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<Comment, CommentDTO>()
                .ForMember(c => c.Created, m => m.MapFrom(s => s.CreatedUtc.SpecifyUtc().ToLocalTime()))
                .ForMember(c => c.LastUpdated, m => m.MapFrom(s => s.LastUpdatedUtc.SpecifyUtc().ToLocalTime()));

            CreateMap<CommentNotificationRequisitionRow, CommentNotificationRequisition>()
                .ForMember(c => c.HasFileAttachment, m => m.MapFrom(s => s.HasFileAttachment == 1))
                .ForMember(c => c.HasSPRItems, m => m.MapFrom(s => s.HasSPRItems == 1))
                .ForMember(c => c.RequisitionerName, m => m.MapFrom(s => $"{s.RequisitionerFirstName} {s.RequisitionerLastName}"));
        }
    }
}