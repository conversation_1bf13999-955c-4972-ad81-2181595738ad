﻿using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using log4net;

namespace eProcurementServices.Utility.WebApi
{
    public static class ApiUtility
    {
        static readonly ILog _logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        /// <summary>
        /// Executes a HTTP GET call to the specified URL/Action
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="apiURL">URL of API</param>
        /// <param name="action">Action on API to be executed</param>
        /// <param name="parameters">Dictionary of parameter_name,parameter_value</param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <param name="customHeaders"></param>
        /// <returns></returns>
        public static T ExecuteApiGetTo<T>(string apiURL, string action, Dictionary<string, string> parameters, string username = null, string password = null, Dictionary<string, string> customHeaders = null)
        {
            var useDefaultCredentials = string.IsNullOrWhiteSpace(username);

            using (var client = ApiFactory.GetClient(apiURL, useDefaultCredentials))
            {
                if (!useDefaultCredentials)
                {
                    var authByteArray = Encoding.ASCII.GetBytes(username + ":" + password);
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("basic", Convert.ToBase64String(authByteArray));
                }

                if (customHeaders != null)
                {
                    foreach (var header in customHeaders)
                    {
                        client.DefaultRequestHeaders.TryAddWithoutValidation(header.Key, header.Value);
                    }
                }

                var queryUrl = QueryStringBuilder(action, parameters);

                var response = client.GetAsync(queryUrl).Result;
                if (response.IsSuccessStatusCode)
                {
                    var returnResponse = response.Content.ReadAsAsync<T>();
                    if (returnResponse.Status == TaskStatus.Faulted)
                    {
                        return default(T);
                    }

                    return returnResponse.Result;
                }

                _logger.Error($"{client.BaseAddress}{queryUrl} returned {response.StatusCode} with content {response.Content}");
                throw new Exception($"HTTP {response.StatusCode} Error calling API service. URL: {client.BaseAddress}{queryUrl}");
            }
        }

        /// <summary>
        /// Returns a URL-encoded query string with a supplied list of parameters
        /// </summary>
        /// <param name="url"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        static string QueryStringBuilder(string url, Dictionary<string, string> parameters)
        {
            var queryUrl = url;
            var queryUrlParameters = "";
            var hasParameters = false;

            if (parameters != null)
            {
                foreach (var parameter in parameters)
                {
                    if (!string.IsNullOrWhiteSpace(parameter.Value))
                    {
                        if (hasParameters)
                        {
                            queryUrlParameters += "&";
                        }
                        hasParameters = true;

                        queryUrlParameters += HttpUtility.UrlEncode(parameter.Key) + "=" + HttpUtility.UrlEncode(parameter.Value);
                    }
                }
            }

            if (hasParameters)
            {
                queryUrl += "?" + queryUrlParameters;
            }

            return queryUrl;
        }
    }
}
