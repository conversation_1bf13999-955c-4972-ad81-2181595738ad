﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.4.1.9004" targetFramework="net472" />
  <package id="Castle.Core" version="4.4.1" targetFramework="net472" />
  <package id="EntityFramework" version="6.1.2" targetFramework="net472" />
  <package id="EntityFramework6.BulkInsert" version="6.0.3.10" targetFramework="net472" />
  <package id="Evolve" version="2.4.0" targetFramework="net472" />
  <package id="log4net" version="2.0.12" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.Agent.Intercept" version="2.0.6" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.DependencyCollector" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.Log4NetAppender" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.PerfCounterCollector" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.Web" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.WindowsServer" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel" version="2.2.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Versioning" version="4.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.2" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Http" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net472" />
  <package id="Newtonsoft.Json.Schema" version="3.0.10" targetFramework="net472" />
  <package id="Smart.Core.Common" version="3.4.1" targetFramework="net472" />
  <package id="Smart.Core.Contracts" version="1.2.1" targetFramework="net472" />
  <package id="Swashbuckle" version="5.2.1" targetFramework="net472" />
  <package id="Swashbuckle.Core" version="5.2.1" targetFramework="net472" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.7.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.2" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="Unity" version="3.5.1404.0" targetFramework="net472" />
  <package id="WebActivatorEx" version="2.0" targetFramework="net472" />
  <package id="WebGrease" version="1.5.2" targetFramework="net472" />
</packages>