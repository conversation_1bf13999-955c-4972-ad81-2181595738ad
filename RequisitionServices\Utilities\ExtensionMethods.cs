﻿using Newtonsoft.Json.Schema;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RequisitionServices.Utilities
{
    public static class ExtensionMethods
    {
        public static string GetValidationErrorsString(this IEnumerable<ValidationError> validationErrors)
        {
            var errors = string.Empty;
            if (validationErrors.Count() > 0)
            {
                var builder = new StringBuilder();
                foreach (var error in validationErrors)
                {
                    builder.Append("Path: ");
                    builder.Append(error.Path);
                    builder.Append(" Error: ");
                    builder.Append(error.Message);
                    builder.Append(Environment.NewLine);
                }
                errors = builder.ToString();
            }
            return errors;
        }

        public static List<string> GetValidationErrorsList(this IEnumerable<ValidationError> validationErrors)
        {
            var errors = new List<string>();
            if (validationErrors.Count() > 0)
            {
                foreach (var error in validationErrors)
                {
                    errors.Add(string.Format("Path: {0}, Error: {1}", error.Path, error.Message));
                }
            }
            return errors;
        }

        public static string GetErrorMessagesString(this List<string> errorMessages)
        {
            return string.Join(Environment.NewLine, errorMessages);
        }
    }
}