<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <connectionStrings>
    <add name="EProcurementContext" providerName="System.Data.SqlClient" connectionString="Server=htnawddbsolp07.hcadev.corpaddev.net; Database=EProcurementQA; Integrated Security=True; MultipleActiveResultSets=True;"/>
    <add name="WorkflowPersistanceStore" providerName="System.Data.SqlClient" connectionString="Server=.;Initial Catalog=WorkflowPersistanceStoreQA;Integrated Security=True;Asynchronous Processing=True;"/>
  </connectionStrings>
  <appSettings>

	  <add key="SSO_grant_type" value="client_credentials"/>
	  <add key="SSO_client_id" value="dev-smart-sec"/>
	  <add key="SSO_client_secret" value="v8plSetKaW5YqJkhvDaXJEGDTeplu7Pd"/>
	  <add key="SSO_scope" value="smart_security"/>
	  <add key="SSO_response_type" value="code"/>
	  <add key="SmartClinetAuthUrl" value="https://qa-sso.healthtrustpg.com/as/token.oauth2"/>
	  <add key="SecurityAPINewUrl" value="https://sbx-api-smartclientauthorization.healthtrustpg.com/users/"/>
	  
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="aspnet:MaxJsonDeserializerMembers" value="**********"/>
    <add key="log4net.Config" value="web.config"/>
    <add key="SmartServicesAPIUrl" value="https://qasbx-api-smartservices.healthtrustpg.com/"/>
    <add key="NewtonSoftJSchemaLicenseKey" value="3359-HwUwWO/9dQC5RQDwaUMH5pXREsaoFFRa/EFzESuBwhfCpJ0K7sPoqEJzwmrVBL2uptNapCrxp8rb42SPcd7g4DLIfLaNfglcqfoqBQOKBcJ2bSpDrOYSxMNBztLy0Vk7o17CV9LB+zGhrVvb7mR6I2sDv91FuR818YMVZKcELTd7IklkIjozMzU5LCJFeHBpcnlEYXRlIjoiMjAxOC0wMy0wOFQwMTo0NDo1My43MzI0NjY2WiIsIlR5cGUiOiJKc29uU2NoZW1hU2l0ZSJ9"/>
    <add key="SecurityAPIUrl" value="http://dev-api.nsa.healthtrustpg.com/v1/"/>
    <add key="SecurtiyAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD"/>
    <add key="ProfileAPIUrl" value="http://dev-api-profile.healthtrustpg.com/v1/"/>
    <add key="EProcurementUrl" value="https://local-smart.healthtrustpg.com/procurement/"/>
    <add key="HomeAPIUrl" value="http://dev-api.healthtrustpg.com/v1/api/"/>
    <add key="SwaggerRootUrl" value="https://local-api-requisitions.healthtrustpg.com"/>
    <add key="SystemNotificationMasterAdmin" value="hej7854"/>
    <add key="AdminsForEmailAlerts" value="<EMAIL>,<EMAIL>"/>
    <add key="maxCartItemCount" value="100"/>
    <add key="AppInsightsEnabled" value="False"/>
    <add key="ikey" value="059f9b41-f9cd-4d50-a025-521516ae8226"/>
    <add key="Environment" value="Development"/>
    <add key="MaxExportedRequisitionCount" value="1000"/>
    <add key="coidsForQATesting" value="57110,57111"/>

    <!-- Feature Flags -->
    <add key="FeatureFlags:RetrieveDelegateUsersDE16834" value="true"/>
    
    <!-- Max Degree of Parallelism -->
    <add key="DelegateMaxDegreeOfParallelism" value="15"/>
  
	<!-- Legacy Connector -->
	<add key="ItemPriceLegacyConnectorAPIUrl" value="https://api-dev.app.hca.cloud/legacy-connector-services/legacy-connector-itemcontract-api/v1/"/>
	<add key="LegacyConnectorTokenUrl" value="https://api-dev.app.hca.cloud/token"/>
	<add key="LegacyConnectorTokenUsername" value="DdnBAgxbFry1w0ewRpHBDwMudSkQDI6QuU5MXkVDm8xlDPS9"/>
	<add key="LegacyConnectorTokenPassword" value="kufW4TtpcA4GP7QTMNDjLAb8RLG4mPxZWVGjYqJVGG0XGMevCfoodPyvffa4vH9D"/>
	<add key="LegacyConnectorTokenGrant_type" value="client_credentials"/>
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.
    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
	<system.web>
		<compilation debug="true" targetFramework="4.7.2"/>
		<httpRuntime targetFramework="4.7.2" executionTimeout="180" maxRequestLength="32768"/>
		<authorization>
			<allow users="*"/>
		</authorization>
		<httpModules>
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"/>
		</httpModules>
	</system.web>
	<system.web.extensions>
		<scripting>
			<webServices>
				<jsonSerialization maxJsonLength="50000000"/>
			</webServices>
		</scripting>
	</system.web.extensions>
	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_IRushApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********"/>
				<binding name="BasicHttpBinding_INonRushApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********"/>
				<binding name="BasicHttpBinding_IVendorApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********"/>
			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://local-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRushApprovalWorkflow" contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow"/>
			<endpoint address="http://local-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_INonRushApprovalWorkflow" contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow"/>
			<endpoint address="http://local-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IVendorApprovalWorkflow" contract="VendorWorkflowSvc.IVendorApprovalWorkflow" name="BasicHttpBinding_IVendorApprovalWorkflow"/>
		</client>
	</system.serviceModel>
	<system.webServer>
		<security>
			<authentication>
				<anonymousAuthentication enabled="true"/>
				<basicAuthentication enabled="true"/>
				<windowsAuthentication enabled="true"/>
			</authentication>
		</security>
		<validation validateIntegratedModeConfiguration="false"/>
		<modules>
			<add name="CorrelationIdModule" type="RequisitionServices.Modules.CorrelationIdModule"/>
			<add name="UserModule" type="RequisitionServices.Modules.UserModule"/>
			<remove name="ApplicationInsightsWebTracking"/>
			<add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler"/>
		</modules>
		<handlers>
			<remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
			<remove name="OPTIONSVerbHandler"/>
			<remove name="TRACEVerbHandler"/>
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
		</handlers>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30AD4FE6B2A6AEED" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<dependentAssembly>
					<assemblyIdentity name="System.ValueTuple" publicKeyToken="CC7B13FFCD2DDD51" culture="neutral"/>
					<bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0"/>
				</dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Practices.Unity" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.0" newVersion="3.5.0.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="0.0.0.0-5.2.2.0" newVersion="5.2.2.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-4.0.6.0" newVersion="4.0.6.0"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral"/>
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<system.net>
		<mailSettings>
			<smtp from="<EMAIL>">
				<network host="smtp-gw.nas.medcity.net"/>
			</smtp>
		</mailSettings>
	</system.net>
	<log4net>
		<appender name="DebugFileAppender" type="log4net.Appender.RollingFileAppender">
			<param name="Threshold" value="DEBUG"/>
			<param name="File" value="Logs/"/>
			<param name="MaxSizeRollBackups" value="-1"/>
			<param name="RollingStyle" value="Date"/>
			<param name="StaticLogFileName" value="false"/>
			<param name="DatePattern" value="yyyyMMdd'_Debug.log'"/>
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} (%F:%L) %m%n"/>
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter">
				<levelMin value="DEBUG"/>
				<levelMax value="FATAL"/>
			</filter>
		</appender>
		<appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
			<param name="Threshold" value="INFO"/>
			<param name="File" value="Logs/"/>
			<param name="MaxSizeRollBackups" value="-1"/>
			<param name="RollingStyle" value="Date"/>
			<param name="StaticLogFileName" value="false"/>
			<param name="DatePattern" value="yyyyMMdd'_Info.log'"/>
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter">
				<levelMin value="INFO"/>
				<levelMax value="WARN"/>
			</filter>
		</appender>
		<appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
			<param name="Threshold" value="ERROR"/>
			<param name="File" value="Logs/"/>
			<param name="MaxSizeRollBackups" value="-1"/>
			<param name="RollingStyle" value="Date"/>
			<param name="StaticLogFileName" value="false"/>
			<param name="DatePattern" value="yyyyMMdd'_Error.log'"/>
			<layout type="log4net.Layout.PatternLayout">
				<param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
			</layout>
			<filter type="log4net.Filter.LevelRangeFilter">
				<levelMin value="ERROR"/>
				<levelMax value="FATAL"/>
			</filter>
		</appender>
		<root>
			<appender-ref ref="DebugFileAppender"/>
			<appender-ref ref="InfoFileAppender"/>
			<appender-ref ref="ErrorFileAppender"/>
			<level value="ALL"/>
			<appender-ref ref="aiAppender"/>
		</root>
		<appender name="aiAppender" type="Microsoft.ApplicationInsights.Log4NetAppender.ApplicationInsightsAppender, Microsoft.ApplicationInsights.Log4NetAppender">
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%message%newline"/>
			</layout>
		</appender>
	</log4net>
	<entityFramework>
		<defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
			<parameters>
				<parameter value="mssqllocaldb"/>
			</parameters>
		</defaultConnectionFactory>
		<providers>
			<provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
		</providers>
	</entityFramework>
</configuration>
