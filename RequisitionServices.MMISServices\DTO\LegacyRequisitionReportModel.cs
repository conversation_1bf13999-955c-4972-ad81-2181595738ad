﻿using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.MMISServices.DTO
{
    public class LegacyRequisitionReportModel
    {
        public int COID { get; set; }

        public int PageNumber { get; set; }

        public IEnumerable<string> PageIndices { get; set; }

        public bool IsLastPage { get; set; }

        public LegacyRequisitionRecordsModel Requisitions { get; set; }

        public string ErrorMessage { get; set; }

        public LegacyRequisitionReportDTO MapToLegacyRequisitionReportDTO()
        {
            var reqIEnumerable = new List<Requisition>().AsEnumerable();
            if(this.Requisitions != null)
            {
                reqIEnumerable = this.Requisitions.convertToIEnumerableOfRequisitions(); 
            }
            return new LegacyRequisitionReportDTO()
            {
                COID = this.COID.ToString(),
                PageNumber = this.PageNumber,
                PageIndices = this.PageIndices,
                IsLastPage = this.IsLastPage,
                Requisitions = reqIEnumerable,
                ErrorMessage = this.ErrorMessage
            };
        }
    }
}
