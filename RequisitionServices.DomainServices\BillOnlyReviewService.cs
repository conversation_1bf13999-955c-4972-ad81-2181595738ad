﻿using log4net;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.Utility.Domain;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.Locations;
using System.Security.Cryptography;

namespace RequisitionServices.DomainServices
{
    public class BillOnlyReviewService: IBillOnlyReviewService
    {
        private readonly ILog _log = LogManager.GetLogger(typeof(BillOnlyReviewService));

        private readonly IBillOnlyReviewRepository _billOnlyReviewRepository;
        private readonly IRequisitionService _requisitionService; 
        private readonly ISmartItemService _smartItemsService; 
        private readonly IParService _parService;  

        public BillOnlyReviewService(IBillOnlyReviewRepository billOnlyReviewRepository, IRequisitionService requisitionService, ISmartItemService smartItemService, IParService parService)
        {
            _billOnlyReviewRepository = billOnlyReviewRepository;
            _requisitionService = requisitionService;
            _smartItemsService = smartItemService;
            _parService = parService;
        }

        public async Task<PaginatedBORDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                var unpaginatedRequisitionsQuery = await Task.Run(() => _billOnlyReviewRepository.GetBillOnlyReviewRequisitions(request));
                var paginatedResult = new PaginatedBORDTO();

                if (unpaginatedRequisitionsQuery == null || !unpaginatedRequisitionsQuery.Any())
                {
                    return paginatedResult;
                }

                var paginatedData = unpaginatedRequisitionsQuery
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize);

                var paginatedList = paginatedData.ToList();

                // Simplified hydration call
                await HydrateBillOnlyReviewItems(request.UserName, paginatedList);

                foreach (var requisition in paginatedList)
                {
                    requisition.IsSPR = false; // Default
                    if (requisition.RequisitionItems != null && requisition.RequisitionItems.Any(item => item.SPRDetail != null))
                    {
                        requisition.IsSPR = true;
                    }
                }
                paginatedResult.DisplayedBORRequisitions = paginatedList;
                paginatedResult.PageNumber = request.PageNumber;
                paginatedResult.PageSize = request.PageSize;
                paginatedResult.TotalCount = unpaginatedRequisitionsQuery.Count();

                return paginatedResult;
            }
            catch (Exception ex)
            {
                _log.Error("Error in GetBillOnlyReviewRequisitions: " + ex.Message, ex);
                throw;
            }
        }

        public async Task<List<BillOnlyReviewRequisitionWithDetailsDTO>> GetRequisitionsDetailsForBORPrint(BillOnlyReviewPrintRequest request)
        {
            try
            {
                var requisitions = await _billOnlyReviewRepository.GetRequisitionsDetailsForBORPrint(request);
                var reqList = new List<BillOnlyReviewRequisitionWithDetailsDTO>();
                foreach (var req in requisitions)
                {
                    if (req?.RequisitionItems != null)
                    {                       
                        var requisition = _requisitionService.HydrateBillOnlyReviewRequisitionWithParentSystemDetails(request.UserName, req);
                        req.RequisitionItems = requisition.RequisitionItems.OrderBy(x => x.ParentRequisitionItemId ?? x.Id).ThenBy(y => y.CreateDate).ToList();
                    }
                    var requisitionWithDetailsDto = _requisitionService.GetWithDetailsDTO(req, request.UserName);
                    var newReqDetails = new BillOnlyReviewRequisitionWithDetailsDTO(requisitionWithDetailsDto, req);
                    reqList.Add(newReqDetails);
                }
                return reqList;
            }
            catch (Exception ex)
            {
                _log.Error("Error getting bill only review requisition with details using a list of RequisitionIds and userName given in RequisitionServices API, Method: BillOnlyReviewService.GetRequisitionsDetailsForBORPrint", ex);
                throw;
            }
        }

        private async Task HydrateBillOnlyReviewItems(string userName, List<BillOnlyReviewDTO> billOnlyReviewDTOs)
        {
            if (billOnlyReviewDTOs == null || !billOnlyReviewDTOs.Any())
            {
                return;
            }

            var tasks = billOnlyReviewDTOs.Select(async borDTO =>
            {
                var locationParts = borDTO.LocationIdentifier.Split('_');
                var usernameParts = userName.Split('/');
                string departmentId = locationParts[1];

                var itemTasks = borDTO.RequisitionItems.Select(async item =>
                {
                    // Populate Item object for each item  
                    try
                    {
                        var itemDataFromSmart = await Task.Run(() => _smartItemsService.GetItemByItemId(usernameParts[1], locationParts[0], item.ItemId));
                        item.Item = itemDataFromSmart ?? null;
                    }
                    catch
                    {
                        item.Item = null;
                    }

                    // Populate Par object for each item  
                    try
                    {
                        var parDataList = await Task.Run(() => _parService.GetParItemsByItem(userName, locationParts[0], int.Parse(departmentId), item.ItemId));
                        item.ParItem = parDataList?.FirstOrDefault();
                    }
                    catch
                    {
                        item.ParItem = null;
                    }
                });

                await Task.WhenAll(itemTasks);
            });

            await Task.WhenAll(tasks);
        }
    }
}
