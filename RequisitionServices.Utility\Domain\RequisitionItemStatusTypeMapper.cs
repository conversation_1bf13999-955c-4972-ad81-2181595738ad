﻿using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.Utility.Domain
{
    public static class RequisitionItemStatusTypeMapper
    {
        public static int GetRequisitionItemStatusType(string reqItemStatusType, int orderQuantity, int issuedQuantity)
        {
            int RequisitionItemStatusType;
            switch (reqItemStatusType)
            {
                case "Need To Print":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.NeedToPrint;
                    break;
                case "Being Pulled":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.BeingPulled;
                    break;
                case "Added To Previous Requisition":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.AddedToPrevious;
                    break;
                case "Requisition Open":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.RequisitionOpen;
                    break;
                case "Purchase Order In Progress":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.POInProcess;
                    break;
                case "Purchase Order Created":
                    RequisitionItemStatusType  = (int)RequisitionItemStatusTypeEnum.POCreated;
                    break;
                case "Item Received":
                    RequisitionItemStatusType = (int) GetPartialFillRequisitionStatus(orderQuantity, issuedQuantity, RequisitionItemStatusTypeEnum.ItemReceived);
                    break;
                case "Need To Send Warehouse":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.NeedToSendToWH;
                    break;
                case "Sent To Warehouse":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.SentToWH;
                    break;
                case "Requisition Filled":
                    RequisitionItemStatusType = (int) GetPartialFillRequisitionStatus(orderQuantity, issuedQuantity, RequisitionItemStatusTypeEnum.RequisitionFilled);
                    break;
                case "Processing":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.Processing;
                    break;
                case "Need To Be Processed":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.Processing;
                    break;
                case "Scheduled":
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.Scheduled;  
                    break;
                default:
                    RequisitionItemStatusType = (int)RequisitionItemStatusTypeEnum.Unknown;
                    break;
            }

            return RequisitionItemStatusType;
        }

        public static RequisitionItemStatusTypeEnum GetPartialFillRequisitionStatus(int orderQuantity, int issuedQuantity, RequisitionItemStatusTypeEnum currentStatus)
        {
            if (issuedQuantity >= orderQuantity)
            {
                return currentStatus;
            }

            return RequisitionItemStatusTypeEnum.NoPartialFill;
        }
    }
}
