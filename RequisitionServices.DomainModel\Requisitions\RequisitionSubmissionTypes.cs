﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionSubmissionTypes
    {
        /// <summary>
        /// The unique identifier for the RequisitionSubmissionType.
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        /// <summary>
        /// The description of the RequisitionSubmissionType.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Description { get; set; }
    }
}
