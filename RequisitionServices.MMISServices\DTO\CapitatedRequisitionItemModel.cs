﻿using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class CapitatedRequisitionItemModel
    {
        public int Id { get; set; }

        public int RequisitionId { get; set; }

        public int Coid { get; set; }

        public int ItemNumber { get; set; }

        public int ItemQuantity { get; set; }

        public List<string> LotNumbers { get; set; }

        public List<string> SerialNumbers { get; set; }

        public decimal ChargeCost { get; set; }

        public string RequisitionerName { get; set; }
    }
}
