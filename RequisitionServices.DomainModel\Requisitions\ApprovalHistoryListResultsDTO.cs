﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class ApprovalHistoryListResultsDTO
    {
        public ApprovalHistoryListResultsDTO(List<ApprovalHistoryDTO> approvalDTOs, long totalCount)
        {
            this.ApprovalDTOs = approvalDTOs;
            this.TotalCount = totalCount;
        }

        public List<ApprovalHistoryDTO> ApprovalDTOs { get; set; }

        public long TotalCount { get; set; }
    }
}
