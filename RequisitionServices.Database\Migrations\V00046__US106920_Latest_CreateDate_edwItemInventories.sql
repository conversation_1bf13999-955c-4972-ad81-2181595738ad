USE [eProcurementQA]
GO

/****** Object:  View [dbo].[edwItemInventories]    Script Date: 1/17/2024 1:41:04 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


ALTER VIEW [dbo].[edwItemInventories] AS
SELECT
[RequisitionItemId]
,I.[RequisitionId]
,[COID]
,[DepartmentId]
,[ParId]
,I.[ItemId]
,I.[CatalogNumber]
,[QuantityOnHand]
,[QuantityOnOrder]
,I.[QuantityToOrder]
,SUBSTRING([UserName],0,(CHARINDEX('/',[UserName]))) AS 'Domain'
,SUBSTRING([UserName],(CHARINDEX('/',[UserName])+1),(LEN([UserName])-CHARINDEX('_',[UserName]))) AS 'UserName'
,CASE
	WHEN RI.[CreateDate] > I.[CreateDate]   THEN RI.[CreateDate]
	ELSE I.[CreateDate] 
 END AS 'CreateDateUTC'
,[LastUpdatedUTC]
,RI.ParentSystemId
,RI.RequisitionItemStatusTypeId
,RI.StockIndicator
,RI.PONumber
FROM [dbo].[ItemInventories]I
JOIN  [EProcurementQA].[dbo].[RequisitionItems] RI
on RI.RequisitionId = I.RequisitionId 
and RI.ItemId=I.ItemId
GO


