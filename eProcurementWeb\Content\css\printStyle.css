﻿@media print {
    body {
        font-family: Tahoma;
        font-size:11pt;
    }

    .purchaseOrderBlade {
        font-size: 70% !important;
    }

    /************HEADER & FOOTER************/
    .stripeWelcome {
        display: none;
    }

    #headerMenu {
        display: none;
    }

    .pageHeader-inner {
        height: 0;
    }

    footer {
        display:none;
    }

    /***********LEFT NAVIGATION************/
    #menuleft {
        display: none;
    }

    /************MAIN CONTENT************/
    .hideWhenPrint {
        display: none;
    }

    .showWhenPrint{
        display:block;
    }

    .mainViewItem {
        margin-top: 93px;
    }

    .mainView-content .container-fluid {
        padding-left: 0;
    }

    .mainViewItem .mainView-header {
        width: 100%;
        position: relative;
        text-align: left;
    }

    .mainView-title {
        color: #1b365d;
        white-space: nowrap;
        -webkit-transform: rotate(0);
        -webkit-transform-origin: left top 0;
        -moz-transform: rotate(0);
        -moz-transform-origin: left top 0;
        -o-transform: rotate(0);
        -o-transform-origin: left top 0;
        -ms-transform: rotate(0);
        -ms-transform-origin: left top 0;
        transform: rotate(0);
        transform-origin: left top 0;
        direction: rtl;
        margin-top: 0;
        margin-left: 0;
        font-size: 16pt;
        filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
        margin-bottom:0;
    }

    .reqStatus .label {
        padding: 4px 6px;
        font-size: 16px;
    }

    .mainView-content {
        float: left;
        margin-left: 0;
        width: 91.1%;
    }

    #contentFromMenu {
        width: 100%;
        height: 100%;
    }

        #contentFromMenu.hideMenu {
            top: 0;
        }

        #contentFromMenu.slide {
            margin-left: 0;
            top: 0;
        }

            #contentFromMenu.slide .hotKeyContainer {
                display: none;
            }

    .pull-right {
        float:right;
    }

    /************Requisition TABLE & Requisition DETAILS************/
    .tableContainer {
        margin-left: 0;
    }
    
    .summaryArea.fix {
        width: 100%;
        margin-top:50px;
    }
   .commentWrap {  
      word-wrap: break-word;
      word-break: break-word;       
   }


    /*********WIDGETS*********/
    .widgetHeader {
        background: #f3f3f3;
        border-bottom: 0 solid #ddd;
        padding: 8px;
        border-top-left-radius: 4px;
        border-top-right-radius: 4px;
        overflow: hidden;
    }

        .widgetHeader h3 {
            text-transform: uppercase;
            color: #333;
            margin: 0;
            font-weight: bold;
        }
        
    .widgetBody .scrollbar-outer {
        max-height: auto;
    }

    .widgetBodyContent {
        padding: 5px 5px 5px 12px;
        overflow: auto;
    }
    .wrapWidgetDetail a {
        font-size: 14px;
    }
     .wrapWidgetDetail p {
        margin-bottom: 0;
    }
    .widgetDetail  {
            float: left;
        }
        .widgetDetail a {
            font-size: 14px;
        }

        .widgetDetail p {
            margin-bottom: 0;
        }

    .widgetDate {
        width: 40px;
        vertical-align: middle;
        text-align: right;
        float: right;
    }

    .widgetDateMonth {
        display: block;
        font-size: 12px;
        font-weight: 600;
        color: #768692;
        text-transform: uppercase;
        text-align: center;
        margin-bottom: -8px;
    }

    .widgetDateDay {
        display: block;
        font-size: 24px;
        color: #00335b;
        text-align: center;
    }

    .widgetDateYear {
        display: block;
        font-size: 10px;
        font-weight: 600;
        color: #aaa;
        text-align: center;
        margin-top: -8px;
    }

    /********CARD********/
    .itemCardContainer {
        margin-bottom: 35px;
        max-height: auto;
    }

    .cardResult {
        border: 0 solid #e6e6e6;
        padding-left: 0;
        margin-bottom: 0;
        position: relative;
        height: auto;
        padding-bottom: 30px;
        padding-top:10px;
        margin-top:10px;
    }

        .cardResult p {
            margin-bottom: 0;
        }

            .cardResult p.miniLabel {
                font-size: 10px;
                padding: 8px;
            }

        .cardResult ul {
            padding: 5px 0;
        }

            .cardResult ul li {
                margin-bottom: 0;
            }

        .cardResult h3 {
            display: inline-block;
        }

            .cardResult h3 a {
                color: #1B365D;
                font-size: 15px;
            }

        .cardResult .btn-group {
            margin: 4px;
        }

    .cardResultFooter {
        margin-left: 0;
        position: relative;
        width: 100%;
        border-top: 0 solid #eee;
        margin-bottom:0;
        margin-top:10px;
    }

    .tooltipIndicator {
        border-bottom: 0 dotted #999;
    }

    /*****PADDINGS & MARGINS******/
    .noMarginPrint {
        margin:0 !important;
    }
    
    .marginLeft10 {
        margin-left: 10px;
    }

    .marginRight10 {
        margin-right: 10px;
    }

    .marginBottom0 {
        margin-bottom:0;
    }

    .marginBottom10 {
        margin-bottom: 10px;
    }

    .marginBottom15 {
        margin-bottom:15px;
    }

    .marginTop10 {
        margin-top: 10px;
    }

    .printMarginTop1 {
        margin-top:1px;
    }

    .printMarginTop10 {
        margin-top:10px;
    }

    .printMarginTop20 {
        margin-top:20px;
    }

    .printMarginTop30{
        margin-top: 30px;
    }

    .printMarginTop45{
        margin-top:45px;
    }

    .printMarginTop60{
        margin-top:60px;
    }

    .printMarginBottom10{
        margin-bottom:10px;
    }

    /*******COLUMNS******/
    .col-print-1 {
        width: 8%;
        float: left;
    }

    .col-print-2 {
        width: 16%;
        float: left;
    }

    .col-print-3 {
        width: 25%;
        float: left;
    }

    .col-print-4 {
        width: 33%;
        float: left;
    }

    .col-print-5 {
        width: 42%;
        float: left;
    }

    .col-print-6 {
        width: 50%;
        float: left;
    }

    .col-print-7 {
        width: 58%;
        float: left;
    }

    .col-print-8 {
        width: 66%;
        float: left;
    }

    .col-print-9 {
        width: 75%;
        float: left;
    }

    .col-print-10 {
        width: 83%;
        float: left;
    }

    .col-print-11 {
        width: 92%;
        float: left;
    }

    .col-print-12 {
        width: 100%;
        float: left;
    }

    /********OTHER********/
    a {
        text-decoration:underline;
    }

        a.noPrintUnderline {
            text-decoration:none;
        }

    .printInline {
        display:inline-block;
    }

    .print50 {
        width:50%;
    }

    .tooltip {
        visibility:hidden;
    }

    .printPOHeader{
      top: 0;
      right: 50px;
      position: fixed;
      color: #1b365d;
    }
}
