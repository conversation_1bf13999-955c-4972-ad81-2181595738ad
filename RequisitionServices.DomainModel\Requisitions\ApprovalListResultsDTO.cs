﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class ApprovalListResultsDTO
    {
        public ApprovalListResultsDTO(List<ApprovalDTO> approvalDTOs, long totalCount)
        {
            this.ApprovalDTOs = approvalDTOs;
            this.TotalCount = totalCount;
        }

        public List<ApprovalDTO> ApprovalDTOs { get; set; }

        public long TotalCount { get; set; }
    }
}
