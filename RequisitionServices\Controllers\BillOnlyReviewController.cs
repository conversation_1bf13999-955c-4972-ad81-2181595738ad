﻿using log4net;
using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class BillOnlyReviewController : ApiController
    {
        private readonly ILog log = LogManager.GetLogger(typeof(BillOnlyReviewController));

        private IBillOnlyReviewService _billOnlyReviewService;

        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewController"/> class.
        /// </summary>
        /// <param name="billOnlyReviewService">The bill only review service.</param>
        public BillOnlyReviewController(IBillOnlyReviewService billOnlyReviewService)
        {
            _billOnlyReviewService = billOnlyReviewService;
        }

        /// <summary>
        /// Retrieves a list of Bill Only Review Requisitions based on the provided request parameters.
        /// </summary>
        /// <param name="request">The request parameters for retrieving Bill Only Review Requisitions.</param>
        /// <returns>A list of Bill Only Review Requisitions.</returns>
        /// <exception cref="Exception">Thrown when an error occurs while retrieving the requisitions.</exception>
        [HttpPost]
        public async Task<PaginatedBORDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request)
        {
            try
            {
                var paginatedBORDTO = await _billOnlyReviewService.GetBillOnlyReviewRequisitions(request);
                return paginatedBORDTO;
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error getting bill only review requisitions using request object given in BillOnlyReviewController in RequisitionServices API, Method: BillOnlyReviewController.GetBillOnlyReviewRequisitions"), ex);
                throw;
            }
        }

        /// <summary>
        /// Retrieves the details of requisitions for BillOnlyReview printing based on the provided a passed list of RequisitionIds.
        /// </summary>
        /// <param name="request">A list of RequisitionIds and the UserName for retrieving requisition details.</param>
        /// <returns>A list of requisitions with details included for a BillOnlyReview Printout report.</returns>
        /// <exception cref="Exception">Thrown when an error occurs while retrieving the requisition details.</exception>
        [HttpPost]
        public async Task<List<BillOnlyReviewRequisitionWithDetailsDTO>> GetRequisitionsDetailsForBORPrint(BillOnlyReviewPrintRequest request)
        {
            try
            {
                var requisitionList = await _billOnlyReviewService.GetRequisitionsDetailsForBORPrint(request);
                return requisitionList;
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Error getting bill only review requisitions using request object given in BillOnlyReviewController in RequisitionServices API, Method: BillOnlyReviewController.GetRequisitionsDetailsForBORPrint"), ex);
                throw;
            }
        }
    }
}