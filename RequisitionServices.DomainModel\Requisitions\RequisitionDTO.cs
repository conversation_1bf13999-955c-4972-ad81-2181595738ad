﻿using RequisitionServices.DomainModel.VPro;
using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// This is the core requisition object to Submit
    /// </summary>
    public class RequisitionDTO
    {
        public RequisitionDTO()
        {
            this.TotalReqAmount = 0; // totalcost project
        }      
        public RequisitionDTO(Requisition requisition)
        {
            this.RequisitionId = requisition.RequisitionId;
            this.RequisitionStatusTypeId = requisition.RequisitionStatusTypeId;
            this.RequisitionTypeId = requisition.RequisitionTypeId;
            this.LocationIdentifier = requisition.LocationIdentifier;
            this.ApprovalStep = requisition.ApprovalStep;
            this.IsCurrentApproverStep = requisition.IsCurrentApproverStep;
            this.PendingReviewCount = requisition.PendingReviewCount;
            this.ApprovedAmount = requisition.ApprovedAmount;
            this.Comments = requisition.Comments;
            this.WorkflowInstanceId = requisition.WorkflowInstanceId;
            this.CreateDate = requisition.CreateDate;
            this.CreatedBy = requisition.CreatedBy;
            this.DenyDelegateByApproverId = requisition.DenyDelegateByApproverId;
            this.RequisitionParClass = requisition.RequisitionParClass;
            this.CountryCode = requisition.CountryCode;
            this.IsSubmitToSmartLegacyBlocked = requisition.IsSubmitToSmartLegacyBlocked;
            this.IsMobile = requisition.IsMobile;
            this.IsVendor = requisition.IsVendor;
            this.RequisitionSubmissionTypeId = requisition.RequisitionSubmissionTypeId;
            this.BadgeLogId = requisition.BadgeLogId;
            this.VProBadgeLog = requisition.VProBadgeLog;

            if (requisition.RequisitionItems != null)
            {
                this.RequisitionItems = new List<RequisitionItemDTO>();
                foreach(var item in requisition.RequisitionItems)
                {
                    this.RequisitionItems.Add(new RequisitionItemDTO(item, requisition.IsVendor, false));
                }
            }
            // Calculate TotalRequisitionAmount
            this.TotalReqAmount = requisition.TotalReqAmount;
        }
        public RequisitionDTO(Requisition requisition, bool fromDB)
        {
            this.RequisitionId = requisition.RequisitionId;
            this.RequisitionStatusTypeId = requisition.RequisitionStatusTypeId;
            this.RequisitionTypeId = requisition.RequisitionTypeId;
            this.LocationIdentifier = requisition.LocationIdentifier;
            this.ApprovalStep = requisition.ApprovalStep;
            this.IsCurrentApproverStep = requisition.IsCurrentApproverStep;
            this.PendingReviewCount = requisition.PendingReviewCount;
            this.ApprovedAmount = requisition.ApprovedAmount;
            this.Comments = requisition.Comments;
            this.WorkflowInstanceId = requisition.WorkflowInstanceId;
            this.CreateDate = requisition.CreateDate;
            this.CreatedBy = requisition.CreatedBy;
            this.DenyDelegateByApproverId = requisition.DenyDelegateByApproverId;
            this.RequisitionParClass = requisition.RequisitionParClass;
            this.CountryCode = requisition.CountryCode;
            this.IsSubmitToSmartLegacyBlocked = requisition.IsSubmitToSmartLegacyBlocked;
            this.IsMobile = requisition.IsMobile;
            this.IsVendor = requisition.IsVendor;
            this.RequisitionSubmissionTypeId = requisition.RequisitionSubmissionTypeId;

            if (requisition.RequisitionItems != null)
            {
                this.RequisitionItems = new List<RequisitionItemDTO>();
                foreach(var item in requisition.RequisitionItems)
                {
                    this.RequisitionItems.Add(new RequisitionItemDTO(item, requisition.IsVendor, fromDB));
                }
            }
            this.TotalReqAmount = requisition.TotalReqAmount; //totalcost addition
        }

        /// <summary>
        /// Primary Key/Identity of requisition. If not submitting an existing requisition, pass a value of 0.
        /// </summary>        
        public int RequisitionId { get; set; }

        /// <summary>
        /// Total Amount of all items on requisition
        /// </summary>
        public decimal? TotalReqAmount { get; set; } // Totalcost Addition

        /// <summary>
        /// Current status of requisition. Pass a value of "1" for a new submission.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Type of requisition. 0 = Normal, 1 = Bill Only, 2 = Bill and Replace, 3 = Capitated - Bill Only, 4 = Capitated - Bill and Replace
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Formatted as: COID_DepartmentNumber. For example, COID 9391 and Dept 712 = 9391_712
        /// </summary>     
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Provides a mechanism to track if the requisition is in the current users approval queue
        /// </summary>
        public bool IsCurrentApproverStep { get; set; }

        /// <summary>
        /// Provide the ability to attach the requisition to the workflow instance it belongs to if applicable
        /// </summary>
        public Guid? WorkflowInstanceId { get; set; }

        /// <summary>
        /// Comments for requisition. If the parent system supports comments, these will be passed. (For example, SMART Bill-Only reqs take comments).
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// The user which created the requisition. This will be populated by the userName performing the parent action.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// The date/time of the requisition creation. New Reqs (Id = 0), will get the current date/time autoset for this value
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// The list of items to add to the requisition
        /// </summary>
        public List<RequisitionItemDTO> RequisitionItems { get; set; }

        //public ICollection<RequisitionStatusHistory> RequisitionStatusHistories { get; set; }      

        /// <summary>
        /// Current step in approval process
        /// </summary>
        public int? ApprovalStep { get; set; }

        /// <summary>
        /// Current amount approved
        /// </summary>
        public decimal ApprovedAmount { get; set; }

        /// <summary>
        /// If the requisition has any pending reviews
        /// </summary>
        public int? PendingReviewCount { get; set; }

        /// <summary>
        /// Identify if the requisition is denied by a Delegate
        /// </summary>
        public int? DenyDelegateByApproverId { get; set; }

        /// <summary>
        /// For BO/BR "header" PAR class
        /// </summary>
        public string RequisitionParClass { get; set; }

        public bool IsSubmitToSmartLegacyBlocked { get; set; }

        public string CountryCode { get; set; }

        public bool IsMobile { get; set; }

        public bool IsVendor { get; set; }

        public int RequisitionSubmissionTypeId { get; set; }
        
        public int? BadgeLogId { get; set; }

        /// <summary>
        /// Gets or sets the VPro badge log associated with the requisition.
        /// </summary>
        public RequisitionVProBadgeLog VProBadgeLog { get; set; }
    }
}
