﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Represents a requisition used for purchasing reports
    /// </summary>
    public class PurchasingRequisitionDTO
    {
        public PurchasingRequisitionDTO() { }

        /// <summary>
        /// Gets or sets the identifier of the vendor associated with the requisition.
        /// </summary>
        public int VendorId { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the requisition.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the location identifier for the requisition.
        /// </summary>
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the requisition status type.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition status.
        /// </summary>
        public string RequisitionStatus { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the requisition type.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition type.
        /// </summary>
        public string RequisitionType { get; set; }

        /// <summary>
        /// Gets or sets the current approval step in the requisition workflow.
        /// </summary>
        public int? ApprovalStep { get; set; }

        /// <summary>
        /// Gets or sets the comments associated with the requisition.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the approved amount for the requisition.
        /// </summary>
        public decimal ApprovedAmount { get; set; }

        /// <summary>
        /// Gets or sets the unique identifier for the workflow instance associated with the requisition.
        /// </summary>
        public Guid? WorkflowInstanceId { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created the requisition.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the name of the user who created the requisition.
        /// </summary>
        public string CreatedByFullName { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the requisition was created.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the country code associated with the requisition.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets the PAR class for the requisition.
        /// </summary>
        public string RequisitionParClass { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition was created on a mobile device.
        /// </summary>
        public bool IsMobile { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition was created by a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the identifier for the requisition submission type.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the Description for RequisitionSubmissionType.
        /// </summary> 
        public string RequisitionSubmissionType { get; set; } = "Standard";

        /// <summary>
        /// Gets or sets the list of requisition items associated with the requisition.
        /// </summary>
        public List<PurchasingRequisitionItemDTO> RequisitionItems { get; set; }
    }
}
