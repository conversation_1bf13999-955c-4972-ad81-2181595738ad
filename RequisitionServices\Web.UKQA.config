<?xml version="1.0" encoding="utf-8"?>

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  
  <connectionStrings>
    <add name="EProcurementContext" providerName="System.Data.SqlClient" connectionString="Server=tcp:hcauk-smart-qa-sql.database.windows.net,1433;Initial Catalog=eProcurement;Persist Security Info=False;User ID=svc-az-reqapi01;Password=*******************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"  xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    <add name="WorkflowPersistanceStore" providerName="System.Data.SqlClient" connectionString="Server=tcp:hcauk-smart-qa-sql.database.windows.net,1433;Initial Catalog=WorkflowPersistanceStore;Persist Security Info=False;User ID=svc-az-appwf01;Password=*******************;MultipleActiveResultSets=False;Asynchronous Processing=True;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"  xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>
  <appSettings>
    <add key="HomeAPIUrl" value="https://homeqa.hcahealthcareqa.co.uk/home-api/v1/api/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

	<add key="SSO_client_id" value="" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
	<add key="SSO_client_secret" value="" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="SmartClinetAuthUrl" value="" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="SecurityAPINewUrl" value="" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	  
    <add key="SmartServicesAPIUrl" value="https://smartqa-api.hcahealthcareqa.co.uk/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SecurityAPIUrl" value="https://passqa.hcahealthcareqa.co.uk/security-api/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SecurtiyAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="ProfileAPIUrl" value="https://passqa.hcahealthcareqa.co.uk/profile-api/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EProcurementUrl" value="https://smartqa.hcahealthcareqa.co.uk/procurement/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SwaggerRootUrl" value="https://requisitionsqa-api.hcahealthcareqa.co.uk" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="AppInsightsEnabled" value="False"  xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ikey" value="059f9b41-f9cd-4d50-a025-521516ae8226" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SystemNotificationMasterAdmin" value="phanson" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="AdminsForEmailAlerts" value="<EMAIL>,<EMAIL>,<EMAIL>" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="coidsForQATesting" value="" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>

  <system.serviceModel>
    <bindings xdt:Transform="Replace">
      <basicHttpsBinding>
        <binding name="BasicHttpBinding_IRushApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" />
        <binding name="BasicHttpBinding_INonRushApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" />
        <binding name="BasicHttpBinding_IVendorApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" />
      </basicHttpsBinding>
    </bindings>
    <client xdt:Transform="Replace">
      <endpoint address="https://approvalworkflowqa.hcahealthcareqa.co.uk/RushWorkflowService.xamlx" binding="basicHttpsBinding" bindingConfiguration="BasicHttpBinding_IRushApprovalWorkflow" contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow" />
      <endpoint address="https://approvalworkflowqa.hcahealthcareqa.co.uk/NonRushWorkflowService.xamlx" binding="basicHttpsBinding" bindingConfiguration="BasicHttpBinding_INonRushApprovalWorkflow" contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow" />
      <endpoint address="https://approvalworkflowqa.hcahealthcareqa.co.uk/VendorWorkflowService.xamlx" binding="basicHttpsBinding" bindingConfiguration="BasicHttpBinding_IVendorApprovalWorkflow" contract="VendorWorkflowSvc.IVendorApprovalWorkflow" name="BasicHttpBinding_IVendorApprovalWorkflow" />
    </client>
  </system.serviceModel>
  <system.webServer>
    <security>
      <authentication>
        <windowsAuthentication enabled="true" useAppPoolCredentials="true" xdt:Transform="SetAttributes" />
      </authentication>
    </security>
  </system.webServer>

  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
  </system.web>

  <system.net>
    <mailSettings>
      <smtp from="<EMAIL>" xdt:Transform="Replace">
        <network host="***********"  />
      </smtp>
    </mailSettings>
  </system.net>

  <log4net xdt:Transform="Replace">
    <appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="INFO"/>
      <param name="File" value="E:\Logs\requisitionServices\"/>
      <param name="MaxSizeRollBackups" value="-1"/>
      <param name="RollingStyle" value="Date"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyyMMdd'_Info.log'"/>
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="INFO"/>
        <levelMax value="WARN"/>
      </filter>
    </appender>
    <appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="ERROR"/>
      <param name="File" value="E:\Logs\requisitionServices\"/>
      <param name="MaxSizeRollBackups" value="-1"/>
      <param name="RollingStyle" value="Date"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyyMMdd'_Error.log'"/>
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="ERROR"/>
        <levelMax value="FATAL"/>
      </filter>
    </appender>
    <root>
      <appender-ref ref="InfoFileAppender"/>
      <appender-ref ref="ErrorFileAppender"/>
    </root>
  </log4net>
  
</configuration>