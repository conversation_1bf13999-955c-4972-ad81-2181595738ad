﻿using RequisitionServices.DomainModel.Clinical;

namespace RequisitionServices.MMISServices.DTO
{
    public class PatientRecordModel
    {
        public Patient MapToPatient()
        {
            return new Patient()
                {
                    PatientId = this.PatientId,
                    Name = this.PatientName
                };
        }

        public string PatientId { get; set; }
        public string PatientName { get; set; }
    }
}
