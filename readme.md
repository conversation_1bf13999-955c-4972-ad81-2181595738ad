See [Procurement.Setup](https://github.com/HCAHealthtrust/smart-procurement-setup) for local setup.

## Prerequisites for Evolve Db migrations

* Evolve .NET Core Tool [2.4.0](https://evolve-db.netlify.com/tool/) or greater
  
    `dotnet tool install --global Evolve.Tool`
  
    If installation fails with a proxy authentication issue, add the following to the `configuration` node of `C:\Users\<USER>\AppData\Roaming\NuGetNuGet.Config` where `abc1234` is your 3-4 
    
    ```
    <config>
      <add key="http_proxy" value="http://proxy.nas.medcity.net:80" />
      <add key="https_proxy" value="http://proxy.nas.medcity.net:80" />
    </config>
    ```

## Database Distribution

### Evolve Migration

If this is your first time executing a migration locally, ensure your local database is first restored from a backup of eProcurementQA from QASBX

From the solution root directory, where `127.0.0.1` is the server address and optional instance:

`evolve migrate sqlserver -c "Server=127.0.0.1;Database=eProcurementQA;Integrated Security=True" -l RequisitionServices\bin\Migrations\`

Examples:

* Local: `evolve migrate sqlserver -c "Server=.\SQLEXPRESS;Database=eProcurementQA;Integrated Security=True" -l RequisitionServices.Database\Migrations\`
* QASBX: `evolve migrate sqlserver -c "Server=htnawddbsolp07.hcadev.corpaddev.net;Database=eProcurementQA;Integrated Security=True" -l RequisitionServices.Database\Migrations\`
