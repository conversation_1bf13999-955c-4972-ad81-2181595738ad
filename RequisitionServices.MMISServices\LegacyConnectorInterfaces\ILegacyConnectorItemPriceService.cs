﻿using RequisitionServices.DomainModel.Items;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.MMISServices.LegacyConnectorInterfaces
{
    /// <summary>
    /// <Userstory>US119491</Userstory>
    /// </summary>
    public interface ILegacyConnectorItemPriceService
    {
        /// <summary>
        /// <para>Get Item Price  details for the vendor Id or re-order number</para>
        /// </summary>
        /// <param name="username">User Id</param>
        /// <param name="COID"> Facility number</param>
        /// <param name="reOrderNumber">Item re-order||vendor part number</param>
        /// <param name="vendorId">Vendor Id</param>
        /// <returns></returns>
        ItemPriceDetails GetItemPrice(string username, string COID, string reOrderNumber, string vendorId);
    }
}
