﻿using RequisitionServices.DomainModel.Cart.Models;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Cart.Entities
{
    public class CartItem
    {
        public long Id { get; set; }
        public long CartId { get; set; }
        [ForeignKey("CartId")]
        public virtual Cart Cart { get; set; }
        public int ItemNumber { get; set; }
        public int Quantity { get; set; }
        [Required]
        public DateTime LastUpdatedUtc { get; set; }
        [NotMapped]
        public CartItemDetails Details { get; set; }
        public int PARTypeId { get; set; }
    }
}
