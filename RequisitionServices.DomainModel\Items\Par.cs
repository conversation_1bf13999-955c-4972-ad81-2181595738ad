﻿using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;
using System.Collections.ObjectModel;

namespace RequisitionServices.DomainModel.Items
{
    public class Par
    {
        private ICollection<ParItem> _parItems;
        public virtual ICollection<ParItem> ParItems
        {
            get { return _parItems ?? (_parItems = new Collection<ParItem>()); }
            set { _parItems = value; }
        }

        public Department Department { get; set; }

        public int ParType { get; set; }

        public string ParId { get; set; }

        public string Description { get; set; }

        public int ShipToCode { get; set; }

        public bool ExternalInterface { get; set; }
    }
}
