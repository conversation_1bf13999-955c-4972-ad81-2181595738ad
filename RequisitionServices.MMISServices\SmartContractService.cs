﻿using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using RequisitionServices.DomainModel.Contract;

namespace RequisitionServices.MMISServices
{
    public class SmartContractService : ISmartContractService
    {
        private const string getContractDetailsMethod = "Contracts/GetContractDetails/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public ContractDetails GetContractDetails(string userName, string coid, string contractId, string vendorNumber)
        {
            

            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if(userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            var contractDetailsModel = ApiUtility.ExecuteApiGetTo<ContractDetailsModel>(endpoint, getContractDetailsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coidString", coid },
                                                                                { "contractIdString", contractId.ToString() },
                                                                                { "vendorNumberString", vendorNumber.ToString() }
                                                                            });
            return contractDetailsModel.MapToContractDetails();            

        }
    }
}
