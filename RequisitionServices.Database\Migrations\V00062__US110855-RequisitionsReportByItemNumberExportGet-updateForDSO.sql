USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_RequisitionsReportByItemNumberExportGet]    Script Date: 5/16/2024 10:32:38 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberExportGet
Purpose     : Returns a non-paginated list of requisitions for the Requisition Report export button on the item number search.
Used By     : SMART Procurement team
Author      : <PERSON> Martinez
Created     : 02-07-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Cassie Martinez		02/07/2018		Script created
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Jeremiah King		05/16/2024		Adding RequisitionSubmissionTypeId for the new 
									DSO column on the exported report. Also added 
									ability to filter on DSO description
***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberExportGet]
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
                BEGIN

SELECT
[PopulatedReq].[RequisitionId]				AS [RequisitionId],
[PopulatedReq].[CreatedBy]					AS [RequisitionerId],
[PopulatedReq].[RequisitionTypeId]			AS [RequisitionTypeId],
[PopulatedReq].[CreateDate]					AS [RequisitionCreateDate],
[PopulatedReq].[RequisitionStatusTypeId]	AS [RequisitionStatusTypeId],
[PopulatedReq].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[PopulatedReq].[FirstName]					AS [RequisitionerFirstName],
[PopulatedReq].[LastName]					AS [RequisitionerLastName],
[PopulatedReq].[IsMobile]					AS [RequisitionIsMobile],
[PopulatedReq].[IsVendor]					AS [RequisitionIsVendor],
[PopulatedReq].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
[ReqItem].[Id]								AS [RequisitionItemId],
[ReqItem].[ItemId]							AS [RequisitionItemNumber],
[ReqItem].[UOMCode]							AS [RequisitionItemUomCode],
[ReqItem].[PONumber]						AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]						AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]						AS [RequisitionItemVendorId],
[ReqItem].[VendorName]						AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]					AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]					AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]							AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]					AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]			AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]					AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]					AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]				AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId]			AS [RequisitionItemParentItemId],
[ReqItem].[Discount]						AS [Discount],
[SprDetails].[UOMCode]						AS [SprDetailsUomCode],
[SprDetails].[VendorId]						AS [SprDetailsVendorId],
[SprDetails].[VendorName]					AS [SprDetailsVendorName],
[SprDetails].[PartNumber]					AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]				AS [SprDetailsDescription],
[SprDetails].[TradeInValue]					AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]					AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]			AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]				AS [SprDetailsEstimatedUnitPrice]
FROM
(
	SELECT DISTINCT
	(CASE 
		WHEN @mobileReqs = 1
		THEN 
			CASE
				WHEN [MatchingReq].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [MatchingReq].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2 
			END
	END) AS [ReqTypeGroupingOrder],
	[MatchingReq].[RequisitionId],
	[MatchingReq].[CreatedBy],
	[MatchingReq].[RequisitionTypeId],
	[MatchingReq].[CreateDate],
	[MatchingReq].[RequisitionStatusTypeId],
	[MatchingReq].[LocationIdentifier],
	[MatchingReq].[IsMobile],
	[MatchingReq].[IsVendor],
	[MatchingReq].[RequisitionSubmissionTypeId],
	[User].[FirstName],
	[User].[LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 14 THEN 5
		WHEN 2 THEN 6
		WHEN 4 THEN 7
		ELSE 8
		END END) AS [ConditionalStatusSorting]
	FROM
	(
		SELECT DISTINCT
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] ON [SprDetail].[RequisitionItemId] = [Item].[Id]
		WHERE
		[Item].[ItemId] = @searchText
		OR [Item].[ReOrder] = @searchText
		OR [Item].[CatalogNumber] = @searchText
		OR [SprDetail].[PartNumber] = @searchText
	) AS [MatchingItem]
	LEFT OUTER JOIN [dbo].[Requisitions] AS [MatchingReq] WITH (NOLOCK) ON [MatchingItem].[RequisitionId] = [MatchingReq].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [MatchingReq].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [MatchingReqItem] WITH (NOLOCK) ON [MatchingReq].[RequisitionId] = [MatchingReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [MatchingReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionSubmissionTypes] AS [RequisitionSubmissionTypes] WITH (NOLOCK) ON [MatchingReq].[RequisitionSubmissionTypeId] = [RequisitionSubmissionTypes].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [MatchingReq].[CreatedBy] = [User].[AccountName]
	WHERE
	substring([MatchingReq].[LocationIdentifier],0,(CHARINDEX('_',[MatchingReq].[LocationIdentifier]))) = @coid
	AND (@filterText IS NULL OR
	([MatchingReq].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [MatchingReq].[Comments] LIKE '%' + @filterText + '%'
	OR[RequisitionSubmissionTypes].[Description] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ItemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ReOrder] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[CatalogNumber] LIKE '%' + @filterText + '%'
	OR [MatchingReq].[LocationIdentifier] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [MatchingReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([MatchingReq].[LocationIdentifier], 0, CHARINDEX('_', [MatchingReq].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [MatchingReq].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)))
	AND 1 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 5 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 8 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 12 <> [MatchingReq].[RequisitionStatusTypeId]
) AS [PopulatedReq]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [PopulatedReq].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
ORDER BY
[ConditionalStatusSorting], [PopulatedReq].[ReqTypeGroupingOrder], 
CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
CASE @oldestFirst WHEN 1 THEN [PopulatedReq].[CreateDate] END ASC,
CASE @oldestFirst WHEN 0 THEN [PopulatedReq].[CreateDate] END DESC

OPTION (RECOMPILE)

END
GO


