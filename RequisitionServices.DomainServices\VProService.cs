﻿using RequisitionServices.DomainModel.VPro;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using log4net;

namespace RequisitionServices.DomainServices
{
    /// <summary>
    /// Provides an implementation of IVProService for handling badge log operations.
    /// </summary>
    public class VProService : IVProService
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(VProService));
        private readonly IVProRepository _repository;

        public VProService(IVProRepository repository)
        {
            _repository = repository;
        }

        public List<RequisitionVProBadgeLog> GetallBadgeLogs()
        {
            return _repository.GetallBadgeLogs();
        }

        public RequisitionVProBadgeLog GetBadgeLogById(int Id)
        {

            return _repository.GetBadgeLogById(Id);
        }

        public RequisitionVProBadgeLog UpdateBadgeLog(RequisitionVProBadgeLog badgeLog)
        {
            return _repository.UpdateBadgeLog(badgeLog);
        }

        public RequisitionVProBadgeLog DeleteVProBadgeLog(int Id)
        {
            try
            {
                return _repository.DeleteVProBadgeLog(Id);
            }
            catch (Exception ex)
            {
                log.Error($"Error deleting VPro badge record for {Id}", ex);
                return null;
            }
        }

        public RequisitionVProBadgeLog CreateVProBadgeLog(RequisitionVProBadgeLog badgeLog)
        {
            if (badgeLog.Id == 0)
            {
                return _repository.CreateVProBadgeLog(badgeLog);
            }
            return _repository.UpdateBadgeLog(badgeLog);
        }
    }
}
