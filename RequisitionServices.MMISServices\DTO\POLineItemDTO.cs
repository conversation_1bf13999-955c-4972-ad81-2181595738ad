﻿using System;
using RequisitionServices.DomainModel.PurchaseOrders;

namespace RequisitionServices.MMISServices.DTO
{
    public class POLineItemDTO
    {
        public int LineNumber { get; set; }

        public int Department { get; set; }

        public long GLAccountNumber { get; set; }

        public int ItemNumber { get; set; }

        public int STDItemNumber { get; set; }

        public string ItemDescription { get; set; }

        public int QuantityOrdered { get; set; }

        public int QuantityReturned { get; set; }

        public int QuantityReceived { get; set; }

        public int QuantityPaid { get; set; }

        public decimal AmountPaid { get; set; }

        public string Category { get; set; }

        public string Generic { get; set; }

        public string PurchaseUOM { get; set; }

        public string IssueUOM { get; set; }

        public string ReorderNumber { get; set; }

        public int Factor { get; set; }

        public string ComplianceCode { get; set; }

        public decimal UnitCost { get; set; }

        public DateTime ItemReceivedDate { get; set; }

        public decimal TotalCost { get; set; }

        public int RequisitionNumber { get; set; }

        public string LineNotes { get; set; }

        public Boolean Tax { get; set; }

        public Boolean BackorderFlag { get; set; }

        public string BackorderComment { get; set; }

        public int RequisitionItemId { get; set; }

        public string ParentSystemId { get; set; }

        public decimal TotalCreditExpected { get; set; }

        public int CreditExpected { get; set; }

        public int CreditReceived { get; set; }

        public string PARClass { get; set; }

        public string AcquisitionType { get; set; }
        public string EquipmentType { get; set; }
        public byte? LaborWarrantyMonths { get; set; }
        public byte? PartsWarrantyMonths { get; set; }

        public POLineItem MapToPOLineItem()
        {
            return new POLineItem()
            {
                AmountPaid = this.AmountPaid,
                BackorderComment = this.BackorderComment,
                BackorderFlag = this.BackorderFlag,
                Category = this.Category,
                ComplianceCode = this.ComplianceCode,
                Department = this.Department,
                Factor = this.Factor,
                Generic = this.Generic,
                GLAccountNumber = this.GLAccountNumber,
                ItemDescription = this.ItemDescription,
                ItemNumber = this.ItemNumber,
                ItemReceivedDate = this.ItemReceivedDate,
                IssueUOM = this.IssueUOM,
                LineNotes = this.LineNotes,
                LineNumber = this.LineNumber,
                PurchaseUOM = this.PurchaseUOM,
                QuantityOrdered = this.QuantityOrdered,
                QuantityPaid = this.QuantityPaid,
                QuantityReceived = this.QuantityReceived,
                QuantityReturned = this.QuantityReturned,
                ReorderNumber = this.ReorderNumber,
                RequisitionNumber = this.RequisitionNumber,
                STDItemNumber = this.STDItemNumber,
                Tax = this.Tax,
                TotalCost = this.TotalCost,
                UnitCost = this.UnitCost,
                RequisitionItemId = this.RequisitionItemId,
                ParentSystemId = this.ParentSystemId,
                CreditExpected = this.CreditExpected,
                TotalCreditExpected = this.TotalCreditExpected,
                CreditReceived = this.CreditReceived,
                PARClass = this.PARClass,
                AcquisitionType = this.AcquisitionType,
                EquipmentType = this.EquipmentType,
                LaborWarrantyMonths = this.LaborWarrantyMonths,
                PartsWarrantyMonths = this.PartsWarrantyMonths
            };
        }

    }
}