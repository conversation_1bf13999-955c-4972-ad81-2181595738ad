﻿using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.Repositories.Interfaces
{
    /// <summary>
    /// Data access for Digital Sign Off processes
    /// </summary>
    public interface IDigitalSignOffRepository
    {
        /// <summary>
        /// Retrieves single record from RequisitionDigitalSignOffs by RequisitionId
        /// </summary>
        /// <returns>RequisitionDigitalSignOffs</returns>
        RequisitionDigitalSignOff GetDigitalSignOff(int requisitionId);
        /// <summary>
        /// Retrieves All records from RequisitionDigitalSignOff
        /// </summary>
        /// <returns>RequisitionDigitalSignOffs</returns>
        IEnumerable<RequisitionDigitalSignOff> GetAllRequisitionDigitalSignOffsByRequisitionId(int requisitionId);
        /// <summary>
        /// Creates a new record to RequisitionDigitalSignOffs
        /// </summary>
        /// <returns>RequisitionDigitalSignOffs</returns>
        RequisitionDigitalSignOff CreateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff);
        /// <summary>
        /// Updates the record from RequisitionDigitalSignOffs by digital sign off Id
        /// </summary>
        /// <returns></returns>
        void UpdateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff, int digitalSignOffId);
        /// <summary>
        /// Soft deletes single record from RequisitionDigitalSignOffs by digital sign off Id
        /// </summary>
        /// <returns></returns>
        void DeleteRequisitionDigitalSignOff(int digitalSignOffId);
        /// <summary>
        /// Soft deletes single record from RequisitionDigitalSignOffs by digital sign off Id when recalled
        /// </summary>
        /// <returns></returns>
        void DeleteRequisitionDigitalSignOffOnRecall(int digitalSignOffId, int requisitionId);
        /// <summary>
        /// Retrieves All records from DigitalSignOffUsers
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        IEnumerable<DigitalSignOffUser> GetAllDigitalSignOffUsers();

        /// <summary>
        /// Adds record to DB
        /// </summary>
        /// <returns>single GetSingleDigitalSignOffUser Record</returns>
        DigitalSignOffUser CreateDigitalSignOffUser(DigitalSignOffUser request);
        /// <summary>
        /// Gets singe user record based off 3/4 ID
        /// </summary>
        /// <returns>single GetSingleDigitalSignOffUser Record</returns>
        DigitalSignOffUser GetSingleDigitalSignOffUser(string accountName);

        /// <summary>
        /// Updates record based off of ID thats passed after finding a discrepancy in AD
        /// </summary>
        /// <returns>single GetSingleDigitalSignOffUser Record</returns>
        void UpdateSingleDigitalSignOffUser(DigitalSignOffUser request);
        /// <summary>
        /// Returns data specifically needed for the DSO Approver
        /// </summary>
        /// <returns>single GetSingleDigitalSignOffUser Record</returns>
        ApproverDigitalSignOffDTO GetApproverDigitalSignOff(int requisitionId);
    }
}
