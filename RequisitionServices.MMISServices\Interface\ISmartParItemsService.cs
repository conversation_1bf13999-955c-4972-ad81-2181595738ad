﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartParItemsService
    {
        IEnumerable<ParItemDetails> GetParItemDetails(string userName, string COID, int departmentId, string parId);
        ParHeaderModel GetParByParId(string userId, string coid, int parDepartment, string parClass);
        ParItemModel GetParItem(string userId, string coid, int parDepartment, string parClass, string itemId);
    }
}
