﻿/* IE specific css */
.stripe .row-fluid [class*='span'] {
    min-height:10px;
}

.pageWhiteTitleBar {
    margin-bottom:0px;
}

/*-----------------MENU----------------*/
.menuNav, .errorMenuNav
{
	border-top: 1px solid #28496b;
}

/*--------------SEARCH BOX--------------*/
.searchBox
{
    margin-top:7px;
}

.advancedSearchLink {
    margin-top:18px;
}

/*-----------TEXTBOX FILTER-----------*/
#filter
{
    padding-bottom:0px;
}

a:focus
{
	outline: none;
	color: #0069aa;
	text-decoration: none;
}

.btn:focus
{
	outline: none;
	color: white;
}

h2 a:hover
{
	border-bottom: 0px dotted #0069aa;
	text-decoration: underline;
}

.smallLabel
{
	margin-bottom: 0px;
}

.orange, .btn.orange:hover
{
	padding: 4px 14px;
}

/* MARKETING CHANGES */
body
{
	line-height: normal;
}

.menuNav
{
	border-top: none;
}

#topSubMenu .nav-pills li a:hover
{
	padding: 4px 8px;
}

#topSubMenu .nav-pills > .active > a,
#topSubMenu .nav-pills > .active > a:hover,
#topSubMenu .nav-pills > .active > a:focus
{
	padding: 4px 8px;
}

#throbber
{
	margin-bottom: 2px;
}

.btn-link
{
    filter: none;
}

.btn-link:hover {
    filter: none;
}

.controls input[type='text'] {
   padding-bottom:0px;
   padding-top:0px;
}

.contentTitleMargin {
    margin-top:0px;
}
