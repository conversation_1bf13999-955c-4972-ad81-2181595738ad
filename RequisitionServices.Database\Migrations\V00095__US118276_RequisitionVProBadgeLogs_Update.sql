USE [eProcurementQA]
GO

ALTER TABLE RequisitionVProBadgeLogs
ADD CreateDate datetime NULL,
    LastModifiedDate datetime NULL;
GO

ALTER TABLE [dbo].[RequisitionVProBadgeLogs] ADD  DEFAULT (getdate()) FOR [CreateDate]
GO

ALTER TABLE [dbo].[RequisitionVProBadgeLogs] ADD  DEFAULT (getdate()) FOR [LastModifiedDate]
GO


CREATE TRIGGER tgr_UpdateLastModifiedDateOnUpdate
ON RequisitionVProBadgeLogs
AFTER UPDATE
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE RequisitionVProBadgeLogs
    SET LastModifiedDate = GETDATE()
    WHERE Id IN (SELECT DISTINCT Id FROM Inserted);
END;
GO