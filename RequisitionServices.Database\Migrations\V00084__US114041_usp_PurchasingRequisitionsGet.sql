USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_PurchasingRequisitionsGet]    Script Date: 9/25/2024 2:15:38 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_PurchasingRequisitionsGet] 
@coids [dbo].[CoidList] READONLY,
@vendors [IdTemplate] READONLY,
@buyers [dbo].[Varchar50template] READONLY,
@reqTypes [dbo].[IdTemplate] READONLY,
@filterText varchar(140) = NULL,
@pageNumber INT = 1,
@pageSize INT = 25,
@sortColumn VARCHAR(128) = NULL,
@sortDirection VARCHAR(4) = NULL
AS
BEGIN
    -- Set default sorting if no sorting parameters are provided
    IF @sortColumn IS NULL OR @sortDirection IS NULL
    BEGIN
        SET @sortColumn = 'RequisitionID';
        SET @sortDirection = 'ASC';
    END

	DECLARE @skip INT = (@pageNumber - 1) * @pageSize;
	DECLARE @take INT = @pageSize;

	WITH RequisitionsByCoid AS (
	SELECT
			[Requisition].[RequisitionId] AS [RequisitionId],
			[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[Requisition].[LocationIdentifier] AS [LocationIdentifier],
			[Requisition].[Comments] AS [Comments],
			[Requisition].[CreatedBy] AS [CreatedBy],
			[Requisition].[CreateDate] AS [CreateDate],
			[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
			[Requisition].[IsMobile] AS [IsMobile],
			[Requisition].[IsVendor] AS [IsVendor],
			[Requisition].[RequisitionSubmissionTypeId]
			FROM
			[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
			WHERE
			[Requisition].[RequisitionStatusTypeId] NOT IN (1, 5, 8, 12)
			AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) in (SELECT DISTINCT Coid FROM @coids)
	), 
	RequisitionDetails AS (
	SELECT 	[Req].[RequisitionId] AS [RequisitionId],
			[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[ReqStatus].[Description] AS [ReqStatusDescription],
			[Req].[LocationIdentifier] AS [LocationIdentifier],
			[Req].[Comments] AS [Comments],
			[Req].[CreatedBy] AS [CreatedBy],
			[Req].[CreateDate] AS [CreateDate],
			[Req].[RequisitionTypeId] AS [RequisitionTypeId],
			[Req].[IsMobile] AS [IsMobile],
			[Req].[IsVendor] AS [IsVendor],
			[Req].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
			[ReqSubmissionTypes].[Description] AS ReqSubmissionTypeDescription,
			[User].[FirstName] AS [FirstName],
			[User].[LastName] AS [LastName],
			[ReqItem].[Id] AS [RequisitionItemId],
			[ReqItemStatus].[Description] AS ReqItemStatusDescription,
			[ReqItem].[ItemId] AS [RequisitionItemNumber],
			[ReqItem].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
			[ReqItem].[ParentSystemId] AS [RequisitionItemParentSystemId],
			[ReqItem].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
			[ReqItem].[PONumber] AS [RequisitionItemPONumber],
			[ReqItem].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
			[ReqItem].[ParIdentifier] AS [RequisitionItemParIdentifier],
			[ReqItem].[Discount] AS [Discount],
			[ReqItem].[VendorId] AS [VendorId],
			[ReqItem].[UnitCost] AS [UnitCost],
			[ReqItem].[QuantityToOrder] AS [QuantityToOrder],
			[ReqItem].[PONumber],
			[ReqItem].[ParentSystemId],
			[ReqItem].[OriginalParentSystemId],
			[ReqItem].[ParIdentifier],
			[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
			[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
			[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
			[ReqItemFileAttachments].[RequisitionItemId] as [FileAttachmentItemId],
	--[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
	[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
	FROM RequisitionsByCoid  AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionSubmissionTypes] AS [ReqSubmissionTypes] WITH (NOLOCK) ON [Req].[RequisitionSubmissionTypeId] = [ReqSubmissionTypes].[Id]
	LEFT OUTER JOIN [dbo].[Users] AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [ReqItemSprDetails].[RequisitionItemId]
	LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [ReqItem].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [ReqItem].[Id] = [VboHoldItemConversion].[RequisitionItemId]
	WHERE (NOT EXISTS (select 1 from @vendors) OR ([ReqItem].[VendorId] IN (SELECT Id FROM @vendors) OR [ReqItemSprDetails].[VendorId] IN (SELECT Id FROM @vendors)))
			AND (NOT EXISTS (select 1 from @buyers) OR SUBSTRING(Req.CreatedBy, CHARINDEX('/', Req.CreatedBy) + 1, LEN(Req.CreatedBy)) IN (SELECT VarcharVal FROM @buyers))
	        AND (NOT EXISTS (select 1 from @reqTypes) OR [Req].[RequisitionTypeId] IN (SELECT Id FROM @reqTypes))
	),
	TotalCountCTE AS (  -- New CTE to calculate total count
	    SELECT COUNT(*) AS TotalRecords
	    FROM RequisitionDetails
	)

	select *,
		(SELECT TotalRecords FROM TotalCountCTE) AS TotalRecords
	from RequisitionDetails
	WHERE @filterText IS NULL 
			OR([RequisitionId] LIKE '%' + @filterText + '%'
				OR ([FirstName] + ' ' + [LastName]) LIKE '%' + @filterText + '%'
				OR [ReqStatusDescription] LIKE '%' + @filterText + '%'
				OR [ReqSubmissionTypeDescription] LIKE '%' + @filterText + '%'
				OR [ReqItemStatusDescription] LIKE '%' + @filterText + '%'
				OR [Comments] LIKE '%' + @filterText + '%'
				OR [PONumber] LIKE '%' + @filterText + '%'
				OR [ParentSystemId] LIKE '%' + @filterText + '%'
				OR [OriginalParentSystemId] LIKE '%' + @filterText + '%'
				OR [LocationIdentifier] LIKE '%' + @filterText + '%'
				OR (@filterText != 'EPR' AND [ParIdentifier] LIKE '%' + @filterText + '%')
			)
	ORDER BY 
		CASE WHEN @sortColumn = 'LocationIdentifier' AND @sortDirection = 'ASC' THEN [LocationIdentifier] end asc,
		CASE WHEN @sortColumn = 'VendorNumber' AND @sortDirection = 'ASC' THEN [SprDetailsVendorId] end asc,
		CASE WHEN @sortColumn = 'VendorName' AND @sortDirection = 'ASC' THEN [SprDetailsVendorName] end asc,
		CASE WHEN @sortColumn = 'Date' AND @sortDirection = 'ASC' THEN [CreateDate] end asc,
		CASE WHEN @sortColumn = 'RequisitionTypeId' AND @sortDirection = 'ASC' THEN [RequisitionTypeId] end asc,
		CASE WHEN @sortColumn = 'FileAttachmentItemId' AND @sortDirection = 'ASC' THEN [FileAttachmentItemId] end asc,
		CASE WHEN @sortColumn = 'RequisitionID' AND @sortDirection = 'ASC' THEN [RequisitionId] end asc,
		CASE WHEN @sortColumn = 'LocationIdentifier' AND @sortDirection = 'DESC' THEN [LocationIdentifier] end desc,
		CASE WHEN @sortColumn = 'VendorNumber' AND @sortDirection = 'DESC' THEN [SprDetailsVendorId] end desc,
		CASE WHEN @sortColumn = 'VendorName' AND @sortDirection = 'DESC' THEN [SprDetailsVendorName] end desc,
		CASE WHEN @sortColumn = 'Date' AND @sortDirection = 'DESC' THEN [CreateDate] end desc,
		CASE WHEN @sortColumn = 'RequisitionTypeId' AND @sortDirection = 'DESC' THEN [RequisitionTypeId] end desc,
		CASE WHEN @sortColumn = 'FileAttachmentItemId' AND @sortDirection = 'DESC' THEN [FileAttachmentItemId] end desc,
		CASE WHEN @sortColumn = 'RequisitionID' AND @sortDirection = 'DESC' THEN [RequisitionId] end desc
	OFFSET @skip ROWS 
	FETCH NEXT @take ROWS ONLY;
END

GO


