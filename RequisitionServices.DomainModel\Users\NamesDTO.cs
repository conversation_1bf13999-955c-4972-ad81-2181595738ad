﻿namespace RequisitionServices.DomainModel.Users
{
    public class NamesDTO
    {
        // Backing field for AccountName to ensure it is always lowercase
        private string _accountName;
        
        public string AccountName 
        {
            get => _accountName;
            set => _accountName = value.ToLower();
        }

        public string FirstName { get; set; }

        public string LastName { get; set; }
    }
}
