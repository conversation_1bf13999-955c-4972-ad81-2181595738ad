﻿using System;
using System.Net.Http;
using System.Net.Http.Headers;

namespace eProcurementServices.Utility.WebApi
{
    static class ApiFactory
    {
        internal static HttpClient GetClient(string apiUrl, bool useDefaultCredentials)
        {
            var client = new HttpClient(new HttpClientHandler { UseDefaultCredentials = useDefaultCredentials }) { BaseAddress = new Uri(apiUrl) };
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            return client;
        }
    }
}
