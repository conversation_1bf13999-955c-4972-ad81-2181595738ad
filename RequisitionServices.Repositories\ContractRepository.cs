﻿using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.Contracts;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;

namespace RequisitionServices.Repositories
{
    public class ContractRepository : AbstractRepository, IContractRepository
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        public ContractRepository(EProcurementContext context) : base(context) { }

        public ContractReportResults GetAllContracts(int rowOffset, int pageSize, string sortOrder, string filterText)
        {
            SqlParameter[] sqlParams = new SqlParameter[4];

            sqlParams[0] = new SqlParameter("@rowOffset", rowOffset);
            sqlParams[0].DbType = System.Data.DbType.Int32;

            sqlParams[1] = new SqlParameter("@pageSize", pageSize);
            sqlParams[1].DbType = System.Data.DbType.Int32;

            sqlParams[2] = filterText == null ? new SqlParameter("@filterText", DBNull.Value) : new SqlParameter("@filterText", filterText);
            sqlParams[2].DbType = System.Data.DbType.String;

            sqlParams[3] = new SqlParameter("@sortingOption", 0);
            sqlParams[3].DbType = System.Data.DbType.Int32;
            switch (sortOrder.ToLower())
            {
                case "contractid":
                    sqlParams[3].Value = 0;
                    break;
                case "type":
                    sqlParams[3].Value = 1;
                    break;
                case "class":
                    sqlParams[3].Value = 2;
                    break;
                default:
                    sqlParams[3].Value = 0;
                    break;
            }

            var rowsReturned = context.Database.SqlQuery<ContractPageRow>("exec dbo.usp_ContractsGet @rowOffset, @pageSize, @filterText, @sortingOption", sqlParams).ToList();

            var allContracts = new ContractReportResults();

            if (rowsReturned.Any())
            {
                foreach (var row in rowsReturned)
                {
                    allContracts.Contracts.Add(new Contract(row));
                }
                allContracts.TotalCount = rowsReturned.FirstOrDefault().TotalCount;
            }

            return allContracts;

        }

        public ContractReportResults GetContractsById(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText)
        {
            SqlParameter[] sqlParams = new SqlParameter[5];

            sqlParams[0] = new SqlParameter("@rowOffset", rowOffset);
            sqlParams[0].DbType = System.Data.DbType.Int32;

            sqlParams[1] = new SqlParameter("@pageSize", pageSize);
            sqlParams[1].DbType = System.Data.DbType.Int32;

            sqlParams[2] = filterText == null ? new SqlParameter("@filterText", DBNull.Value) : new SqlParameter("@filterText", filterText);
            sqlParams[2].DbType = System.Data.DbType.String;

            sqlParams[3] = new SqlParameter("@sortingOption", 0);
            sqlParams[3].DbType = System.Data.DbType.Int32;
            switch (sortOrder.ToLower())
            {
                case "contractid":
                    sqlParams[3].Value = 0;
                    break;
                case "type":
                    sqlParams[3].Value = 1;
                    break;
                case "class":
                    sqlParams[3].Value = 2;
                    break;
                default:
                    sqlParams[3].Value = 0;
                    break;
            }

            sqlParams[4] = new SqlParameter("@searchText", searchText);
            sqlParams[4].DbType = System.Data.DbType.String;

            var rowsReturned = context.Database.SqlQuery<ContractPageRow>("exec dbo.usp_ContractsGetByContractId @rowOffset, @pageSize, @filterText, @sortingOption, @searchText", sqlParams).ToList();


            var allContracts = new ContractReportResults();

            if (rowsReturned.Any())
            {
                foreach (var row in rowsReturned)
                {
                    allContracts.Contracts.Add(new Contract(row));
                }
                allContracts.TotalCount = rowsReturned.FirstOrDefault().TotalCount;
            }

            return allContracts;
        }

        public ContractReportResults GetContractsByVendor(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText)
        {
            SqlParameter[] sqlParams = new SqlParameter[5];

            sqlParams[0] = new SqlParameter("@rowOffset", rowOffset);
            sqlParams[0].DbType = System.Data.DbType.Int32;

            sqlParams[1] = new SqlParameter("@pageSize", pageSize);
            sqlParams[1].DbType = System.Data.DbType.Int32;

            sqlParams[2] = filterText == null ? new SqlParameter("@filterText", DBNull.Value) : new SqlParameter("@filterText", filterText);
            sqlParams[2].DbType = System.Data.DbType.String;

            sqlParams[3] = new SqlParameter("@sortingOption", 0);
            sqlParams[3].DbType = System.Data.DbType.Int32;
            switch (sortOrder.ToLower())
            {
                case "contractid":
                    sqlParams[3].Value = 0;
                    break;
                case "type":
                    sqlParams[3].Value = 1;
                    break;
                case "class":
                    sqlParams[3].Value = 2;
                    break;
                default:
                    sqlParams[3].Value = 0;
                    break;
            }

            sqlParams[4] = new SqlParameter("@searchText", searchText);
            sqlParams[4].DbType = System.Data.DbType.String;

            var rowsReturned = context.Database.SqlQuery<ContractPageRow>("exec dbo.usp_ContractsGetByVendorNumber @rowOffset, @pageSize, @filterText, @sortingOption, @searchText", sqlParams).ToList();

            var allContracts = new ContractReportResults();

            if (rowsReturned.Any())
            {
                foreach (var row in rowsReturned)
                {
                    allContracts.Contracts.Add(new Contract(row));
                }
                allContracts.TotalCount = rowsReturned.FirstOrDefault().TotalCount;
            }

            return allContracts;
        }
    }
}
