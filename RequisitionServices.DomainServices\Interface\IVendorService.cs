﻿using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IVendorService
    {
        IEnumerable<PunchOutVendor> GetPunchoutVendors(Facility facility);

        Vendor GetVendordByVendorId(string COID, int vendorId);

        VendorDetails GetVendorDetailsByVendorId(string COID, int vendorId);

        List<VendorHeaderInfo> GetAllVendorsForCoid(string COID);

        List<Vendor> GetAllVendorsForSpecificCoid(string COID);

        Vendor GetVendorInformationById(string COID, int vendorId);
    }
}
