﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.DigitalSignOff
{
        /// <summary>
        /// Holds the data passed from the DSO user
        /// Frontend User input
        /// </summary>
    public class ActiveDirectoryValidation    {
        /// <summary>
        /// 3/4 for signer
        /// </summary>
        public string Username { get; set; }
        /// <summary>
        /// First name for signer
        /// </summary>
        public string FirstName { get; set; }
        /// <summary>
        /// Last name for signer
        /// </summary>
        public string LastName { get; set; }
        /// <summary>
        /// Domain to be called for DSO info
        /// </summary>
        public List<string> Domain { get; set; }
    }
}