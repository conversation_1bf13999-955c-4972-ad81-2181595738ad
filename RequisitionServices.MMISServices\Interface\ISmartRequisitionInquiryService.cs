﻿using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartRequisitionInquiryService
    {
        LegacyRequisitionReportDTO GetRequisitionCollectionByCreateDate(LegacyRequisitionReportRequestDTO request);

        Requisition GetRequisitionById(string userName, int reqId, string coid, string countryCode);
        
     
    }
}
