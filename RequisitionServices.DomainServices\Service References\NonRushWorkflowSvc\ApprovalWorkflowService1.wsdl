<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:i0="http://local-approvalworkflow.healthtrustpg.com/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ApprovalWorkflowService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:import namespace="http://local-approvalworkflow.healthtrustpg.com/" location="http://local-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?wsdl=wsdl0" />
  <wsdl:types />
  <wsdl:binding name="BasicHttpBinding_INonRushApprovalWorkflow" type="i0:INonRushApprovalWorkflow">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="StartNonRushApproval">
      <soap:operation soapAction="http://local-approvalworkflow.healthtrustpg.com/INonRushApprovalWorkflow/StartNonRushApproval" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ApproveNonRushRequisition">
      <soap:operation soapAction="http://local-approvalworkflow.healthtrustpg.com/INonRushApprovalWorkflow/ApproveNonRushRequisition" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ApprovalWorkflowService">
    <wsdl:port name="BasicHttpBinding_INonRushApprovalWorkflow" binding="tns:BasicHttpBinding_INonRushApprovalWorkflow">
      <soap:address location="http://local-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>