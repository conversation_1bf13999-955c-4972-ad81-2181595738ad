USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_ApprovalHistoryGet]    Script Date: 3/21/2025 1:51:13 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
***************************************************************************
Database	: eProcurement
Name		: usp_ApprovalHistoryGet
Purpose		: Returns a paginated list of requisition history items for the MyApprovals page.
Used By		: SMART Procurement team
Author		: <PERSON>
Created		: 11-20-2017
Usage		: Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Peter Hurlburt		11/20/2017		Script created
Peter Hurlburt		11/28/2017		Submitted for deployment 24
Peter Hurlburt		12/15/2017		Removed an overly-verbose union statement
									Submitted for deployment 24
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		10/27/2021		Rewriting the sproc to optimize the query
									Remove restriction of only showing histories
									since most recent Requisition draft status
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Farhan Khan			07/14/2022		Added a condition to limit the approval history
									records for last 12 months
Peter Hurlburt		08/15/2022		Optimizing GROUP BY sections of the SQL, and
									removing 12 month limit
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs
Jeremiah King		05/16/2024		Adding RequisitionSubmissionTypeId to create DSO tag
									 and use for filtering for Approval History
Jeremiah King		03/21/2025		Updated how value for PendingReviewsExits gets 
									 added and returned in the results properly. Fixes
									 the sort functionality for the My Approvals History.
***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_ApprovalHistoryGet]
    @userName varchar(100),
    @rowOffset int,
    @pageSize int,
    @filterText varchar(140),
    @reqTypeIdGroup [dbo].[IdTemplate] READONLY,
    @statusSorting bit,
    @oldestFirst bit,
    @vboFirst bit,
    @facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
    @departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @historyList TABLE
(
    RowOrder INT NOT NULL,
    HistoryItemId INT NOT NULL,
    HistoryItemCreateDate DATETIME NOT NULL,
    HistoryItemStatusTypeId INT NOT NULL,
    RequisitionId INT NOT NULL,
    IsVendor BIT,
    RequisitionSubmissionTypeId INT,
    PatientId VARCHAR(50),
    PendingReviewsExist BIT
)
DECLARE @tempReqTypeIds TABLE (Id INT)
DECLARE @totalhistoryCount BIGINT

INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup

INSERT INTO @historyList
SELECT
    ROW_NUMBER() OVER
    (
        ORDER BY SortOrder,
            CASE @oldestFirst WHEN 1 THEN HistoryItemCreateDate END ASC,
            CASE @vboFirst WHEN 1 THEN IsVendor END DESC,
            CASE @oldestFirst WHEN 0 THEN HistoryItemCreateDate END DESC
    ) AS RowOrder,
    HistoryItemId,
    HistoryItemCreateDate,
    HistoryItemStatusTypeId,
    RequisitionId,
    IsVendor,
    RequisitionSubmissionTypeId,
    PatientId,
    PendingReviewsExist
FROM
(
    SELECT
        CASE @statusSorting
            WHEN 1 THEN CASE requisitionHistory.RequisitionStatusTypeId
                WHEN 6 THEN 3
                WHEN 3 THEN 2
                ELSE review.Recommended END
            ELSE CASE
                WHEN requisition.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
                THEN 1
                ELSE 2
                END
            END AS SortOrder,
        CASE requisitionHistory.RequisitionStatusTypeId
            WHEN 10 THEN CASE review.Recommended
                WHEN 1 THEN 'Recommend Approve'
                ELSE 'Recommend Deny'
                END
            ELSE
                requisitionStatus.[Description]
            END AS StatusForFilter,
		CASE
            WHEN review.Id IS NOT NULL 
			AND review.Recommended IS NULL
			THEN 1
            ELSE 0
        END AS PendingReviewsExist,
        requisitionHistory.Id AS HistoryItemId,
        requisitionHistory.CreateDate AS HistoryItemCreateDate,
        requisitionHistory.RequisitionStatusTypeId AS HistoryItemStatusTypeId,
        requisitionHistory.RequisitionId AS RequisitionId,
        requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
        requisition.LocationIdentifier AS RequisitionLocationIdentifier,
        requisition.Comments AS RequisitionComments,
        requisition.RequisitionTypeId AS RequisitionTypeId,
        requisition.CreatedBy AS RequisitionerId,
        reqItem.ParIdentifier AS RequisitionItemParIdentifier,
        requisitioner.FirstName AS RequisitionerFirstName,
        requisitioner.LastName AS RequisitionerLastName,
        reviewRequester.AccountName AS RequesterId,
        reviewRequester.FirstName AS RequesterFirstName,
        reviewRequester.LastName AS RequesterLastName,
        review.RequesterComments AS RequesterComments,
        review.Recommended AS ReviewerRecommended,
        review.ReviewerComments AS ReviewerComments,
        requisition.IsVendor,
        requisitionSubmissionTypes.Description,
        requisition.RequisitionSubmissionTypeId,
        PatientDetails.PatientId
    FROM RequisitionStatusHistories requisitionHistory WITH (NOLOCK)
    INNER JOIN RequisitionStatusTypes requisitionStatus WITH (NOLOCK)
        ON requisitionHistory.RequisitionStatusTypeId = requisitionStatus.Id
    INNER JOIN Requisitions requisition WITH (NOLOCK)
        ON requisitionHistory.RequisitionId = requisition.RequisitionId
    INNER JOIN Users requisitioner WITH (NOLOCK)
        ON requisition.CreatedBy = requisitioner.AccountName
    LEFT OUTER JOIN AdhocReviews review WITH (NOLOCK)
        ON requisitionHistory.RequisitionId = review.RequisitionId
		AND review.Recommended IS NULL
    LEFT OUTER JOIN Users reviewRequester WITH (NOLOCK)
        ON review.Requester = reviewRequester.AccountName
    LEFT OUTER JOIN RequisitionItems AS reqItem WITH (NOLOCK)
        ON Requisition.RequisitionId = reqItem.RequisitionId
    LEFT OUTER JOIN ClinicalUseDetails AS PatientDetails WITH (NOLOCK)
        ON PatientDetails.RequisitionItemId = reqItem.Id
    LEFT OUTER JOIN RequisitionSubmissionTypes AS requisitionSubmissionTypes WITH (NOLOCK)
        ON requisition.requisitionSubmissionTypeId = requisitionSubmissionTypes.Id
    WHERE requisitionHistory.CreatedBy = @userName
    AND requisitionHistory.RequisitionStatusTypeId IN (3, 6, 10) --Approved, Denied, Review Provided
) AS HistoryRecord
WHERE @filterText IS NULL OR
    (HistoryRecord.[RequisitionId] LIKE '%' + @filterText + '%'
    OR HistoryRecord.[RequisitionerId] LIKE '%' + @filterText + '%'
    OR (HistoryRecord.[RequisitionerFirstName] + ' ' + HistoryRecord.[RequisitionerLastName]) LIKE '%' + @filterText + '%'
    OR HistoryRecord.[RequesterId] LIKE '%' + @filterText + '%'
    OR (HistoryRecord.[RequesterFirstName] + ' ' + HistoryRecord.[RequesterLastName]) LIKE '%' + @filterText + '%'
    OR HistoryRecord.[RequisitionComments] LIKE '%' + @filterText + '%'
    OR HistoryRecord.[Description] LIKE '%' + @filterText + '%'
    OR HistoryRecord.[RequesterComments] LIKE '%' + @filterText + '%'
    OR HistoryRecord.[ReviewerComments] LIKE '%' + @filterText + '%'
    OR HistoryRecord.[StatusForFilter] LIKE '%' + @filterText + '%'
    OR RequisitionLocationIdentifier LIKE '%' + @filterText + '%'
    OR HistoryRecord.PatientId LIKE @filterText + '%'
    OR (@filterText != 'EPR' AND RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
    OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
    OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
GROUP BY
    SortOrder,
    HistoryItemId,
    HistoryItemCreateDate,
    HistoryItemStatusTypeId,
    RequisitionId,
    IsVendor,
    RequisitionSubmissionTypeId,
    PatientId,
	PendingReviewsExist

SELECT @totalHistoryCount = COUNT(*) FROM @historyList

SELECT
    orderedHistory.HistoryItemId,
    orderedHistory.HistoryItemCreateDate,
    orderedHistory.HistoryItemStatusTypeId,
    orderedHistory.RequisitionId,
    orderedHistory.RequisitionStatusTypeId,
    orderedHistory.RequisitionLocationIdentifier,
    orderedHistory.RequisitionComments,
    orderedHistory.RequisitionTypeId,
    item.Id AS RequisitionItemId,
    item.RequisitionItemStatusTypeId,
    item.QuantityToOrder AS RequisitionItemQuantityToOrder,
    item.ParentSystemId AS RequisitionItemParentSystemId,
    item.ParIdentifier AS RequisitionItemParIdentifier,
    SPR.VendorId AS SprDetailsVendorId,
    SPR.VendorName AS SprDetailsVendorName,
    SPR.PartNumber AS SprDetailsPartNumber,
    SPR.EstimatedPrice AS SprDetailsEstimatedPrice,
    ReqItemFileAttachments.RequisitionItemId AS SprDetailsFileAttachment,
    orderedHistory.RequisitionerId,
    orderedHistory.RequisitionerFirstName,
    orderedHistory.RequisitionerLastName,
    orderedHistory.RequesterId,
    orderedHistory.RequesterFirstName,
    orderedHistory.RequesterLastName,
    orderedHistory.RequesterComments,
    orderedHistory.ReviewerRecommended,
    orderedHistory.ReviewerComments,
    orderedHistory.IsVendor,
    orderedHistory.RequisitionSubmissionTypeId,
	orderedHistory.PendingReviewsExist,
    @totalhistoryCount AS TotalReqCount,
    VboHoldItemConversion.UnitCost AS VboHoldItemConversionUnitCost
FROM
(
    SELECT
        filteredHistory.HistoryItemId,
        filteredHistory.HistoryItemCreateDate,
        filteredHistory.HistoryItemStatusTypeId,
        filteredHistory.RequisitionId,
        filteredHistory.IsVendor,
        filteredHistory.RequisitionSubmissionTypeId,
        filteredHistory.PendingReviewsExist,
        requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
        requisition.LocationIdentifier AS RequisitionLocationIdentifier,
        requisition.Comments AS RequisitionComments,
        requisition.RequisitionTypeId AS RequisitionTypeId,
        requisition.CreatedBy AS RequisitionerId,
        requisitioner.FirstName AS RequisitionerFirstName,
        requisitioner.LastName AS RequisitionerLastName,
        reviewRequester.AccountName AS RequesterId,
        reviewRequester.FirstName AS RequesterFirstName,
        reviewRequester.LastName AS RequesterLastName,
        review.RequesterComments AS RequesterComments,
        review.Recommended AS ReviewerRecommended,
        review.ReviewerComments AS ReviewerComments
    FROM @historyList filteredHistory
    INNER JOIN Requisitions requisition WITH (NOLOCK)
        ON filteredHistory.RequisitionId = requisition.RequisitionId
    INNER JOIN Users requisitioner WITH (NOLOCK)
        ON requisition.CreatedBy = requisitioner.AccountName
    LEFT OUTER JOIN AdhocReviews review WITH (NOLOCK)
        ON filteredHistory.HistoryItemId = review.ReviewerRequisitionStatusHistoryId
    LEFT OUTER JOIN Users reviewRequester WITH (NOLOCK)
        ON review.Requester = reviewRequester.AccountName
    LEFT OUTER JOIN RequisitionSubmissionTypes requisitionSubmissionTypes WITH (NOLOCK)
        ON requisition.RequisitionSubmissionTypeId = requisitionSubmissionTypes.Id
    ORDER BY filteredHistory.RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS orderedHistory
LEFT OUTER JOIN RequisitionItems item WITH (NOLOCK)
    ON orderedHistory.RequisitionId = item.RequisitionId
LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
    ON item.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) ReqItemFileAttachments
    ON item.Id = ReqItemFileAttachments.RequisitionItemId
LEFT OUTER JOIN VboHoldItemConversions AS VboHoldItemConversion
    ON item.Id = VboHoldItemConversion.RequisitionItemId
END
GO


