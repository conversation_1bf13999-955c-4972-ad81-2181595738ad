﻿using log4net;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    public class SmartIINItemService : ISmartIINItemService
    {
        private const string getIINItemMethod = "IINItem/GetIINItemById";

        private ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IINItemRecordModel GetIINItemById(string userId, string COID, string IINitemNumber)
        {
            IINItemRecordModel IINItem = new IINItemRecordModel();
            List<FStoreDeptModel> FStore = new List<FStoreDeptModel>();

            if (String.IsNullOrWhiteSpace(userId))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userId.IndexOf('/') != -1)
            {
                userId = userId.Split('/')[1];
            }

            int intCOID;
            if (!Int32.TryParse(COID, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", COID));
            }

            try
            {
                SmartInputValidator.CheckItemId(IINitemNumber);
            }
            catch
            {
                return null;
            }

            try
            {
                var IINItemRecord = ApiUtility.ExecuteApiGetTo<IINItemRecordDTO>(endpoint, getIINItemMethod, new Dictionary<string, string>()
                {
                    { "userId", userId },
                    { "COID", COID },
                    { "IINitemNumber", IINitemNumber }
                });
            
                if (IINItemRecord != null)
                {
                    IINItem =  IINItemRecord.MapToIINItem();
                }

                if (IINItemRecord.FStore != null)
                {
                    foreach (var fstoreitem in IINItemRecord.FStore)
                    {
                        FStore.Add(fstoreitem.MapToFStoreItem());
                    }
                    IINItem.FStore = FStore;
                }

                return IINItem;
            }
            catch (Exception ex)
            {
                Log.Error(string.Format("Error on SMARTIInItemService GetIINItemByID with coid:{0}, itemNumber:{1}", COID, IINitemNumber), ex);
                return null;
            }
        }
    }
}
