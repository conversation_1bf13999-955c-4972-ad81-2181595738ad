﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{79F7E2EE-74BC-4854-B2D2-E9B5035AC1EE}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RequisitionServices.Repositories</RootNamespace>
    <AssemblyName>RequisitionServices.Repositories</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\QA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <OutputPath>bin\Production\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <OutputPath>bin\Sandbox\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <OutputPath>bin\QASandbox\</OutputPath>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.2\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.BulkInsert">
      <HintPath>..\packages\EntityFramework6.BulkInsert.6.0.3.10\lib\net45\EntityFramework.BulkInsert.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.2\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AbstractRepository.cs" />
    <Compile Include="AdhocReviewRepository.cs" />
    <Compile Include="AuditRepository.cs" />
    <Compile Include="BillOnlyReviewRepository.cs" />
    <Compile Include="CartRepository.cs" />
    <Compile Include="Interfaces\IVProRepository.cs" />
    <Compile Include="VProRepository.cs" />
    <Compile Include="CommentRepository.cs" />
    <Compile Include="ConnectionFactory.cs" />
    <Compile Include="ContractRepository.cs" />
    <Compile Include="DigitalSignOffRepository.cs" />
    <Compile Include="FacilityWorkflowRepository.cs" />
    <Compile Include="Interfaces\IBillOnlyReviewRepository.cs" />
    <Compile Include="IConnectionFactory.cs" />
    <Compile Include="Interfaces\IAdhocReviewRepository.cs" />
    <Compile Include="Interfaces\IAuditRepository.cs" />
    <Compile Include="Interfaces\ICartRepository.cs" />
    <Compile Include="Interfaces\ICommentRepository.cs" />
    <Compile Include="Interfaces\IContractRepository.cs" />
    <Compile Include="Interfaces\IFacilityWorkflowRepository.cs" />
    <Compile Include="Interfaces\IDigitalSignOffRepository.cs" />
    <Compile Include="Interfaces\ISystemNotificationRepository.cs" />
    <Compile Include="Interfaces\IUserAlertMessageRepository.cs" />
    <Compile Include="Interfaces\IUserRepository.cs" />
    <Compile Include="Interfaces\IVendorRepository.cs" />
    <Compile Include="Interfaces\IViraRepository.cs" />
    <Compile Include="Interfaces\IWorkflowRepository.cs" />
    <Compile Include="Interfaces\IConfigurationRepository.cs" />
    <Compile Include="Interfaces\IRequisitionStatusRepository.cs" />
    <Compile Include="Interfaces\IRequisitionRepository.cs" />
    <Compile Include="Interfaces\ITypeRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RequisitionStatusRepository.cs" />
    <Compile Include="RequisitionRepository.cs" />
    <Compile Include="SystemNotificationRepository.cs" />
    <Compile Include="UserAlertMessageRepository.cs" />
    <Compile Include="VendorRepository.cs" />
    <Compile Include="UserRepository.cs" />
    <Compile Include="ViraRepository.cs" />
    <Compile Include="WorkflowRepository.cs" />
    <Compile Include="ConfigurationRepository.cs" />
    <Compile Include="TypeRepository.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RequisitionServices.Database\RequisitionServices.Database.csproj">
      <Project>{75cfcf81-401a-4388-be1f-ed579c161b41}</Project>
      <Name>RequisitionServices.Database</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.DomainModel\RequisitionServices.DomainModel.csproj">
      <Project>{6ebe285e-ac6c-4a2a-9aa6-0a906b7ba723}</Project>
      <Name>RequisitionServices.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Utility\RequisitionServices.Utility.csproj">
      <Project>{6a4f4b1c-f81d-45ff-8b83-b693a6dba39d}</Project>
      <Name>RequisitionServices.Utility</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>