﻿using RequisitionServices.DomainModel.DigitalSignOff;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Association model for Requisitions and DigitalSignoffUsers
    /// </summary>
    public class RequisitionDigitalSignOff
    {
       /// <summary>
       /// Primary key
       /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// FK for Requisitions
        /// </summary>
        public int RequisitionId { get; set; }
        /// <summary>
        /// FK for DigitalSignOffUsers
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// Flag for if a signature was captured during Digital signoff
        /// </summary>
        public bool SignatureCaptured { get; set; }
        /// <summary>
        /// Flag for if user input matches AD record during signing process
        /// </summary>
        public bool ADValidated { get; set; }
        /// <summary>
        /// Flag for if user DSO is soft deleted
        /// </summary>
        public bool IsDeleted { get; set; }
        /// <summary>
        /// Captures day the the digital sign off was created
        /// </summary>
        public DateTime CreateDate { get; set; }
    }
}
