﻿using System.Linq;
using Moq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.DomainServices;
using RequisitionServices.Tests.Helper;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class UserServiceTests
    {
        private static Mock<ISmartCOIDService> mockSmartCOIDService;
        private static Mock<ISmartDepartmentService> mockSmartDepartmentService;
        private static Mock<IParService> mockParService;
        private static Mock<IUserRepository> mockUserRepository;
        private static Mock<IWorkflowRepository> mockWorkflowRepository;
        private static Mock<IRequisitionRepository> mockRequisitionRepository;
        private static Mock<IRequisitionStatusRepository> mockRequisitionStatusRepository;
        private static Mock<IWorkflowService> mockWorkflowService;
        private static Mock<ITypeRepository> mockTypeRepository;
        private static Mock<IEmailService> mockEmailService;
        private static Mock<IVendorService> mockVendorSvc;
        private static Mock<IAuditService> mockAuditService;
        private static Mock<IConfigurationService> mockConfigurationService;
        private static Mock<IFacilityWorkflowService> mockFacilityWorkflowService;

        private static UserService userService;

        [ClassInitialize()]
        public static void ClassInit(TestContext context)
        {
            mockSmartCOIDService = new Mock<ISmartCOIDService>();
            mockSmartDepartmentService = new Mock<ISmartDepartmentService>();
            mockParService = new Mock<IParService>();
            mockUserRepository = new Mock<IUserRepository>();
            mockWorkflowRepository = new Mock<IWorkflowRepository>();
            mockRequisitionRepository = new Mock<IRequisitionRepository>();
            mockRequisitionStatusRepository = new Mock<IRequisitionStatusRepository>();
            mockWorkflowService = new Mock<IWorkflowService>();
            mockTypeRepository = new Mock<ITypeRepository>();
            mockEmailService = new Mock<IEmailService>();
            mockVendorSvc = new Mock<IVendorService>();
            mockAuditService = new Mock<IAuditService>();
            mockConfigurationService = new Mock<IConfigurationService>();
            mockFacilityWorkflowService = new Mock<IFacilityWorkflowService>();
        }


        [TestInitialize()]
        public void Initialize()
        {
            userService = new  UserService(mockSmartCOIDService.Object,mockSmartDepartmentService.Object,mockParService.Object,mockUserRepository.Object,mockWorkflowRepository.Object,mockRequisitionRepository.Object,mockRequisitionStatusRepository.Object,mockWorkflowService.Object,mockEmailService.Object,mockTypeRepository.Object,mockVendorSvc.Object,mockAuditService.Object,mockConfigurationService.Object, mockFacilityWorkflowService.Object);
        }

        [TestCleanup()]
        public void Cleanup()
        {
            userService = null;
        }

        [ClassCleanup()]
        public static void ClassCleanup()
        {
            mockSmartCOIDService = null;
            mockSmartDepartmentService = null;
            mockUserRepository = null;
            mockWorkflowRepository = null;
            mockRequisitionRepository = null;
            mockRequisitionStatusRepository = null;
            mockWorkflowService = null;
            mockTypeRepository = null;
            mockEmailService = null;
            mockVendorSvc = null;
            mockAuditService = null;
            mockConfigurationService = null;
        }

        [TestMethod]
        public void TestGetAllDepartments()
        {
            mockSmartDepartmentService.Setup(x => x.GetDepartments(UserServiceTestData.userId, UserServiceTestData.coid)).Returns(UserServiceTestData.ListDeptResponse());

            var response = userService.GetAllDepartments(UserServiceTestData.userId, UserServiceTestData.coid);

            var setupData = UserServiceTestData.ListDeptResponse().First();
            var actualData = response.First();
            Assert.AreEqual(actualData.COID, setupData.COID);
            Assert.AreEqual(actualData.Description, setupData.Description);
            Assert.AreEqual(actualData.IsActive, setupData.IsActive);
        }
    }
}
