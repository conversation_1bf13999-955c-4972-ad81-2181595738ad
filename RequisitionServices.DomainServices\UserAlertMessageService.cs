﻿using log4net;
using RequisitionServices.DomainModel.UserAlertMessage;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.ServiceModel.Channels;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices
{
    public class UserAlertMessageService : IUserAlertMessageService
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(UserAlertMessageService));
        private readonly IUserAlertMessageRepository _userAlertMessageRepository;

        public UserAlertMessageService(IUserAlertMessageRepository userAlertMessageRepository)
        {
            _userAlertMessageRepository = userAlertMessageRepository;
        }

        public async Task<List<UserAlertMessage>> GetAllMessages()
        {
            try
            {
                return await _userAlertMessageRepository.GetAllMessages();
            }
            catch (Exception ex)
            {
                log.Error("Error in GetAllMessages", ex);
                throw;
            }
        }

        public async Task<UserAlertMessage> GetMessageById(int messageId)
        {
            try
            {
                return await _userAlertMessageRepository.GetMessageById(messageId);
            }
            catch (KeyNotFoundException ex)
            {
                log.Info($"{messageId} not found to update record", ex);
                throw;
            }
            catch (Exception ex)
            {
                log.Error($"Error in GetMessageById for messageId: {messageId}", ex);
                throw;
            }
        }

        public async Task<UserAlertMessage> CreateMessage(UserAlertMessageRequest messageRequest)
        {
            try
            {
                return await _userAlertMessageRepository.CreateMessage(messageRequest);
            }
            catch (Exception ex)
            {
                log.Error("Error in CreateMessage", ex);
                throw;
            }
        }

        public async Task<UserAlertMessage> UpdateMessage(UserAlertMessageRequest messageRequest)
        {
            try
            {
                return await _userAlertMessageRepository.UpdateMessage(messageRequest);
            }
            catch (KeyNotFoundException ex)
            {
                log.Info($"{messageRequest.Id} not found to update", ex);
                throw;
            }
            catch (Exception ex)
            {
                log.Error("Error in UpdateMessage", ex);
                throw;
            }
        }

        public async Task DeleteMessage(int messageId)
        {
            try
            {
                await _userAlertMessageRepository.DeleteMessage(messageId);
            }
            catch (KeyNotFoundException ex)
            {
                log.Info($"{messageId} not found to delete", ex);
                throw;
            }
            catch (Exception ex)
            {
                log.Error($"Error in DeleteMessage for messageId: {messageId}", ex);
                throw;
            }
        }
    }
}
