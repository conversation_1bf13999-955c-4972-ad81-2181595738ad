﻿using System;
using System.Collections.Generic;
using RequisitionServices.DomainModel.PurchaseOrders;

namespace RequisitionServices.MMISServices.DTO
{
    public class POConfirmationDetailDTO
    {
        public int POCCOID { get; set; }
        public int POCNumber { get; set; }
        public int POCLine { get; set; }
        public int POCSequentialNumber { get; set; }
        public string POCVendorPONumber { get; set; }
        public int POCItemNumber { get; set; }
        public DateTime POCDateTime { get; set; }
        public List<string> POConfirmationComments { get; set; }

        public POConfirmationDetailDTO()
        {
            POConfirmationComments = new List<String>();
        }

        public POConfirmationDetail MapToPOConfirmationDetail()
        {
            return new POConfirmationDetail()
            {
                POCCOID = this.POCCOID,
                POCVendorPONumber = this.POCVendorPONumber,
                POCDateTime = this.POCDateTime,
                POCSequentialNumber = this.POCSequentialNumber,
                POCItemNumber = this.POCItemNumber,
                POCLine = this.POCLine,
                POCNumber = this.POCNumber,
                POConfirmationComments = this.POConfirmationComments
            };
        }
    }
}