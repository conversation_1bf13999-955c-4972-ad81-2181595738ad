﻿using RequisitionServices.DomainModel.Requisitions;
using System.ComponentModel.DataAnnotations;
using RequisitionServices.DomainModel.Items;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    public class BillOnlyReviewItemDTO
    {
        public int Id { get; set; }

        public int RequisitionId { get; set; }

        [StringLength(50)]
        public string ItemId { get; set; }

        public decimal? UnitCost { get; set; }

        public int? MainItemId { get; set; }

        public decimal? Discount { get; set; }

        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Gets or sets the VBO hold item conversion details.
        /// </summary>
        public VboHoldItemConversionDto VboHoldItemConversion { get; set; }

        /// <summary>
        /// Gets or sets the SPR detail information.
        /// </summary>
        public SPRDetailDTO SPRDetail { get; set; }

        /// <summary>
        /// Gets or sets the status type ID of the requisition item.
        /// </summary>
        public int RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the associated item details originating from smart item service if available.
        /// </summary>
        public Item Item { get; set; }

        /// <summary>
        /// Gets or sets the associated PAR item details originating from par service if available.
        /// </summary>
        public ParItem ParItem { get; set; }
    }
}
