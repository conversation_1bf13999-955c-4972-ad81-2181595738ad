﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Users
{
    public class Approver
    {
        // Backing field for CreatedBy to ensure it is always lowercase
        private string _createdBy;
        
        public int Id { get; set; }

        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public User User { get; set; }

        public decimal MaxApprovalAmount { get; set; }

        public decimal CapitalMaxApprovalAmount { get; set; }

        public bool IsActive { get; set; }

        public int? Delegate { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy 
        {
            get => _createdBy;
            set => _createdBy = value.ToLower();
        }

        public DateTime CreateDate { get; set; }

        [StringLength(10)]
        public string COID { get; set; }

        public bool IsCERReviewer { get; set; }

        public Approver CreateDeepCopy(Approver approver)
        {
            Approver copy = (Approver)MemberwiseClone();
            copy.Id = approver.Id;
            copy.UserId = approver.UserId;
            copy.User = approver.User;
            copy.MaxApprovalAmount = approver.MaxApprovalAmount;
            copy.CapitalMaxApprovalAmount = approver.CapitalMaxApprovalAmount;
            copy.IsActive = approver.IsActive;
            copy.CreatedBy = approver.CreatedBy;
            copy.CreateDate = approver.CreateDate;
            copy.Delegate = approver.Delegate;
            copy.COID = approver.COID;
            copy.IsCERReviewer = approver.IsCERReviewer;

            return copy;
        }
    }
}
