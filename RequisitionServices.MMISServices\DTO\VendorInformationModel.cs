﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using RequisitionServices.DomainModel.Vendors;

namespace RequisitionServices.MMISServices.DTO
{
    public class VendorInformationModel
    {
        public Vendor MapToVendor()
        {
            return new Vendor()
            {
                Id = StandardNationalVendorNumber,
                Name = VendorName
            };
        }
        public int FacilityId { get; set; }

        public string LegalName { get; set; }

        public string Orderfrom_AddressLine1 { get; set; }

        public string Orderfrom_AddressLine2 { get; set; }

        public string Orderfrom_City { get; set; }

        public string Orderfrom_State { get; set; }

        public string Orderfrom_County { get; set; }

        public string Orderfrom_Country { get; set; }

        public string Orderfrom_Zipcode { get; set; }

        public string Orderfrom_Phone { get; set; }

        public string Orderfrom_TransmissionFax { get; set; }

        public string PurchRep_Phone { get; set; }

        public string PurchRep_Name { get; set; }

        public string RemitTo_AddressLine1 { get; set; }

        public string RemitTo_AddressLine2 { get; set; }

        public string RemitTo_City { get; set; }

        public string RemitTo_State { get; set; }

        public string RemitTo_County { get; set; }

        public string RemitTo_Country { get; set; }

        public string RemitTo_Zipcode { get; set; }

        public string AccountRep_Phone { get; set; }

        public string FreeOnBoard { get; set; }

        public int StandardNationalVendorNumber { get; set; }

        public string SendViaEDI { get; set; }

        public string EdiId { get; set; }

        public string JITOrderType { get; set; }

        public string JITId { get; set; }

        public string SendViaFax { get; set; }

        public string SendViaEmail { get; set; }

        public string FreightOverride { get; set; }

        public string CustomerNumber { get; set; }

        public string FreightAccount { get; set; }

        public string PhysicianOwned { get; set; }

        public string PhysicianAgreement { get; set; }

        public string StdNationalContract { get; set; }

        public string TaxIdType { get; set; }

        public string TaxId { get; set; }

        public string LocalContract { get; set; }

        public int LocalContractExpiration { get; set; }

        public string SalesTax { get; set; }

        public string Code_1099 { get; set; }

        public string EmailAddress1 { get; set; }

        public string EmailAddress2 { get; set; }

        public string EmailAddress3 { get; set; }

        public int ShipViaOption { get; set; }

        public string Substitution { get; set; }

        public int MaxPoLine { get; set; }

        public string PaymentTerms { get; set; }

        public string DirectPay { get; set; }

        public string FreightDiscrepancy { get; set; }

        public string CompanyNumber { get; set; }

        public string ParentInfo { get; set; }

        public string VendorName { get; set; }

        public string VendorTypeCode { get; set; }

        public string VendorTypeCodeDesc { get; set; }

        public string APvendFlag { get; set; }

        public string FvendFlag { get; set; }

        public string VendOnHold { get; set; }
    }
}
