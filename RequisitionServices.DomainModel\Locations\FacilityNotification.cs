﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Locations
{
    public class FacilityNotification
    {
        public int Id { get; set; }

        public int FacilityNotificationTypeId { get; set; }
        [ForeignKey("FacilityNotificationTypeId")]
        public virtual FacilityNotificationType FacilityNotificationType { get; set; }

        [Required(AllowEmptyStrings = true)]
        public string EmailAddresses { get; set; }

        [Required]
        public string COID { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }
    }
}
