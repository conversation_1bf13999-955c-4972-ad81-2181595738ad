﻿
using Newtonsoft.Json;
using Newtonsoft.Json.Schema;
using Newtonsoft.Json.Schema.Generation;
using RequisitionServices.DomainModel.Enum;
using System;

namespace RequisitionServices.DomainModel.Requisitions
{
    public abstract class BaseRequisitionItemStatusDTO
    {
        /// <summary>
        /// Messsage Id - Unique Id per message
        /// </summary>
        [JsonProperty("MessageId", Required = Required.Always)]
        public string MessageId { get; set; }

        /// <summary>
        /// COID
        /// </summary>
        [JsonProperty("COID", Required = Required.Always)]
        public string COID { get; set; }

        /// <summary>
        /// Department
        /// </summary>
        [JsonProperty("Dept", Required = Required.Always)]
        public string Dept { get; set; }

        /// <summary>
        /// Parent System Id
        /// </summary>
        [JsonProperty("ParentSystemId", Required = Required.Always)]
        public string ParentSystemId { get; set; }

        /// <summary>
        /// Unique Id
        /// </summary>
        [JsonProperty("Id", Required = Required.AllowNull)]
        public int? Id { get; set; }

        /// <summary>
        /// Quantity to order
        /// </summary>
        [JsonProperty("QuantityToOrder", Required = Required.Always)]
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// Requisition Item Status Type Id
        /// </summary>
        [JsonProperty("RequisitionItemStatusTypeId", Required = Required.Always)]
        public string RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// PO Number
        /// </summary>
        [JsonProperty("PONumber", Required = Required.AllowNull)]
        public int? PONumber { get; set; }

        /// <summary>
        /// CreateDateTime
        /// </summary>
        [JsonProperty("CreateDateTime", Required = Required.Always)]
        public DateTime CreateDateTime { get; set; }

        /// <summary>
        /// UTC Offset
        /// </summary>
        [JsonProperty("UTCOffset", Required = Required.Always)]
        public int UTCOffset { get; set; }

        /// <summary>
        /// TimeZoneOffset
        /// </summary>
        public DateTimeOffset GetCreateDate()
        {
            return new DateTimeOffset(CreateDateTime).ToOffset(TimeSpan.FromHours(UTCOffset));
        }

        /// <summary>
        /// Update requisition item status
        /// </summary>
        /// <param name="reqItem"></param>
        public abstract void UpdateRequisitionItemStatus(RequisitionItem reqItem);

        /// <summary>
        /// Requisition
        /// </summary>
        /// <returns></returns>
        public abstract RequisitionItemStatusTypeEnum GetRequisitionItemStatusType();

        /// <summary>
        /// Comments
        /// </summary>
        /// <returns></returns>
        public virtual string GetRequisitionItemStatusComments()
        {
            return null;
        }

        /// <summary>
        /// Setup common items
        /// </summary>
        /// <param name="reqItem"></param>
        public virtual void SetupCommonItems(RequisitionItem reqItem)
        {
            if (this.PONumber > 0)
            {
                reqItem.PONumber = this.PONumber;
            }

            UpdateRequisitionNumber(reqItem);
        }

        /// <summary>
        /// Update requisition number
        /// </summary>
        /// <param name="reqItem"></param>
        public virtual void UpdateRequisitionNumber(RequisitionItem reqItem)
        {
            if (this.Id > 0 && string.IsNullOrEmpty(reqItem.ParentSystemId))
            {
                reqItem.ParentSystemId = this.ParentSystemId;
            }
        }

        /// <summary>
        /// Generate schema for type
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        public static JSchema GenerateSchemaForClass(Type type)
        {
            var schemaGenerator = new JSchemaGenerator();
            var schema = schemaGenerator.Generate(type);
            schema.Title = type.Name;
            return schema;
        }
    }
}
