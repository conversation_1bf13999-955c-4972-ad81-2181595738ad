<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="448f73ec-0e98-495b-b049-c0d6fe174cb1" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="NonRushWorkflowService11.wsdl" MetadataType="Wsdl" ID="9c1cdb75-9f13-4173-968d-29db27f1a248" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?wsdl=wsdl0" />
    <MetadataFile FileName="NonRushWorkflowService11.disco" MetadataType="Disco" ID="6b1079df-efc3-4daa-a680-33772aca9444" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?disco" />
    <MetadataFile FileName="NonRushWorkflowService33.xsd" MetadataType="Schema" ID="7e58ab25-46df-470b-9371-2fab31be5921" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?xsd=xsd0" />
    <MetadataFile FileName="NonRushWorkflowService34.xsd" MetadataType="Schema" ID="89a36649-62c1-4f5e-b56e-cd2ca60a719d" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?xsd=xsd2" />
    <MetadataFile FileName="ApprovalWorkflowService11.wsdl" MetadataType="Wsdl" ID="526d5d97-6a8a-4070-933e-c30e625b9603" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?wsdl" />
    <MetadataFile FileName="NonRushWorkflowService35.xsd" MetadataType="Schema" ID="05900191-3db5-4550-8e98-5cd917261507" SourceId="1" SourceUrl="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx?xsd=xsd1" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>