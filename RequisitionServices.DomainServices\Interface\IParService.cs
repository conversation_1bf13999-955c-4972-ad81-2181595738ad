﻿using RequisitionServices.DomainModel.Items;
using System.Collections.Generic;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.Search;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IParService
    {
        IEnumerable<Par> GetParsByUserLocation(string userName, string COID, int? departmentId = null);

        IEnumerable<ParItem> GetParItems(string userName, string COID, int departmentId, string parId);

        GetParItemsWithLastOrderedInfoDTO GetParItemsWithLastOrderedInfo(ItemSearchCriteria itemSearchCriteria);

        IEnumerable<ParItem> GetParItemsByItem(string userName, string COID, int departmentId, string itemId, Item item = null, ApiVersion version = null);

        Par GetParById(string userName, string COID, int departmentId, string parId);
    }
}
