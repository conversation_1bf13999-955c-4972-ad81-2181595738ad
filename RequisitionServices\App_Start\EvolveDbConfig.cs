﻿using System;
using System.Configuration;
using System.IO;
using System.Reflection;
using System.Web.Mvc;
using log4net;
using RequisitionServices.Repositories;

namespace RequisitionServices
{
    public static class EvolveDbConfig
    {
        public static void Migrate()
        {
            var environment = ConfigurationManager.AppSettings.Get("Environment");
            if (!environment.ToLowerInvariant().Equals("qasbx", StringComparison.OrdinalIgnoreCase))
            {
                return;
            }

            var logger = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

            try
            {
                var dbConnection = DependencyResolver.Current.GetService<IConnectionFactory>().GetConnection();
                var evolve = new Evolve.Evolve(dbConnection, msg => logger.Info(msg))
                {
                    Locations = new[] { Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "bin", "Migrations") },
                    IsEraseDisabled = true,
                };

                evolve.Migrate();
            }
            catch (Exception ex)
            {
                logger.Error(ex);
                throw;
            }
        }
    }
}