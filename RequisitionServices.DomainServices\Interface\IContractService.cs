﻿using RequisitionServices.DomainModel.Contract;
using RequisitionServices.DomainModel.Contracts;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IContractService
    {
        ContractReportResults GetAllContracts(int rowOffset, int pageSize, string sortOrder, string filterText);
        ContractReportResults GetContractsById(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText);
        ContractReportResults GetContractsByVendor(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText);
        ContractDetails GetContractDetails(string userName, string COID, string contractId, string vendorNumber);
    }
}
