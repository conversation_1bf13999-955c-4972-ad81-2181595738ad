﻿using System;

namespace RequisitionServices.DomainModel.Contracts
{
    public class Contract
    {
        public int Id { get; set; }
        public int ContractId { get; set; }
        public int VendorId { get; set; }
        public string VendorName { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
        public DateTime EffDate { get; set; }
        public DateTime ExpDate { get; set; }
        public string Class { get; set; }
        public DateTime CreateDate { get; set; }

        public Contract(ContractPageRow page)
        {
            this.Id = page.Id;
            this.ContractId = page.ContractId;
            this.VendorId = page.VendorId;
            this.VendorName = page.VendorName;
            this.Status = page.Status;
            this.Type = page.Type;
            this.EffDate = page.EffDate;
            this.ExpDate = page.ExpDate;
            this.Class = page.Class;
            this.CreateDate = page.CreateDate;
        }

        public Contract() { }
    }
}
