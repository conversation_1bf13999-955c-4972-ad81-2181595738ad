﻿using System;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;
using System.Linq;

namespace RequisitionServices.Database
{
    public class ResilientExecutionStrategy : DbExecutionStrategy
    {
        public ResilientExecutionStrategy()
        {
        }

        public ResilientExecutionStrategy(int maxRetryCount, TimeSpan maxDelay):base(maxRetryCount, maxDelay)
        {
        }

        protected override bool ShouldRetryOn(Exception exception)
        {
            var retry = false;

            var sqlException = exception as SqlException;
            if (sqlException != null)
            {
                int[] errorsToRetry =
                {
                    1205  //Deadlock
                };

                if (sqlException.Errors.Cast<SqlError>().Any(x => errorsToRetry.Contains(x.Number)))
                {
                    retry = true;
                }
                else
                {
                }
            }
            return retry;
        }
    }
}
