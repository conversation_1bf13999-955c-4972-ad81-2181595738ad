﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Vendors;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class SPRDetailDTO
    {
        public SPRDetailDTO() {}
        public SPRDetailDTO(SPRDetail sprDetail)
        {
            if (sprDetail != null)
            {
                this.RequisitionItemId = sprDetail.RequisitionItemId;
                this.ItemDescription = sprDetail.ItemDescription;
                this.EstimatedPrice = sprDetail.EstimatedPrice;
                this.PartNumber = sprDetail.PartNumber;
                this.GeneralLedgerCode = sprDetail.GeneralLedgerCode;
                this.AcquisitionType = sprDetail.AcquisitionType;
                this.EquipmentType = sprDetail.EquipmentType;
                this.DeliveryMethodTypeId = sprDetail.DeliveryMethodTypeId;
                this.BudgetNumber = sprDetail.BudgetNumber;
                this.IsTradeIn = sprDetail.IsTradeIn;
                this.TradeInValue = sprDetail.TradeInValue;

                if (sprDetail.DeliveryMethodType != null)
                {
                    this.DeliveryMethodTypeDescription = sprDetail.DeliveryMethodType.Description;
                }
                this.AdditionalInformation = sprDetail.AdditionalInformation;

                if (sprDetail.ShipToAddressId != null)
                {
                    this.ShipToAddress = new Address() { ExternalSystemId = sprDetail.ShipToAddressId };
                }
   
                this.Vendor = new Vendor() { Id = sprDetail.VendorId, Name = sprDetail.VendorName };

                if(sprDetail.Vendor != null && sprDetail.Vendor.Id != 0)
                {
                    this.Vendor = sprDetail.Vendor;
                }

                this.UOM = new UOM() { Code = sprDetail.UOMCode };

                this.SPRTypeId = sprDetail.SPRTypeId;
                this.ParIdentifier = sprDetail.ParIdentifier;
                this.IsAddToParRequest = sprDetail.IsAddToParRequest;
                this.RejectCode = sprDetail.RejectCode;
                this.RejectionComments = sprDetail.RejectionComments;

                this.UOMHasChanged = sprDetail.UOMHasChanged;
                this.DescriptionHasChanged = sprDetail.DescriptionHasChanged;
                this.EstimatedPriceHasChanged = sprDetail.EstimatedPriceHasChanged;
                this.VendorHasChanged = sprDetail.VendorHasChanged;
                this.PartNumberHasChanged = sprDetail.PartNumberHasChanged;

                if (sprDetail.FileAttachments != null)
                {
                    this.FileAttachments = new List<FileAttachmentDTO>();
                    foreach (var file in sprDetail.FileAttachments)
                    {
                        this.FileAttachments.Add(new FileAttachmentDTO(file));
                    }
                }
            }
        }
        
        public int RequisitionItemId { get; set; }
        public string ItemDescription { get; set; }
        public UOM UOM { get; set; }
        public string AcquisitionType { get; set; }
        public string EquipmentType { get; set; }
        public bool HasApproverChangedEstimatedPrice { get; set; }
        public decimal? EstimatedPrice { get; set; }
        public Vendor Vendor { get; set; }
        public string PartNumber { get; set; }
        public Address ShipToAddress { get; set; }
        public string GeneralLedgerCode { get; set; }
        public int DeliveryMethodTypeId { get; set; }
        public string DeliveryMethodTypeDescription { get; set; }
        public string AdditionalInformation { get; set; }

        public List<FileAttachmentDTO> FileAttachments { get; set; }

        public string BudgetNumber { get; set; }
        public bool IsTradeIn { get; set; }
        public decimal? TradeInValue { get; set; }

        public int SPRTypeId { get; set; } 
        public string ParIdentifier { get; set; }

        public bool IsAddToParRequest { get; set; }
        
        public string RejectCode { get; set; }
        
        public string RejectionComments { get; set; }

        public bool UOMHasChanged { get; set; }

        public bool DescriptionHasChanged { get; set; }

        public bool EstimatedPriceHasChanged { get; set; }

        public bool VendorHasChanged { get; set; }

        public bool PartNumberHasChanged { get; set; }

    }
}
