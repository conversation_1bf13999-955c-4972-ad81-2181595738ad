﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.Interface;

namespace RequisitionServices.MMISServices.Utilities.SmartItemUtilities
{
    public interface ISmartItemUtility
    {
        // Calls the necessary method in MMIS Services to get status update information from SMART
        List<Requisition> GetRequisitionsFromSmart(string userName, string coid, List<string> reqIds, ref ISmartRequisitionService requisitionService);

        // Finds all the RequisitionItems that match smartItem based on existing RequisitionItem information
        List<RequisitionItemWithSubItem> MatchRequisitionItemsToSmartItem(ref IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem);

        // Finds all the RequisitionItems that match smartItem based on existing RequisitionItem information for items that have a discount
        List<RequisitionItemWithSubItem> MatchDiscountRequisitionItemsToSmartItem(ref IEnumerable<RequisitionItem> submittedReqItems, RequisitionItem smartItem);

        // updates the RequisitionItem fields desired with information form smartItem
        //TODO: CWG this MIGHT be where we do the item insertion for new substitute items
        void UpdateRequisitionItemInfoFromSmartItem(ref RequisitionItem reqItem, RequisitionItem smartItem);

        // updates the RequisitionItem fields desired for discount items
        void UpdateDiscountRequisitionItemInfoFromSmartItem(ref RequisitionItem reqItem, RequisitionItem smartItem);

    }
}
