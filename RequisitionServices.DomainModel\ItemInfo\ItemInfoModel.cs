﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.ItemInfo
{
   public class ItemInfoModel
    {
        public long ItemID { get; set; }

        public string FacilityID { get; set; }

        public string UniversalItemNumber { get; set; }

        public string StandardNationalItemNumber { get; set; }

        public string ManufacturerVendorNumber { get; set; }

        public string ManufacturerVendorName { get; set; }

        public string ManufacturerCatalogNumber { get; set; }

        public bool? StockedAtWarehouse { get; set; }

        public decimal? ListPrice { get; set; }

        public string PurchaseUOM { get; set; }

        public decimal? PurchaseUOMPrice { get; set; }

        public int Factor { get; set; }

        public string IssueUnitOfMeasure { get; set; }

        public bool StandardNationalContract { get; set; }

        public string DistributionPoint { get; set; }

        public string ItemDescription { get; set; }

        public string AlternateDescription { get; set; }

        public bool? IsHazardous { get; set; }

        public bool? IsLatex { get; set; }

        public string EOCCode { get; set; }

        public string HCPCS { get; set; }

        public string RevenueCode { get; set; }

        public string ChargeCode { get; set; }

        public string ChargeCodeDescription { get; set; }

        public decimal ChargeAmount { get; set; }

        public string InPatientChargeAlgorithm { get; set; }

        public string OutPatientChargeAlgorithm { get; set; }

        public string SupplierVendorNumber { get; set; }

        public string SupplierVendorName { get; set; }

        public string SupplierReorderNumber { get; set; }

        public short? TaxCode { get; set; }

        public string TaxCategory { get; set; }

        public string ItemCategory { get; set; }

        public string ContractCode { get; set; }

        public string ChargeDept { get; set; }

        public List<UnitOfMeasure> UnitofMeasure { get; set; }

        public string InPatientGLAccountNumber { get; set; }

        public string OutPatientAccountNumber { get; set; }

        public string ParentInfo { get; set; }

        public string ContractFlag { get; set; }

        public bool IsActive { get; set; }


    }
}
