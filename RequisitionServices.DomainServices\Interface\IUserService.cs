﻿using System;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using System.Collections.Generic;
using System.Threading.Tasks;
using RequisitionServices.DomainModel.FacilityWorkflow;

namespace RequisitionServices.DomainServices.Interface
{
    /// <summary>
    /// Interface for user-related services, providing methods to manage user profiles,
    /// approvers, locations, departments, and workflow steps.
    /// </summary>
    public interface IUserService
    {
        Profile GetProfile(string domainSlashUserName);

        /// <summary>
        /// Replaces GetProfile(string domain, string userName)
        /// to call the new Security API instead of Home API
        /// </summary>
        /// <param name="domain"></param>
        /// <param name="userName"></param>
        /// <returns></returns>
        Profile GetSecurityProfile(string domain, string userName);

        string GetUserFullName(string createdBy);

        IEnumerable<AppPart> GetUserParts(string domainSlashUserName);

        IEnumerable<AppPart> GetUserParts(string domain, string userName);

        Approver GetApproverByUserNameAndCOID(string accountName, string COID);

        Task<IEnumerable<ActiveApproversDto>> GetActiveApproversAsync();
        
        Task<IEnumerable<ApproverWorkflowDto>> GetApproverWorkflowsAsync(int approverUserId);

        Approver GetFirstApproverByUserName(string accountName);

        Approver GetApproverByUserIdCOIDAndDelegateId(int? userId, string COID, int delegateId);

        User GetDelegateUserByApproverId(int approverId);

        IEnumerable<Location> GetLocations(string userName);

        Location GetLocation(string userName, string COID);

        IEnumerable<Department> GetAllDepartments(string userName, string COID, bool userIsVendor = false);

        string GetFavoriteFacilityId(string userName);

        PersonalizationDTO SetFavoriteFacility(PersonalizationDTO personalizationDTO);

        void DeleteFavoriteFacility(string userName);

        int GetFavoriteDepartmentId(string userName, string COID);

        Department GetFavoriteDepartment(string userName, string cOID);

        PersonalizationDTO SetFavoriteDepartment(PersonalizationDTO personalization);

        void DeleteFavoriteDepartment(PersonalizationDTO personalization);

        string GetFavoriteParId(string userName, string COID, int departmentId);

        Par GetFavoritePar(string userName, string COID, int departmentId);

        PersonalizationDTO SetFavoritePar(PersonalizationDTO personalization);

        void DeleteFavoritePar(PersonalizationDTO personalization);

        IEnumerable<Department> GetAllDepartmentsForCache(string userName, string COID);

        Department GetDepartment(string userName, string COID, int departmentId);

        //IEnumerable<Approver> RetrieveApprovers(string userName, string COID, IEnumerable<NamesDTO> accountNames);

        IEnumerable<User> RetrieveUsers(string userName, string COID, IEnumerable<NamesDTO> accountNames);

        IEnumerable<Approver> UpdateApprovers(string userName, IEnumerable<Approver> approvers, string COID);

        Task<IEnumerable<Approver>> UpdateApproversAsync(string userName, IEnumerable<Approver> approvers, string COID);

        void UpdateUserName(string accountName, string firstName, string lastName);
        
        IEnumerable<UserWorkflowStep> GetUserWorkflowSteps(string userName, string COID, int? workflowTypeId = null);

        UserSetupWorkflows GetUserSetupWorkflows(string userName, string COID);

        IEnumerable<WorkflowExportDTO> GetUserWorkflowsWithoutDelegates(IEnumerable<string> userNames, string COID, string smartCountryCode);

        void ReorderMultipleWorkflowSteps(string updater, string approverUserName, bool changeCapitals, bool changeOthers, string COID);

        void SaveFacilityWorkflowSteps(SaveFacilityWorkflowDTO request);

        Task<List<RequisitionStatusHistory>> ReorderMultipleWorkflowStepsAsync(string updater, string approverUserName,
            bool changeCapitals, bool changeOthers, string coiD);

        IEnumerable<WorkflowType> GetAllWorkflowTypes();

        IEnumerable<WorkflowType> GetAllUserWorkflowTypes();

        void SaveUserEditInfo(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO);

        Task<bool> SaveUserEditInfoAsync(string userName, SaveUserEditInfoDTO saveUserEditInfoDTO);

        IEnumerable<UserWorkflowDTO> SaveWorkflows(string updater, SaveWorkflowsDTO saveWorkflowsDTO);

        IEnumerable<UserWorkflowStep> SaveUserWorkflowSteps(string updater, string userName,int workflowTypeId, string COID, IEnumerable<UserWorkflowStep> userworkflowSteps);

        ValidationOfUserWorkflowsDTO GetValidationOfUserWorkflows(ValidateUserWorkflowsRequestDTO validateUserWorkflowsRequestDTO);

        WorkflowValidationDTO ValidateUserWorkflow(IEnumerable<UserWorkflowStep> userSteps, string username, WorkflowTypeEnum workflowType, string COID = null, decimal? requisitionTotal = null, bool isEmptyOnSetup = false);

        WorkflowValidationDTO ValidateUserWorkflow(string userName, WorkflowTypeEnum workflowType, string COID, decimal? requisitionTotal = null);

        IEnumerable<UserReportInfoDTO> GetUserReportInfo(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID);

        UserEditDTO GetUserEditUsers(string userName, IEnumerable<UserReportProfileDTO> usersNames, string COID);

        void DeleteDelegatesForApprover(string userName, string delegateEmail);

        void AssignDelegateForApprover(int delegateUserId, string delegateEmail, string userName);

        bool RequisitionerMaxApprovalAmountIsGreaterOrEqual(Approver approver, decimal? requisitionTotal = null);

        bool RequisitionerCapitalMaxApprovalAmountIsGreaterOrEqual(Approver approver, decimal? requisitionTotal = null);

        string GetVendorsForEmailTemplate(Requisition requisition);

        User GetUserByAccountName(string accountName);

        User GetUserByAccountNameWithoutDomain(string accountName);

        //FIXME: Needs peer review that it's truly not being used
        string BulkAddMissingUsersByCoid(string accountName, string COID);

        void SaveBulkApproverJobDetails(string userName, BulkApproverJobTracker bulkApproverJobTracker);

        GetBulkJobDetailsDTO GetBulkApproverJobDetails(string userName);

        BulkApproverJobTracker GetBulkApproverJobTracker(Guid bulkApproverId);

        void UpdateBulkApproverJobStatus(BulkApproverJobStatusDTO bulkApproverJobStatusDto);

        Approver GetApproverByUserId(int userId, string coid);

        Task<ActiveApproversDto> GetActiveApproversForBulkExchangeAsync(string searchAccountName, int selectedApproverUserId);
    }
}
