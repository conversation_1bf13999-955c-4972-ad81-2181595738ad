﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="10.1.1" targetFramework="net472" />
  <package id="Castle.Core" version="4.4.1" targetFramework="net472" />
  <package id="Evolve" version="2.4.0" targetFramework="net472" />
  <package id="log4net" version="2.0.12" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Versioning" version="4.0.0" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.1" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Http" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Options" version="3.1.8" targetFramework="net472" />
  <package id="Microsoft.Extensions.Primitives" version="3.1.8" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net472" />
  <package id="Smart.Core.Common" version="3.4.1" targetFramework="net472" />
  <package id="Smart.Core.Contracts" version="1.2.1" targetFramework="net472" />
  <package id="System.Buffers" version="4.4.0" targetFramework="net472" />
  <package id="System.ComponentModel.Annotations" version="4.7.0" targetFramework="net472" />
  <package id="System.Memory" version="4.5.2" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.4.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
</packages>