﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class ApprovalPageResults
    {
        public ApprovalPageResults(List<Approval> approvals, long totalCount)
        {
            this.Approvals = approvals;
            this.TotalCount = totalCount;
        }

        public List<Approval> Approvals { get; set; }

        public long TotalCount { get; set; }
    }
}
