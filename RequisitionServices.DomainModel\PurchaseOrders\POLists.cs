﻿using System;

namespace RequisitionServices.DomainModel.PurchaseOrders
{
    public class POLists
    {
        public int PONumber { get; set; }

        public int COID { get; set; }

        public string Status { get; set; }

        public string VendorName { get; set; }

        public int VendorNumber { get; set; }

        public DateTime CreateDate { get; set; }

        public bool Outstanding { get; set; }

        public string POComment1 { get; set; }

        public string POComment2 { get; set; }

        public string POComment3 { get; set; }

        public string POType { get; set; }

        public string SpecialInstruction2 { get; set; }

        public bool BackorderFlag { get; set; }

        public bool CreditFlag { get; set; }

        public bool ReturnFlag { get; set; }
    }
}
