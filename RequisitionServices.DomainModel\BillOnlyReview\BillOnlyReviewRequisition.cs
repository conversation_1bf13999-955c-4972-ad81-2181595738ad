﻿using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.BillOnlyReview
{
    /// <summary>
    /// Represents a requisition for Bill Only Review.
    /// </summary>
    [NotMapped]
    public class BillOnlyReviewRequisition : Requisition
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewRequisition"/> class.
        /// </summary>
        public BillOnlyReviewRequisition() { }

        /// <summary>
        /// Gets or sets the description of the requisition status type.
        /// </summary>
        public string RequisitionStatusTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the description of the requisition type.
        /// </summary>
        public string RequisitionTypeDescription { get; set; }

        /// <summary>
        /// Gets or sets the digital sign-off user.
        /// </summary>
        public DigitalSignOffUser DigitalSignOffUser { get; set; }

        /// <summary>
        /// Gets or sets the user associated with the requisition.
        /// </summary>
        public User User { get; set; }
    }
}
    
