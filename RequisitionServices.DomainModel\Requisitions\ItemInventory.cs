﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class ItemInventory
    {

        [Key, ForeignKey("RequisitionItem")]
        public int RequisitionItemId { get; set; }

        //NOTE: Must be set to null before returning to ePro API.  Will cause a circular reference
        public RequisitionItem RequisitionItem { get; set; }

        [ForeignKey("Requisition")]
        public int RequisitionId { get; set; }

        //NOTE: Must be set to null before returning to ePro API.  Will cause a circular reference
        public Requisition Requisition { get; set; }

        [Required]
        [StringLength(10)]
        public string COID { get; set; }

        public int DepartmentId { get; set; }

        [Required]
        [StringLength(50)]
        public string ParId { get; set; }

        [Required]
        [StringLength(50)]
        public string ItemId { get; set; }

        [Required(AllowEmptyStrings = true)]
        [StringLength(20)]
        public string CatalogNumber { get; set; }

        public int QuantityOnHand { get; set; }

        public int QuantityOnOrder { get; set; }

        public int QuantityToOrder { get; set; }

        [Required]
        [StringLength(100)]
        public string UserName { get; set; }

        public DateTime CreateDate { get; set; } //always save in UTC
        
        [Required]
        public DateTime LastUpdatedUTC { get; set; }
    }
}
