﻿using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.Tests.Helper;

namespace RequisitionServices.Tests
{
    [TestClass]
    public class AuthenticationTests
    {
        [TestMethod]
        [TestCategory("IntegrationTests")]
        public void TestAuthentication()
        {
            IEnumerable<Location> locations = UserControllerHelper.GetLocations("dbe7859");
            Assert.IsTrue(locations.Count() > 0, "We should get some locations");
        }
    }
}
