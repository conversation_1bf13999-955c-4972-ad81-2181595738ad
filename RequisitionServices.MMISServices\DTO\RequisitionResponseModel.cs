﻿namespace RequisitionServices.MMISServices.DTO
{
    /// <summary>
    /// Requisition Response Model
    /// </summary>
    public class RequisitionResponseModel
    {
        /// <summary>
        /// Id
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Requisition Id
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Requisition Number
        /// </summary>
        public int RequisitionNumber { get; set; }

        /// <summary>
        /// Coid
        /// </summary>
        public int Coid { get; set; }

        /// <summary>
        /// Par Department
        /// </summary>
        public int ParDepartment { get; set; }

        /// <summary>
        /// Par Class
        /// </summary>
        public string ParClass { get; set; }

        /// <summary>
        /// Item Number
        /// </summary>
        public int ItemNumber { get; set; }

        /// <summary>
        /// Re Order
        /// </summary>
        public string ReOrder { get; set; }

        /// <summary>
        /// SPR Number
        /// </summary>
        public string SPRNumber { get; set; }

        /// <summary>
        /// Vendor Number
        /// </summary>
        public int VendorNumber { get; set; }

        /// <summary>
        /// Vendor Name
        /// </summary>
        public string VendorName { get; set; }

        /// <summary>
        /// Detail
        /// </summary>
        public string Detail { get; set; }

        /// <summary>
        /// Line Item
        /// </summary>
        public int LineItem { get; set; }

        /// <summary>
        /// Error Msg
        /// </summary>
        public string ErrorMsg { get; set; }

        /// <summary>
        /// Status Code
        /// </summary>
        public int StatusCode { get; set; }
    }
}
