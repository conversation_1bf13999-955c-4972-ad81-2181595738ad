USE [eProcurementQA]
GO

/*
***************************************************************************
Database    : eProcurement
Name        : usp_ApprovalHistoryGet
Purpose     : Returns a paginated list of requisition history items for the MyApprovals page.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 11-20-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
<PERSON>		11/20/2017		Script created
Peter Hurlburt		11/28/2017		Submitted for deployment 24
Peter Hurlburt		12/15/2017		Removed an overly-verbose union statement
									Submitted for deployment 24
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		10/27/2021		Rewriting the sproc to optimize the query
									Remove restriction of only showing histories
									since most recent Requisition draft status

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_ApprovalHistoryGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit
AS
                BEGIN

DECLARE @historyList TABLE 
(
	RowOrder INT NOT NULL,
	SortOrder INT NOT NULL,
	HistoryItemId INT NOT NULL,
	HistoryItemCreateDate DATETIME NOT NULL,
	HistoryItemStatusTypeId INT NOT NULL,
	RequisitionId INT NOT NULL,
	RequisitionStatusTypeId INT NOT NULL,
	RequisitionLocationIdentifier VARCHAR(50) NOT NULL,
	RequisitionComments VARCHAR(255) NULL,
	RequisitionTypeId INT NOT NULL,
	RequisitionerId VARCHAR(100) NOT NULL,
	RequisitionerFirstName VARCHAR(255) NOT NULL,
	RequisitionerLastName VARCHAR(255) NOT NULL,
	RequesterId VARCHAR(255) NULL,
	RequesterFirstName VARCHAR(255) NULL,
	RequesterLastName VARCHAR(255) NULL,
	RequesterComments VARCHAR(255) NULL,
	ReviewerRecommended BIT NULL,
	ReviewerComments VARCHAR(255) NULL
)
DECLARE @tempReqTypeIds TABLE (Id INT)
DECLARE @totalhistoryCount BIGINT

INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup

INSERT INTO @historyList
SELECT 
	ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
		CASE @oldestFirst WHEN 0 THEN [HistoryItemCreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [HistoryItemCreateDate] END ASC) AS RowOrder,
	SortOrder,
	HistoryItemId,
	HistoryItemCreateDate,
	HistoryItemStatusTypeId,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionTypeId,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	ReviewerRecommended,
	ReviewerComments
FROM 
(
	SELECT 
		CASE @statusSorting
			WHEN 1 THEN CASE requisitionHistory.RequisitionStatusTypeId
				WHEN 6 THEN 3
				WHEN 3 THEN 2
				ELSE review.Recommended END
			ELSE CASE
				WHEN requisition.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
				THEN 1
				ELSE 2
				END
			END AS SortOrder,
		CASE requisitionHistory.RequisitionStatusTypeId
			WHEN 10
			THEN
				CASE review.Recommended
				WHEN 1 THEN 'Recommend Approve'
				ELSE 'Recommend Deny'
				END
			ELSE	
				requisitionStatus.[Description] 
			END AS StatusForFilter,
		requisitionHistory.Id AS HistoryItemId,
		requisitionHistory.CreateDate AS HistoryItemCreateDate,
		requisitionHistory.RequisitionStatusTypeId AS historyItemStatusTypeId,
		requisitionHistory.RequisitionId AS RequisitionId,
		requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
		requisition.LocationIdentifier AS RequisitionLocationIdentifier,
		requisition.Comments AS RequisitionComments,
		requisition.RequisitionTypeId AS RequisitionTypeId,
		requisition.CreatedBy AS RequisitionerId,
		requisitioner.FirstName AS RequisitionerFirstName,
		requisitioner.LastName AS RequisitionerLastName,
		reviewRequester.AccountName AS RequesterId,
		reviewRequester.FirstName AS RequesterFirstName,
		reviewRequester.LastName AS RequesterLastName,
		review.RequesterComments AS RequesterComments,
		review.Recommended AS ReviewerRecommended,
		review.ReviewerComments AS ReviewerComments
	FROM RequisitionStatusHistories requisitionHistory WITH (NOLOCK)
	INNER JOIN RequisitionStatusTypes requisitionStatus WITH (NOLOCK)
		ON requisitionHistory.RequisitionStatusTypeId = requisitionStatus.Id
	INNER JOIN Requisitions requisition WITH (NOLOCK)
		ON requisitionHistory.RequisitionId = requisition.RequisitionId
	INNER JOIN Users requisitioner WITH (NOLOCK)
		ON requisition.CreatedBy = requisitioner.AccountName
	LEFT OUTER JOIN AdhocReviews review WITH (NOLOCK)
		ON requisitionHistory.Id = review.ReviewerRequisitionStatusHistoryId
	LEFT OUTER JOIN Users reviewRequester WITH (NOLOCK)
		ON review.Requester = reviewRequester.AccountName
	WHERE requisitionHistory.CreatedBy = @userName
	AND requisitionHistory.RequisitionStatusTypeId IN (3, 6, 10) --Approved, Denied, Review Provided
) AS HistoryRecord
WHERE @filterText IS NULL OR
	(HistoryRecord.[RequisitionId] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequisitionerId] LIKE '%' + @filterText + '%'
	OR (HistoryRecord.[RequisitionerFirstName] + ' ' + HistoryRecord.[RequisitionerLastName]) LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequesterId] LIKE '%' + @filterText + '%'
	OR (HistoryRecord.[RequesterFirstName] + ' ' + HistoryRecord.[RequesterLastName]) LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequisitionComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequesterComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[ReviewerComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[StatusForFilter] LIKE '%' + @filterText + '%')

SELECT @totalHistoryCount = COUNT(*) FROM @historyList

SELECT 
	historyList.HistoryItemId,
	historyList.HistoryItemCreateDate,
	historyList.HistoryItemStatusTypeId,
	historyList.RequisitionId,
	historyList.RequisitionStatusTypeId,
	historyList.RequisitionLocationIdentifier,
	historyList.RequisitionComments,
	historyList.RequisitionTypeId,
	(CASE
		WHEN EXISTS (
			SELECT 1
			FROM AdhocReviews AS review WITH (NOLOCK)
			WHERE historyList.RequisitionId = review.RequisitionId
			AND review.Recommended IS NULL
		)
		THEN CAST (1 AS BIT)
		ELSE CAST (0 AS BIT) 
		END) AS PendingReviewsExist,
	item.Id AS RequisitionItemId,
	item.RequisitionItemStatusTypeId,
	item.QuantityToOrder AS RequisitionItemQuantityToOrder,
	item.ParentSystemId AS RequisitionItemParentSystemId,
	SPR.VendorId AS SprDetailsVendorId,
	SPR.VendorName AS SprDetailsVendorName,
	SPR.PartNumber AS SprDetailsPartNumber,
	SPR.EstimatedPrice AS SprDetailsEstimatedPrice,
	ReqItemFileAttachments.RequisitionItemId AS SprDetailsFileAttachment,
	historyList.RequisitionerId,
	historyList.RequisitionerFirstName,
	historyList.RequisitionerLastName,
	historyList.RequesterId,
	historyList.RequesterFirstName,
	historyList.RequesterLastName,
	historyList.RequesterComments,
	historyList.ReviewerRecommended,
	historyList.ReviewerComments,
	@totalhistoryCount AS TotalReqCount
FROM
(
	SELECT * FROM @historyList 
	ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS historyList
LEFT OUTER JOIN RequisitionItems item WITH (NOLOCK)
	ON historyList.RequisitionId = item.RequisitionId
LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
	ON item.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) ReqItemFileAttachments
	ON item.Id = ReqItemFileAttachments.RequisitionItemId
ORDER BY historyList.RowOrder, item.Id

                END
