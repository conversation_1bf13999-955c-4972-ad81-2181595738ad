﻿USE eProcurementQA
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<PERSON>- Create date: 2022-03-08
-- Description:	Vendor BO/BR Requisition Report by date range
-- =============================================
CREATE PROCEDURE [dbo].[usp_VendorUserRequisitionsReportGet] 
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT DISTINCT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT 
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			INNER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO


-- =============================================
-- Author:		Colin Glasco
-- Create date: 2022-03-08
-- Description:	Vendor BO/BR Requisition Report by date range export
-- =============================================
CREATE PROCEDURE [dbo].[usp_VendorUserRequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@maxExportedRequistiionCount int = 1000,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT DISTINCT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT 
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			INNER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT TOP (@maxExportedRequistiionCount)
		*
		FROM @requisitionList
		ORDER BY RowOrder
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO

-- =============================================
-- Author:		Colin Glasco
-- Create date: 2022-03-08
-- Description:	Vendor BO/BR Requisition Report by ItemNumber
-- =============================================
CREATE PROCEDURE [dbo].[usp_VendorUserRequisitionsReportByItemNumberGet] 
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT DISTINCT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT 
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			INNER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (ItemId = @searchText
			OR ReOrder = @searchText
			OR CatalogNumber = @searchText
			OR SPR.PartNumber = @searchText)
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO

-- =============================================
-- Author:		Colin Glasco
-- Create date: 2022-03-08
-- Description:	Vendor BO/BR Requisition Report by Item number export
-- =============================================
CREATE PROCEDURE [dbo].[usp_VendorUserRequisitionsReportByItemNumberExportGet]
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT DISTINCT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT 
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			INNER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (ItemId = @searchText
			OR ReOrder = @searchText
			OR CatalogNumber = @searchText
			OR SPR.PartNumber = @searchText)
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT
		*
		FROM @requisitionList
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	ORDER BY requisitionPage.RowOrder
	
END
GO

-- =============================================
-- Author:		Colin Glasco
-- Create date: 2022-03-08
-- Description:	Vendor BO/BR Requisition Report by Vendor
-- =============================================
CREATE PROCEDURE [dbo].[usp_VendorUserRequisitionsVendorReportGet] 
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT DISTINCT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT 
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			INNER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (@VendorId IS NULL OR Cast(RI.VendorId as VARCHAR(32)) = @VendorId OR Cast(SPR.VendorId as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR RI.VendorName = @VendorName OR SPR.VendorName = @VendorName)
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO

-- =============================================
-- Author:		Colin Glasco
-- Create date: 2022-03-08
-- Description:	Vendor BO/BR Requisition Report by Vendor export
-- =============================================
CREATE PROCEDURE [dbo].[usp_VendorUserRequisitionsVendorReportExportGet]
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT DISTINCT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT 
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			INNER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (@VendorId IS NULL OR Cast(RI.VendorId as VARCHAR(32)) = @VendorId OR Cast(SPR.VendorId as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR RI.VendorName = @VendorName OR SPR.VendorName = @VendorName)
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT
		*
		FROM @requisitionList
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId	
	ORDER BY requisitionPage.RowOrder
	
END
GO
