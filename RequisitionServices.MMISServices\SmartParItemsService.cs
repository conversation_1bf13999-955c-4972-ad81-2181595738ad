﻿using log4net;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    public class SmartParItemsService : ISmartParItemsService
    {
        private ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private const string getParItemDetailsMethod = "PARItem/GetParItemDetails/";

        private const string getParItemDetailsByItemIdMethod = "PARItem/GetParItemDetailsByItemId/";

        private const string getParByParIdMethod = "PARItem/GetParByParId/";

        private const string getParItemMethod = "PARItem/GetParItem/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IEnumerable<ParItemDetails> GetParItemDetails(string userName, string COID, int departmentId, string parId)
        {

            var parItems = new List<ParItemDetails>();
            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(COID);

                var parItemRecords = ApiUtility.ExecuteApiGetTo<IEnumerable<ParItemDetailsModel>>(endpoint, getParItemDetailsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", COID },
                                                                                { "parDept", departmentId.ToString() },
                                                                                { "parClass", parId }
                                                                            });
                
                if (parItemRecords != null)
                {
                    foreach (var parItemDetailRecord in parItemRecords)
                    {
                        parItems.Add(parItemDetailRecord.MapToParItemDetails());
                    }
                }
            }
            catch(Exception ex)
            {
                Log.Error("Exception calling method GetParItemDetails", ex);
                throw;
            }

            return parItems;
        }

        public ParHeaderModel GetParByParId(string userId, string coid, int parDepartment, string parClass)
        {
            var parHeader = new ParHeaderModel();
            try
            {
                SmartInputValidator.CheckUserName(ref userId);
                SmartInputValidator.CheckCoid(coid);

                parHeader = ApiUtility.ExecuteApiGetTo<ParHeaderModel>(endpoint, getParByParIdMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userId },
                                                                                { "coid", coid },
                                                                                { "parDept", parDepartment.ToString() },
                                                                                { "parClass", parClass }
                                                                            });

            }
            catch(Exception ex)
            {
                Log.Error("Exception calling method GetParByParId", ex);
                throw;
            }

            return parHeader;
        }

        public ParItemModel GetParItem(string userId, string coid, int parDepartment, string parClass, string itemId)
        {
            var parItem = new ParItemModel();
            try
            {
                SmartInputValidator.CheckUserName(ref userId);
                SmartInputValidator.CheckCoid(coid);

                parItem = ApiUtility.ExecuteApiGetTo<ParItemModel>(endpoint, getParItemMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userId },
                                                                                { "coid", coid },
                                                                                { "parDept", parDepartment.ToString() },
                                                                                { "parClass", parClass },
                                                                                { "itemId",  itemId }
                                                                            });
            }
            catch(Exception ex)
            {
                Log.Error("Exception calling method GetParItem", ex);
                throw;
            }
            return parItem;
        }
    }
}
