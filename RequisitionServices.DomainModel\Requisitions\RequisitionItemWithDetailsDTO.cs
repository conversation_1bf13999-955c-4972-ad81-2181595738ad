﻿using RequisitionServices.DomainModel.Items;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionItemWithDetailsDTO
    {
        public RequisitionItemWithDetailsDTO() { }
        public RequisitionItemWithDetailsDTO(RequisitionItem requisitionItem, Item item, List<ParItem> parItems) 
        {
            this.Id = requisitionItem.Id;
            this.RequisitionId = requisitionItem.RequisitionId;
            this.ItemId = requisitionItem.ItemId;
            this.ParIdentifier = requisitionItem.ParIdentifier;
            this.MainItemId = requisitionItem.MainItemId;
            this.RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId;
            this.QuantityToOrder = requisitionItem.QuantityToOrder;
            this.QuantityFulfilled = requisitionItem.QuantityFulfilled;
            this.PONumber = requisitionItem.PONumber;
            this.IsRushOrder = requisitionItem.IsRushOrder;
            this.ParentSystemId = requisitionItem.ParentSystemId;
            this.CreatedBy = requisitionItem.CreatedBy;
            this.CreateDate = requisitionItem.CreateDate;
            this.RequisitionScheduledDate = requisitionItem.RequisitionScheduledDate.HasValue ? requisitionItem.RequisitionScheduledDate.Value.LocalDateTime : (DateTime?) null;
            this.IsFileItem = requisitionItem.IsFileItem;
            this.FileItemHasChanged = requisitionItem.FileItemHasChanged;
            this.SmartItemNumber = requisitionItem.SmartItemNumber;
            this.StockIndicator = requisitionItem.StockIndicator;
            this.UnitCost = requisitionItem.UnitCost;
            this.TotalCost = requisitionItem.TotalCost;
            this.Discount = requisitionItem.Discount;
            this.GLAccount = requisitionItem.GeneralLedgerCode;
            this.ParentRequisitionItemId = requisitionItem.ParentRequisitionItemId;
            this.ParentRequisitionItemNumber = requisitionItem.RequisitionItemAutoSubFlag;
            this.RejectionComments = requisitionItem.RejectionComments;
            this.Inventory = requisitionItem.Inventory;
            this.PartsWarrantyMonths = requisitionItem.PartsWarrantyMonths;
            this.LaborWarrantyMonths = requisitionItem.LaborWarrantyMonths;

            if (this.Inventory != null)
            {
                this.Inventory.RequisitionItem = null;
                this.Inventory.Requisition = null;
            }

            LotSerialPairs = new List<LotSerialPair>();
            this.IsPurged = requisitionItem.IsPurged;

            if (item != null)
            {
                this.Item = item;
            }
            else
            {
                this.Item = new Item() {
                    Id = this.ItemId,
                    Description = requisitionItem.ItemDescription,
                    Vendor = new Vendors.Vendor()
                    {
                        Id = requisitionItem.VendorId,
                        Name = requisitionItem.VendorName
                    },
                    UOM = requisitionItem.UOMCode,
                    Price = requisitionItem.UnitCost,
                    IsStock = Convert.ToBoolean(requisitionItem.StockIndicator),
                };
                if (!string.IsNullOrEmpty(requisitionItem.ReOrder))
                {
                    this.Item.ReorderNumber = requisitionItem.ReOrder;
                }
                if (!string.IsNullOrEmpty(requisitionItem.CatalogNumber))
                {
                    this.Item.ManufacturerCatalogNumber = requisitionItem.CatalogNumber;
                }
            }

            this.AvailableParItems = parItems;

            if (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any())
            {
                var clincialDetails = requisitionItem.ClinicalUseDetails.First();
                this.ClinicalUseId = clincialDetails.Id;
                this.UpchargeCost = clincialDetails.UpchargeCost;
                this.Provider = clincialDetails.Provider;
                this.PatientId = clincialDetails.PatientId;
                this.PatientName = clincialDetails.PatientName;
                this.ProcedureDate = clincialDetails.ProcedureDate;
                this.PatientIdHasChanged = clincialDetails.PatientIdHasChanged;
                this.PatientNameHasChanged = clincialDetails.PatientNameHasChanged;
                this.ProcedureDateHasChanged = clincialDetails.ProcedureDateHasChanged;
                this.ProviderHasChanged = clincialDetails.ProviderHasChanged;


                foreach (var clinicalDetail in requisitionItem.ClinicalUseDetails)
                {
                    LotSerialPairs.Add(new LotSerialPair() { ClinicalUseDetailsId = clinicalDetail.Id, LotNumber = clinicalDetail.LotNumber, SerialNumber = clinicalDetail.SerialNumber });                     
                }

                if (!LotSerialPairs.Any())
                {
                    LotSerialPairs.Add(new LotSerialPair());
                }
            }

            if (requisitionItem.SPRDetail != null)                                                                                                                                                                
            {
                this.SPRDetailDTO = new SPRDetailDTO(requisitionItem.SPRDetail);
            }

            HasConversionChanged = requisitionItem.HasConversionChanged;
            if (requisitionItem.VboHoldItemConversion != null)
            {
                this.vboHoldItemConversionDto = new VboHoldItemConversionDto(requisitionItem.VboHoldItemConversion);
            }
        }

        public int Id { get; set; }

        public int RequisitionId { get; set; }
        
        public string ItemId { get; set; }

        public Item Item { get; set; }

        public List<ParItem> AvailableParItems { get; set; }
              
        public string ParIdentifier { get; set; }

        public int? MainItemId { get; set; }

        public int RequisitionItemStatusTypeId { get; set; }

        public string ParentSystemId { get; set; }

        public int QuantityToOrder { get; set; }

        public int? QuantityFulfilled { set; get; }

        public bool IsRushOrder { get; set; }

        public int? PONumber { set; get; }

        public string CreatedBy { get; set; }

        public DateTime CreateDate { get; set; }

        public int? ClinicalUseId { get; set; }

        public List<LotSerialPair> LotSerialPairs { get; set; }

        public decimal? UpchargeCost { get; set; }

        public string Provider { get; set; }

        public string PatientId { get; set; }

        public string PatientName { get; set; }

        public bool PatientIdHasChanged { get; set; }

        public bool PatientNameHasChanged { get; set; }

        public bool ProcedureDateHasChanged { get; set; }

        public bool ProviderHasChanged { get; set; }

        public DateTime? ProcedureDate { get; set; }

        public SPRDetailDTO SPRDetailDTO { get; set; }

        public VboHoldItemConversionDto vboHoldItemConversionDto { get; set; }

        public bool? HasConversionChanged { get; set; }

        public DateTime? RequisitionScheduledDate { get; set; }

        public bool IsFileItem { get; set; }

        public bool FileItemHasChanged { get; set; }

        public int? SmartItemNumber { get; set; }

        public bool? StockIndicator { get; set; }

        public decimal? UnitCost { get; set; }

        public decimal? TotalCost { get; set; }

        public decimal? Discount { get; set; }

        public string GLAccount { get; set; }

        public string ParentRequisitionItemNumber { get; set; }

        public int? ParentRequisitionItemId { get; set; }

        public string RejectionComments { get; set; }

        public ItemInventory Inventory { get; set; }
        public bool IsPurged { get; set; }

        public byte? PartsWarrantyMonths { get; set; }

        public byte? LaborWarrantyMonths { get; set; }
    }
}
