﻿using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.Vira;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;


namespace RequisitionServices.Repositories
{
    public class ViraRepository : IViraRepository
    {
        private readonly ILog log = LogManager.GetLogger(typeof(ViraRepository));
        private readonly EProcurementContext _procurementContext;

        public ViraRepository(EProcurementContext procurementContext)
        {
            _procurementContext = procurementContext;
        }

        public async Task<ViraItemStatus> GetViraItemStatusRecordById(int requisitionId, int requisitionItemId)
        {
            try
            {
                return await Task.FromResult(
                    _procurementContext.ViraItemStatus
                        .Where(x => x.RequisitionId == requisitionId && x.RequisitionItemId == requisitionItemId)
                        .OrderByDescending(x => x.Id)
                        .FirstOrDefault()
                );
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: Error in {0}", "ViraRepository.GetViraItemStatusRecordById"), ex);
                return null;
            }
        }

        public async Task<ViraItemStatus> CreateViraItemStatusRecord(ViraItemStatus viraItemStatus)
        {
            try
            {
                _procurementContext.ViraItemStatus.Add(viraItemStatus);
                _procurementContext.SaveChanges();
                return await Task.FromResult(viraItemStatus);
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: Error in {0}", "ViraRepository.CreateViraItemStatusRecord"), ex);
                return null;
            }
        }

        public async Task<string> DeleteViraItemStatusRecord(int requisitionId, int requisitionItemId)
        {
            try
            {
                var itemStatus = await GetViraItemStatusRecordById(requisitionId, requisitionItemId);
                if (itemStatus != null)
                {
                    _procurementContext.ViraItemStatus.Remove(itemStatus);
                    _procurementContext.SaveChanges();
                    return "Deleted";
                }
                return "Not Found";
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: Error in {0}", "ViraRepository.DeleteViraItemStatusRecord"), ex);
                return "Error";
            }
        }

        public async Task<string> UpdateViraItemStatusRecord(ViraItemStatus viraItemStatus)
        {
            try
            {
                ViraItemStatus originalViraItemStatusRecord = await GetViraItemStatusRecordById(viraItemStatus.RequisitionId, viraItemStatus.RequisitionItemId);

                if (originalViraItemStatusRecord != null)
                {
                    var propertiesToUpdate = new Dictionary<string, Action>
                    {
                        { nameof(ViraItemStatus.RequisitionId), () => originalViraItemStatusRecord.RequisitionId = viraItemStatus.RequisitionId },
                        { nameof(ViraItemStatus.RequisitionItemId), () => originalViraItemStatusRecord.RequisitionItemId = viraItemStatus.RequisitionItemId },
                        { nameof(ViraItemStatus.PublishStatus), () => originalViraItemStatusRecord.PublishStatus = viraItemStatus.PublishStatus },
                        { nameof(ViraItemStatus.RetryCount), () => originalViraItemStatusRecord.RetryCount = viraItemStatus.RetryCount },
                        { nameof(ViraItemStatus.LastRetry), () => originalViraItemStatusRecord.LastRetry = viraItemStatus.LastRetry },
                        { nameof(ViraItemStatus.ViraApprovalStatus), () => originalViraItemStatusRecord.ViraApprovalStatus = viraItemStatus.ViraApprovalStatus },
                    };

                    foreach (var property in propertiesToUpdate)
                    {
                        switch (property.Key)
                        {
                            case nameof(ViraItemStatus.RequisitionId):
                                if (originalViraItemStatusRecord.RequisitionId != viraItemStatus.RequisitionId)
                                    property.Value();
                                break;
                            case nameof(ViraItemStatus.RequisitionItemId):
                                if (originalViraItemStatusRecord.RequisitionItemId != viraItemStatus.RequisitionItemId)
                                    property.Value();
                                break;
                            case nameof(ViraItemStatus.PublishStatus):
                                if (originalViraItemStatusRecord.PublishStatus != viraItemStatus.PublishStatus)
                                    property.Value();
                                break;
                            case nameof(ViraItemStatus.RetryCount):
                                if (originalViraItemStatusRecord.RetryCount != viraItemStatus.RetryCount)
                                    property.Value();
                                break;
                            case nameof(ViraItemStatus.LastRetry):
                                if (originalViraItemStatusRecord.LastRetry != viraItemStatus.LastRetry)
                                    property.Value();
                                break;
                            case nameof(ViraItemStatus.ViraApprovalStatus):
                                if (originalViraItemStatusRecord.ViraApprovalStatus != viraItemStatus.ViraApprovalStatus)
                                    property.Value();
                                break;
                        }
                    }
                    _procurementContext.SaveChanges();
                    return "Updated";
                }
                return "Not Found";
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: Error in {0}", "ViraRepository.UpdateViraItemStatusRecord"), ex);
                return "Error";
            }
        }
    }
}
