﻿using System;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Items
{
    public class ParItem
    {
        public static ParItem ConvertLegacyRequisitionItemToParItem(RequisitionItem legacyReqItem, RequisitionTypeEnum reqType)
        {
            var type = MapRequisitionTypeEnumToParType(reqType);
            int parsedItemId;
            long glCode;

            Int64.TryParse(legacyReqItem.GeneralLedgerCode, out glCode);

            if (legacyReqItem != null && legacyReqItem.ParIdentifier != "EPR" && Int32.TryParse(legacyReqItem.ItemId, out parsedItemId))
            {
                var item =  new ParItem()
                {
                    ParId = legacyReqItem.ParIdentifier,
                    ItemId = parsedItemId,
                    Item = Item.ConvertLegacyRequisitionItemToItem(legacyReqItem),
                    MinStock = legacyReqItem.MinStock,
                    MaxStock = legacyReqItem.MaxStock,
                    IssueUOM = legacyReqItem.UOMCode,
                    Location = legacyReqItem.PARLocation,
                    ParPrice = legacyReqItem.UnitCost ?? 0,
                    GLAccount = glCode,
                    ParType = (int)type
                };

                if (item.ParType == (int)Enum.ParType.Capitated && legacyReqItem.MainItemId == null)
                {
                    item.Item.IsCapitated = true;
                }

                return item;
            }
            else
            {
                return null;
            }
        }

        private static ParType MapRequisitionTypeEnumToParType(RequisitionTypeEnum reqType)
        {
            switch (reqType)
            {
                case RequisitionTypeEnum.BillOnly:
                case RequisitionTypeEnum.BillAndReplace:
                    return Enum.ParType.BillOnly;
                case RequisitionTypeEnum.CapitatedBillOnly:
                case RequisitionTypeEnum.CapitatedBillAndReplace:
                    return Enum.ParType.Capitated;
                default: //We won't know if a PAR is pharmacy or not from legacy, so don't worry about it.
                    return Enum.ParType.Normal;
            }
        }

        public string ParId { get; set; }
        
        public int ItemId { get; set; }
        
        public int? MinStock { get; set; }

        public int? MaxStock { get; set; }
        
        public string IssueUOM { get; set; }

        public Item Item { get; set; }

        public string ParDescription { get; set; }

        public int ParType { get; set; }

        public long GLAccount { get; set; }

        public long IGLAccount { get; set; }

        public string Location { get; set; }

        public decimal ParPrice { get; set; }
        public bool ManualOrderFlag { get; set; }
    }
}
