﻿using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.ComponentModel;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class RequisitionItem
    {
        public RequisitionItem() { }
        public RequisitionItem(RequisitionItemDTO requisitionItemDTO, int requisitionTypeId, bool IsVendor) 
        {
            if (requisitionItemDTO != null)
            {
                this.Id = requisitionItemDTO.Id;
                this.RequisitionId = requisitionItemDTO.RequisitionId;
                this.ItemId = requisitionItemDTO.ItemId;
                this.RequisitionerName = requisitionItemDTO.RequisitionerName;
                this.ParIdentifier = requisitionItemDTO.ParIdentifier;
                this.MainItemId = requisitionItemDTO.MainItemId;
                this.RequisitionItemStatusTypeId = requisitionItemDTO.RequisitionItemStatusTypeId;
                this.QuantityToOrder = requisitionItemDTO.QuantityToOrder;
                this.QuantityFulfilled = requisitionItemDTO.QuantityFulfilled;
                this.HasQuantityToOrderChanged = requisitionItemDTO.HasQuantityToOrderChanged;
                this.PONumber = requisitionItemDTO.PONumber;
                this.IsRushOrder = requisitionItemDTO.IsRushOrder;
                this.ParentSystemId = requisitionItemDTO.ParentSystemId;
                this.CatalogNumber = requisitionItemDTO.CatalogNumber;
                this.CreatedBy = requisitionItemDTO.CreatedBy;
                this.CreateDate = requisitionItemDTO.CreateDate;
                this.RequisitionScheduledDate = requisitionItemDTO.RequisitionScheduledDate;
                this.IsFileItem = requisitionItemDTO.IsFileItem;
                this.FileItemHasChanged = requisitionItemDTO.FileItemHasChanged;
                this.SmartItemNumber = requisitionItemDTO.SmartItemNumber;
                this.StockIndicator = requisitionItemDTO.StockIndicator;
                this.HasDiscountChanged = requisitionItemDTO.HasDiscountChanged;
                this.Discount = requisitionItemDTO.Discount;
                this.IsWastePar = requisitionItemDTO.IsWastePar;
                this.ParentRequisitionItemId = requisitionItemDTO.ParentRequisitionItemId;
                this.RequisitionItemAutoSubFlag = requisitionItemDTO.ParentRequisitionItemNumber;
                this.Inventory = requisitionItemDTO.Inventory;
                this.IsOnContract = requisitionItemDTO.IsOnContract;
                this.PartsWarrantyMonths = requisitionItemDTO.PartsWarrantyMonths;
                this.LaborWarrantyMonths = requisitionItemDTO.LaborWarrantyMonths;



                if (requisitionItemDTO.ClinicalUseId != null || (requisitionTypeId == (int)RequisitionTypeEnum.BillOnly 
                    || requisitionTypeId == (int)RequisitionTypeEnum.BillAndReplace 
                    || requisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace 
                    || requisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillOnly))
                {
                    if (requisitionItemDTO.LotSerialPairs != null && requisitionItemDTO.LotSerialPairs.Any())
                    {
                        foreach (var lspair in requisitionItemDTO.LotSerialPairs)
                        {
                            this.ClinicalUseDetails.Add(new ClinicalUseDetail()
                            {
                                Id = lspair.ClinicalUseDetailsId,
                                RequisitionItemId = requisitionItemDTO.Id,
                                HasLotNumberChanged = lspair.HasLotNumberChanged,
                                LotNumber = lspair.LotNumber,
                                HasSerialNumberChanged = lspair.HasSerialNumberChanged,
                                SerialNumber = lspair.SerialNumber,
                                UpchargeCost = requisitionItemDTO.UpchargeCost,
                                Provider = requisitionItemDTO.Provider,
                                PatientId = requisitionItemDTO.PatientId,
                                PatientName = requisitionItemDTO.PatientName,
                                ProcedureDate = requisitionItemDTO.ProcedureDate,
                                ProviderHasChanged = requisitionItemDTO.ProviderHasChanged,
                                PatientIdHasChanged = requisitionItemDTO.PatientIdHasChanged,
                                PatientNameHasChanged = requisitionItemDTO.PatientNameHasChanged,
                                ProcedureDateHasChanged = requisitionItemDTO.ProcedureDateHasChanged,
                                ChangeStatus = lspair.ChangeStatus
                            });
                        }
                    }
                    else
                    {
                        this.ClinicalUseDetails.Add(new ClinicalUseDetail()
                        {
                            Id = requisitionItemDTO.ClinicalUseId ?? 0,
                            RequisitionItemId = requisitionItemDTO.Id,
                            UpchargeCost = requisitionItemDTO.UpchargeCost,
                            Provider = requisitionItemDTO.Provider,
                            PatientId = requisitionItemDTO.PatientId,
                            PatientName = requisitionItemDTO.PatientName,
                            ProcedureDate = requisitionItemDTO.ProcedureDate,
                            ProviderHasChanged = requisitionItemDTO.ProviderHasChanged,
                            PatientIdHasChanged = requisitionItemDTO.PatientIdHasChanged,
                            PatientNameHasChanged = requisitionItemDTO.PatientNameHasChanged,
                            ProcedureDateHasChanged = requisitionItemDTO.ProcedureDateHasChanged
                        });
                    }
                }

                HasConversionChanged = requisitionItemDTO.HasConversionChanged;
                if (requisitionItemDTO.SPRDetailDTO != null)
                {
                    this.SPRDetail = new SPRDetail(requisitionItemDTO.SPRDetailDTO, this.Id);
                }

                if (requisitionItemDTO.VboHoldItemConversionDto != null)
                {
                    VboHoldItemConversion = new VboHoldItemConversion(requisitionItemDTO.VboHoldItemConversionDto);
                }

                this.TrackerIndex = requisitionItemDTO.TrackerIndex;

                if((requisitionItemDTO.ParIdentifier!="EPR" && requisitionItemDTO.ParIdentifier != null) || (requisitionItemDTO.IsOnContract ?? false && IsVendor))
                {
                    this.ReOrder = requisitionItemDTO.ReOrder;
                    this.PARLocation = requisitionItemDTO.PARLocation;
                    this.ItemDescription = requisitionItemDTO.ItemDescription;
                    this.VendorId = requisitionItemDTO.VendorId;
                    this.VendorName = requisitionItemDTO.VendorName;
                    this.GeneralLedgerCode = requisitionItemDTO.GLAccount;
                    this.UOMCode = requisitionItemDTO.UOM;
                    this.UnitCost = requisitionItemDTO.UnitCost;
                    this.TotalCost = requisitionItemDTO.TotalCost;
                    this.MinStock = requisitionItemDTO.MinStock;
                    this.MaxStock = requisitionItemDTO.MaxStock;
                }
            }
        }

        public int Id { get; set; }

        public int RequisitionId { get; set; }
        [ForeignKey("RequisitionId")]
        public virtual Requisition Requisition { get; set; }

        [StringLength(50)]
        public string ItemId { get; set; }

        [StringLength(50)]
        public string ParIdentifier { get; set; }

        public int? MainItemId { get; set; }

        [NotMapped]
        public string RequisitionItemAutoSubFlag { get; set; }

        public int? ParentRequisitionItemId { get; set; }

        public int RequisitionItemStatusTypeId { get; set; }

        [ForeignKey("RequisitionItemStatusTypeId")]
        public RequisitionItemStatusType RequisitionItemStatusType { get; set; }

        public int QuantityToOrder { get; set; }

        [NotMapped]
        public bool HasQuantityToOrderChanged { get; set; }

        public bool IsRushOrder { get; set; }

        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }
        public DateTime CreateDate { get; set; }

        [StringLength(50)]
        public string ParentSystemId { get; set; }

        public int? QuantityFulfilled { set; get; }

        //TODO: Remove IsStatusChange [DEPRECATED] - will also require approval workflow changes 
        [NotMapped]
        public bool IsStatusChange { set; get; } 


        [StringLength(50)]
        public string OriginalParentSystemId { get; set; }
        [NotMapped]
        public int? TrackerIndex { get; set; }

        [NotMapped]
        public string RequisitionerName { get; set; }

        private ICollection<RequisitionItemStatusHistory> _requisitionItemStatusHistories;
        public virtual ICollection<RequisitionItemStatusHistory> RequisitionItemStatusHistories
        {
            get { return _requisitionItemStatusHistories ?? (_requisitionItemStatusHistories = new Collection<RequisitionItemStatusHistory>()); }
            set { _requisitionItemStatusHistories = value; }
        }
        
        private ICollection<ClinicalUseDetail> _clinicalUseDetails;
        public virtual ICollection<ClinicalUseDetail> ClinicalUseDetails
        {
            get { return _clinicalUseDetails ?? (_clinicalUseDetails = new Collection<ClinicalUseDetail>()); }
            set { _clinicalUseDetails = value; }
        }

        public virtual SPRDetail SPRDetail { get; set; }

        [NotMapped]
        public bool? HasConversionChanged { get; set; }
        public virtual VboHoldItemConversion VboHoldItemConversion { get; set; }

        public DateTimeOffset? RequisitionScheduledDate { get; set; }

        public bool IsFileItem { get; set; }

        public bool FileItemHasChanged { get; set; }

        public int? PONumber { get; set; }

        public int? SmartItemNumber { get; set; }

        public string ReOrder { get; set; }

        public string CatalogNumber { get; set; }

        public string PARLocation { get; set; }

        public string ItemDescription { get; set; } 

        [DefaultValue(0)]
        public int VendorId { get; set; }

        [StringLength(32)]
        public string VendorName { get; set; }

        [StringLength(12)]
        public string GeneralLedgerCode { get; set; }

        public bool? StockIndicator { get; set; }

        [StringLength(50)]
        public string UOMCode { get; set; }

        public decimal? UnitCost { get; set; }

        public decimal? TotalCost { get; set; }

        public int? MinStock { get; set; }

        public int? MaxStock { get; set; }
        
        [NotMapped]
        public bool HasDiscountChanged { get; set; }
        public decimal? Discount { get; set; }

        [NotMapped]
        public bool IsWastePar { get; set; }

        [NotMapped]
        public string RejectionComments { get; set; }

        public virtual ItemInventory Inventory { get; set; }

        [NotMapped]
        public bool IsPurged { get; set; } 

        [NotMapped]
        public int? DuplicateItemId { get; set; }

        [NotMapped]
        public bool? IsOnContract { get; set; }

        public byte? PartsWarrantyMonths { get; set; }

        public byte? LaborWarrantyMonths { get; set; }
    }
}
