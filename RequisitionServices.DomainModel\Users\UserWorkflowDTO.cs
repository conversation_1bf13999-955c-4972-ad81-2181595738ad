﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Users
{
    public class UserWorkflowDTO
    {
        private string _userName;
        
        public IEnumerable<UserWorkflowStep> UserworkflowSteps { get; set; }

        public string UserName
        {
            get => _userName;
            set => _userName = value.ToLower();
        }

        public int WorkflowTypeId { get; set; }

        public string COID { get; set; }
    }
}
