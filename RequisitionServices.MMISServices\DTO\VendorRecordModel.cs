﻿using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class VendorRecordModel
    {
        public Vendor MapToVendor()
        {
            return new Vendor()
            {
                Id = VendorNumber,
                Name = VendorName
            };
        }

        public VendorDetails MapToVendorDetails()
        {
            return new VendorDetails()
            {
                VendorNumber = VendorNumber,
                Coid = Coid,
                VendorName = VendorName,
                Address1 = Address1,
                Address2 = Address2,
                City = City,
                State = State,
                County = County,
                Country = Country,
                ZipCode = ZipCode,
                WorkPhone = WorkPhone,
                WorkExtension = WorkExtension,
                HomePhone = HomePhone,
                FaxNumber = FaxNumber,
                TermsCode = TermsCode,
                Terms = Terms,
                ShippingMethod = ShippingMethod,
                FreightOnBoard = FreightOnBoard,
                NumberOfDaysInOrderCycle = NumberOfDaysInOrderCycle,
                ContactName = ContactName,
                StandardNationalVendorNumber = StandardNationalVendorNumber,
                EdiFlag = EdiFlag,
                EdiId = EdiId,
                JitFlag = JitFlag,
                JitId = JitId,
                FaxFlag = FaxFlag,
                EmailFlag = EmailFlag,
                OrderFillRatio = OrderFillRatio,
                SendFlag867 = SendFlag867,
                FreightOnBoardOverrideFlag = FreightOnBoardOverrideFlag,
                Status = Status,
                FillKillFlag = FillKillFlag,
                BuyerId = BuyerId,
                AccountsPayable = AccountsPayable,
                OrderDay = OrderDay,
                CustomerId = CustomerId,
                SupplierId = SupplierId,
                AddressId = AddressId,
                ParentVendor = ParentVendor,
                ParentCoid = ParentCoid,
                MarkUpPercent = MarkUpPercent,
                SpecialOption = SpecialOption,
                HealthIndustryNumberFlag = HealthIndustryNumberFlag,
                HealthIndustryNumber = HealthIndustryNumber,
                UniversalProductNumberFLAG = UniversalProductNumberFLAG,
                MinorityFlag = MinorityFlag,
                FreightFlag = FreightFlag,
                ShipFromCity = ShipFromCity,
                ShipFromCounty = ShipFromCounty,
                ShipFromState = ShipFromState,
                ShipFromZip = ShipFromZip,
                ShipInCityFlag = ShipInCityFlag,
                ShipOrderAcceptanceCity = ShipOrderAcceptanceCity,
                ShipOrderAcceptanceCounty = ShipOrderAcceptanceCounty,
                ShipOrderAcceptanceState = ShipOrderAcceptanceState,
                ShipOrderAcceptanceZipCode = ShipOrderAcceptanceZipCode,
                ShipOrderAcceptanceInCityFlag = ShipOrderAcceptanceInCityFlag,
                MinimumDollar = MinimumDollar,
                MinimumQuantity = MinimumQuantity,
                YearToDateDollar = YearToDateDollar,
                YearToDatePo = YearToDatePo,
                LastYearToDateDollar = LastYearToDateDollar,
                LastYearToDatePo = LastYearToDatePo,
                LastYearYear = LastYearYear,
                NumberOfDeliveries = NumberOfDeliveries,
                LastYearNumberOfDeliveries = LastYearNumberOfDeliveries,
                DateIncrement = DateIncrement,
                AverageLine = AverageLine,
                AlternateFaxNumber1 = AlternateFaxNumber1,
                AlternateFaxNumber2 = AlternateFaxNumber2,
                AlternateFaxNumber3 = AlternateFaxNumber3,
                AlternateFaxIdentifier1 = AlternateFaxIdentifier1,
                AlternateFaxIdentifier2 = AlternateFaxIdentifier2,
                AlternateFaxIdentifier3 = AlternateFaxIdentifier3,
                AlternateEmailAddress1 = AlternateEmailAddress1,
                AlternateEmailAddress2 = AlternateEmailAddress2,
                AlternateEmailAddress3 = AlternateEmailAddress3,
                AlternateEmailIdentifier1 = AlternateEmailIdentifier1,
                AlternateEmailIdentifier2 = AlternateEmailIdentifier2,
                AlternateEmailIdentifier3 = AlternateEmailIdentifier3,
                OrderTimes = this.OrderTimes.MapToOrderTimes()
            };
        }

        public int VendorNumber { get; set; }

        public int Coid { get; set; }

        public string VendorName { get; set; }

        public string Address1 { get; set; }

        public string Address2 { get; set; }

        public string City { get; set; }

        public string State { get; set; }

        public string County { get; set; }

        public string Country { get; set; }

        public string ZipCode { get; set; }

        public string WorkPhone { get; set; }

        public string WorkExtension { get; set; }

        public string HomePhone { get; set; }

        public string FaxNumber { get; set; }

        public int TermsCode { get; set; }

        public string Terms { get; set; }

        public string ShippingMethod { get; set; }

        public string FreightOnBoard { get; set; }

        public int NumberOfDaysInOrderCycle { get; set; }

        public string ContactName { get; set; }

        public int StandardNationalVendorNumber { get; set; }

        public bool EdiFlag { get; set; }

        public string EdiId { get; set; }

        public string JitFlag { get; set; }

        public string JitId { get; set; }

        public bool FaxFlag { get; set; }

        public bool EmailFlag { get; set; }

        public decimal OrderFillRatio { get; set; }

        public bool SendFlag867 { get; set; }

        public bool FreightOnBoardOverrideFlag { get; set; }

        public string Status { get; set; }

        public bool FillKillFlag { get; set; }

        public string BuyerId { get; set; }

        public int AccountsPayable { get; set; }

        public string OrderDay { get; set; }

        public string CustomerId { get; set; }

        public string SupplierId { get; set; }

        public int AddressId { get; set; }

        public int ParentVendor { get; set; }

        public int ParentCoid { get; set; }

        public decimal MarkUpPercent { get; set; }

        public List<string> SpecialOption { get; set; }

        public bool HealthIndustryNumberFlag { get; set; }

        public string HealthIndustryNumber { get; set; }

        public bool UniversalProductNumberFLAG { get; set; }

        public bool MinorityFlag { get; set; }

        public bool FreightFlag { get; set; }

        public string ShipFromCity { get; set; }

        public string ShipFromCounty { get; set; }

        public string ShipFromState { get; set; }

        public string ShipFromZip { get; set; }

        public bool ShipInCityFlag { get; set; }

        public string ShipOrderAcceptanceCity { get; set; }

        public string ShipOrderAcceptanceCounty { get; set; }

        public string ShipOrderAcceptanceState { get; set; }

        public string ShipOrderAcceptanceZipCode { get; set; }

        public bool ShipOrderAcceptanceInCityFlag { get; set; }

        public int MinimumDollar { get; set; }

        public int MinimumQuantity { get; set; }

        public int YearToDateDollar { get; set; }

        public int YearToDatePo { get; set; }

        public int LastYearToDateDollar { get; set; }

        public int LastYearToDatePo { get; set; }

        public int LastYearYear { get; set; }

        public int NumberOfDeliveries { get; set; }

        public int LastYearNumberOfDeliveries { get; set; }

        public DateTime DateIncrement { get; set; }

        public decimal AverageLine { get; set; }

        public string AlternateFaxNumber1 { get; set; }

        public string AlternateFaxNumber2 { get; set; }

        public string AlternateFaxNumber3 { get; set; }

        public string AlternateFaxIdentifier1 { get; set; }

        public string AlternateFaxIdentifier2 { get; set; }

        public string AlternateFaxIdentifier3 { get; set; }

        public string AlternateEmailAddress1 { get; set; }

        public string AlternateEmailAddress2 { get; set; }

        public string AlternateEmailAddress3 { get; set; }

        public string AlternateEmailIdentifier1 { get; set; }

        public string AlternateEmailIdentifier2 { get; set; }

        public string AlternateEmailIdentifier3 { get; set; }

        public OrderTimesDTO OrderTimes { get; set; }

        public VendorRecordModel()
        {

        }
    }
}
