﻿using System;
using RequisitionServices.DomainModel.Users;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IUserRepository
    {
        User GetUser(int id);

        User GetUser(string userName);

        User GetUserWithoutDomain(string userName);

        IEnumerable<User> GetUsers(IEnumerable<string> accountNames);

        IEnumerable<User> GetUsersWithUserProfiles(IEnumerable<UserProfile> profiles);

        IEnumerable<User> GetUsers(IEnumerable<int> ids);

        User InsertUser(User user);

        void UpdateUser(User user, string comments, string userName);

        Approver GetApprover(int id);

        Approver GetApproverByUserId(int userId, string COID);

        Approver GetApproverByUserNameAndCOID(string userName, string COID);

        Task<IEnumerable<ActiveApproversDto>> GetActiveApproversAsync();

        Task<IEnumerable<ApproverWorkflowDto>> GetApproverWorkflowsAsync(int approverUserId);


        Approver GetApproverByUserIdCOIDAndDelegateId(int? userId, string COID, int delegateId);

        IEnumerable<Approver> GetApproversByUserId(int userId);

        IEnumerable<Approver> GetApprovers(IEnumerable<string> accountNames, string COID);

        Task<IEnumerable<Approver>> GetApproversAsync(IEnumerable<string> accountNames, string COID);

        IEnumerable<Approver> GetApproversWithUserProfilesInUser(IEnumerable<UserProfile> profiles, string COID);

        IEnumerable<Approver> GetApprovers(IEnumerable<int> ids);

        Approver InsertApprover(Approver approver, string comments);

        void UpdateApprover(Approver approver, string comments, string userName);

        bool IsApproverByUserName(string userName);

        IEnumerable<UserReportAndEditDBInfo> GetUserReportAndEditDBInfos(IEnumerable<UserReportProfileDTO> userNames, string coid);

        string GetFavoriteFacilityId(string userName);

        PersonalizationDTO SetFavoriteFacility(PersonalizationDTO personalizationDTO);

        void DeleteFavoriteFacility(string userName);

        int GetFavoriteDepartmentId(string userName, string COID);

        PersonalizationDTO SetFavoriteDepartment(PersonalizationDTO personalization);

        void DeleteFavoriteDepartment(PersonalizationDTO personalization);

        string GetFavoriteParId(string userName, string COID, int departmentId);

        PersonalizationDTO SetFavoritePar(PersonalizationDTO personalization);

        void DeleteFavoritePar(PersonalizationDTO personalization);

        BulkApproverJobTracker GetBulkApproverJobTracker(Guid bulkApproverId);

        void UpdateBulkApproverJobTrackerStatus(BulkApproverJobStatusDTO bulkApproverJobStatusDto);

        Task<ActiveApproversDto> GetUserAndDelegateAsync(int selectedApproverUserId);
    }
}
