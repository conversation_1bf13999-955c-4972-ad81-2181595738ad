﻿using Microsoft.Web.Http;
using RequisitionServices.DomainModel.PurchaseOrders;
using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IPOService
    {
        PODetails GetDetailsByPO(string userName, string COID, string PONumber, string stockIndicator, ApiVersion version = null);

        List<POConfirmationDetail> GetPOConfirmationDetails(string userName, string COID, string PONumber, string lineNumber);

        List<POLists> GetDetailsByDateRange(string userName, string COID, DateTime startDate, DateTime endDate, int department);

        List<POOptions> GetPoByOptions(string userName, string COID, DateTime startDate, DateTime endDate, string poType, int department, string reorderNumber);

        List<POLists> GetPoByProjectNumber(string userName, string COID, string ProjectNumber);

        List<POHistory> GetHistoryByPO(string userName, string COID, string PONumber);

    }
}
