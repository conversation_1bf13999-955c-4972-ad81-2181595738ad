﻿using System;
using System.Collections.Generic;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.PurchaseOrders;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartPOService
    {
        PODetails GetDetailsByPO(string userName, string COID, string PONumber, string stockIndicator, ApiVersion version = null);

        IEnumerable<POConfirmationDetail> GetPOConfirmationDetails(string userName, string COID, string PONumber, string lineNumber);

        IEnumerable<POLists> GetDetailsByDateRange(string userName, string coid, DateTime startDate, DateTime endDate, int department);

        IEnumerable<POHistory> GetHistoryByPO(string userName, string coid, string PONumber);

        IEnumerable<POOptions> GetPoByOptions(string userName, string coid, DateTime startDate, DateTime endDate, string poType, int department, string reorderNumber);

        IEnumerable<POLists> GetPoHeaderInfoByPoNumbers(string userName, string coid, IEnumerable<int> poNumbers);
    }
}
