﻿using RequisitionServices.DomainModel.Locations;
using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class BillOnlyReviewRequest
    {
        /// <summary>
        /// Gets the patient ID.
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// Gets the start date.
        /// </summary>
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Gets the end date.
        /// </summary>
        public DateTime EndDate { get; set; }

        /// <summary>
        /// Gets the Procedure Date.
        /// </summary>
        public DateTime? ProcedureDate { get; set; }

        /// <summary>
        /// Gets the Patients Last Name.
        /// </summary>
        public string PatientLastName { get; set; }

        /// <summary>
        /// Gets the Patients Last Name.
        /// </summary>
        public string ProviderLastName { get; set; }

        /// <summary>
        /// Gets the list of Facility IDs.
        /// </summary>
        public List<Facility> Facilities { get; set; }

        /// <summary>
        /// Gets or sets the current page number for paginated results.
        /// Defaults to 1.
        /// </summary>
        public int PageNumber { get; set; }
        /// <summary>
        /// Gets or sets the number of items per page for paginated results.
        /// Defaults to 10.
        /// </summary>
        public int PageSize { get; set; }
        /// <summary>
        /// Gets or sets the sort type for the results.
        /// </summary>
        public int SortType { get; set; }
        /// <summary>
        /// Gets or sets the search term for requisition filtering.
        /// </summary>
        public string RequisitionSearch { get; set; }
        /// <summary>
        /// Gets or sets the list of matched locations based on the search criteria.
        /// </summary>
        public List<string> Locations { get; set; }

        /// <summary>  
        /// Gets or sets the username of the user making the request.  
        /// </summary>  
        public string UserName { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="BillOnlyReviewRequest"/> class
        /// with default values for pagination.
        /// </summary>
        public BillOnlyReviewRequest()
        {
            PageNumber = 1; // default to first page
            PageSize = 10;  // default page size
        }
    }
}
