﻿using log4net;
using RequisitionServices.Utility.Domain;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using RequisitionServices.DomainModel.Users;
using System.Data.SqlClient;
using System.Configuration;
using RequisitionServices.DomainModel.FacilityWorkflow;

namespace RequisitionServices.DomainServices
{
    public class WorkflowService : IWorkflowService
    {
        private string workflowConnectionString = ConfigurationManager.ConnectionStrings["WorkflowPersistanceStore"].ConnectionString;

        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private IWorkflowRepository workflowRepository;
        private IFacilityWorkflowRepository facilityWorkflowRepository;
        private IRequisitionRepository requisitionRepository;
        private IConfigurationService configurationService;

        public WorkflowService(IWorkflowRepository workflowRepo, IFacilityWorkflowRepository facilityWorkflowRepo, IRequisitionRepository requisitionRepo, IConfigurationService configurationSvc)
        {
            workflowRepository = workflowRepo;
            facilityWorkflowRepository = facilityWorkflowRepo;
            requisitionRepository = requisitionRepo;
            configurationService = configurationSvc;
        }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserName(string userName)
        {
            return workflowRepository.GetUserWorkflowStepsDelegatedByUserName(userName);
        }

        public IEnumerable<UserWorkflowStep> GetUserWorkflowStepsDelegatedByUserId(int userId)
        {
            return workflowRepository.GetUserWorkflowStepsDelegatedByUserId(userId);
        }

        public void DenyRequisition(Requisition requisition, int denierId)
        {
            if (requisition != null)
            {
                if (requisition.IsVendor)
                {
                    //Remove all VBO conversions for these items
                    foreach (var requisitionItem in requisition.RequisitionItems)
                    {
                        requisitionItem.VboHoldItemConversion = null;
                    }

                    var vendorReq = this.MapRequisitionToVendorWorkflowRequisition(requisition, denierId);

                    //Deny all items
                    foreach (var requisitionItem in vendorReq.RequisitionItems)
                    {
                        requisitionItem.IsApproved = false;
                    }

                    this.ApproveDenyVendorRequisitionToService(vendorReq);
                }
                else if (requisition.IsRush)
                {
                    var rushReq = this.MapRequisitionToRushWorkflowRequisition(requisition, denierId, null);

                    //Deny all items
                    foreach (var requisitionItem in rushReq.RequisitionItems)
                    {
                        requisitionItem.IsApproved = false;
                    }

                    this.ApproveDenyRushRequisitionToService(rushReq);
                }
                else
                {
                    var nonRushReq = this.MapRequisitionToNonRushWorkflowRequisition(requisition, denierId, null);

                    //Deny all items
                    foreach (var requisitionItem in nonRushReq.RequisitionItems)
                    {
                        requisitionItem.IsApproved = false;
                    }

                    this.ApproveDenyNonRushRequisitionToService(nonRushReq);
                }
            }
        }

        public void ApproveRequistion(DomainModel.Requisitions.Requisition requisition, int approverId, int approvalStep)
        {
            if (requisition != null)
            {
                if (requisition.IsVendor)
                {
                    var vendorReq = this.MapRequisitionToVendorWorkflowRequisition(requisition, approverId);

                    //Approve all items
                    foreach (var requisitionItem in vendorReq.RequisitionItems)
                    {
                        requisitionItem.IsApproved = true;
                    }

                    this.ApproveDenyVendorRequisitionToService(vendorReq);
                }
                else if (requisition.IsRush)
                {
                    var rushReq = this.MapRequisitionToRushWorkflowRequisition(requisition, approverId, approvalStep);

                    //Approve all items
                    foreach (var requisitionItem in rushReq.RequisitionItems)
                    {
                        requisitionItem.IsApproved = true;
                    }

                    this.ApproveDenyRushRequisitionToService(rushReq);
                }
                else
                {
                    var nonRushReq = this.MapRequisitionToNonRushWorkflowRequisition(requisition, approverId, approvalStep);

                    //Approve all items
                    foreach (var requisitionItem in nonRushReq.RequisitionItems)
                    {
                        requisitionItem.IsApproved = true;
                    }

                    this.ApproveDenyNonRushRequisitionToService(nonRushReq);
                }
            }
        }
        
        public DomainModel.Requisitions.Requisition StartWorkflow(DomainModel.Requisitions.Requisition requisition, string userName)
        {
            bool isWorkflowRequired = false;
            bool isVendorReqBlocked = false;

            if (requisition.IsVendor)
            {
                requisition.ApprovalStep = 0;
            }
            else if (requisition.ApprovalStep == null || requisition.ApprovalStep == 0)
            {
                requisition.ApprovalStep = 1;
            }

            try
            {
                if (requisition.IsVendor)
                {
                    isVendorReqBlocked = this.InitiateVendorWorkflow(ref requisition);
                }
                else if (requisition.IsRush)
                {
                    isWorkflowRequired = this.InitiateRushWorkflow(ref requisition);
                }
                else
                {
                    isWorkflowRequired = this.InitiateNonRushWorkflow(ref requisition);
                }            
            }
            catch(Exception ex)
            {
                log.Error(String.Format("Error calling workflow - Requisition ({0}) set to SUBMISSION ERROR", requisition.RequisitionId.ToString()), ex);
                requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.SubmissionError;
            }

            if (requisition.IsVendor)
            {
                if (isVendorReqBlocked)
                {
                    requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.OnHold;
                }
                else
                {
                    requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.PendingApproval;
                }
            }
            else if (isWorkflowRequired)
            {   
                requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.PendingApproval;
            }
            else
            {
                requisition.ApprovalStep = null;
            }

            return requisition;
        }

        private RushWorkflowSvc.Requisition MapRequisitionToRushWorkflowRequisition(DomainModel.Requisitions.Requisition requisition, int? approverId, int? approvalStep)
        {
            if (requisition == null)
            {
                return null;
            }

            var approvers = new List<RushWorkflowSvc.RequisitionApprover>();
            var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
            if (!String.IsNullOrWhiteSpace(COID))
            {
                //Can these not be passed in?
                var userWorkflowSteps = workflowRepository.GetUserWorkflowSteps(requisition.CreatedBy, COID);

                userWorkflowSteps = userWorkflowSteps.Where(x => x.WorkflowTypeId == (int)requisition.ApplicableWorkflowType);

                if (userWorkflowSteps != null)
                {
                    userWorkflowSteps = userWorkflowSteps.Where(x => x.Approver != null);

                    //Map over to RequisitionApprovers 
                    foreach (var userWorkflowStep in userWorkflowSteps)
                    {
                        var userWorkflowStepMaxApprovalAmountForWorkflowType = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? userWorkflowStep.Approver.CapitalMaxApprovalAmount : userWorkflowStep.Approver.MaxApprovalAmount;

                        if (approverId != null)
                        {
                            var isCurrentApprover = false;
                            if (approvalStep != null)
                            {
                                if (approvalStep == userWorkflowStep.Step)
                                {
                                    isCurrentApprover = userWorkflowStep.ApproverId == approverId;
                                }
                            }
                            else
                            {
                                isCurrentApprover = userWorkflowStep.ApproverId == approverId;
                            }

                            approvers.Add(new RushWorkflowSvc.RequisitionApprover()
                            {
                                ApprovalLevel = userWorkflowStep.Step,
                                ApprovalLimit = userWorkflowStepMaxApprovalAmountForWorkflowType,
                                ApproverId = userWorkflowStep.ApproverId,
                                IsCurrentApprover = isCurrentApprover
                            });
                        }
                        else
                        {
                            approvers.Add(new RushWorkflowSvc.RequisitionApprover()
                            {
                                ApprovalLevel = userWorkflowStep.Step,
                                ApprovalLimit = userWorkflowStepMaxApprovalAmountForWorkflowType,
                                ApproverId = userWorkflowStep.ApproverId,
                                IsCurrentApprover = userWorkflowStep.Step == requisition.ApprovalStep
                            });
                        }
                    }

                    //If not current approver, need to check for dropping down user, set last instance of them to current approver
                    if(approverId != null && approvers != null && !approvers.Where(x => x.IsCurrentApprover).Any())
                    {
                        var stepsUserIsIn = approvers.Where(x => x.ApproverId == approverId);
                        if(stepsUserIsIn.Any())
                        {
                            if (userWorkflowSteps.Any(x => x.IsFinalRushStep && x.ApproverId == approverId))
                            {
                                stepsUserIsIn.Where(x => x.ApprovalLevel == (stepsUserIsIn.Last().ApprovalLevel - 1)).FirstOrDefault().IsCurrentApprover = true;
                            }
                            else
                            {
                                stepsUserIsIn.Last().IsCurrentApprover = true;
                            }
                            
                        }
                    }
                }
            }
            
            var workflowRequisitionItems = new List<RushWorkflowSvc.RequisitionItem>();
            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    var workflowClinicalUse = new List<RushWorkflowSvc.ClinicalUseDetail>();
                    if (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any())
                    {
                        workflowClinicalUse.Add(new RushWorkflowSvc.ClinicalUseDetail()
                        {
                            Id = requisitionItem.ClinicalUseDetails.First().Id,
                            RequisitionItemId = requisitionItem.ClinicalUseDetails.First().RequisitionItemId,
                            LotNumber = requisitionItem.ClinicalUseDetails.First().LotNumber,
                            ProcedureDate = requisitionItem.ClinicalUseDetails.First().ProcedureDate,
                            PatientId = requisitionItem.ClinicalUseDetails.First().PatientId,
                            Provider = requisitionItem.ClinicalUseDetails.First().Provider,
                            SerialNumber = requisitionItem.ClinicalUseDetails.First().SerialNumber,
                            UpchargeCost = requisitionItem.ClinicalUseDetails.First().UpchargeCost
                        });
                    }
                    var workflowSPRDetail = new RushWorkflowSvc.SPRDetail();
                    if (requisitionItem.SPRDetail != null)
                    {
                        workflowSPRDetail.AdditionalInformation = requisitionItem.SPRDetail.AdditionalInformation;
                        workflowSPRDetail.BudgetNumber = requisitionItem.SPRDetail.AdditionalInformation;
                        workflowSPRDetail.DeliveryMethodTypeId = requisitionItem.SPRDetail.DeliveryMethodTypeId;
                        workflowSPRDetail.EstimatedPrice = requisitionItem.SPRDetail.EstimatedPrice;
                        workflowSPRDetail.GeneralLedgerCode = requisitionItem.SPRDetail.GeneralLedgerCode;
                        workflowSPRDetail.ItemDescription = requisitionItem.SPRDetail.ItemDescription;
                        workflowSPRDetail.ParIdentifier = requisitionItem.SPRDetail.ParIdentifier;
                        workflowSPRDetail.PartNumber = requisitionItem.SPRDetail.PartNumber;
                        workflowSPRDetail.ShipToAddressId = requisitionItem.SPRDetail.ShipToAddressId;
                        workflowSPRDetail.TradeInValue = requisitionItem.SPRDetail.TradeInValue;
                        workflowSPRDetail.ShipToAddressId = requisitionItem.SPRDetail.ShipToAddressId;
                        workflowSPRDetail.UOMCode = requisitionItem.SPRDetail.UOMCode;
                        workflowSPRDetail.VendorId = requisitionItem.SPRDetail.VendorId;
                    }
                    else
                    {
                        workflowSPRDetail = null;
                    }

                    workflowRequisitionItems.Add(new RushWorkflowSvc.RequisitionItem()
                    {
                        Id = requisitionItem.Id,
                        RequisitionId = requisitionItem.RequisitionId,
                        CreateDate = requisitionItem.CreateDate,
                        CreatedBy = requisitionItem.CreatedBy,
                        IsRushOrder = requisitionItem.IsRushOrder,
                        RequisitionScheduledDate = requisitionItem.RequisitionScheduledDate.HasValue ? requisitionItem.RequisitionScheduledDate.Value.LocalDateTime : (DateTime?) null,
                        RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId,
                        ClinicalUseDetails = workflowClinicalUse.ToArray(),
                        SPRDetail = workflowSPRDetail,
                        ItemId = requisitionItem.ItemId,
                        MainItemId = requisitionItem.MainItemId,
                        ParIdentifier = requisitionItem.ParIdentifier,
                        QuantityToOrder = requisitionItem.QuantityToOrder
                    });
                }
            }

            var workflowRequisition = new RushWorkflowSvc.Requisition()
            {
                Comments = requisition.Comments,
                CreateDate = requisition.CreateDate,
                CreatedBy = requisition.CreatedBy,
                LocationIdentifier = requisition.LocationIdentifier,
                Approvers = approvers.OrderBy(x => x.ApprovalLevel).ToArray(),
                RequisitionId = requisition.RequisitionId,
                RequisitionItems = workflowRequisitionItems.ToArray(),
                RequisitionStatusTypeId = requisition.RequisitionStatusTypeId,
                RequisitionTypeId = requisition.RequisitionTypeId
            };

            return workflowRequisition;
        }

        private NonRushWorkflowSvc.Requisition MapRequisitionToNonRushWorkflowRequisition(DomainModel.Requisitions.Requisition requisition, int? approverId, int? approvalStep)
        {
            if (requisition == null)
            {
                return null;
            }

            var approvers = new List<NonRushWorkflowSvc.RequisitionApprover>();
            var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
            if (!String.IsNullOrWhiteSpace(COID))
            {
                //Can these not be passed in?
                var userWorkflowSteps = workflowRepository.GetUserWorkflowSteps(requisition.CreatedBy, COID);

                userWorkflowSteps = userWorkflowSteps.Where(x => x.WorkflowTypeId == (int)requisition.ApplicableWorkflowType);
                if (userWorkflowSteps != null)
                {
                    userWorkflowSteps = userWorkflowSteps.Where(x => x.Approver != null);

                    //Map over to RequisitionApprovers 
                    foreach (var userWorkflowStep in userWorkflowSteps)
                    {
                        var userWorkflowStepMaxApprovalAmountForWorkflowType = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? userWorkflowStep.Approver.CapitalMaxApprovalAmount : userWorkflowStep.Approver.MaxApprovalAmount;
                        if (approverId != null)
                        {
                            var isCurrentApprover = false;
                            if (approvalStep != null)
                            {
                                if (approvalStep == userWorkflowStep.Step)
                                {
                                    isCurrentApprover = userWorkflowStep.ApproverId == approverId;
                                }
                            }
                            else
                            {
                                isCurrentApprover = userWorkflowStep.ApproverId == approverId;
                            }

                            approvers.Add(new NonRushWorkflowSvc.RequisitionApprover()
                            {
                                ApprovalLevel = userWorkflowStep.Step,
                                ApprovalLimit = userWorkflowStepMaxApprovalAmountForWorkflowType,
                                ApproverId = userWorkflowStep.ApproverId,
                                IsCurrentApprover = isCurrentApprover
                            });
                        }
                        else
                        {
                            approvers.Add(new NonRushWorkflowSvc.RequisitionApprover()
                            {
                                ApprovalLevel = userWorkflowStep.Step,
                                ApprovalLimit = userWorkflowStepMaxApprovalAmountForWorkflowType,
                                ApproverId = userWorkflowStep.ApproverId,
                                IsCurrentApprover = userWorkflowStep.Step == requisition.ApprovalStep
                            });
                        }
                    }

                    //If not current approver, need to check for dropping down user, set last instance of them to current approver
                    if (approverId != null && approvers != null && !approvers.Where(x => x.IsCurrentApprover).Any())
                    {
                        var stepsUserIsIn = approvers.Where(x => x.ApproverId == approverId);
                        if (stepsUserIsIn.Any())
                        {
                            stepsUserIsIn.Last().IsCurrentApprover = true;
                        }
                    }
                }
            }
            
            var workflowRequisitionItems = new List<NonRushWorkflowSvc.RequisitionItem>();
            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    var workflowClinicalUse = new List<NonRushWorkflowSvc.ClinicalUseDetail>();
                    if (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any())
                    {
                        workflowClinicalUse.Add(new NonRushWorkflowSvc.ClinicalUseDetail()
                        {
                            Id = requisitionItem.ClinicalUseDetails.First().Id,
                            RequisitionItemId = requisitionItem.ClinicalUseDetails.First().RequisitionItemId,
                            LotNumber = requisitionItem.ClinicalUseDetails.First().LotNumber,
                            ProcedureDate = requisitionItem.ClinicalUseDetails.First().ProcedureDate,
                            PatientId = requisitionItem.ClinicalUseDetails.First().PatientId,
                            Provider = requisitionItem.ClinicalUseDetails.First().Provider,
                            SerialNumber = requisitionItem.ClinicalUseDetails.First().SerialNumber,
                            UpchargeCost = requisitionItem.ClinicalUseDetails.First().UpchargeCost
                        });
                    }
                    var workflowSPRDetail = new NonRushWorkflowSvc.SPRDetail();
                    if (requisitionItem.SPRDetail != null)
                    {
                        workflowSPRDetail.AdditionalInformation = requisitionItem.SPRDetail.AdditionalInformation;
                        workflowSPRDetail.BudgetNumber = requisitionItem.SPRDetail.AdditionalInformation;
                        workflowSPRDetail.DeliveryMethodTypeId = requisitionItem.SPRDetail.DeliveryMethodTypeId;
                        workflowSPRDetail.EstimatedPrice = requisitionItem.SPRDetail.EstimatedPrice;
                        workflowSPRDetail.GeneralLedgerCode = requisitionItem.SPRDetail.GeneralLedgerCode;
                        workflowSPRDetail.ItemDescription = requisitionItem.SPRDetail.ItemDescription;
                        workflowSPRDetail.ParIdentifier = requisitionItem.SPRDetail.ParIdentifier;
                        workflowSPRDetail.PartNumber = requisitionItem.SPRDetail.PartNumber;
                        workflowSPRDetail.ShipToAddressId = requisitionItem.SPRDetail.ShipToAddressId;
                        workflowSPRDetail.TradeInValue = requisitionItem.SPRDetail.TradeInValue;
                        workflowSPRDetail.ShipToAddressId = requisitionItem.SPRDetail.ShipToAddressId;
                        workflowSPRDetail.UOMCode = requisitionItem.SPRDetail.UOMCode;
                        workflowSPRDetail.VendorId = requisitionItem.SPRDetail.VendorId;
                    }
                    else
                    {
                        workflowSPRDetail = null;
                    }

                    workflowRequisitionItems.Add(new NonRushWorkflowSvc.RequisitionItem()
                    {
                        Id = requisitionItem.Id,
                        RequisitionId = requisitionItem.RequisitionId,
                        CreateDate = requisitionItem.CreateDate,
                        CreatedBy = requisitionItem.CreatedBy,
                        IsRushOrder = requisitionItem.IsRushOrder,
                        RequisitionScheduledDate = requisitionItem.RequisitionScheduledDate.HasValue ? requisitionItem.RequisitionScheduledDate.Value.LocalDateTime : (DateTime?) null,
                        RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId,
                        ClinicalUseDetails = workflowClinicalUse.ToArray(),
                        SPRDetail = workflowSPRDetail,
                        ItemId = requisitionItem.ItemId,
                        MainItemId = requisitionItem.MainItemId,
                        ParIdentifier = requisitionItem.ParIdentifier,
                        QuantityToOrder = requisitionItem.QuantityToOrder
                    });
                }
            }

            var workflowRequisition = new NonRushWorkflowSvc.Requisition()
            {
                Comments = requisition.Comments,
                CreateDate = requisition.CreateDate,
                CreatedBy = requisition.CreatedBy,
                LocationIdentifier = requisition.LocationIdentifier,
                Approvers = approvers.OrderBy(x => x.ApprovalLevel).ToArray(),
                RequisitionId = requisition.RequisitionId,
                RequisitionItems = workflowRequisitionItems.ToArray(),
                RequisitionStatusTypeId = requisition.RequisitionStatusTypeId,
                RequisitionTypeId = requisition.RequisitionTypeId
            };

            return workflowRequisition;
        }
        
        private VendorWorkflowSvc.Requisition MapRequisitionToVendorWorkflowRequisition(DomainModel.Requisitions.Requisition requisition, int? approverId)
        {
            if (requisition == null)
            {
                return null;
            }

            var approvers = new List<VendorWorkflowSvc.RequisitionApprover>();
            var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
            if (!String.IsNullOrWhiteSpace(COID))
            {
                var userWorkflowSteps = workflowRepository.GetUserWorkflowSteps(requisition.CreatedBy, COID);

                IEnumerable<FacilityWorkflowStep> facilityWorkflowSteps = facilityWorkflowRepository.Get(COID, WorkflowTypeEnum.Vendor);

                if (facilityWorkflowSteps != null)
                {
                    facilityWorkflowSteps = facilityWorkflowSteps.Where(x => x.Approver != null);

                    //Map over to RequisitionApprovers 
                    foreach (var facilityWorkflowStep in facilityWorkflowSteps)
                    {
                        var facilityWorkflowStepMaxApprovalAmountForWorkflowType = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? facilityWorkflowStep.Approver.CapitalMaxApprovalAmount : facilityWorkflowStep.Approver.MaxApprovalAmount;
                        if (approverId != null)
                        {
                            var isCurrentApprover = facilityWorkflowStep.ApproverId == approverId;

                            approvers.Add(new VendorWorkflowSvc.RequisitionApprover()
                            {
                                ApprovalLevel = 0,
                                ApprovalLimit = facilityWorkflowStepMaxApprovalAmountForWorkflowType,
                                ApproverId = facilityWorkflowStep.ApproverId,
                                IsCurrentApprover = isCurrentApprover
                            });
                        }
                        else
                        {
                            approvers.Add(new VendorWorkflowSvc.RequisitionApprover()
                            {
                                ApprovalLevel = 0,
                                ApprovalLimit = facilityWorkflowStepMaxApprovalAmountForWorkflowType,
                                ApproverId = facilityWorkflowStep.ApproverId,
                                IsCurrentApprover = 0 == requisition.ApprovalStep
                            });
                        }
                    }

                    //If not current approver, need to check for dropping down user, set last instance of them to current approver
                    if (approverId != null && approvers != null && !approvers.Where(x => x.IsCurrentApprover).Any())
                    {
                        var stepsUserIsIn = approvers.Where(x => x.ApproverId == approverId);
                        if (stepsUserIsIn.Any())
                        {
                            stepsUserIsIn.Last().IsCurrentApprover = true;
                        }
                    }
                }
            }
            
            var workflowRequisitionItems = new List<VendorWorkflowSvc.RequisitionItem>();
            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    var workflowClinicalUse = new List<VendorWorkflowSvc.ClinicalUseDetail>();
                    if (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any())
                    {
                        workflowClinicalUse.Add(new VendorWorkflowSvc.ClinicalUseDetail()
                        {
                            Id = requisitionItem.ClinicalUseDetails.First().Id,
                            RequisitionItemId = requisitionItem.ClinicalUseDetails.First().RequisitionItemId,
                            LotNumber = requisitionItem.ClinicalUseDetails.First().LotNumber,
                            ProcedureDate = requisitionItem.ClinicalUseDetails.First().ProcedureDate,
                            PatientId = requisitionItem.ClinicalUseDetails.First().PatientId,
                            Provider = requisitionItem.ClinicalUseDetails.First().Provider,
                            SerialNumber = requisitionItem.ClinicalUseDetails.First().SerialNumber,
                            UpchargeCost = requisitionItem.ClinicalUseDetails.First().UpchargeCost
                        });
                    }
                    var workflowSPRDetail = new VendorWorkflowSvc.SPRDetail();
                    if (requisitionItem.SPRDetail != null)
                    {
                        workflowSPRDetail.AdditionalInformation = requisitionItem.SPRDetail.AdditionalInformation;
                        workflowSPRDetail.BudgetNumber = requisitionItem.SPRDetail.AdditionalInformation;
                        workflowSPRDetail.DeliveryMethodTypeId = requisitionItem.SPRDetail.DeliveryMethodTypeId;
                        workflowSPRDetail.EstimatedPrice = requisitionItem.SPRDetail.EstimatedPrice;
                        workflowSPRDetail.GeneralLedgerCode = requisitionItem.SPRDetail.GeneralLedgerCode;
                        workflowSPRDetail.ItemDescription = requisitionItem.SPRDetail.ItemDescription;
                        workflowSPRDetail.ParIdentifier = requisitionItem.SPRDetail.ParIdentifier;
                        workflowSPRDetail.PartNumber = requisitionItem.SPRDetail.PartNumber;
                        workflowSPRDetail.ShipToAddressId = requisitionItem.SPRDetail.ShipToAddressId;
                        workflowSPRDetail.TradeInValue = requisitionItem.SPRDetail.TradeInValue;
                        workflowSPRDetail.ShipToAddressId = requisitionItem.SPRDetail.ShipToAddressId;
                        workflowSPRDetail.UOMCode = requisitionItem.SPRDetail.UOMCode;
                        workflowSPRDetail.VendorId = requisitionItem.SPRDetail.VendorId;
                    }
                    else
                    {
                        workflowSPRDetail = null;
                    }

                    workflowRequisitionItems.Add(new VendorWorkflowSvc.RequisitionItem()
                    {
                        Id = requisitionItem.Id,
                        RequisitionId = requisitionItem.RequisitionId,
                        CreateDate = requisitionItem.CreateDate,
                        CreatedBy = requisitionItem.CreatedBy,
                        IsRushOrder = requisitionItem.IsRushOrder,
                        RequisitionScheduledDate = requisitionItem.RequisitionScheduledDate.HasValue ? requisitionItem.RequisitionScheduledDate.Value.LocalDateTime : (DateTime?) null,
                        RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId,
                        ClinicalUseDetails = workflowClinicalUse.ToArray(),
                        SPRDetail = workflowSPRDetail,
                        ItemId = requisitionItem.ItemId,
                        MainItemId = requisitionItem.MainItemId,
                        ParIdentifier = requisitionItem.ParIdentifier,
                        QuantityToOrder = requisitionItem.QuantityToOrder
                    });
                }
            }

            var workflowRequisition = new VendorWorkflowSvc.Requisition()
            {
                Comments = requisition.Comments,
                CreateDate = requisition.CreateDate,
                CreatedBy = requisition.CreatedBy,
                LocationIdentifier = requisition.LocationIdentifier,
                Approvers = approvers.OrderBy(x => x.ApprovalLevel).ToArray(),
                RequisitionId = requisition.RequisitionId,
                RequisitionItems = workflowRequisitionItems.ToArray(),
                RequisitionStatusTypeId = requisition.RequisitionStatusTypeId,
                RequisitionTypeId = requisition.RequisitionTypeId
            };

            return workflowRequisition;
        }

        private void ApproveDenyRushRequisitionToService(RushWorkflowSvc.Requisition rushRequisition)
        {
            using (var workflowService = new RushWorkflowSvc.RushApprovalWorkflowClient())
            {
                workflowService.ApproveRushRequisition(rushRequisition);
            }
        }

        private void ApproveDenyNonRushRequisitionToService(NonRushWorkflowSvc.Requisition nonRushRequisition)
        {
            using (var workflowService = new NonRushWorkflowSvc.NonRushApprovalWorkflowClient())
            {
                workflowService.ApproveNonRushRequisition(nonRushRequisition);
            }
        }

        private void ApproveDenyVendorRequisitionToService(VendorWorkflowSvc.Requisition vendorRequisition)
        {
            using (var workflowService = new VendorWorkflowSvc.VendorApprovalWorkflowClient())
            {
                workflowService.ApproveVendorRequisition(vendorRequisition);
            }
        }

        private bool InitiateRushWorkflow(ref DomainModel.Requisitions.Requisition requisition)
        {
            bool isWorkflowRequired = false;

            var workflowRequisition = this.MapRequisitionToRushWorkflowRequisition(requisition, null, null);

            workflowRequisition = this.StartApprovalRushWorkflowToService(workflowRequisition);

            requisition.WorkflowInstanceId = workflowRequisition.WorkflowInstanceId;

            if (workflowRequisition.ErrorMessage != null)
            {
                throw new Exception("Error returned from WORKFLOW SERVICE. ERROR MESSAGE: " + workflowRequisition.ErrorMessage.Message);
            }

            isWorkflowRequired = workflowRequisition.RequiresApproval;

            return isWorkflowRequired;
        }

        private bool InitiateNonRushWorkflow(ref DomainModel.Requisitions.Requisition requisition)
        {
            bool isWorkflowRequired = false;

            var workflowRequisition = this.MapRequisitionToNonRushWorkflowRequisition(requisition, null, null);

            workflowRequisition = this.StartApprovalNonRushWorkflowToService(workflowRequisition);

            requisition.WorkflowInstanceId = workflowRequisition.WorkflowInstanceId;

            if (workflowRequisition.ErrorMessage != null)
            {
                throw new Exception("Error returned from WORKFLOW SERVICE. ERROR MESSAGE: " + workflowRequisition.ErrorMessage.Message);
            }

            isWorkflowRequired = workflowRequisition.RequiresApproval;

            return isWorkflowRequired;
        }

        private bool InitiateVendorWorkflow(ref DomainModel.Requisitions.Requisition requisition)
        {
            var workflowRequisition = this.MapRequisitionToVendorWorkflowRequisition(requisition, null);

            workflowRequisition = this.StartApprovalVendorWorkflowToService(workflowRequisition);

            requisition.WorkflowInstanceId = workflowRequisition.WorkflowInstanceId;

            if (workflowRequisition.ErrorMessage != null)
            {
                throw new Exception("Error returned from WORKFLOW SERVICE. ERROR MESSAGE: " + workflowRequisition.ErrorMessage.Message);
            }

            var holdItems = requisition.RequisitionItems.Where(x => x.ParIdentifier == null);
            bool isVendorReqBlocked = holdItems.Any();

            // Mark SPR items as Hold status
            if (isVendorReqBlocked)
            {
                foreach (var item in holdItems)
                {
                    item.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Hold;
                }
            }

            return isVendorReqBlocked;
        }

        private RushWorkflowSvc.Requisition StartApprovalRushWorkflowToService(RushWorkflowSvc.Requisition rushWorkflowRequisition)
        {
            using (var workflowService = new RushWorkflowSvc.RushApprovalWorkflowClient())
            {
                workflowService.StartRushApproval(ref rushWorkflowRequisition);

                return rushWorkflowRequisition;
            }
        }

        private NonRushWorkflowSvc.Requisition StartApprovalNonRushWorkflowToService(NonRushWorkflowSvc.Requisition nonRushWorkflowRequisition)
        {
            using (var workflowService = new NonRushWorkflowSvc.NonRushApprovalWorkflowClient())
            {
                workflowService.StartNonRushApproval(ref nonRushWorkflowRequisition);

                return nonRushWorkflowRequisition;
            }
        }

        private VendorWorkflowSvc.Requisition StartApprovalVendorWorkflowToService(VendorWorkflowSvc.Requisition nonRushWorkflowRequisition)
        {
            using (var workflowService = new VendorWorkflowSvc.VendorApprovalWorkflowClient())
            {
                workflowService.StartVendorApproval(ref nonRushWorkflowRequisition);

                return nonRushWorkflowRequisition;
            }
        }

        public void DeleteAllWorkflow()
        {
            log.Info("All workflows DELETED!");

            using (var connection = new SqlConnection(workflowConnectionString))
            {
                string commandString = "DELETE FROM [System.Activities.DurableInstancing].[IdentityOwnerTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[InstanceMetadataChangesTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[InstancePromotedPropertiesTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[KeysTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[LockOwnersTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[RunnableInstancesTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[ServiceDeploymentsTable]; ";
                       commandString += "DELETE FROM [System.Activities.DurableInstancing].[InstancesTable];";

                var command = new SqlCommand(commandString, connection);
                connection.Open();
                command.ExecuteNonQuery();
            }
        }

        public void ReInitializeAllWorkflow(string userName)
        {
            //Need to delete all workflows in flight first    
            DeleteAllWorkflow();

            var requisitionQuery = requisitionRepository.GetRequisitions().Where(x => x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval
                                                                               && x.ApprovalStep != null).ToList();

            if (requisitionQuery != null)
            {
                foreach (var requisition in requisitionQuery)
                {
                    //Call workflow to re-start workflow
                    var submittedToWorkflowReq = StartWorkflow(requisition, userName);
                    requisitionRepository.UpdateRequisition(requisition, "System", false, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);
                }
            }
        }

        public void ReInitializeWorkflowByRequisition(Requisition requisition, string userName, bool updateRequisition = false)
        {
            this.ReInitializeWorkflowForRequisition(requisition, userName, updateRequisition);
        }

        public void ReInitializeWorkflowByRequisition(int requisitionId, string userName, bool updateRequisition)
        {
            var requisition = requisitionRepository.GetRequisition(requisitionId);
            this.ReInitializeWorkflowForRequisition(requisition, userName, updateRequisition);
        }

        private void ReInitializeWorkflowForRequisition(Requisition requisition, string userName, bool updateRequisition)
        {
            if (requisition == null)
            {
                throw new Exception("Requisition not found");
            }

            if (requisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.PendingApproval)
            {
                throw new Exception("Requisition not in Pending Approval status, cannot re-initialize");
            }

            //Need to delete this workflow in flight first
            if (requisition.WorkflowInstanceId != null)
            {
                this.DeleteRequisitionFromWorkflow(requisition.RequisitionId, (Guid)requisition.WorkflowInstanceId);
            }

            //Call workflow to re-start workflow
            this.StartWorkflow(requisition, userName);
            if (updateRequisition)
            {
                requisitionRepository.UpdateRequisition(requisition, userName, false, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);
            }
        }

        public void DeleteRequisitionFromWorkflow(int requisitionId, Guid workflowInstanceId)
        {

            log.Info(String.Format("Deleting workflow at RequisitionId = {0} and WorkflowInstanceId = {1}",requisitionId.ToString(), workflowInstanceId.ToString()));

            if (workflowInstanceId != null)
            {
                string surrogateInstanceId = null;

                //Now delete it
                using (var connection = new SqlConnection(workflowConnectionString))
                {
                    string commandString = String.Format("SELECT SurrogateInstanceId FROM[System.Activities.DurableInstancing].[InstancesTable] WHERE Id = '{0}'", workflowInstanceId.ToString());
                    var command = new SqlCommand(commandString, connection);
                    connection.Open();

                    SqlDataReader reader = command.ExecuteReader();

                    while (reader.Read())
                    {
                        if (reader["SurrogateInstanceId"] != DBNull.Value)
                        {
                            surrogateInstanceId = reader["SurrogateInstanceId"].ToString();
                        }
                    }

                    reader.Close();

                    if (!String.IsNullOrWhiteSpace(surrogateInstanceId))
                    {
                        string commandDeleteSurrogatesString = "[System.Activities.DurableInstancing].[DeleteInstance]";

                        var commandDeleteWorkflowInstance = new SqlCommand(commandDeleteSurrogatesString, connection);
                        commandDeleteWorkflowInstance.CommandType = System.Data.CommandType.StoredProcedure;
                        commandDeleteWorkflowInstance.Parameters.Add(new SqlParameter("@surrogateInstanceId", System.Data.SqlDbType.BigInt)).Value = surrogateInstanceId;
                        commandDeleteWorkflowInstance.ExecuteScalar();
                    }
                }
            }
        }
    }
}
