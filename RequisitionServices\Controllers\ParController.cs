﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Search;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Linq;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    /// <summary>
    /// Methods for interacting with Pars
    /// </summary>
    public class ParController : ApiController
    {
        private IParService parService;

        public ParController(IParService parSvc)
        {
            this.parService = parSvc;
        }

        /// <summary>
        /// Retrieves a list of available Pars for a given user and location
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <returns></returns>
        [HttpGet]
        public List<Par> GetParsByUserLocation(string userName, string COID, int? departmentId = null)
        {
            var pars = parService.GetParsByUserLocation(userName, COID, departmentId);
            return pars == null ? null : pars.ToList();
        }

        /// <summary>
        /// Get the PAR item details for a given Par
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <param name="parId"></param>
        /// <returns></returns>
        [HttpGet]
        public List<ParItem> GetParItems(string userName, string COID, int departmentId, string parId)
        {
            return parService.GetParItems(userName, COID, departmentId, parId).ToList();
        }

        /// <summary>
        /// Get the PAR item details for a given Item
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="departmentId"></param>
        /// <param name="itemId"></param>
        /// <returns></returns>
        [HttpGet]
        public List<ParItem> GetParItemsByItem(string userName, string COID, int departmentId, string itemId)
        {
            return parService.GetParItemsByItem(userName, COID, departmentId, itemId).ToList();
        }

        [HttpPost]
        public GetParItemsWithLastOrderedInfoDTO GetParItemsWithLastOrderedInfo(ItemSearchCriteria itemSearchCriteria)
        {
            return parService.GetParItemsWithLastOrderedInfo(itemSearchCriteria);
        }
    }
}
