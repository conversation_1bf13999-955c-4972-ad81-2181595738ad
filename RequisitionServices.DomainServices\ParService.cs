﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Web.Http;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.DomainModel.Search;
using Smart.Core.Common.Extensions;

namespace RequisitionServices.DomainServices
{
    public class ParService : IParService
    {
        private ISmartParService smartParService;
        private ISmartItemService smartItemService;
        private ISmartParItemsService smartParItemsService;
        private IRequisitionRepository requisitionRepository;
        public ParService(ISmartParService smartParSvc, ISmartItemService smartItemSvc, ISmartParItemsService smartParItemsSvc, IRequisitionRepository requisitionRepo)
        {
            smartParService = smartParSvc;
            smartItemService = smartItemSvc;
            smartParItemsService = smartParItemsSvc;
            requisitionRepository = requisitionRepo;
        }

        public IEnumerable<Par> GetParsByUserLocation(string userName, string COID, int? departmentId = null)
        {
            return smartParService.GetPars(userName, COID, departmentId);
        }

        public IEnumerable<ParItem> GetParItemsByItem(string userName, string COID, int departmentId, string itemId, Item item = null, ApiVersion version = null)
        {
            /* IMPORTANT: Do not use this on it's own.  Items in ParItems will not have a QOH
             * 
             * Looking up an item by parId can give a different QOH, but we don't use
             * the Item in the ParItem object.  If we ever start using it for the actual Item object or 
             * the QOH, this method will be erroneous.
             * 
             * Paul says we want to show the right GL Account depending on the PAR, so this might change later.
             */

            List<ParItem> parItems = null;
            parItems = smartParService.GetParItemsByItem(userName, COID, departmentId, itemId, version).ToList();
            if (parItems != null) //TODO: Always enters loop because ToList() returns list with 0 (never null)
            {
                if (item == null)
                {
                    var itemPars = parItems.Select(x => new ItemParDTO { ItemId = x.ItemId.ToString(), ParId = departmentId + x.ParId }).ToList();
                    if (itemPars != null && itemPars.Count() > 0)
                    {
                        var items = smartItemService.GetItems(userName, COID, itemPars); //TODO: Do we need to getItems again? We get item from GetRequisitionWithDetails to make this call
                        if (items != null)
                        {
                            foreach (var parItem in parItems)
                            {
                                parItem.Item = items.Where(x => x.Id == parItem.ItemId.ToString() && x.ParId == parItem.ParId).FirstOrDefault();
                            }
                        }
                    }
                }
                else
                {
                    foreach (var par in parItems)
                    {
                        par.Item = item;
                    }
                }

                //Remove capitated items from non-Cap PARs
                parItems.RemoveAll(x => x.ParType != (int)ParType.Capitated && x.Item != null && x.Item.IsCapitated);

                //Remove stock items from BO and CAP PARs
                parItems.RemoveAll(x => (x.ParType == (int)ParType.Capitated || x.ParType == (int)ParType.BillOnly)
                                                && x.Item != null
                                                && (x.Item.IsStock && !x.Item.IsTempStock));                
            }

            return parItems;
        }

        public IEnumerable<ParItem> GetParItems(string userName, string COID, int departmentId, string parId)
        {
            if(String.IsNullOrWhiteSpace(parId))
            {
                return new List<ParItem>();
            }

            ParItemDetailsModel pam = new ParItemDetailsModel();
            var parItemsDetails = new List<ParItemDetails>();
            parItemsDetails = smartParItemsService.GetParItemDetails(userName, COID, departmentId, parId).ToList();

            var parItems = pam.CreatePARItemDetailsModel(parItemsDetails);

            parItems.ForEach(x =>
            {
                if (x.IssueUOM.ToUpperInvariant() == x.Item.PUOM.ToUpperInvariant())
                {
                    x.Item.FactorDisplay = x.Item.Factor.ToString();
                }
                else if (x.IssueUOM.ToUpperInvariant() == x.Item.IUOM.ToUpperInvariant())
                {
                    x.Item.FactorDisplay = 1.ToString();
                }
                else
                {
                    var alternateUOM = x.Item.AlternateUOMs.Where(y => y.UOM.ToUpperInvariant() == x.IssueUOM.ToUpperInvariant()).FirstOrDefault();
                    x.Item.FactorDisplay = alternateUOM != null ? alternateUOM.Factor.ToString() : "?"; // Just in case we're still missing the needed UOM/Factor.
                }
            });

            if (parItems != null)
            {
                bool isCapitatedPar = false;
                bool isBillOnlyPar = false;

                parItems.ForEach(x => 
                {
                    if (x.ParType == (int)ParType.Capitated)
                    {
                        isCapitatedPar = true;
                    }
                    if (x.ParType == (int)ParType.BillOnly)
                    {
                        isBillOnlyPar = true;
                    }
                });

                //Remove cap items from non-cap PARs
                if (!isCapitatedPar)
                {
                    parItems = parItems.Where(x => x.Item != null && x.Item.IsCapitated == false).ToList();
                }

                //Remove stock items from billing PARs
                if (isBillOnlyPar || isCapitatedPar)
                {
                    parItems = parItems.Where(x => x.Item != null 
                                                    && (x.Item.IsStock == false || (x.Item.IsTempStock && x.Item.IsStock))                                                    
                                                ).ToList();
                }
            }

            return parItems;
        }     

        public Par GetParById(string userName, string COID, int departmentId, string parId)
        {
            return smartParService.GetParById(userName, COID, departmentId, parId);
        }

        public GetParItemsWithLastOrderedInfoDTO GetParItemsWithLastOrderedInfo(ItemSearchCriteria itemSearchCriteria)
        {
            var parItems = GetParItems(itemSearchCriteria.UserName, itemSearchCriteria.COID, Int32.Parse(itemSearchCriteria.Department), itemSearchCriteria.ParId).ToList();
            var filterCriteria = itemSearchCriteria.FilterCriteria?.FirstOrDefault()?.FieldValues?.FirstOrDefault();

            if (!string.IsNullOrEmpty(filterCriteria))
            {
                parItems = parItems.Where(parItem => DoesMatchFilter(parItem, filterCriteria)).ToList();
            }

            switch (itemSearchCriteria.SortByField.ToUpperInvariant())
            {
                case "ITEMNUMBER":
                    parItems.Sort(CompareItemNumber);
                    break;
                case "LOCATION":
                    parItems.Sort(CompareLocation);
                    break;
                default:
                    parItems.Sort(CompareItemDescription);
                    break;
            }

            var numFound = parItems.Count;

            parItems = parItems.Paginate(itemSearchCriteria.StartAtRecord, itemSearchCriteria.PageSize);

            var parItemsWithLastOrdered = requisitionRepository.GetLastOrderedDetails(itemSearchCriteria.COID, itemSearchCriteria.Department, itemSearchCriteria.ParId, parItems);

            return new GetParItemsWithLastOrderedInfoDTO()
            {
                ParItems = parItemsWithLastOrdered,
                NumFound = numFound
            };
        }

        private bool DoesMatchFilter(ParItem parItem, string filter)
        {
            if (null != parItem && !string.IsNullOrWhiteSpace(filter))
            {
                filter = filter.Trim().ToUpperInvariant();

                if (parItem.ItemId.ToString().StartsWith(filter)) return true;

                if (null != parItem.Location && 
                    (parItem.Location.ToUpperInvariant().StartsWith(filter) 
                        || parItem.Location.ToUpperInvariant().Contains(" " + filter)))
                {
                    return true;
                }

                if (String.IsNullOrWhiteSpace(parItem.Location) &&
                    "N/A".StartsWith(filter))
                {
                    return true;
                }

                if (null != parItem.Item) 
                {
                    if (null != parItem.Item.Description && 
                        (parItem.Item.Description.ToUpperInvariant().StartsWith(filter) 
                            || parItem.Item.Description.ToUpperInvariant().Contains(" " + filter)))
                    {
                        return true;
                    }

                    if (null != parItem.Item.Vendor?.Name && 
                        (parItem.Item.Vendor.Name.ToUpperInvariant().StartsWith(filter) 
                        || parItem.Item.Vendor.Name.ToUpperInvariant().Contains(" " + filter)))
                    {
                        return true;
                    }

                    if (null != parItem.Item.Vendor &&
                        (parItem.Item.Vendor.Id.ToString().ToUpperInvariant().StartsWith(filter)))
                    {
                        return true;
                    }

                    if (null != parItem.Item.ManufacturerCatalogNumber &&
                        (parItem.Item.ManufacturerCatalogNumber.ToUpperInvariant().StartsWith(filter) 
                        || parItem.Item.ManufacturerCatalogNumber.ToUpperInvariant().Contains(" " + filter)))
                    {
                        return true;
                    }
                }
            }

            return false;
        }

        private static int CompareItemNumber(ParItem x, ParItem y)
        {
            return x.ItemId.CompareTo(y.ItemId);
        }

        private static int CompareLocation(ParItem x, ParItem y)
        {
            return x.Location.CompareTo(y.Location);
        }

        private static int CompareItemDescription(ParItem x, ParItem y)
        {
            return x.Item.Description.CompareTo(y.Item.Description);
        }
    }
}
