﻿using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Cart.Responses;
using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Linq;
using System.Reflection;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Users;
using System.Data.Entity;
using System.Collections.Generic;
using System.Data;
using System.Security.Cryptography;
using System.Data.SqlClient;
using System.Configuration;
using RequisitionServices.Utility.Model;

namespace RequisitionServices.Repositories
{
    public class CartRepository : AbstractRepository, ICartRepository
    {
        private readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        readonly IConnectionFactory _connectionFactory;
        private string eProcurementConnectionString = ConfigurationManager.ConnectionStrings["EProcurementContext"].ConnectionString;

        public CartRepository(EProcurementContext context, IConnectionFactory connectionFactory) : base(context)
        {
            _connectionFactory = connectionFactory;
        }

        public Cart AddCart(CartRequest request)
        {
            var cart = new Cart
            {
                Coid = request.Coid,
                DepartmentNumber = request.DepartmentNumber,
                ParId = request.ParId,
                Username = request.Username,
                CreatedUtc = DateTime.UtcNow,
                LastUpdatedUtc = DateTime.UtcNow,
                CartTypeId = Convert.ToInt32(CartType.Procurement)
            };

            context.Carts.Add(cart);

            TrySaveChanges();

            return cart;
        }

        public Cart AddPOUCart(CartRequest request)
        {
            var cart = new Cart
            {
                Coid = request.Coid,
                DepartmentNumber = request.DepartmentNumber,
                ParId = request.ParId,
                Username = request.Username,
                CreatedUtc = DateTime.UtcNow,
                LastUpdatedUtc = DateTime.UtcNow,
                //IsPOU = true
                CartTypeId = Convert.ToInt32(CartType.POU)
            };

            context.Carts.Add(cart);
            TrySaveChanges();
            return cart;
        }

        public Cart GetCart(CartRequest request)
        {
            Cart cart;
            try
            {

                //if (request.IsPOU == true && request.CartId == null)
                if (request.CartTypeId == CartType.POU.ToInt() && request.CartId == null)
                {
                    int CType = Convert.ToInt32(CartType.POU);
                    cart = context.Carts.Where(x =>
                    x.Username.ToUpper() == request.Username.ToUpper()
                    && x.Coid == request.Coid
                    //&& x.IsPOU == true).FirstOrDefault();
                    && x.CartTypeId == CType).FirstOrDefault();

                }
                else if (request.CartId == null && request.DepartmentNumber > 0)
                {
                    int CType = Convert.ToInt32(CartType.Procurement);
                    cart = context.Carts.Where(x =>
                        x.Username.ToUpper() == request.Username.ToUpper()
                        && x.Coid == request.Coid
                        && x.DepartmentNumber == request.DepartmentNumber
                        && x.ParId.ToUpper() == request.ParId.ToUpper()
                        //&& x.IsPOU == false).FirstOrDefault();
                        && x.CartTypeId == CType).FirstOrDefault();
                }
                else
                {
                    cart = context.Carts.Where(x => x.Id == request.CartId).FirstOrDefault();
                }
                return cart;

            }
            catch (Exception ex)
            {
                _log.Error(ex);
                throw;
            }
        }

        public Cart GetPOUCart(CartRequest request)
        {
            Cart cart;
            try
            {
                if (request.CartId == null)
                {
                    int CType = Convert.ToInt32(CartType.POU);
                    cart = context.Carts.Where(x =>
                    x.Username.ToUpper() == request.Username.ToUpper()
                    && x.Coid == request.Coid
                    //&& x.IsPOU == true).FirstOrDefault();
                    && x.CartTypeId == CType).FirstOrDefault();
                }
                else
                {
                    cart = context.Carts.Where(x => x.Id == request.CartId).FirstOrDefault();
                }

                return cart;
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                throw;
            }
        }

        public void Delete(long cartId)
        {
            var cart = GetCart(new CartRequest { CartId = cartId });
            if (cart != null)
            {
                context.Carts.Remove(cart);
                TrySaveChanges();
            }
        }

        public void CorrelateCartAndRequisition(long cartId, int requisitionId)
        {
            var cart = GetCart(new CartRequest { CartId = cartId });

            cart.RequisitionId = requisitionId;
            cart.LastUpdatedUtc = DateTime.UtcNow;

            TrySaveChanges();
        }

        public AddToCartResponse AddToCart(Cart cart, CartItem item)
        {
            cart.Items.Add(item);
            cart.LastUpdatedUtc = DateTime.UtcNow;

            TrySaveChanges();

            return new AddToCartResponse
            {
                Status = AddToCartStatus.Added,
                CartQuantity = item.Quantity,
                CartId = cart.Id,
                CartItemCount = cart.Items.Count
            };
        }

        public void UpdateCartItems(CartUpdateItemsRequest request)
        {
            if (request.Items != null)
            {
                var cart = GetCart(new CartRequest { CartId = request.CartId });

                request.Items.ForEach(x =>
                {
                    var existingItem = cart.Items.FirstOrDefault(y => y.Id == x.Id);
                    if (existingItem != null)
                    {
                        var newQuantity = request.Override ? x.Quantity : (existingItem.Quantity + x.Quantity);
                        existingItem.Quantity = newQuantity;
                        existingItem.LastUpdatedUtc = DateTime.UtcNow;
                    }
                });

                cart.LastUpdatedUtc = DateTime.UtcNow;

                TrySaveChanges();
            }
        }

        public void UpdateCartPOUItems(CartUpdateItemsRequest request)
        {
            if (request.Items != null)
            {
                var cart = GetPOUCart(new CartRequest { CartId = request.CartId });

                request.Items.ForEach(x =>
                {
                    var existingItem = cart.Items.FirstOrDefault(y => y.Id == x.Id);
                    if (existingItem != null)
                    {
                        existingItem.Quantity = x.Quantity;
                        existingItem.LastUpdatedUtc = DateTime.UtcNow;
                        existingItem.PARTypeId = x.PARTypeId;
                    }
                });

                cart.LastUpdatedUtc = DateTime.UtcNow;

                TrySaveChanges();
            }
        }

        public int DeleteCartItem(CartDeleteItemRequest request)
        {
            var cart = GetCart(new CartRequest { CartId = request.CartId });

            if (cart != null && cart.Items.Any(x => x.Id == request.CartItemId))
            {
                var deletedItem = cart.Items.Where(x => x.Id == request.CartItemId).FirstOrDefault();

                cart.Items.Remove(deletedItem);
                context.CartItems.Remove(deletedItem);
                cart.LastUpdatedUtc = DateTime.UtcNow;

                TrySaveChanges();
            }

            return cart.Items.Count;
        }

        public int DeletePOUCartItem(CartDeleteItemRequest request)
        {
            var cart = GetPOUCart(new CartRequest { CartId = request.CartId });

            if (cart != null && cart.Items.Any(x => x.Id == request.CartItemId))
            {
                var deletedItem = cart.Items.Where(x => x.Id == request.CartItemId).FirstOrDefault();

                cart.Items.Remove(deletedItem);
                context.CartItems.Remove(deletedItem);
                cart.LastUpdatedUtc = DateTime.UtcNow;

                TrySaveChanges();
            }
            if (cart.Items.Count < 1)
            {
                context.Carts.Remove(cart);
                TrySaveChanges();
            }

            return cart.Items.Count;
        }

        private void TrySaveChanges()
        {
            try
            {
                context.SaveChanges();
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                throw;
            }
        }

        public Item_RFID GetRfidCartAttributes(CartRequest request)
        {
            Item_RFID rfidCart = null;
            using (var connection = new SqlConnection(eProcurementConnectionString))
            {
                string commandString = "EXEC CreateRFIDCart @Coid, @DepartmentNumber, @ParId, @UserName";
                var command = new SqlCommand(commandString, connection);
                command.Parameters.AddWithValue("@Coid", request.Coid);
                command.Parameters.AddWithValue("@DepartmentNumber", request.DepartmentNumber);
                command.Parameters.AddWithValue("@ParId", request.ParId);
                command.Parameters.AddWithValue("@UserName", request.Username);
                connection.Open();

                SqlDataReader reader = command.ExecuteReader();

                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        if (!reader.IsDBNull(reader.GetOrdinal("CartId")) && !reader.IsDBNull(reader.GetOrdinal("MostRecentScannedUtc")))
                        {
                            rfidCart = new Item_RFID
                            {
                                CartId = reader.GetInt64(reader.GetOrdinal("CartId")),
                                MostRecentScannedUtc = reader.GetDateTime(reader.GetOrdinal("MostRecentScannedUtc"))
                            };
                        }
                        else
                        {
                            _log.Warn("CartId or MostRecentScannedUtc is null.");
                        }
                    }
                }
                else
                {
                    _log.Warn("No rows returned from the stored procedure.");
                }

                reader.Close();
            }
            return rfidCart;
        }

        public Cart AddCartMerge(CartRequest request)
        {
            var cart = new Cart
            {
                Coid = request.Coid,
                DepartmentNumber = request.DepartmentNumber,
                ParId = request.ParId,
                Username = request.Username,
                CreatedUtc = DateTime.UtcNow,
                LastUpdatedUtc = DateTime.UtcNow,
                //CartTypeId = Convert.ToInt32(CartType.Procurement)
                CartTypeId = request.CartTypeId
            };

            context.Carts.Add(cart);
            TrySaveChanges();
            return cart;
        }

        public Cart GetCartMerge(CartRequest request)
        {
            Cart cart;
            try
            {
                if (request.CartId == null && request.DepartmentNumber > 0)
                {
                    //int CType = Convert.ToInt32(CartType.Procurement);
                    int CType = request.CartTypeId;
                    cart = context.Carts.Where(x =>
                        x.Username.ToUpper() == request.Username.ToUpper()
                        && x.Coid == request.Coid
                        && x.DepartmentNumber == request.DepartmentNumber
                        && x.ParId.ToUpper() == request.ParId.ToUpper()
                        //&& x.IsPOU == false).FirstOrDefault();
                        && x.CartTypeId == CType).FirstOrDefault();
                }
                else
                {
                    cart = context.Carts.Where(x => x.Id == request.CartId).FirstOrDefault();
                }
                return cart;
            }
            catch (Exception ex)
            {
                _log.Error(ex);
                throw;
            }
        }

        public void UpdateCartItemsMerge(CartUpdateItemsRequest request)
        {
            if (request.Items != null)
            {
                var cart = GetCartMerge(new CartRequest { CartId = request.CartId });
                request.Items.ForEach(x =>
                {
                    var existingItem = cart.Items.FirstOrDefault(y => y.Id == x.Id);
                    if (existingItem != null)
                    {
                        request.Override = x.PARTypeId == 3 ? request.Override = true : request.Override;
                        var newQuantity = request.Override ? x.Quantity : (existingItem.Quantity + x.Quantity);
                        existingItem.Quantity = newQuantity;
                        existingItem.LastUpdatedUtc = DateTime.UtcNow;
                        existingItem.PARTypeId = x.PARTypeId;
                    }
                });
                cart.LastUpdatedUtc = DateTime.UtcNow;
                TrySaveChanges();
            }
        }

        public int DeleteCartItemMerge(CartDeleteItemRequest request)
        {
            var cart = GetCartMerge(new CartRequest { CartId = request.CartId });

            if (cart != null && cart.Items.Any(x => x.Id == request.CartItemId))
            {
                var deletedItem = cart.Items.Where(x => x.Id == request.CartItemId).FirstOrDefault();

                cart.Items.Remove(deletedItem);
                context.CartItems.Remove(deletedItem);
                cart.LastUpdatedUtc = DateTime.UtcNow;

                TrySaveChanges();
            }

            return cart.Items.Count;
        }
    }
}