﻿using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.DomainModel.SystemNotifications
{
    public class SystemNotificationAdminDTO
    {
        public SystemNotificationAdminDTO() { }

        public SystemNotificationAdminDTO(SystemNotificationAuthorizedUser systemNotificationAuthorizedUser)
        {
            if(systemNotificationAuthorizedUser != null)
            {
                this.AdminId = systemNotificationAuthorizedUser.Id;
                this.UserId = systemNotificationAuthorizedUser.UserId;
                this.UserName = systemNotificationAuthorizedUser.User.AccountName;
                this.FirstName = systemNotificationAuthorizedUser.User.FirstName;
                this.LastName = systemNotificationAuthorizedUser.User.LastName;
            }
        }

        public SystemNotificationAdminDTO(User user)
        {
            if(user != null)
            {
                this.UserId = user.Id;
                this.UserName = user.AccountName;
                this.FirstName = user.FirstName;
                this.LastName = user.LastName;
            }
        }


        public int AdminId { get; set; }

        public int UserId { get; set; }

        public string UserName { get; set; }

        public string FirstName { get; set; }

        public string LastName { get; set; }

    }
}
