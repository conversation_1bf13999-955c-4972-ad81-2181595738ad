﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.DomainServices
{
    public class LocationService : ILocationService
    {
        private ISmartCOIDService smartCOIDService;
        private ISmartLocationService smartLocatorService;
        private IConfigurationRepository configurationRepository;

        public LocationService(ISmartCOIDService smartCOIDSvc, IConfigurationRepository configurationRepo, ISmartLocationService smartLocatorSvc)
        {
            this.smartCOIDService = smartCOIDSvc;
            this.configurationRepository = configurationRepo;
            this.smartLocatorService = smartLocatorSvc;
        }

        //public IEnumerable<GLAccount> GetAllGLAccounts(string userName, string coid, int? costCode)
        //{
        //    return smartCOIDService.GetAllGLAccounts(userName, coid, costCode);
        //}

        public IEnumerable<GLAccount> GetAllGLAccounts(string userName, string coid, string accountStringPartial)
        {
            return smartCOIDService.GetAllGLAccounts(userName, coid, accountStringPartial);
        }

        public IEnumerable<int> GetAllCostCodes(string userName, string coid)
        {
            return smartCOIDService.GetAllCostCodes(userName, coid);
        }

        public GLAccount GetGLAccount(string userName, string coid, long accountNumber)
        {
            return smartCOIDService.GetGLAccount(userName, coid, accountNumber);
        }

        public IEnumerable<Address> GetAllAddresses(string userName, string coid)
        {
            return smartCOIDService.GetAddresses(userName, coid);
        }

        public Address GetAddress(string userName, string coid, int shipNumber)
        {
            return smartCOIDService.GetAddress(userName, coid, shipNumber);
        }

        public IEnumerable<FacilityNotification> SaveFacilityNotifications(IEnumerable<FacilityNotification> facilityNotifications, string userName, string coid)
        {
            if (facilityNotifications == null)
            {
                facilityNotifications = new List<FacilityNotification>();
            }

            var currentNotifications = this.GetFacilityNotifications(userName, coid).ToList();
            
            for (int i = 0; i < currentNotifications.Count(); i++)
            {
                var newMatch = facilityNotifications.Where(x => x.FacilityNotificationTypeId == currentNotifications[i].FacilityNotificationTypeId).FirstOrDefault();
                if (newMatch != null)
                {
                    currentNotifications[i].EmailAddresses = newMatch.EmailAddresses;
                }
                else
                {
                    currentNotifications[i].EmailAddresses = "";
                }

                if(currentNotifications[i].Id == 0)
                {
                    configurationRepository.InsertFacilityNotification(currentNotifications[i]);
                }
                else
                {
                    configurationRepository.UpdateFacilityNotification(currentNotifications[i], userName);
                }
            }
            
            return facilityNotifications;
        }

        public IEnumerable<FacilityNotification> GetFacilityNotifications(string userName, string coid)
        {
            var facilityNotificationTypes = configurationRepository.GetFacilityNotificationTypes();

            var facilityNotifications = configurationRepository.GetFacilityNotifications(coid);
            if (facilityNotifications == null)
            {
                facilityNotifications = new List<FacilityNotification>();
            }

            var returnFacilityNotifications = new List<FacilityNotification>();
            if (facilityNotificationTypes != null)
            {
                foreach(var facilityNotificationType in facilityNotificationTypes)
                {
                    var matched = facilityNotifications.Where(x => x.FacilityNotificationTypeId == facilityNotificationType.Id).FirstOrDefault();
                    if(matched == null)
                    {
                        returnFacilityNotifications.Add(new FacilityNotification() {
                                FacilityNotificationTypeId = facilityNotificationType.Id,
                                FacilityNotificationType = facilityNotificationType,
                                COID = coid,
                                CreateDate = DateTime.Now,
                                CreatedBy = userName
                        });
                    }
                    else
                    {
                        returnFacilityNotifications.Add(matched);
                    }
                }
            }

            return returnFacilityNotifications;
        }

        public Locator UpdateLocator(string userId, Locator locator)
        {
            return smartLocatorService.UpdateLocator(userId, locator);
        }
    }
}
