﻿using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Locations
{
    public class Location
    {
        /// <summary>
        /// COID
        /// </summary>
        public string Id { get; set; }

        /// <summary>
        /// ParentCoid
        /// </summary>
        public string ParentId { get; set; }

        /// <summary>
        /// ParentCoidName
        /// </summary>
        public string ParentDescription { get; set; }

        /// <summary>
        /// COID Name
        /// </summary>
        public string Description { get; set; }

        public ParentSystemType ParentSystem { get; set; }

        public bool IsDefault { get; set; }

        public bool MQEnabled { get; set; }

        public string CompanyCode { get; set; }

        public string SmartCountryCode { get; set; }
    }
}
