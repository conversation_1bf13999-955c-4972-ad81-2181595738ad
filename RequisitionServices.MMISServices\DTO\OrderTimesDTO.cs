﻿using RequisitionServices.DomainModel.Vendors;
using System;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class OrderTimesDTO
    {
        public List<DateTime> MondayTimes { get; set; }
        public List<DateTime> TuesdayTimes { get; set; }
        public List<DateTime> WednesdayTimes { get; set; }
        public List<DateTime> ThursdayTimes { get; set; }
        public List<DateTime> FridayTimes { get; set; }
        public List<DateTime> SaturdayTimes { get; set; }
        public List<DateTime> SundayTimes { get; set; }

        public OrderTimes MapToOrderTimes()
        {
            return new OrderTimes()
            {
                MondayTimes = this.MondayTimes,
                TuesdayTimes = this.TuesdayTimes,
                WednesdayTimes = this.WednesdayTimes,
                ThursdayTimes = this.ThursdayTimes,
                FridayTimes = this.FridayTimes,
                SaturdayTimes = this.SaturdayTimes,
                SundayTimes = this.SundayTimes
            };
        }
       
    }
}
