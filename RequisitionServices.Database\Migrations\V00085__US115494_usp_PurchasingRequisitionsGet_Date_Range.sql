USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_PurchasingRequisitionsGet]    Script Date: 10/21/2024 1:55:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO






ALTER PROCEDURE [dbo].[usp_PurchasingRequisitionsGet] 
@coids [dbo].[CoidList] READONLY,
@vendors [IdTemplate] READONLY,
@buyers [dbo].[Varchar50template] READONLY,
@reqTypes [dbo].[IdTemplate] READONLY,
@filterText varchar(140) = NULL,
@pageNumber INT = 1,
@pageSize INT = 25,
@sortColumn VARCHAR(128) = NULL,
@sortDirection VARCHAR(4) = NULL,
@startDate DATE = NULL,
@endDate DATE = NULL
AS
BEGIN
    -- Set default sorting if no sorting parameters are provided
    IF @sortColumn IS NULL OR @sortDirection IS NULL
    BEGIN
        SET @sortColumn = 'RequisitionID';
        SET @sortDirection = 'ASC';
    END;

	  IF @startDate IS NULL
        SET @startDate = '17530101'; -- Minimum date for SQL Server

    IF @endDate IS NULL
        SET @endDate = '99991231'; -- Maximum date for SQL Server


	WITH RequisitionDetails AS (
	SELECT 	[Req].[RequisitionId] AS [RequisitionId],
		[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[ReqStatus].[Description] AS [RequisitionStatusType],
		[Req].[LocationIdentifier] AS [LocationIdentifier],
		[Req].[Comments] AS [Comments],
		[Req].[CreatedBy] AS [CreatedBy],
		[Req].[CreateDate] AS [CreateDate],
		[Req].[RequisitionTypeId] AS [RequisitionTypeId],
		[ReqTypes].[Description] AS [RequisitionType],
		[Req].[IsMobile] AS [IsMobile],
		[Req].[IsVendor] AS [IsVendor],
		[Req].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
		[ReqSubmissionTypes].[Description] AS [RequisitionSubmissionType],
		ISNULL([User].[LastName], '') + ', ' + ISNULL([User].[FirstName], '') AS [CreatedByFullName],
		[ReqItem].[Id] AS [RequisitionItemId],
		[ReqItem].[RequisitionId] AS RequisitionItemRequisitionId,
		[ReqItemStatus].[Description] AS ReqItemStatusDescription,
		[ReqItem].[ItemId] AS [RequisitionItemNumber],
		[ReqItem].[CreateDate] AS [RequisitionItemCreateDate],
		[ReqItem].[CreatedBy] AS [RequisitionItemCreatedBy],
		[ReqItem].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
		[ReqItemStatus].[Description] AS [RequisitionItemStatusType],
		[ReqItem].[ParentSystemId] AS [RequisitionItemParentSystemId],
		[ReqItem].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
		[ReqItem].[PONumber] AS [RequisitionItemPONumber],
		[ReqItem].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
		[ReqItem].[ParIdentifier] AS [RequisitionItemParIdentifier],
		[ReqItem].[Discount] AS [Discount],
		[ReqItem].[VendorId] AS [VendorId],
		[ReqItem].[UnitCost] AS [UnitCost],
		[ReqItem].[QuantityToOrder] AS [QuantityToOrder],
		[ReqItem].[PONumber],
		[ReqItem].[ParentSystemId],
		[ReqItem].[OriginalParentSystemId],
		[ReqItem].[ParIdentifier],
		[ReqItem].[QuantityFulfilled],
		[ReqItem].[IsFileItem],
		[ReqItem].[SmartItemNumber],
		[ReqItem].[GeneralLedgerCode],
		[ReqItem].[CatalogNumber],
		[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
		[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
		[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
		[ReqItemFileAttachments].[RequisitionItemId] as [SprDetailsFileAttachment],
		[ReqItemFileAttachments].[FileName] as [SprDetailsFileName],
		[ReqItemFileAttachments].[CreateDate] as [SprDetailsFileAttachmentCreateDate],
		[ReqItemFileAttachments].[CreatedBy] as [SprDetailsFileAttachmentCreatedBy],
		[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
	FROM Requisitions AS [Req]
	JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	JOIN [dbo].[RequisitionTypes] AS [ReqTypes] ON [Req].[RequisitionTypeId] = [ReqTypes].[Id]
	LEFT JOIN [dbo].[RequisitionSubmissionTypes] AS [ReqSubmissionTypes] WITH (NOLOCK) ON [Req].[RequisitionSubmissionTypeId] = [ReqSubmissionTypes].[Id]
	LEFT JOIN [dbo].[Users] AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	LEFT JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [ReqItemSprDetails].[RequisitionItemId]
	LEFT JOIN (SELECT DISTINCT [RequisitionItemId], [RequisitionId], [FileName], [CreateDate], [CreatedBy] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] 
		ON [ReqItem].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [ReqItem].[Id] = [VboHoldItemConversion].[RequisitionItemId]
	WHERE
		([RequisitionStatusTypeId] NOT IN (1, 2, 5, 6, 8, 12))
		AND (CAST([Req].CreateDate as DATE) BETWEEN @startDate and @endDate)
		AND (NOT EXISTS (select 1 from @vendors) OR ([ReqItem].[VendorId] IN (SELECT Id FROM @vendors) OR [ReqItemSprDetails].[VendorId] IN (SELECT Id FROM @vendors)))
		AND (NOT EXISTS (select 1 from @buyers) OR SUBSTRING(Req.CreatedBy, CHARINDEX('/', Req.CreatedBy) + 1, LEN(Req.CreatedBy)) IN (SELECT VarcharVal FROM @buyers))
	    AND (NOT EXISTS (select 1 from @reqTypes) OR [Req].[RequisitionTypeId] IN (SELECT Id FROM @reqTypes))
		AND (substring([Req].[LocationIdentifier],0,(CHARINDEX('_',[Req].[LocationIdentifier]))) in (SELECT DISTINCT Coid FROM @coids))
		AND ( @filterText IS NULL 
			OR([Req].[RequisitionId] LIKE '%' + @filterText + '%'
				OR ([FirstName] + ' ' + [LastName]) LIKE '%' + @filterText + '%'
				OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
				OR [ReqSubmissionTypes].[Description] LIKE '%' + @filterText + '%'
				OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
				OR [Comments] LIKE '%' + @filterText + '%'
				OR [PONumber] LIKE '%' + @filterText + '%'
				OR [ParentSystemId] LIKE '%' + @filterText + '%'
				OR [OriginalParentSystemId] LIKE '%' + @filterText + '%'
				OR [LocationIdentifier] LIKE '%' + @filterText + '%'
				OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
			))
	)
	
	SELECT *
	FROM RequisitionDetails
ORDER BY 
	CASE WHEN @sortColumn = 'LocationIdentifier' AND @sortDirection = 'ASC' THEN [LocationIdentifier] end asc,
	CASE WHEN @sortColumn = 'VendorNumber' AND @sortDirection = 'ASC' THEN [SprDetailsVendorId] end asc,
	CASE WHEN @sortColumn = 'VendorName' AND @sortDirection = 'ASC' THEN [SprDetailsVendorName] end asc,
	CASE WHEN @sortColumn = 'Date' AND @sortDirection = 'ASC' THEN [CreateDate] end asc,
	CASE WHEN @sortColumn = 'RequisitionTypeId' AND @sortDirection = 'ASC' THEN [RequisitionTypeId] end asc,
	CASE WHEN @sortColumn = 'FileAttachmentItemId' AND @sortDirection = 'ASC' THEN [SprDetailsFileAttachment] end asc,
	CASE WHEN @sortColumn = 'RequisitionID' AND @sortDirection = 'ASC' THEN [RequisitionId] end asc,
	CASE WHEN @sortColumn = 'LocationIdentifier' AND @sortDirection = 'DESC' THEN [LocationIdentifier] end desc,
	CASE WHEN @sortColumn = 'VendorNumber' AND @sortDirection = 'DESC' THEN [SprDetailsVendorId] end desc,
	CASE WHEN @sortColumn = 'VendorName' AND @sortDirection = 'DESC' THEN [SprDetailsVendorName] end desc,
	CASE WHEN @sortColumn = 'Date' AND @sortDirection = 'DESC' THEN [CreateDate] end desc,
	CASE WHEN @sortColumn = 'RequisitionTypeId' AND @sortDirection = 'DESC' THEN [RequisitionTypeId] end desc,
	CASE WHEN @sortColumn = 'FileAttachmentItemId' AND @sortDirection = 'DESC' THEN [SprDetailsFileAttachment] end desc,
	CASE WHEN @sortColumn = 'RequisitionID' AND @sortDirection = 'DESC' THEN [RequisitionId] end desc
	END