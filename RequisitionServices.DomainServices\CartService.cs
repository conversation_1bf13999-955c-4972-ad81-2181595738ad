﻿using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Cart.Responses;
using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainModel.Cart.Models;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.MMISServices.DTO;
using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.DomainModel.Items;
using System.Configuration;
using log4net;
using System.Reflection;
using RequisitionServices.Utility.Model;

namespace RequisitionServices.DomainServices
{
    public class CartService : ICartService
    {
        private readonly IItemService _itemService;
        private readonly IRequisitionService _requisitionService;
        private readonly ICartRepository _cartRepository;
        private readonly IRequisitionRepository _requisitionRepository;
        private readonly ISmartCOIDService _smartCoidService;

        private readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public CartService(IItemService itemService, IRequisitionService requisitionService, ICartRepository cartRepository, IRequisitionRepository requisitionRepository, ISmartCOIDService smartCoidService)
        {
            _itemService = itemService;
            _requisitionService = requisitionService;
            _cartRepository = cartRepository;
            _requisitionRepository = requisitionRepository;
            _smartCoidService = smartCoidService;
        }

        public Cart GetCart(CartRequest request)
        {
            var cart = new Cart();
            //if (request.IsPOU)
            if (request.CartTypeId == CartType.POU.ToInt())
            {
                cart = _cartRepository.GetPOUCart(request);
            }
            else
            {
                cart = _cartRepository.GetCart(request);

                if (cart == null)
                {
                    cart = _cartRepository.AddCart(request);
                }
            }

            if (cart?.Items?.Count > 0)
            {
                cart.Items = PopulateCartItemsWithDetails(request.Username, cart);
                var missingDetails = cart.Items.Where(x => x.Details == null).ToList();
                if (missingDetails.Any())
                {
                    foreach (var cartItem in missingDetails)
                    {
                        _cartRepository.DeleteCartItem(new CartDeleteItemRequest
                        {
                            CartId = cart.Id,
                            CartItemId = cartItem.Id
                        });
                    }
                }
            }

            return cart;
        }

        private ICollection<CartItem> PopulateCartItemsWithDetails(string username, Cart cart)
        {
            var itemInfoList = GetUnprocessedItemInfo(username, cart.Coid, cart.DepartmentNumber, cart.ParId, cart.Items.ToList());

            itemInfoList.ForEach(x =>
            {
                if (x.parItem != null && x.inFlightQuantity != null) x.cartItem.Details = new CartItemDetails(x.parItem, (int)x.inFlightQuantity);
            });

            return itemInfoList.Select(x => x.cartItem).ToList();
        }

        private List<(CartItem cartItem, ParItemWithLastOrdered parItem, int? inFlightQuantity)> GetUnprocessedItemInfo(string username, string coid, int departmentNumber, string parId, List<CartItem> items)
        {
            var itemsWithDetails = _itemService.GetItemsWithDetails(
                username,
                coid,
                departmentNumber,
                items.Select(x => new ItemParDTO
                {
                    ItemId = x.ItemNumber.ToString(),
                    ParId = parId
                }).ToList()
            );

            var parItems = itemsWithDetails.SelectMany(itemWithDetails => itemWithDetails.AvailableParItems)
                .Where(parItem => parItem.ParId.ToUpperInvariant() == parId.ToUpperInvariant());

            var parItemsWithLastOrdered = _requisitionRepository.GetLastOrderedDetails(coid, departmentNumber.ToString(), parId, parItems.ToList());

            var itemInfoList = new List<(CartItem cartItem, ParItemWithLastOrdered parItem, int? inFlightQuantity)>();

            items.ToList().ForEach(x =>
            {
                var parItemWithLastOrdered = parItemsWithLastOrdered.Where(item => item.ItemId == x.ItemNumber).FirstOrDefault();
                InFlightQty inFlightQuantity = null;
                if (parItemWithLastOrdered != null)
                {
                    inFlightQuantity = _requisitionService.GetInFlightQuantity(username, coid, departmentNumber, parId, parItemWithLastOrdered.ItemId.ToString());
                }
                itemInfoList.Add((x, parItemWithLastOrdered, inFlightQuantity?.InFlightQuantity));
            });

            return itemInfoList;
        }

        public CartAttributesResponse GetAttributes(CartRequest request)
        {
            var cart = _cartRepository.GetCart(request);

            if (cart == null)
            {
                cart = _cartRepository.AddCart(request);
            }
            return new CartAttributesResponse
            {
                CartId = cart.Id,
                Count = cart.Items?.Count
            };
        }

        public CartAttributesResponse GetPOUAttributes(CartRequest request)
        {
            var cart = _cartRepository.GetPOUCart(request);

            if (cart == null)
            {
                cart = new Cart();
            }
            return new CartAttributesResponse
            {
                CartId = cart.Id,
                Count = cart.Items.Count,
                //for pou(if user logout and login again
                DeptId = cart.DepartmentNumber,
                ParId = cart.ParId

            };
        }

        public bool CartItemExists(CartItemExistsRequest request)
        {
            var cart = _cartRepository.GetCart(new CartRequest { CartId = request.CartId });

            return cart?.Items.Any(x => x.ItemNumber == request.ItemNumber) ?? false;
        }

        public AddToCartResponse AddToCart(CartAddItemRequest request)
        {
            var cart = _cartRepository.GetCart(request) ?? _cartRepository.AddCart(request);

            var existingCartItem = cart.Items.FirstOrDefault(x => x.ItemNumber == request.ItemNumber);

            if (existingCartItem != null)
            {
                return new AddToCartResponse
                {
                    Status = AddToCartStatus.Exists,
                    CartQuantity = existingCartItem.Quantity,
                    ExistingItem = existingCartItem
                };
            }
            else
            {
                var itemInfo = GetUnprocessedItemInfo(request.Username, cart.Coid, cart.DepartmentNumber, cart.ParId, new List<CartItem> { new CartItem { ItemNumber = request.ItemNumber } }).FirstOrDefault();

                if (itemInfo.parItem == null)
                {
                    _log.Info($"Invalid item {request.ItemNumber} cannot be added to cart {cart.Id}");
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.Invalid
                    };
                }

                var maxCartItemCount = int.Parse(ConfigurationManager.AppSettings.Get("maxCartItemCount"));
                if (cart.Items.Count == maxCartItemCount)
                {
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.Full
                    };
                }

                if (cart.Items.Count == maxCartItemCount - 1 && !request.Override)
                {
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.AlmostFull
                    };
                }

                var item = new CartItem
                {
                    CartId = cart.Id,
                    ItemNumber = request.ItemNumber,
                    LastUpdatedUtc = DateTime.UtcNow,
                    Quantity = request.Quantity
                };

                return _cartRepository.AddToCart(cart, item);
            }
        }

        public AddToCartResponse AddToPOUCart(CartAddItemRequest request)
        {
            var cart = _cartRepository.GetPOUCart(request) ?? _cartRepository.AddPOUCart(request);

            var existingCartItem = cart.Items.FirstOrDefault(x => x.ItemNumber == request.ItemNumber);

            if (existingCartItem != null)
            {
                return new AddToCartResponse
                {
                    Status = AddToCartStatus.Exists,
                    CartQuantity = existingCartItem.Quantity,
                    ExistingItem = existingCartItem,
                    CartId = cart.Id,
                    CartItemCount = cart.Items.Count
                };
            }
            else
            {
                var itemInfo = GetUnprocessedItemInfo(request.Username, cart.Coid, cart.DepartmentNumber, cart.ParId, new List<CartItem> { new CartItem { ItemNumber = request.ItemNumber } }).FirstOrDefault();

                if (itemInfo.parItem == null)
                {
                    _log.Info($"Invalid item {request.ItemNumber} cannot be added to cart {cart.Id}");
                    //delete cart if cart item is 0(first item is invalid)
                    if (cart.Items.Count == 0)
                    {
                        _cartRepository.Delete(cart.Id);
                    }
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.Invalid
                    };
                }

                var maxCartItemCount = int.Parse(ConfigurationManager.AppSettings.Get("maxCartItemCount"));
                if (cart.Items.Count == maxCartItemCount)
                {
                    //delete cart if cart item is 0(first item is invalid)
                    if (cart.Items.Count == 0)
                    {
                        _cartRepository.Delete(cart.Id);
                    }
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.Full
                    };
                }

                if (cart.Items.Count == maxCartItemCount - 1 && !request.Override)
                {
                    //delete cart if cart item is 0(first item is invalid)
                    if (cart.Items.Count == 0)
                    {
                        _cartRepository.Delete(cart.Id);
                    }
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.AlmostFull
                    };
                }

                var item = new CartItem
                {
                    CartId = cart.Id,
                    ItemNumber = request.ItemNumber,
                    LastUpdatedUtc = DateTime.UtcNow,
                    Quantity = request.Quantity,
                    PARTypeId = request.PARTypeId
                };

                return _cartRepository.AddToCart(cart, item);
            }
        }

        public void UpdateCartItems(CartUpdateItemsRequest request)
        {
            _cartRepository.UpdateCartItems(request);
        }

        public void UpdateCartPOUItems(CartUpdateItemsRequest request)
        {
            _cartRepository.UpdateCartPOUItems(request);
        }

        public int DeleteCartItem(CartDeleteItemRequest request)
        {
            return _cartRepository.DeleteCartItem(request);
        }

        public int DeletePOUCartItem(CartDeleteItemRequest request)
        {
            return _cartRepository.DeletePOUCartItem(request);
        }

        public RequisitionWithDetailsDTO GetRequisition(CartRequest request)
        {
            var cart = _cartRepository.GetCart(request);

            if (cart == null) throw new Exception($"cart {request.CartId} does not exist for {request.Username}");

            Requisition requisition;
            if (cart.RequisitionId != null)
            {
                requisition = _requisitionService.GetRequisition(request.Username, (int)cart.RequisitionId);
                if (requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Submitted)
                {
                    requisition = ConvertCartToRequisition(request.Username, cart);
                }
                else
                {
                    requisition = UpdateExistingRequisitionForCart(requisition, request.Username, cart);
                    requisition = RevertRequisitionToDraft(requisition);
                }
            }
            else
            {
                requisition = ConvertCartToRequisition(request.Username, cart);
            }

            return _requisitionService.GetWithDetailsDTO(requisition, request.Username);
        }

        private Requisition UpdateExistingRequisitionForCart(Requisition requisition, string username, Cart cart)
        {
            requisition.RequisitionItems = requisition.RequisitionItems.Where(x => cart.Items.Any(y => y.ItemNumber.ToString() == x.ItemId)).ToList();

            var itemInfoList = GetUnprocessedItemInfo(username, cart.Coid, cart.DepartmentNumber, cart.ParId, cart.Items.ToList());

            foreach (var item in cart.Items)
            {
                var requisitionItem = requisition.RequisitionItems.FirstOrDefault(y => y.ItemId == item.ItemNumber.ToString());
                var (cartItem, parItem, inFlightQuantity) = itemInfoList.FirstOrDefault(y => y.parItem.ItemId == item.ItemNumber);
                if (requisitionItem != null && parItem != null)
                {
                    requisitionItem.QuantityToOrder = requisitionItem.Inventory.QuantityToOrder = GetQuantityToOrder(item.Quantity, (int)inFlightQuantity, (int)parItem.MinStock, (int)parItem.MaxStock);
                    requisitionItem.Inventory.QuantityOnOrder = (int)inFlightQuantity;
                    requisitionItem.Inventory.QuantityOnHand = item.Quantity;
                    requisitionItem.Inventory.LastUpdatedUTC = DateTime.UtcNow;
                    if (requisitionItem.QuantityToOrder == 0)
                    {
                        requisitionItem.RequisitionItemStatusTypeId = RequisitionItemStatusTypeEnum.ItemNotOrdered.ToInt();
                        requisitionItem.RequisitionItemStatusType = null;
                    }
                }
                else if (requisitionItem == null && parItem != null)
                {
                    var reqItem = ConvertCartItemToRequisitionItem(username, cart.Coid, cart.DepartmentNumber, cart.ParId, item, parItem, inFlightQuantity, requisition);
                    if (reqItem != null)
                    {
                        requisition.RequisitionItems.Add(reqItem);
                    }
                }
            }

            return requisition;
        }

        private Requisition ConvertCartToRequisition(string username, Cart cart)
        {
            var coid = _smartCoidService.GetCOID(username, cart.Coid);

            var requisition = new Requisition
            {
                RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.Draft,
                RequisitionTypeId = (int)RequisitionTypeEnum.Standard,
                CreatedBy = username,
                CreateDate = DateTime.Now,
                LocationIdentifier = $"{cart.Coid}_{cart.DepartmentNumber}",
                CountryCode = coid.SmartCountryCode,
                IsMobile = true
            };


            requisition.RequisitionItems = PopulateRequisitionWithCartItems(username, cart.Coid, cart.DepartmentNumber, cart.ParId, cart.Items.ToList(), requisition);

            return requisition;
        }

        private List<RequisitionItem> PopulateRequisitionWithCartItems(string username, string coid, int departmentNumber, string parId, List<CartItem> items, Requisition requisition)
        {
            var itemInfoList = GetUnprocessedItemInfo(username, coid, departmentNumber, parId, items.ToList());

            var requisitionItems = new List<RequisitionItem>();
            itemInfoList.ForEach(x =>
            {
                var reqItem = ConvertCartItemToRequisitionItem(username, coid, departmentNumber, parId, x.cartItem, x.parItem, x.inFlightQuantity, requisition);
                if (reqItem != null)
                {
                    requisitionItems.Add(reqItem);
                }
            });

            return requisitionItems;
        }

        private RequisitionItem ConvertCartItemToRequisitionItem(string username, string coid, int departmentNumber, string parId, CartItem cartItem, ParItemWithLastOrdered parItem, int? inFlightQuantity, Requisition requisition)
        {
            if (parItem == null)
            {
                _log.Info($"Cart: {cartItem.CartId}, no Item info found for ItemNumber {cartItem.ItemNumber}");
                return null;
            }
            if (inFlightQuantity == null)
            {
                throw new Exception($"Cart: {cartItem.CartId}, no InFlightQuantity found for ItemNumber {cartItem.ItemNumber}");
            }

            int quantityToOrder = GetQuantityToOrder(cartItem.Quantity, (int)inFlightQuantity, parItem.MinStock ?? 0, parItem.MaxStock ?? 0);

            return new RequisitionItem
            {
                Id = 0,
                RequisitionId = requisition?.RequisitionId ?? 0,
                Requisition = requisition,
                ItemId = cartItem.ItemNumber.ToString(),
                ParIdentifier = parId,
                RequisitionItemStatusTypeId = quantityToOrder == 0 ? RequisitionItemStatusTypeEnum.ItemNotOrdered.ToInt() : RequisitionItemStatusTypeEnum.Valid.ToInt(),
                QuantityToOrder = quantityToOrder,
                CreatedBy = username,
                CreateDate = DateTime.Now, //Requisitions use server time, not UTC
                IsFileItem = true,
                ReOrder = parItem.Item.ReorderNumber,
                PARLocation = parItem.Location,
                ItemDescription = parItem.Item.Description,
                VendorId = parItem.Item.Vendor.Id,
                VendorName = parItem.Item.Vendor.Name,
                GeneralLedgerCode = parItem.GLAccount.ToString(),
                StockIndicator = parItem.Item.IsStock,
                UOMCode = parItem.IssueUOM,
                UnitCost = parItem.ParPrice,
                TotalCost = parItem.ParPrice * quantityToOrder,
                MinStock = parItem.MinStock,
                MaxStock = parItem.MaxStock,
                CatalogNumber = parItem.Item.ManufacturerCatalogNumber,
                Inventory = new ItemInventory()
                {
                    COID = coid,
                    DepartmentId = departmentNumber,
                    ParId = parId,
                    ItemId = cartItem.ItemNumber.ToString(),
                    CatalogNumber = parItem.Item.ManufacturerCatalogNumber,
                    QuantityOnHand = cartItem.Quantity,
                    QuantityOnOrder = (int)inFlightQuantity,
                    QuantityToOrder = quantityToOrder,
                    UserName = username
                }
            };
        }

        private int GetQuantityToOrder(int quantityOnHand, int inFlightQuantity, int minStock, int maxStock)
        {
            int quantityToOrder = 0;
            if ((quantityOnHand + inFlightQuantity) <= minStock)
            {
                quantityToOrder = Math.Max(0, (maxStock - (quantityOnHand + inFlightQuantity)));
            }

            return quantityToOrder;
        }

        private Requisition RevertRequisitionToDraft(Requisition requisition)
        {
            requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.Draft;
            requisition.RequisitionItems.ToList().ForEach(x => { x.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Valid; });
            return requisition;
        }

        public Item_RFID GetRfidCartAttributes(CartRequest request)
        {
            var rfidCart = _cartRepository.GetRfidCartAttributes(request);
            return rfidCart;
        }

        public Cart GetCartMerge(CartRequest request)
        {
            var cart = new Cart();
            cart = _cartRepository.GetCartMerge(request);

            if (cart == null)
            {
                cart = _cartRepository.AddCartMerge(request);
            }

            if (cart?.Items?.Count > 0)
            {
                cart.Items = PopulateCartItemsWithDetails(request.Username, cart);
                var missingDetails = cart.Items.Where(x => x.Details == null).ToList();
                if (missingDetails.Any())
                {
                    foreach (var cartItem in missingDetails)
                    {
                        _cartRepository.DeleteCartItem(new CartDeleteItemRequest
                        {
                            CartId = cart.Id,
                            CartItemId = cartItem.Id
                        });
                    }
                }
            }
            return cart;
        }

        public CartAttributesResponse GetAttributesMerge(CartRequest request)
        {
            var cart = _cartRepository.GetCartMerge(request);

            if (cart == null)
            {
                cart = _cartRepository.AddCartMerge(request);
            }
            return new CartAttributesResponse
            {
                CartId = cart.Id,
                Count = cart.Items?.Count
            };
        }

        public void UpdateCartItemsMerge(CartUpdateItemsRequest request)
        {
            _cartRepository.UpdateCartItemsMerge(request);
        }

        public int DeleteCartItemMerge(CartDeleteItemRequest request)
        {
            return _cartRepository.DeleteCartItemMerge(request);
        }

        public AddToCartResponse AddToCartMerge(CartAddItemRequest request)
        {
            var cart = _cartRepository.GetCartMerge(request) ?? _cartRepository.AddCartMerge(request);

            var existingCartItem = cart.Items.FirstOrDefault(x => x.ItemNumber == request.ItemNumber);

            if (existingCartItem != null)
            {
                return new AddToCartResponse
                {
                    Status = AddToCartStatus.Exists,
                    CartQuantity = existingCartItem.Quantity,
                    ExistingItem = existingCartItem,
                    CartItemCount = cart.Items.Count
                };
            }
            else
            {
                var itemInfo = GetUnprocessedItemInfo(request.Username, cart.Coid, cart.DepartmentNumber, cart.ParId, new List<CartItem> { new CartItem { ItemNumber = request.ItemNumber } }).FirstOrDefault();
                if (itemInfo.parItem == null)
                {
                    _log.Info($"Invalid item {request.ItemNumber} cannot be added to cart {cart.Id}");
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.Invalid
                    };
                }
                var maxCartItemCount = int.Parse(ConfigurationManager.AppSettings.Get("maxCartItemCount"));
                if (cart.Items.Count == maxCartItemCount)
                {
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.Full
                    };
                }

                if (cart.Items.Count == maxCartItemCount - 1 && !request.Override)
                {
                    return new AddToCartResponse
                    {
                        Status = AddToCartStatus.AlmostFull
                    };
                }
                var item = new CartItem
                {
                    CartId = cart.Id,
                    ItemNumber = request.ItemNumber,
                    LastUpdatedUtc = DateTime.UtcNow,
                    Quantity = request.Quantity,
                    PARTypeId = request.PARTypeId
                };
                return _cartRepository.AddToCart(cart, item);
            }
        }
    }
}