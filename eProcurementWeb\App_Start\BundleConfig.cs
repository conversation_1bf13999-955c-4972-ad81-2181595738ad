﻿using System.Web;
using System.Web.Optimization;

namespace eProcurementWeb
{
    public class BundleConfig
    {
        public static void RegisterBundles(BundleCollection bundles)
        {
            var ProcurementServiceUrl = System.Configuration.ConfigurationManager.AppSettings["ProcurementServiceUrl"];
            if (ProcurementServiceUrl == "https://api-eprocurement.healthtrustpg.com/" 
                || ProcurementServiceUrl == "https://dev-api-eprocurement.healthtrustpg.com/"
                || ProcurementServiceUrl == "https://qa-api-eprocurement.healthtrustpg.com/")
            {
                //JQuery
                bundles.Add(new ScriptBundle("~/bundles/scripts/jquery")
                                .Include("~/Scripts/jquery-{version}.js",
                                         "~/Content/bootstrap/js/bootstrapDropdown.js")); //for Tools dropdown menu

                //IE specific js
                bundles.Add(new ScriptBundle("~/bundles/IEcompatibility").Include(
                            "~/Scripts/ieSpecific/iePolyfills.js",
                            "~/Scripts/ieSpecific/number-polyfill.js",
                            "~/Scripts/ieSpecific/json3.js",
                            "~/Scripts/ieSpecific/html5shiv.js",
                            "~/Scripts/ieSpecific/respond.js",
                            "~/Scripts/ieSpecific/placeholders.min.js"));

                //Angular
                bundles.Add(new ScriptBundle("~/bundles/angular").Include(
                            "~/Scripts/Angular/angular.js",
                            "~/Scripts/Angular/angular-animate.js",
                            "~/Scripts/Angular/angular-route.js",
                            "~/Scripts/Angular/angular-sanitize.min.js",
                            "~/Scripts/Angular/angular-resource.js",
                            "~/Content/bootstrap/js/ui-bootstrap-tpls-0.13.4.min.js",
                            "~/Scripts/jquery.scrollbar.js",
                            "~/Scripts/angular-vertilize.min.js",
                            "~/Scripts/EProcurementApp.js",
                            "~/oidc-client-ts.min.js",
                            "~/AuthNSilentRefresh.js")
                            .IncludeDirectory("~/Scripts/Services", "*.js")
                            .IncludeDirectory("~/Scripts/Controllers", "*.js")
                            .IncludeDirectory("~/Scripts/Directives", "*.js")
                            .IncludeDirectory("~/Scripts/Filters", "*.js"));

                //Angular Old Version for IE8 Support
                bundles.Add(new ScriptBundle("~/bundles/angularV12").Include(
                            "~/Scripts/AngularV12/angular.js",
                            "~/Scripts/AngularV12/angular-animate.js",
                            "~/Scripts/AngularV12/angular-route.js",
                            "~/Scripts/AngularV12/angular-resource.js",
                            "~/Content/bootstrap/js/ui-bootstrap-tpls-0.12.1.min.js",
                            "~/Scripts/jquery.scrollbar.js",
                            "~/Scripts/EProcurementApp.js")
                            .IncludeDirectory("~/Scripts/Services", "*.js")
                            .IncludeDirectory("~/Scripts/Controllers", "*.js")
                            .IncludeDirectory("~/Scripts/Directives", "*.js")
                            .IncludeDirectory("~/Scripts/Filters", "*.js"));
            }
            else
            {
                //JQuery
                bundles.Add(new Bundle("~/bundles/scripts/jquery")
                                .Include("~/Scripts/jquery-{version}.js",
                                         "~/Content/bootstrap/js/bootstrapDropdown.js")); //for Tools dropdown menu

                //IE specific js
                bundles.Add(new Bundle("~/bundles/IEcompatibility").Include(
                            "~/Scripts/ieSpecific/iePolyfills.js",
                            "~/Scripts/ieSpecific/number-polyfill.js",
                            "~/Scripts/ieSpecific/json3.js",
                            "~/Scripts/ieSpecific/html5shiv.js",
                            "~/Scripts/ieSpecific/respond.js",
                            "~/Scripts/ieSpecific/placeholders.min.js"));

                //Angular
                bundles.Add(new Bundle("~/bundles/angular").Include(
                            "~/Scripts/Angular/angular.js",
                            "~/Scripts/Angular/angular-animate.js",
                            "~/Scripts/Angular/angular-route.js",
                            "~/Scripts/Angular/angular-sanitize.min.js",
                            "~/Scripts/Angular/angular-resource.js",
                            "~/Content/bootstrap/js/ui-bootstrap-tpls-0.13.4.min.js",
                            "~/Scripts/jquery.scrollbar.js",
                            "~/Scripts/angular-vertilize.min.js",
                            "~/Scripts/EProcurementApp.js",
                            "~/oidc-client-ts.min.js",
                            "~/AuthNSilentRefresh.js")
                            .IncludeDirectory("~/Scripts/Services", "*.js")
                            .IncludeDirectory("~/Scripts/Controllers", "*.js")
                            .IncludeDirectory("~/Scripts/Directives", "*.js")
                            .IncludeDirectory("~/Scripts/Filters", "*.js"));

                //Angular Old Version for IE8 Support
                bundles.Add(new Bundle("~/bundles/angularV12").Include(
                            "~/Scripts/AngularV12/angular.js",
                            "~/Scripts/AngularV12/angular-animate.js",
                            "~/Scripts/AngularV12/angular-route.js",
                            "~/Scripts/AngularV12/angular-resource.js",
                            "~/Content/bootstrap/js/ui-bootstrap-tpls-0.12.1.min.js",
                            "~/Scripts/jquery.scrollbar.js",
                            "~/Scripts/EProcurementApp.js",
                            "~/oidc-client-ts.min.js",
                            "~/AuthNSilentRefresh.js")
                            .IncludeDirectory("~/Scripts/Services", "*.js")
                            .IncludeDirectory("~/Scripts/Controllers", "*.js")
                            .IncludeDirectory("~/Scripts/Directives", "*.js")
                            .IncludeDirectory("~/Scripts/Filters", "*.js"));
            }
            
            //Printer CSS bundle
            bundles.Add(new StyleBundle("~/bundles/PrintCss")
                .Include("~/Content/css/printStyle.css"));

            //CSS bundle
            bundles.Add(new StyleBundle("~/bundles/css")
                .Include("~/Content/bootstrap/css/bootstrap.css")
                .Include("~/Content/css/animate.css")
                .Include("~/Content/css/jquery.scrollbar.css")
                .Include("~/Content/css/style.css")
                .Include("~/Content/css/site.css")
                .Include("~/Content/css/billOnlyReviewerStyle.css")
                .Include("~/Content/css/WorkflowDetailsModal.css"));

            bundles.Add(new StyleBundle("~/bundles/iecss")
                .Include("~/Content/css/style_ie.css", new CssRewriteUrlTransform())
                .Include("~/Content/css/site_ie.css", new CssRewriteUrlTransform()));

            bundles.Add(new ScriptBundle("~/bundles/googleChart").Include(
                    "~/Scripts/ng-google-chart.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/ngCsv").Include(
                "~/Scripts/ng-csv.min.js"));

            bundles.Add(new Bundle("~/bundles/authn")
                .Include("~/Scripts/AuthnSilentRefresh.js")
                .Include("~/Scripts/Services/AuthnService.js")
                .Include("~/Scripts/oidc-client-ts.min.js"));

            BundleTable.EnableOptimizations = true;
        }
    }
}

