USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_RequisitionsVendorReportExportGet]    Script Date: 2/25/2025 3:17:25 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsVendorReportExportGet
Purpose     : Returns a non-paginated list of requisitions for the Vendor Search Requisition Report export button.
Used By     : SMART Procurement team
Author      : <PERSON><PERSON>asa<PERSON>han
Created     : 02-06-2018
Usage       : Executed by the Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Add IsMobile column
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Jeremiah King		05/16/2024		Added RequisitionSubmissionTypeId as new property
									for exported report.
Jeremiah King		02/26/2025		Added BadgeLogId and BadgeIn property from the RequisitionVProBadgeLog
									table to return in exported requisition results
***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsVendorReportExportGet]
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
BEGIN

SELECT
[PopulatedReq].[RequisitionId]				AS [RequisitionId],
[PopulatedReq].[CreatedBy]					AS [RequisitionerId],
[PopulatedReq].[RequisitionTypeId]			AS [RequisitionTypeId],
[PopulatedReq].[CreateDate]					AS [RequisitionCreateDate],
[PopulatedReq].[RequisitionStatusTypeId]	AS [RequisitionStatusTypeId],
[PopulatedReq].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[PopulatedReq].[FirstName]					AS [RequisitionerFirstName],
[PopulatedReq].[LastName]					AS [RequisitionerLastName],
[PopulatedReq].[IsMobile]					AS [RequisitionIsMobile],
[PopulatedReq].[IsVendor]					AS [RequisitionIsVendor],
[PopulatedReq].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
[PopulatedReq].[BadgeLogId]					AS [BadgeLogId],
[VPro].[BadgeIn]							AS [BadgeIn],
[ReqItem].[Id]								AS [RequisitionItemId],
[ReqItem].[ItemId]							AS [RequisitionItemNumber],
[ReqItem].[UOMCode]							AS [RequisitionItemUomCode],
[ReqItem].[PONumber]						AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]						AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]						AS [RequisitionItemVendorId],
[ReqItem].[VendorName]						AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]					AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]					AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]							AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]					AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]			AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]					AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]					AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]				AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId]			AS [RequisitionItemParentItemId],
[ReqItem].[Discount]						AS [Discount],
[SprDetails].[UOMCode]						AS [SprDetailsUomCode],
[SprDetails].[VendorId]						AS [SprDetailsVendorId],
[SprDetails].[VendorName]					AS [SprDetailsVendorName],
[SprDetails].[PartNumber]					AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]				AS [SprDetailsDescription],
[SprDetails].[TradeInValue]					AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]					AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]			AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]				AS [SprDetailsEstimatedUnitPrice]
FROM
(
	SELECT DISTINCT
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [MatchingVendorReq].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [MatchingVendorReq].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END
	END) AS [ReqTypeGroupingOrder],
	[MatchingVendorReq].[RequisitionId],
	[MatchingVendorReq].[CreatedBy],
	[MatchingVendorReq].[RequisitionTypeId],
	[MatchingVendorReq].[CreateDate],
	[MatchingVendorReq].[RequisitionStatusTypeId],
	[MatchingVendorReq].[LocationIdentifier],
	[MatchingVendorReq].[IsMobile],
	[MatchingVendorReq].[IsVendor],
	[MatchingVendorReq].[RequisitionSubmissionTypeId],
	[MatchingVendorReq].[BadgeLogId],
	[User].[FirstName],
	[User].[LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId]
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 14 THEN 5
		WHEN 2 THEN 6
		WHEN 4 THEN 7
		ELSE 8
		END END) AS [ConditionalStatusSorting]
	FROM
	(
		SELECT DISTINCT
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] ON [SprDetail].[RequisitionItemId] = [Item].[Id]
		WHERE
		(@VendorId IS NULL OR Cast([Item].[VendorId] as VARCHAR(32)) = @VendorId OR Cast([SprDetail].[VendorId] as VARCHAR(32)) = @VendorId)
		AND (@VendorName IS NULL OR [Item].[VendorName] = @VendorName OR [SprDetail].[VendorName] = @VendorName)
	) AS [MatchingVendorItem]
	LEFT OUTER JOIN [dbo].[Requisitions] AS [MatchingVendorReq] ON [MatchingVendorItem].[RequisitionId] = [MatchingVendorReq].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [MatchingVendorReqItems] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionId] = [MatchingVendorReqItems].[RequisitionId]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] WITH (NOLOCK) ON [MatchingVendorReqItems].[Id] = [SprDetail].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [MatchingVendorReqItems].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionSubmissionTypes] AS [RequisitionSubmissionTypes] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionSubmissionTypeId] = [RequisitionSubmissionTypes].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [MatchingVendorReq].[CreatedBy] = [User].[AccountName]
	WHERE
	substring([MatchingVendorReq].[LocationIdentifier],0,(CHARINDEX('_',[MatchingVendorReq].[LocationIdentifier]))) = @coid
	AND (@filterText IS NULL OR
	([MatchingVendorReq].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [RequisitionSubmissionTypes].[Description] LIKE '%' + @filterText + '%' 
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%')
	OR [MatchingVendorReq].[Comments] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[PONumber] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReq].[LocationIdentifier] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [MatchingVendorReqItems].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([MatchingVendorReq].[LocationIdentifier], 0, CHARINDEX('_', [MatchingVendorReq].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [MatchingVendorReq].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	AND 1 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 5 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 8 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 12 <> [MatchingVendorReq].[RequisitionStatusTypeId]
) AS [PopulatedReq]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [PopulatedReq].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
LEFT OUTER JOIN [dbo].[RequisitionVProBadgeLogs] AS VPro WITH (NOLOCK) ON [Vpro].[Id] = [PopulatedReq].[BadgeLogId]
ORDER BY
[ConditionalStatusSorting], [PopulatedReq].[ReqTypeGroupingOrder], 
CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
CASE @oldestFirst WHEN 1 THEN [PopulatedReq].[CreateDate] END ASC,
CASE @oldestFirst WHEN 0 THEN [PopulatedReq].[CreateDate] END DESC
OPTION (RECOMPILE)

END
GO


