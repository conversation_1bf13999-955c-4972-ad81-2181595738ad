﻿using System;

namespace RequisitionServices.Utility.Date
{
    public static class DateTimeExtensions
    {
        public static DateTime? SpecifyUtc(this DateTime? dateTimeUtc)
        {
            if (dateTimeUtc.HasValue)
            {
                return SpecifyUtc(dateTimeUtc.Value);
            }

            return null;
        }

        public static DateTime SpecifyUtc(this DateTime dateTimeUtc)
        {
            return DateTime.SpecifyKind(dateTimeUtc, DateTimeKind.Utc);
        }
    }
}