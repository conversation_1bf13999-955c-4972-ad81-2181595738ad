﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IRequisitionRepository
    {
        Requisition GetRequisition(int id);

        Requisition GetRequisition(int id, string coid);

        IQueryable<Requisition> GetRequisitions();

        RequisitionReportResults GetRequisitionsForReport(int rowOffset, int pageSize, RequisitionSortOrder sortOrder,
            string coid, DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds,
            List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations = null);

        RequisitionReportResults GetVBORequisitionsForReport(int rowOffset, int pageSize, RequisitionSortOrder sortOrder,
            string coid, DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds,
            List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations);

        RequisitionReportExportResults GetRequisitionsForReportExport(RequisitionSortOrder sortOrder, string coid,
            DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds,
            List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations);

        RequisitionReportExportResults GetVBORequisitionsForReportExport(RequisitionSortOrder sortOrder, string coid,
            DateTime startDate, DateTime endDate, string filterText, List<int> departmentIds,
            List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations);

        RequisitionReportResults GetRequisitionsForReportByItemNumber(int rowOffset, int pageSize, RequisitionSortOrder sortOrder,
            string coid, string searchText, string filterText,
            List<int> vendorAffiliations, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        RequisitionReportExportResults GetRequisitionsForReportByItemNumberExport(RequisitionSortOrder sortOrder,
            string coid, string searchText, string filterText,
            List<int> vendorAffiliations, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        RequisitionReportResults GetRequisitionsForReportByVendor(int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string coid, string vendorIdName, string filterText, List<int> vendorAffiliations, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        RequisitionReportExportResults GetRequisitionsForReportByVendorExport(RequisitionSortOrder sortOrder, string coid,
            string vendorIdName, string filterText,
            List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter, List<int> vendorAffiliations);

        RequisitionReportResults GetRequisitionsForUser(string username, int rowOffset, int pageSize, string filterText,
            RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        ApprovalPageResults GetPendingApprovalsForApprover(string userName, bool isExcludeVboChecked, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        ApprovalPageResults GetUpcomingApprovalsForApprover(string userName, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        ApprovalPageResults GetApprovalHistoryForApprover(string userName, int rowOffset, int pageSize, string filterText, RequisitionSortOrder sortOrder, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        List<Requisition> GetRequisitionsWithDetailsByDateRange(string COID, int departmentId, DateTime startDate, DateTime endDate);

        void UpdateRequisitionStatusToSubmittedIfNeeded(string messageId, int reqItemId, bool isFromMQMessage);

        RequisitionReportResults GetTemplatesforAUser(string username, int rowOffset, int pageSize, string filterText, List<string> facilitiesMatchedOnFilter, List<string> departmentsMatchedOnFilter);

        void InsertRequisition(Requisition requisition, string userName);

        ClinicalUseDetail GetClinicalUseDetail(int id);

        RequisitionItem GetReqRequisitionItem(int? id, string coid, string parentSystemId, string itemId);

        RequisitionItem GetSprRequisitionItem(int? id, string coid, string parentSystemId, int vendorId, string partNumber);

        void UpdateRequisition(Requisition requisition, string userName, bool isAudited, bool isMQEnabled, bool isFromSMARTResponse, bool isApprover = false);

        void UpdateRequisitionAsApprover(Requisition updatedRequisition, string userName, bool isAudited, bool isMQEnabled, bool isFromSMARTResponse);

        void UpdateRequisitionOnReorderWorkflowAndAddHistory(Requisition requisition, string userName);

        RequisitionStatusHistory BuildRequisitionStatusHistory(Requisition requisition, string updater, int reqStatusTypeId);

        void BulkInsertRequisitionStatusHistory(IEnumerable<RequisitionStatusHistory> requisitionStatusHistories);

        int UpdateRequisitionItemPONumber(RequisitionItem requisitionItem);

        bool DeleteAttachment(List<string> fileNames);

        List<Requisition> GetRequisitionsByWorkflowOwners(string userName, IEnumerable<UserWorkflowStep> stepsCanAffect, string COID);

        List<Requisition> GetPendingRequisitionsByFacilityWorkflow(string COID);
        
        List<RequisitionItem> GetReqRequisitionItems(int? id, string coid, string parentSystemId, string itemId, bool loadRequisitionItemStatusHistory = false);

        RequisitionItem GetSprRequisitionItem(int? id, string coid, string parentSystemId, int vendorId, string partNumber, bool loadRequisitionItemStatusHistory = false);

        void UpdateRequisitionItemAndAddHistory(IEnumerable<RequisitionItem> requisitionItems);

        List<FileAttachment> GetFileAttachments(int requisitionItemId);

        List<Requisition> GetPendingRequisitionsForUserCOID(string userName, string COID);

        void UpdateSprDetails(IEnumerable<SPRDetail> sprDetails, string userName);

        List<int> GetPoNumbersByProjectNumberAndCoid(string coid, string projectNumber);

        Tuple<RequisitionItem, int> AddNewSubstituteRequisitionItemtoRequisition(RequisitionItem originalItem, RequisitionItem subItem, string userName);

        RequisitionItem GetRequisitionItemById(int Id);

        LastOrderDetailsDTO GetLastOrderDetails(string coid, string dept, string parClass, string itemId);

        SmartItemRequisitionIdDTO GetSmartItemRequisitionIdByPONumber(SmartItemRequisitionIdDTO smartItemRequisitionIdDTO);

        List<ParItemWithLastOrdered> GetLastOrderedDetails(string coid, string department, string parId, List<ParItem> parItems);

        void UpdatePriceForVboHoldItemConversions(List<VboHoldItemConversionDto> updateItems);

        void DeleteClinicalUseDetails(IEnumerable<ClinicalUseDetail> clinicalUseDetails);

        void DeleteRequisitionItems(IEnumerable<RequisitionItem> reqItems);

        /// <summary>
        /// Retrieves a queue of purchasing requisitions for the purchasing report from the data source.
        /// </summary>
        /// <param name="reportParameters">The parameters for the purchasing requisition report.</param>
        /// <returns>A <see cref="PurchasingRequisitionReportResultsDTO"/> containing the list of requisitions and the total count.</returns>
        PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport(PurchasingRequisitionReportParameters reportParameters);
        
        /// <summary>
        /// GetAdvancedFiltersForPurchasingReport method is used to get the advanced filter value for the Purchasing using LINQ
        /// </summary>
        /// <param name="filterList"></param>
        /// <returns>mulitple list of values</returns>

        RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList);

        /// <summary>
        /// Retrieves a list of requisitions based on the provided purchase order number and COID.
        /// </summary>
        /// <param name="poNumber">The purchase order number.</param>
        /// <param name="coid">The company ID.</param>
        /// <returns>A list of <see cref="RequisitionDTO"/> objects that match the specified criteria.</returns>
        List<RequisitionDTO> GetRequisitionAndItemsByPONumber(int poNumber, string coid);
    }
}
