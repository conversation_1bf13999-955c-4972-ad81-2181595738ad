<?xml version="1.0" encoding="utf-8"?>

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>

  <connectionStrings>
    <add name="EProcurementContext"
         providerName="System.Data.SqlClient"
         connectionString="Server=htnawpdbsolp07.hca.corpad.net; Database=EProcurement; Integrated Security=True; MultipleActiveResultSets=True;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>

    <add name="WorkflowPersistanceStore"
         providerName="System.Data.SqlClient"
         connectionString="Server=htnawpdbsolp07.hca.corpad.net;Initial Catalog=WorkflowPersistanceStore;Integrated Security=True;Asynchronous Processing=True;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>

  <appSettings>

	<add key="SSO_client_id" value="prod-smart-sec" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
	<add key="SSO_client_secret" value="GIsFHFVwN8yH0I96P7bhpiwQmz7YvTjk" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="SmartClinetAuthUrl" value="https://sso.healthtrustpg.com/as/token.oauth2" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="SecurityAPINewUrl" value="https://api-smartclientauthorization.healthtrustpg.com/users/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	  
    <add key="HomeAPIUrl" value="http://api.healthtrustpg.com/v1/api/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SmartServicesAPIUrl" value="http://api.smartservices.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <!--TODO: replace when QA and PROD are ready for HTTPS-->
    <!--<add key="SmartServicesAPIUrl" value="https://api-smartservices.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>-->

    <add key="SecurityAPIUrl" value="http://api.nsa.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SecurtiyAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="ProfileAPIUrl" value="http://api-profile.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EProcurementUrl" value="http://smart.healthtrustpg.com/procurement/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SwaggerRootUrl" value="http://api.requisitions.healthtrustpg.com" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <!--TODO: replace when QA and PROD are ready for HTTPS-->
    <!--<add key="SwaggerRootUrl" value="https://api-requisitions.healthtrustpg.com" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>-->
    <add key="AppInsightsEnabled" value="True"  xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ikey" value="0f2d57e4-96d1-4153-9cda-8fccbf0334ce" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="Environment" value="Production" xdt:Transform="SetAttributes" xdt:Locator="Match(key)" />
    
    <add key="coidsForQATesting" value="" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

	<!-- Legacy Connector -->
	<add key="ItemPriceLegacyConnectorAPIUrl" value="https://api.app.hca.cloud/legacy-connector-services/legacy-connector-itemcontract-api/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenUrl" value="https://api.app.hca.cloud/token" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenUsername" value="taWPpBQLVWXkDr7tmbZB7HUvUnIs0lPb8rnOt2b4pkGosyPR" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenPassword" value="rNUnNVqPbRVHNceuDyY9oTr7DH4fHQdJN5gV5dGTF2KDLhe9TzCTEcQKrKgSSMMA" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenGrant_type" value="client_credentials" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

  </appSettings>

  <system.serviceModel>
    <client>
      <endpoint address="http://approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx"
          binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IRushApprovalWorkflow"
          contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpBinding_IRushApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
      <endpoint address="http://approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx"
          binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_INonRushApprovalWorkflow"
          contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
      <endpoint address="http://approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx"
          binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IVendorApprovalWorkflow"
          contract="VendorWorkflowSvc.IVendorApprovalWorkflow" name="BasicHttpBinding_IVendorApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </client>
  </system.serviceModel>

  <system.web>
    <compilation xdt:Transform="RemoveAttributes(debug)" />
  </system.web>

  <log4net xdt:Transform="Replace">
    <appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="INFO"/>
      <param name="File" value="E:\Logs\requisitionServices\"/>
      <param name="MaxSizeRollBackups" value="-1"/>
      <param name="RollingStyle" value="Date"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyyMMdd'_Info.log'"/>
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="INFO"/>
        <levelMax value="WARN"/>
      </filter>
    </appender>
    <appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="ERROR"/>
      <param name="File" value="E:\Logs\requisitionServices\"/>
      <param name="MaxSizeRollBackups" value="-1"/>
      <param name="RollingStyle" value="Date"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyyMMdd'_Error.log'"/>
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="ERROR"/>
        <levelMax value="FATAL"/>
      </filter>
    </appender>
    <root>
      <appender-ref ref="InfoFileAppender"/>
      <appender-ref ref="ErrorFileAppender"/>
    </root>
  </log4net>
  
</configuration>