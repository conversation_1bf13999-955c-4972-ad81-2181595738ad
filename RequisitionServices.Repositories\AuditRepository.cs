﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Audit;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Database;

namespace RequisitionServices.Repositories
{
    public class AuditRepository : AbstractRepository, IAuditRepository
    {
        public AuditRepository(EProcurementContext context) : base(context) { }

        public void UpdateAuditChangeLog(IEnumerable<AuditEventsDTO> AuditChangeInformation)
        {
            using (EProcurementContext contextChangeLog = new EProcurementContext())
            {
                foreach (AuditEventsDTO approverChangeInfo in AuditChangeInformation)
                {
                    contextChangeLog.AuditEvents.Add(new AuditEvents()
                    {
                        AdminUserID = approverChangeInfo.AdminUserID,
                        Entity = approverChangeInfo.Entity,
                        EditedUserID = approverChangeInfo.EditedUserID,
                        COID = approverChangeInfo.COID,
                        AuditEventType = approverChangeInfo.AuditEventType,
                        CreatedDateTime = approverChangeInfo.CreatedDateTime,
                        Comments = approverChangeInfo.Comments,
                        ChangeXML = approverChangeInfo.ChangeXML.ToString()
                    });
                }
                contextChangeLog.SaveChanges();
            }
        }
    }
}
