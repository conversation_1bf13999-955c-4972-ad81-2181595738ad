﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Utility.Domain;
using RequisitionServices.Utility.Model;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.MMISServices.DTO
{
    public class RequisitionModel
    {
        public RequisitionModel() { }
        public RequisitionModel(Requisition requisition) 
        { 
            if(requisition != null)
            {
                this.SmartCountryCode = requisition.CountryCode;
                this.Comments = requisition.Comments;

                if(requisition.RequisitionItems != null)
                { 
                this.RequisitionItems = new List<RequisitionItemModel>();
                    foreach (var reqItem in requisition.RequisitionItems)
                    {
                        if (reqItem.SPRDetail != null)
                        {
                            long glCode = 0;
                            Int64.TryParse(reqItem.SPRDetail.GeneralLedgerCode, out glCode);

                            var sprItem = new RequisitionItemModel()
                            {
                                Id = reqItem.Id,
                                Coid = Int32.Parse(LocationMapper.GetCOID(requisition.LocationIdentifier)),
                                ParDepartment = Int32.Parse(LocationMapper.GetDepartmentId(requisition.LocationIdentifier)),
                                ItemQuantity = reqItem.QuantityToOrder,
                                ReqRush = reqItem.IsRushOrder,
                                RequestDate = requisition.CreateDate,
                                RequisitionId = requisition.RequisitionId,
                                SPRType = SPRTypeMapper.GetSPRType(reqItem.IsRushOrder, (RequisitionTypeEnum)requisition.RequisitionTypeId, (SPRTypeEnum)reqItem.SPRDetail.SPRTypeId),
                                RequisitionerName = reqItem.RequisitionerName,
                                ReorderNumber = reqItem.SPRDetail.PartNumber,
                                VendorName = reqItem.SPRDetail.Vendor != null ? reqItem.SPRDetail.Vendor.Name : null,
                                VendorNumber = reqItem.SPRDetail.VendorId,
                                ItemDescription = reqItem.SPRDetail.ItemDescription,
                                UnitOfMeasure = reqItem.SPRDetail.UOMCode,
                                ItemPrice = reqItem.SPRDetail.EstimatedPrice ?? 0,
                                GLAccountNumber = glCode,
                                ShipToFacilityName = reqItem.SPRDetail.ShipToAddress != null ? reqItem.SPRDetail.ShipToAddress.AddressName : null,
                                ShipToAddress = reqItem.SPRDetail.ShipToAddress != null ? reqItem.SPRDetail.ShipToAddress.Address1 : null,
                                ShipToCity = reqItem.SPRDetail.ShipToAddress != null ? reqItem.SPRDetail.ShipToAddress.City : null,
                                ShipToState = reqItem.SPRDetail.ShipToAddress != null ? reqItem.SPRDetail.ShipToAddress.State : null,
                                ShipToZip = reqItem.SPRDetail.ShipToAddress != null ? reqItem.SPRDetail.ShipToAddress.Zip : null,
                                ShippingInstructions = reqItem.SPRDetail.DeliveryMethodTypeId != 0 && reqItem.SPRDetail.DeliveryMethodType != null ? reqItem.SPRDetail.DeliveryMethodType.Description : null,
                                SpecialInstructions = requisition.Comments,
                                Justification = EnumUtility.GetEnumDescription((SPRTypeEnum)reqItem.SPRDetail.SPRTypeId) + ":" + reqItem.SPRDetail.AdditionalInformation,
                                AttachmentFlag = reqItem.SPRDetail.FileAttachments.Any(),
                                BudgetNumber = reqItem.SPRDetail.BudgetNumber,
                                TradeIn = reqItem.SPRDetail.TradeInValue ?? 0,
                                ShipToCode = reqItem.SPRDetail.ShipToAddressId ?? 0,
                                BillReplaceType = BillReplaceTypeMapper.GetBillReplaceType((SPRTypeEnum)reqItem.SPRDetail.SPRTypeId),
                                ParClass = reqItem.ParIdentifier,
                                ItemNumber = reqItem.SmartItemNumber != null ? (int)reqItem.SmartItemNumber : 0
                            };


                            this.RequisitionItems.Add(sprItem);
                        }
                        else if (reqItem.Discount != null)
                        {
                            this.RequisitionItems.Add(new RequisitionItemModel()
                            {
                                Id = reqItem.Id,
                                Coid = Int32.Parse(LocationMapper.GetCOID(requisition.LocationIdentifier)),
                                ParDepartment = Int32.Parse(LocationMapper.GetDepartmentId(requisition.LocationIdentifier)),
                                ItemNumber = Int32.Parse(reqItem.ItemId),
                                ItemQuantity = reqItem.QuantityToOrder,
                                ParClass = reqItem.ParIdentifier,
                                ReqRush = reqItem.IsRushOrder,
                                RequestDate = requisition.CreateDate,
                                RequisitionId = requisition.RequisitionId,
                                RequisitionerName = reqItem.RequisitionerName,
                                ItemPrice = Math.Round((decimal)reqItem.UnitCost * (100 - (decimal)reqItem.Discount) / 100, 2),
                                ReorderNumber = reqItem.ReOrder,
                                SPRType = SPRTypeMapper.GetSPRType(reqItem.IsRushOrder, (RequisitionTypeEnum)requisition.RequisitionTypeId, SPRTypeEnum.WastePar),
                                VendorName = reqItem.VendorName,
                                VendorNumber = reqItem.VendorId,
                                ItemDescription = reqItem.ItemDescription,
                                UnitOfMeasure = reqItem.UOMCode,
                                GLAccountNumber = Int32.Parse(reqItem.GeneralLedgerCode),
                                ShipToFacilityName =  null,
                                ShipToAddress =  null,
                                ShipToCity =  null,
                                ShipToState = null,
                                ShipToZip = null,
                                ShippingInstructions = null,
                                SpecialInstructions = requisition.Comments,
                                Justification = EnumUtility.GetEnumDescription(SPRTypeEnum.WastePar) + " " + reqItem.Discount.ToString() + "% Discount",
                                AttachmentFlag = false,
                                TradeIn = 0,
                                ShipToCode = 0,
                                BillReplaceType = BillReplaceTypeMapper.GetBillReplaceType(SPRTypeEnum.WastePar)
                            });
                        }
                        else
                        {
                            this.RequisitionItems.Add(new RequisitionItemModel()
                            {
                                Id = reqItem.Id,
                                Coid = Int32.Parse(LocationMapper.GetCOID(requisition.LocationIdentifier)),
                                ParDepartment = Int32.Parse(LocationMapper.GetDepartmentId(requisition.LocationIdentifier)),
                                ItemNumber = Int32.Parse(reqItem.ItemId),
                                ItemQuantity = reqItem.QuantityToOrder,
                                ParClass = reqItem.ParIdentifier,
                                ReqRush = reqItem.IsRushOrder,
                                RequestDate = requisition.CreateDate,
                                RequisitionId = requisition.RequisitionId,
                                RequisitionerName = reqItem.RequisitionerName
                            });
                        }
                    }
                }
            }
        }

        public string SmartCountryCode { get; set; }
        public string Comments { get; set; }

        public List<RequisitionItemModel> RequisitionItems { get; set; }
    }
}
