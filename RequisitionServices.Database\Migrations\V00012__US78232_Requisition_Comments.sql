USE [eProcurementQA]
GO

IF (EXISTS (SELECT * 
                 FROM INFORMATION_SCHEMA.TABLES 
                 WHERE TABLE_SCHEMA = 'dbo' 
                 AND  TABLE_NAME = '__MigrationHistory'))
BEGIN
	PRINT N'Dropping table __MigrationHistory...';
	DROP TABLE __MigrationHistory
END
GO

CREATE TABLE [dbo].[Comments](
	[Id] [bigint] IDENTITY (1, 1) NOT NULL,
	[RequisitionId] [int] NOT NULL,
	[UserId] INT NOT NULL,
	[Text] NVARCHAR(1000) NOT NULL,
    [CreatedUtc] DATETIME2(7) NOT NULL,
	[LastUpdatedUtc] DATETIME2(7) NOT NULL,
 CONSTRAINT [PK__Comments] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Comments] 
	ADD  CONSTRAINT [DF__Comments__LastUpdatedUtc]  DEFAULT (sysutcdatetime()) FOR [LastUpdatedUtc]
GO

ALTER TABLE [dbo].[Comments] 
	ADD  CONSTRAINT [DF__Comments__CreatedUtc]  DEFAULT (sysutcdatetime()) FOR [CreatedUtc]
GO

ALTER TABLE [dbo].[Comments] WITH NOCHECK
    ADD CONSTRAINT [FK__Comments__Requisitions__RequisitionId] FOREIGN KEY ([RequisitionId]) REFERENCES [dbo].[Requisitions] ([RequisitionId])
GO

ALTER TABLE [dbo].[Comments] WITH NOCHECK
    ADD CONSTRAINT [FK__Comments__Users__Id] FOREIGN KEY ([UserId]) REFERENCES [dbo].[Users] ([Id])
GO
