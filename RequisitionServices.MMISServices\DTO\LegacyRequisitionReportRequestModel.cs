﻿using System;
using System.Collections.Generic;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.MMISServices.DTO
{
    public class LegacyRequisitionReportRequestModel
    {
        public int COID { get; set; }

        public string UserName { get; set; }

        public int PageNumber { get; set; }

        public IEnumerable<string> PageIndices { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public int ItemNumber { get; set; }

        public int VendorNumber { get; set; }

        public LegacyRequisitionReportRequestModel(LegacyRequisitionReportRequestDTO request)
        {
            int coid = 0;
            int.TryParse(request.COID, out coid);
            this.COID = coid;

            UserName = request.UserName;
            PageNumber = request.PageNumber;
            PageIndices = request.PageIndices;
            StartDate = request.StartDate;
            EndDate = request.EndDate;
            ItemNumber = request.ItemNumber;
            VendorNumber = request.VendorNumber;
        }
    }
}
