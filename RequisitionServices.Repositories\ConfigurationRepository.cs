﻿using RequisitionServices.Database;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.Repositories
{
    public class ConfigurationRepository : AbstractRepository, IConfigurationRepository
    {
        public ConfigurationRepository(EProcurementContext context) : base(context) { }

        public IEnumerable<FacilityNotification> GetFacilityNotifications(string cOID)
        {
            return context.FacilityNotifications.Where(x => x.COID == cOID);
        }

        public FacilityNotification GetGetFacilityNotification(int id)
        {
            return context.FacilityNotifications.Where(x => x.Id == id).FirstOrDefault();
        }

        public FacilityNotification InsertFacilityNotification(FacilityNotification facilityNotification)
        {
            context.FacilityNotifications.Add(facilityNotification);
            context.SaveChanges();

            return facilityNotification;
        }

        public void UpdateFacilityNotification(FacilityNotification facilityNotification, string userName)
        {
            //Get current FacilityNotification from DB
            var currentFacilityNotification = this.GetGetFacilityNotification(facilityNotification.Id);

            //Set scalar values from incoming object
            context.Entry(currentFacilityNotification).CurrentValues.SetValues(facilityNotification);

            context.SaveChanges();
        }

        public void DeleteFacilityNotification(int id)
        {
            var currentFacilityNotification = this.GetGetFacilityNotification(id);
            
            if(currentFacilityNotification != null)
            {
                context.FacilityNotifications.Remove(currentFacilityNotification);

                context.SaveChanges();
            }
        }

        public IEnumerable<FacilityNotificationType> GetFacilityNotificationTypes()
        {
            return context.FacilityNotificationTypes.ToList();
        }

        public FacilityNotificationType GetGetFacilityNotificationType(int id)
        {
            return context.FacilityNotificationTypes.Where(x => x.Id == id).FirstOrDefault();
        }
    }
}
