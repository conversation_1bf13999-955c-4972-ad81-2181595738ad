﻿using RequisitionServices.DomainServices.Interface;
using System;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Locations;
using log4net;
using RequisitionServices.MMISServices.Interface;
using System.Reflection;
using System.Web;
using System.Web.Caching;

namespace RequisitionServices.DomainServices
{
    public class ConfigurationService : IConfigurationService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private ISmartCOIDService smartCoidService;
        private const string UserName = "System";
        private const string CacheName = "CoidCache";
        private static readonly object CacheReadLock = new object();

        public ConfigurationService(ISmartCOIDService smartCoidSvc)
        {
            if (smartCoidSvc == null)
            {
                throw new ArgumentNullException("smartCoidSvc");
            }

            this.smartCoidService = smartCoidSvc;
        }

        public bool UseStatusFromIIB(string coid)
        {
            var coidConfiguration = HttpRuntime.Cache[CacheName] as IEnumerable<Location>;

            if (coidConfiguration == null)
            {
                lock(CacheReadLock)
                {
                    coidConfiguration = HttpRuntime.Cache[CacheName] as IEnumerable<Location>;
                    if (coidConfiguration == null)
                    {
                        coidConfiguration = GetCoidConfiguration();
                        HttpRuntime.Cache.Insert(CacheName, coidConfiguration, null, DateTime.UtcNow.AddMinutes(60), Cache.NoSlidingExpiration);
                    }
                }
            }

            var smartCoidConfig = coidConfiguration.FirstOrDefault(x => x.Id == coid);

            return smartCoidConfig != null ? smartCoidConfig.MQEnabled : false;
        }

        private IEnumerable<Location> GetCoidConfiguration()
        {
            return this.smartCoidService.GetUserCOIDs(UserName);
        }
    }
}
