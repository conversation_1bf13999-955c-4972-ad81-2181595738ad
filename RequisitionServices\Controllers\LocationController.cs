﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    public class LocationController : ApiController
    {
        private ILocationService locationService;

        public LocationController(ILocationService locationSvc)
        {
            this.locationService = locationSvc;
        }

        ///// <summary>
        ///// Get All GL Accounts for a specific facility
        ///// </summary>
        ///// <param name="userName"></param>
        ///// <param name="COID"></param>
        ///// <param name="costCode">Optional</param>
        ///// <returns></returns>
        //[HttpGet]
        //public IEnumerable<GLAccount> GetAllGLAccounts(string userName, string COID, int? costCode = null)
        //{
        //    var gLAccounts = locationService.GetAllGLAccounts(userName, COID, costCode);

        //    return gLAccounts;
        //}

        /// <summary>
        /// Get All GL Accounts for a specific by 3 character filter
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="accountStringPartial"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<GLAccount> GetAllGLAccounts(string userName, string COID, string accountStringPartial)
        {
            var gLAccounts = locationService.GetAllGLAccounts(userName, COID, accountStringPartial);

            return gLAccounts;
        }

        /// <summary>
        /// Get all cost codes for a specific facility
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<int> GetAllCostCodes(string userName, string COID)
        {
            var costCodes = locationService.GetAllCostCodes(userName, COID);

            return costCodes;
        }

        /// <summary>
        /// Get a specific GL Account by it's account number
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="accountNumber"></param>
        /// <returns></returns>
        [HttpGet]
        public GLAccount GetGLAccount(string userName, string COID, long accountNumber)
        {
            var gLAccount = locationService.GetGLAccount(userName, COID, accountNumber);

            return gLAccount;
        }

        /// <summary>
        /// Get a specific address by it's ship number (External System Id)
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="shipNumber"></param>
        /// <returns></returns>
        [HttpGet]
        public Address GetAddress(string userName, string COID, int shipNumber)
        {
            var address = locationService.GetAddress(userName, COID, shipNumber);

            return address;
        }

        /// <summary>
        /// Get a list of addresses for a facility
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<Address> GetAddresses(string userName, string COID)
        {
            var addresses = locationService.GetAllAddresses(userName, COID);

            return addresses;
        }

        /// <summary>
        /// Saves updated list of facility notifications
        /// </summary>
        /// <param name="facilityNotifications"></param>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpPost]
        public IEnumerable<FacilityNotification> SaveFacilityNotifications(IEnumerable<FacilityNotification> facilityNotifications, string userName, string COID)
        {
            var notifications = locationService.SaveFacilityNotifications(facilityNotifications, userName, COID);

            return notifications;
        }

        /// <summary>
        /// Get a list of all notifications available for a facility
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <returns></returns>
        [HttpGet]
        public IEnumerable<FacilityNotification> GetFacilityNotifications(string userName, string COID)
        {
            var notifications = locationService.GetFacilityNotifications(userName, COID);

            return notifications;
        }

        /// <summary>
        /// Update Locations and return results
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="locator"></param>
        /// <returns></returns>
        [HttpPost]

        public Locator UpdateLocator(string userName, Locator locator)
        {
            return locationService.UpdateLocator(userName, locator);
        }
    }
}
