﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Requisitions;

namespace RequisitionServices.DomainServices.Interface
{
    public interface ICensorService
    {
        Requisition CensorPrivateRequisitionData(string userName, Requisition requisition);

        RequisitionWithDetailsDTO CensorPrivateRequisitionData(string userName, RequisitionWithDetailsDTO requisition);

        IEnumerable<Requisition> CensorPrivateRequisitionData(string userName, IEnumerable<Requisition> requisition);

        IEnumerable<RequisitionWithDetailsDTO> CensorPrivateRequisitionData(string userName, IEnumerable<RequisitionWithDetailsDTO> requisition);
    }
}
