﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class PurchasingRequisitionReportResultsDTO
    {

            /// <summary>
            /// Initializes a new instance of the <see cref="PurchasingRequisitionReportRestulsDTO"/> class.
            /// </summary>
            /// <param name="requisitions">The list of purchasing requisitions in the queue.</param>
            /// <param name="totalCount">The total number of requisitions available.</param>
            public PurchasingRequisitionReportResultsDTO(List<PurchasingRequisitionDTO> requisitions, int totalCount)
            {
                this.Requisitions = requisitions;
                this.TotalCount = totalCount;
            }

            /// <summary>
            /// Gets or sets the total number of requisitions available.
            /// This property is used for pagination purposes.
            /// </summary>
            public int TotalCount { get; set; }

            /// <summary>
            /// Gets or sets the list of purchasing requisitions in the queue.
            /// </summary>
            public List<PurchasingRequisitionDTO> Requisitions { get; set; }
        }
}
