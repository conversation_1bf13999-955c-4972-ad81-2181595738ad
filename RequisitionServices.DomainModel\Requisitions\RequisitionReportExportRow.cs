﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{

    [NotMapped]
    public class RequisitionReportExportRow
    {
        public int RequisitionId { get; set; }

        public string RequisitionerId { get; set; }

        public int RequisitionTypeId { get; set; }

        public DateTime RequisitionCreateDate { get; set; }

        public int RequisitionStatusTypeId { get; set; }

        public string RequisitionLocationIdentifier { get; set; }

        public bool RequisitionIsMobile { get; set; }

        public bool RequisitionIsVendor { get; set; }

        public string RequisitionerFirstName { get; set; }

        public string RequisitionerLastName { get; set; }

        public int? RequisitionItemId { get; set; }

        public string RequisitionItemNumber { get; set; }

        public string RequisitionItemUomCode { get; set; }

        public int? RequisitionItemPoNumber { get; set; }

        public decimal? RequisitionItemUnitCost { get; set; }

        public int? RequisitionItemVendorId { get; set; }

        public string RequisitionItemVendorName { get; set; }

        public string RequisitionItemDescription { get; set; }

        public string RequisitionItemParIdentifier { get; set; }

        public string RequisitionItemReorderNumber { get; set; }

        public string RequisitionItemParentSystemId { get; set; }

        public decimal? Discount { get; set; }

        public string RequisitionItemOriginalParentSystemId { get; set; }

        public int? RequisitionItemQuantityOrdered { get; set; }

        public int? RequisitionItemSmartItemNumber { get; set; }

        public string RequisitionItemGeneralLedgerCode { get; set; }

        public int? RequisitionItemParentItemId { get; set; }

        public string SprDetailsUomCode { get; set; }

        public int? SprDetailsVendorId { get; set; }

        public string SprDetailsVendorName { get; set; }

        public string SprDetailsPartNumber { get; set; }

        public string SprDetailsDescription { get; set; }

        public decimal? SprDetailsTradeInValue { get; set; }

        public string SprDetailsBudgetNumber { get; set; }

        public string SprDetailsGeneralLedgerCode { get; set; }

        public decimal? SprDetailsEstimatedUnitPrice { get; set; }

        public int? RequisitionItemStatusTypeId { get; set; } 

        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Id for the badge log from the Requisitions table
        /// </summary>
        public int? BadgeLogId { get; set; }

        /// <summary>
        /// Indicates if the badge in exists
        /// </summary>
        public int? BadgeInStatusId { get; set; }
    }
}
