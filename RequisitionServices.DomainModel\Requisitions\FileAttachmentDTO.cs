﻿using System;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class FileAttachmentDTO
    {
        public FileAttachmentDTO() { }

        public FileAttachmentDTO(FileAttachment fileAttachment)
        {
            this.Id = fileAttachment.Id;
            this.RequisitionItemId = fileAttachment.RequisitionItemId;
            this.RequisitionId = fileAttachment.RequisitionId;
            this.FileName = fileAttachment.FileName;
            this.FilePath = fileAttachment.FilePath;
            this.CreatedBy = fileAttachment.CreatedBy;
            this.CreateDate = fileAttachment.CreateDate;
        }

        public int Id { get; set; }

        public int RequisitionItemId { get; set; }

        public int RequisitionId { get; set; }
        
        public string FilePath { get; set; }
        
        public string FileName { get; set; }
        

        public string CreatedBy { get; set; }

        public DateTime CreateDate { get; set; }
    }
}
