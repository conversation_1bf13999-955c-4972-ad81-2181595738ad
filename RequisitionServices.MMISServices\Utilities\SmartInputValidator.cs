﻿using System;
using RequisitionServices.Utility.Domain;

namespace RequisitionServices.MMISServices.Utilities
{
    public static class SmartInputValidator
    {
        public static void CheckUserName(ref string userName)
        {
            if (string.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }
        }

        public static int CheckCoid(string coid)
        {
            if (!int.TryParse(coid, out var coidInt))
            {
                throw new ArgumentException($"Smart does not support non-numeric characters for COID. COID = {coid}");
            }

            return coidInt;
        }

        public static void CheckItemId(string itemNumber)
        {
            if (!int.TryParse(itemNumber, out _))
            {
                throw new ArgumentException($"Smart does not support non-numeric characters for item number. Item number = {itemNumber}");
            }
        }

        public static void CheckCountryCode(string smartCountryCode)
        {
            if (!LocationMapper.CountryCodeIsRecognized(smartCountryCode))
            {
                throw new ArgumentException($"Invalid Country Code.  Smart does not recognize code {smartCountryCode}");
            }
        }
    }
}
