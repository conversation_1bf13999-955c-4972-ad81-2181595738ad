﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7C18DF69-7090-4CD5-944A-D9ED0B2DEBD0}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RequisitionServices.DomainServices</RootNamespace>
    <AssemblyName>RequisitionServices.DomainServices</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\QA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <OutputPath>bin\Production\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <OutputPath>bin\Sandbox\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <OutputPath>bin\QASandbox\</OutputPath>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AutoMapper, Version=10.0.0.0, Culture=neutral, PublicKeyToken=be96cd2c38ef1005, processorArchitecture=MSIL">
      <HintPath>..\packages\AutoMapper.10.1.1\lib\net461\AutoMapper.dll</HintPath>
    </Reference>
    <Reference Include="Castle.Core, Version=4.0.0.0, Culture=neutral, PublicKeyToken=407dd0808d44fbdc, processorArchitecture=MSIL">
      <HintPath>..\packages\Castle.Core.4.4.1\lib\net45\Castle.Core.dll</HintPath>
    </Reference>
    <Reference Include="Evolve, Version=2.4.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Evolve.2.4.0\lib\net461\Evolve.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.WebApi.Versioning, Version=4.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Versioning.4.0.0\lib\net45\Microsoft.AspNet.WebApi.Versioning.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.1.1.1\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.3.1.8\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Http, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Http.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=3.1.8.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.3.1.8\lib\netstandard2.0\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Smart.Core.Common, Version=3.4.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Smart.Core.Common.3.4.1\lib\netstandard2.0\Smart.Core.Common.dll</HintPath>
    </Reference>
    <Reference Include="Smart.Core.Contracts, Version=1.2.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Smart.Core.Contracts.1.2.1\lib\netstandard2.0\Smart.Core.Contracts.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.4.0\lib\netstandard2.0\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.4.7.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Memory, Version=4.0.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.2\lib\netstandard2.0\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.4.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.7.1\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BillOnlyReviewService.cs" />
    <Compile Include="CartService.cs" />
    <Compile Include="CensorService.cs" />
    <Compile Include="AuditService.cs" />
    <Compile Include="DigitalSignOffService.cs" />
    <Compile Include="COIDService.cs" />
    <Compile Include="CommentService.cs" />
    <Compile Include="ConfigurationService.cs" />
    <Compile Include="ContractService.cs" />
    <Compile Include="EmailService.cs" />
    <Compile Include="FacilityWorkflowService.cs" />
    <Compile Include="Interface\IBillOnlyReviewService.cs" />
    <Compile Include="Interface\IDigitalSignOffService.cs" />
    <Compile Include="IINItemService.cs" />
    <Compile Include="Interface\ICartService.cs" />
    <Compile Include="Interface\ICensorService.cs" />
    <Compile Include="Interface\IAuditService.cs" />
    <Compile Include="Interface\ICOIDService.cs" />
    <Compile Include="Interface\ICommentService.cs" />
    <Compile Include="Interface\IConfigurationService.cs" />
    <Compile Include="Interface\IContractService.cs" />
    <Compile Include="Interface\IEmailService.cs" />
    <Compile Include="Interface\IFacilityWorkflowService.cs" />
    <Compile Include="Interface\IIINItemService.cs" />
    <Compile Include="Interface\IItemInfoService.cs" />
    <Compile Include="Interface\IPOService.cs" />
    <Compile Include="Interface\ISystemNotificationService.cs" />
    <Compile Include="Interface\IUserAlertMessageService.cs" />
    <Compile Include="Interface\IVendorService.cs" />
    <Compile Include="Interface\IViraService.cs" />
    <Compile Include="Interface\IVProService.cs" />
    <Compile Include="ItemInfoService.cs" />
    <Compile Include="MappingProfile.cs" />
    <Compile Include="POService.cs" />
    <Compile Include="Service References\NonRushWorkflowSvc\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\RushWorkflowSvc\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Service References\VendorWorkflowSvc\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="SystemNotificationService.cs" />
    <Compile Include="UserAlertMessageService.cs" />
    <Compile Include="VendorService.cs" />
    <Compile Include="ViraService.cs" />
    <Compile Include="VProService.cs" />
    <Compile Include="WorkflowService.cs" />
    <Compile Include="ClinicalDataService.cs" />
    <Compile Include="Interface\IWorkflowService.cs" />
    <Compile Include="Interface\IClinicalDataService.cs" />
    <Compile Include="Interface\IItemService.cs" />
    <Compile Include="Interface\IParService.cs" />
    <Compile Include="Interface\IRequisitionService.cs" />
    <Compile Include="Interface\IUserService.cs" />
    <Compile Include="ItemService.cs" />
    <Compile Include="Interface\ILocationService.cs" />
    <Compile Include="ParService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="RequisitionService.cs" />
    <Compile Include="LocationService.cs" />
    <Compile Include="UserService.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RequisitionServices.DomainModel\RequisitionServices.DomainModel.csproj">
      <Project>{6ebe285e-ac6c-4a2a-9aa6-0a906b7ba723}</Project>
      <Name>RequisitionServices.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.MMISServices\RequisitionServices.MMISServices.csproj">
      <Project>{706343dc-ab56-4a18-a4fc-cb852c6ca64c}</Project>
      <Name>RequisitionServices.MMISServices</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Repositories\RequisitionServices.Repositories.csproj">
      <Project>{79f7e2ee-74bc-4854-b2d2-e9b5035ac1ee}</Project>
      <Name>RequisitionServices.Repositories</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Utility\RequisitionServices.Utility.csproj">
      <Project>{6a4f4b1c-f81d-45ff-8b83-b693a6dba39d}</Project>
      <Name>RequisitionServices.Utility</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Service References\NonRushWorkflowSvc\ApprovalWorkflowService1.wsdl" />
    <None Include="Service References\NonRushWorkflowSvc\ApprovalWorkflowService11.wsdl" />
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService1.wsdl" />
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService11.wsdl" />
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService31.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService32.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService33.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService34.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService35.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\RequisitionServices.DomainServices.NonRushWorkflowSvc.Requisition.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\NonRushWorkflowSvc\RequisitionServices.DomainServices.NonRushWorkflowSvc.StartNonRushApprovalResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RushWorkflowSvc\ApprovalWorkflowService1.wsdl" />
    <None Include="Service References\RushWorkflowSvc\ApprovalWorkflowService11.wsdl" />
    <None Include="Service References\RushWorkflowSvc\RequisitionServices.DomainServices.RushWorkflowSvc.Requisition.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RushWorkflowSvc\RequisitionServices.DomainServices.RushWorkflowSvc.StartRushApprovalResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService1.wsdl" />
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService11.wsdl" />
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService31.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService32.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService33.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService34.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService35.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\VendorWorkflowSvc\ApprovalWorkflowService.wsdl" />
    <None Include="Service References\VendorWorkflowSvc\RequisitionServices.DomainServices.VendorWorkflowSvc.Requisition.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\VendorWorkflowSvc\RequisitionServices.DomainServices.VendorWorkflowSvc.StartVendorApprovalResponse.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Service References\VendorWorkflowSvc\VendorWorkflowService.wsdl" />
    <None Include="Service References\VendorWorkflowSvc\VendorWorkflowService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\VendorWorkflowSvc\VendorWorkflowService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Service References\VendorWorkflowSvc\VendorWorkflowService2.xsd">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Service References\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Service References\NonRushWorkflowSvc\" />
    <WCFMetadataStorage Include="Service References\RushWorkflowSvc\" />
    <WCFMetadataStorage Include="Service References\VendorWorkflowSvc\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RushWorkflowSvc\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RushWorkflowSvc\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RushWorkflowSvc\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NonRushWorkflowSvc\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NonRushWorkflowSvc\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NonRushWorkflowSvc\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService11.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\NonRushWorkflowSvc\NonRushWorkflowService1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\RushWorkflowSvc\RushWorkflowService11.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\VendorWorkflowSvc\VendorWorkflowService.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\VendorWorkflowSvc\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\VendorWorkflowSvc\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Service References\VendorWorkflowSvc\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>