﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6EBE285E-AC6C-4A2A-9AA6-0A906B7BA723}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RequisitionServices.DomainModel</RootNamespace>
    <AssemblyName>RequisitionServices.DomainModel</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\QA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <OutputPath>bin\Production\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <OutputPath>bin\Sandbox\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <OutputPath>bin\QASandbox\</OutputPath>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json.Schema, Version=3.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.Schema.3.0.10\lib\net45\Newtonsoft.Json.Schema.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Audit\AuditEvents.cs" />
    <Compile Include="Audit\AuditEventsDTO.cs" />
    <Compile Include="Audit\AuditEventsChange.cs" />
    <Compile Include="Audit\AuditEventTypes.cs" />
    <Compile Include="Audit\ChangedProperties.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReviewItemDTO.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReviewPrintRequest.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReviewRequisition.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReviewRequisitionWithDetailsDTO.cs" />
    <Compile Include="Cart\Entities\Item_RFID.cs" />
    <Compile Include="Cart\Requests\CartItemExistsRequest.cs" />
    <Compile Include="Cart\Responses\AddToCartResponse.cs" />
    <Compile Include="Cart\Requests\CartAddItemRequest.cs" />
    <Compile Include="Cart\Requests\CartDeleteItemRequest.cs" />
    <Compile Include="Cart\Requests\CartRequest.cs" />
    <Compile Include="Cart\Requests\CartSubmitRequest.cs" />
    <Compile Include="Cart\Requests\CartUpdateItemsRequest.cs" />
    <Compile Include="Cart\Requests\ItemRequest.cs" />
    <Compile Include="Cart\Entities\Cart.cs" />
    <Compile Include="Cart\Entities\CartItem.cs" />
    <Compile Include="Cart\Models\CartItemDetails.cs" />
    <Compile Include="Cart\Responses\CartAttributesResponse.cs" />
    <Compile Include="BillOnlyReview\PaginatedBORDTO.cs" />
    <Compile Include="Clinical\Patient.cs" />
    <Compile Include="Clinical\Provider.cs" />
    <Compile Include="Comments\UnreadComment.cs" />
    <Compile Include="Constants\AppInsightsRequestFilterKeywords.cs" />
    <Compile Include="Constants\EmailConstants.cs" />
    <Compile Include="Constants\CompanyConstants.cs" />
    <Compile Include="Constants\EmailImages.cs" />
    <Compile Include="Constants\Names.cs" />
    <Compile Include="Constants\UserProfileRoleNames.cs" />
    <Compile Include="Constants\Values.cs" />
    <Compile Include="Contracts\Contract.cs" />
    <Compile Include="Contracts\ContractPageRow.cs" />
    <Compile Include="Contracts\ContractReportResults.cs" />
    <Compile Include="DigitalSignOff\ApproverDigitalSignOffDTO.cs" />
    <Compile Include="DigitalSignOff\DigitalSignOffUser.cs" />
    <Compile Include="Email\EmailModel.cs" />
    <Compile Include="Email\EmailRequest.cs" />
    <Compile Include="Enum\AddToCartStatus.cs" />
    <Compile Include="Enum\AppPartType.cs" />
    <Compile Include="Enum\AuditEventTypeEnum.cs" />
    <Compile Include="Enum\CartType.cs" />
    <Compile Include="Enum\ChangeStatus.cs" />
    <Compile Include="Enum\EntityTypes.cs" />
    <Compile Include="Enum\FacilityNotificationTypeEnum.cs" />
    <Compile Include="Enum\OrganizationalLevelHierarchyType.cs" />
    <Compile Include="Enum\ParentSystemType.cs" />
    <Compile Include="Enum\EmailTypeEnum.cs" />
    <Compile Include="Enum\ParType.cs" />
    <Compile Include="Enum\RequisitionItemStatusTypeEnum.cs" />
    <Compile Include="Enum\RequisitionSortOrder.cs" />
    <Compile Include="Enum\RequisitionTypeEnum.cs" />
    <Compile Include="Enum\RequisitionStatusTypeEnum.cs" />
    <Compile Include="Enum\SPRTypeEnum.cs" />
    <Compile Include="Enum\WorkflowTypeEnum.cs" />
    <Compile Include="Contracts\ContractDetails.cs" />
    <Compile Include="FacilityWorkflow\FacilityWorkflowDTO.cs" />
    <Compile Include="FacilityWorkflow\FacilityWorkflowStep.cs" />
    <Compile Include="FacilityWorkflow\SaveFacilityWorkflowDTO.cs" />
    <Compile Include="ItemInfo\ItemInfoModel.cs" />
    <Compile Include="ItemInfo\UnitOfMeasure.cs" />
    <Compile Include="Items\AlternateUOM.cs" />
    <Compile Include="Items\AlternateUOMRecordModel.cs" />
    <Compile Include="Items\DistributionPoint.cs" />
    <Compile Include="Items\ItemParDetailsModel.cs" />
    <Compile Include="Items\ItemParModel.cs" />
    <Compile Include="Items\ItemType.cs" />
    <Compile Include="Items\ItemPriceDetails.cs" />
    <Compile Include="Items\UnitOfMeasureModel.cs" />
    <Compile Include="Items\UnitOfMeasureType.cs" />
    <Compile Include="Requisitions\AddCommentDTO.cs" />
    <Compile Include="Comments\Comment.cs" />
    <Compile Include="Comments\CommentDTO.cs" />
    <Compile Include="Comments\CommentNotificationRequisition.cs" />
    <Compile Include="Comments\CommentNotificationRequisitionsDTO.cs" />
    <Compile Include="Comments\CommentNotificationRequisitionRow.cs" />
    <Compile Include="Comments\CommentsResponse.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReview.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReviewDTO.cs" />
    <Compile Include="BillOnlyReview\BillOnlyReviewRequest.cs" />
    <Compile Include="Requisitions\PurchasingRequisitionItemsDTO.cs" />
    <Compile Include="Requisitions\PurchasingRequisitionQueDTO.cs" />
    <Compile Include="Requisitions\PurchasingRequisitionReportResultsDTO.cs" />
    <Compile Include="Requisitions\PurchasingRequisitionsDTO.cs" />
    <Compile Include="Requisitions\ReqTypeModel.cs" />
    <Compile Include="Requisitions\RequisitionDigitalSignOff.cs" />
    <Compile Include="Requisitions\RequisitionDigitalSignOffDTO.cs" />
    <Compile Include="DigitalSignOff\DigitalSignOffResponse.cs" />
    <Compile Include="DigitalSignOff\ActiveDirectoryValidation.cs" />
    <Compile Include="Requisitions\RequisitionForVendorUserRequestDto.cs" />
    <Compile Include="Requisitions\RequisitionPurchasingAdvancedFilterRequest.cs" />
    <Compile Include="Requisitions\RequisitionPurchasingAdvancedFiltersDto.cs" />
    <Compile Include="Requisitions\RequisitionPurchasingReportItemDTO.cs" />
    <Compile Include="Requisitions\RequisitionPurchasingReportPageRow.cs" />
    <Compile Include="Requisitions\RequisitionSubmissionTypes.cs" />
    <Compile Include="Requisitions\RequisitionListMultiRequestDto.cs" />
    <Compile Include="Requisitions\ApprovalListMultiResultsDTO.cs" />
    <Compile Include="Requisitions\RequisitionListMultiResultsDTO.cs" />
    <Compile Include="Requisitions\RequisitionListRequestDto.cs" />
    <Compile Include="Requisitions\RequisitionReportRequestDto.cs" />
    <Compile Include="Requisitions\RequisitionPurchasingReportRequestDto.cs" />
    <Compile Include="Requisitions\VboHoldItemConversion.cs" />
    <Compile Include="Requisitions\VboHoldItemConversionDto.cs" />
    <Compile Include="Search\FilterCriteria.cs" />
    <Compile Include="Items\FStoreDeptModel.cs" />
    <Compile Include="Items\GetParItemsWithLastOrderedInfoDTO.cs" />
    <Compile Include="Items\IINItemRecordModel.cs" />
    <Compile Include="Items\ItemDetailsDTO.cs" />
    <Compile Include="Search\ItemSearchCriteria.cs" />
    <Compile Include="Items\LastOrderDetailsDTO.cs" />
    <Compile Include="Items\LastOrderedPageRow.cs" />
    <Compile Include="Items\LotSerialPair.cs" />
    <Compile Include="Items\ParItemDetails.cs" />
    <Compile Include="Items\ParItemInfo.cs" />
    <Compile Include="Items\ParItemWithLastOrdered.cs" />
    <Compile Include="Locations\Locator.cs" />
    <Compile Include="Locations\LocatorErrors.cs" />
    <Compile Include="Locations\LocatorLine.cs" />
    <Compile Include="PurchaseOrders\POHistory.cs" />
    <Compile Include="PurchaseOrders\POInvoice.cs" />
    <Compile Include="PurchaseOrders\POLists.cs" />
    <Compile Include="PurchaseOrders\POOptions.cs" />
    <Compile Include="Requisitions\ApprovalHistoryDTO.cs" />
    <Compile Include="Requisitions\ApprovalIdKey.cs" />
    <Compile Include="Requisitions\ApprovalHistoryListResultsDTO.cs" />
    <Compile Include="Requisitions\ApprovalListResultsDTO.cs" />
    <Compile Include="Requisitions\ApprovalHistoryPageRow.cs" />
    <Compile Include="Requisitions\FileAttachment.cs" />
    <Compile Include="Items\Item.cs" />
    <Compile Include="Items\ParItem.cs" />
    <Compile Include="Items\Par.cs" />
    <Compile Include="Items\UOM.cs" />
    <Compile Include="Locations\Address.cs" />
    <Compile Include="Locations\Facility.cs" />
    <Compile Include="Locations\FacilityNotification.cs" />
    <Compile Include="Locations\FacilityNotificationType.cs" />
    <Compile Include="Locations\GLAccount.cs" />
    <Compile Include="Locations\Department.cs" />
    <Compile Include="Locations\Location.cs" />
    <Compile Include="Locations\SpanOfControl.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PurchaseOrders\POConfirmationDetail.cs" />
    <Compile Include="PurchaseOrders\POLineItem.cs" />
    <Compile Include="PurchaseOrders\POHeader.cs" />
    <Compile Include="PurchaseOrders\PODetails.cs" />
    <Compile Include="PurchaseOrders\POVendor.cs" />
    <Compile Include="Requisitions\AdhocReview.cs" />
    <Compile Include="Requisitions\AdhocReviewDTO.cs" />
    <Compile Include="Requisitions\ApprovalDTO.cs" />
    <Compile Include="Requisitions\BaseRequisitionItemStatusDTO.cs" />
    <Compile Include="Requisitions\ClinicalUseDetail.cs" />
    <Compile Include="Requisitions\FileAttachmentDTO.cs" />
    <Compile Include="Requisitions\FileNamesDTO.cs" />
    <Compile Include="Requisitions\InFlightQty.cs" />
    <Compile Include="Requisitions\ItemInventory.cs" />
    <Compile Include="Requisitions\LegacyRequisitionReportDTO.cs" />
    <Compile Include="Requisitions\LegacyRequisitionReportRequestDTO.cs" />
    <Compile Include="Requisitions\ReqRequisitionItemStatusDTO.cs" />
    <Compile Include="Requisitions\Approval.cs" />
    <Compile Include="Requisitions\RequisitionForExportDTO.cs" />
    <Compile Include="Requisitions\RequisitionForExport.cs" />
    <Compile Include="Requisitions\RequisitionAdvanceDTO.cs" />
    <Compile Include="Requisitions\RequisitionDashboard.cs" />
    <Compile Include="Requisitions\RequisitionItemStatusUpdateDTO.cs" />
    <Compile Include="Requisitions\RequisitionItemWithDetailsDTO.cs" />
    <Compile Include="Requisitions\DeliveryMethodType.cs" />
    <Compile Include="Requisitions\ApprovalPageResults.cs" />
    <Compile Include="Requisitions\RequisitionItemWithSubItemDTO.cs" />
    <Compile Include="Requisitions\RequisitionReportExportResults.cs" />
    <Compile Include="Requisitions\RequisitionReportExportResultsDTO.cs" />
    <Compile Include="Requisitions\RequisitionListResultsDTO.cs" />
    <Compile Include="Requisitions\RequisitionReportResults.cs" />
    <Compile Include="Requisitions\ApprovalPageRow.cs" />
    <Compile Include="Requisitions\RequisitionReportExportRow.cs" />
    <Compile Include="Requisitions\RequisitionReportPageRow.cs" />
    <Compile Include="Requisitions\RequisitionPendingReview.cs" />
    <Compile Include="Requisitions\RequisitionPageRow.cs" />
    <Compile Include="Requisitions\RequisitionType.cs" />
    <Compile Include="Requisitions\RequisitionWithDetailsDTO.cs" />
    <Compile Include="Requisitions\RequisitionDTO.cs" />
    <Compile Include="Requisitions\Requisition.cs" />
    <Compile Include="Requisitions\RequisitionItemDTO.cs" />
    <Compile Include="Requisitions\RequisitionItem.cs" />
    <Compile Include="Requisitions\RequisitionItemStatusHistory.cs" />
    <Compile Include="Requisitions\RequisitionStatusHistoryDTO.cs" />
    <Compile Include="Requisitions\RequisitionStatusHistory.cs" />
    <Compile Include="Requisitions\RequisitionItemStatusType.cs" />
    <Compile Include="Requisitions\RequisitionStatusType.cs" />
    <Compile Include="Requisitions\SmartItemRequisitionIdDTO.cs" />
    <Compile Include="Requisitions\SPRDetailDTO.cs" />
    <Compile Include="Requisitions\SPRDetail.cs" />
    <Compile Include="Requisitions\SprRequisitionItemStatusDTO.cs" />
    <Compile Include="Requisitions\SPRType.cs" />
    <Compile Include="Requisitions\StatusUpdateDTO.cs" />
    <Compile Include="Requisitions\UpcomingApprovalsRepoDTO.cs" />
    <Compile Include="Requisitions\UpdateRequisitionItemStatusResultDTO.cs" />
    <Compile Include="SystemNotifications\SystemNotifcationTabInfoDTO.cs" />
    <Compile Include="SystemNotifications\SystemNotification.cs" />
    <Compile Include="SystemNotifications\SystemNotificationAdminDTO.cs" />
    <Compile Include="SystemNotifications\SystemNotificationAuthorizedUser.cs" />
    <Compile Include="UserAlertMessage\UserAlertMessageRequest.cs" />
    <Compile Include="UserAlertMessage\UserAlertMessageType.cs" />
    <Compile Include="UserAlertMessage\UserAlertMessage.cs" />
    <Compile Include="Users\ActiveApproversDto.cs" />
    <Compile Include="Users\AppPart.cs" />
    <Compile Include="Users\Approver.cs" />
    <Compile Include="Users\ApproverWorkflowDto.cs" />
    <Compile Include="Users\BulkApproverJobStatusDTO.cs" />
    <Compile Include="Users\BulkApproverJobTracker.cs" />
    <Compile Include="Users\BuyerModel.cs" />
    <Compile Include="Users\GetBulkJobDetailsDTO.cs" />
    <Compile Include="Users\NamesDTO.cs" />
    <Compile Include="Users\Personalization.cs" />
    <Compile Include="Users\PersonalizationDTO.cs" />
    <Compile Include="Users\Profile.cs" />
    <Compile Include="Users\SaveBulkApproverJobTrackerDTO.cs" />
    <Compile Include="Users\SaveUserEditInfoDTO.cs" />
    <Compile Include="Users\SaveWorkflowsDTO.cs" />
    <Compile Include="Users\UserEditDTO.cs" />
    <Compile Include="Users\UserReportAndEditDBInfo.cs" />
    <Compile Include="Users\UserReportInfoDTO.cs" />
    <Compile Include="Users\UserReportInfoRequestDTO.cs" />
    <Compile Include="Users\UserReportProfileDTO.cs" />
    <Compile Include="Users\UserSetupWorkflows.cs" />
    <Compile Include="Users\UserProfile.cs" />
    <Compile Include="Users\UserRequestDTO.cs" />
    <Compile Include="Users\ApproverUpdateDTO.cs" />
    <Compile Include="Users\User.cs" />
    <Compile Include="Users\UserRole.cs" />
    <Compile Include="Users\UserWorkflowDTO.cs" />
    <Compile Include="Users\UserWorkflowStep.cs" />
    <Compile Include="Users\ValidateUserWorkflowsRequestDTO.cs" />
    <Compile Include="Users\ValidationOfUserWorkflowsDTO.cs" />
    <Compile Include="Users\WorkflowExportDTO.cs" />
    <Compile Include="Users\WorkflowExportInputDTO.CS" />
    <Compile Include="Users\WorkflowRequestValidationDTO.cs" />
    <Compile Include="Users\WorkflowType.cs" />
    <Compile Include="Users\WorkflowValidationDTO.cs" />
    <Compile Include="Vendors\OrderTimes.cs" />
    <Compile Include="Vendors\PunchOutVendor.cs" />
    <Compile Include="Vendors\Vendor.cs" />
    <Compile Include="Vendors\VendorDetails.cs" />
    <Compile Include="Vendors\VendorHeaderInfo.cs" />
    <Compile Include="Vendors\VendorModel.cs" />
    <Compile Include="Vira\ViraItemStatus.cs" />
    <Compile Include="VPro\VProBadgeLogDTO.cs" />
    <Compile Include="VPro\RequisitionVProBadgeLog.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>