﻿using RequisitionServices.DomainModel.Users;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.SystemNotifications
{
    public class SystemNotificationAuthorizedUser
    {
        public SystemNotificationAuthorizedUser() { }

        public SystemNotificationAuthorizedUser(SystemNotificationAdminDTO systemNotificationAdminDTO)
        {
            if (systemNotificationAdminDTO != null)
            {
                this.Id = systemNotificationAdminDTO.AdminId;
                this.UserId = systemNotificationAdminDTO.UserId;
            }
        }

        public int Id { get; set; }

        public int UserId { get; set; }

        [ForeignKey("UserId")]
        public virtual User User { get; set; }
    }
}
