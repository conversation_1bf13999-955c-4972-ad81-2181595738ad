﻿USE [eProcurementQA]
GO
/****** Object:  StoredProcedure [dbo].[usp_RequisitionsReportExportGet]    Script Date: 2/15/2022 12:53:47 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportExportGet
Purpose     : Returns a non-paginated list of requisitions for the Requisition Report export button.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 11-02-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/02/2017		Script created
Peter Hurlburt		11/03/2017		Version 1.0 submitted for deployment 22
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
Peter Hurlburt		11/09/2017		Changed date filters to DateTime objects from Date
Peter Hurlburt		11/13/2017		Removed Deleted requisitions from result set
									Resubmitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Jonathan Moosekian	08/13/2020		Adding IsMobile column
Jonathan Moosekian	08/19/2020		Adding multi-department selection
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Change @departmentIds to use general IdTemplate Table Type
Aditya Joshi		10/17/2020		Added [RequisitionItemStatusTypeId] to get the item status in export 
Julio Pozo      02/16/2022  Added parameter to limit max number of requisitions returned

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@maxExportedRequisitionCount int = 1000
AS
BEGIN

SELECT  
	[Req].[RequisitionId]				AS [RequisitionId],
	[Req].[CreatedBy]					AS [RequisitionerId],
	[Req].[RequisitionTypeId]			AS [RequisitionTypeId],
	[Req].[CreateDate]					AS [RequisitionCreateDate],
	[Req].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
	[Req].[IsMobile]					AS [RequisitionIsMobile],
	[User].[FirstName]					AS [RequisitionerFirstName],
	[User].[LastName]					AS [RequisitionerLastName],
	[ReqItem].[Id]						AS [RequisitionItemId],
	[ReqItem].[ItemId]					AS [RequisitionItemNumber],
	[ReqItem].[UOMCode]					AS [RequisitionItemUomCode],
	[ReqItem].[PONumber]				AS [RequisitionItemPONumber],
	[ReqItem].[UnitCost]				AS [RequisitionItemUnitCost],
	[ReqItem].[VendorId]				AS [RequisitionItemVendorId],
	[ReqItem].[VendorName]				AS [RequisitionItemVendorName],
	[ReqItem].[ItemDescription]			AS [RequisitionItemDescription],
	[ReqItem].[ParIdentifier]			AS [RequisitionItemParIdentifier],
	[ReqItem].[ReOrder]					AS [RequisitionItemReorderNumber],
	[ReqItem].[ParentSystemId]			AS [RequisitionItemParentSystemId],
	[ReqItem].[OriginalParentSystemId]	AS [RequisitionItemOriginalParentSystemId],
	[ReqItem].[QuantityToOrder]			AS [RequisitionItemQuantityOrdered],
	[ReqItem].[SmartItemNumber]			AS [RequisitionItemSmartItemNumber],
	[ReqItem].[GeneralLedgerCode]		AS [RequisitionItemGeneralLedgerCode],
	[ReqItem].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
	[ReqItem].[Discount]				AS [Discount],
	[ReqItem].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
	[SprDetails].[UOMCode]				AS [SprDetailsUomCode],
	[SprDetails].[VendorId]				AS [SprDetailsVendorId],
	[SprDetails].[VendorName]			AS [SprDetailsVendorName],
	[SprDetails].[PartNumber]			AS [SprDetailsPartNumber],
	[SprDetails].[ItemDescription]		AS [SprDetailsDescription],
	[SprDetails].[TradeInValue]			AS [SprDetailsTradeInValue],
	[SprDetails].[BudgetNumber]			AS [SprDetailsBudgetNumber],
	[SprDetails].[GeneralLedgerCode]	AS [SprDetailsGeneralLedgerCode],
	[SprDetails].[EstimatedPrice]		AS [SprDetailsEstimatedUnitPrice],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
FROM
(
	SELECT TOP (@maxExportedRequisitionCount)
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
	END)										AS [ReqTypeGroupingOrder],
	[Requisition].[RequisitionId]				AS [RequisitionId],
	[Requisition].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Requisition].[LocationIdentifier]			AS [LocationIdentifier],
	[Requisition].[IsMobile]					AS [IsMobile],
	[Requisition].[Comments]					AS [Comments],
	[Requisition].[CreatedBy]					AS [CreatedBy],
	[Requisition].[CreateDate]					AS [CreateDate],
	[Requisition].[RequisitionTypeId]			AS [RequisitionTypeId]
	FROM [dbo].[Requisitions]					AS [Requisition] WITH (NOLOCK)
	WHERE
	1 <> [Requisition].[RequisitionStatusTypeId]
	AND 5 <> [Requisition].[RequisitionStatusTypeId]
	AND 8 <> [Requisition].[RequisitionStatusTypeId]
	AND 12 <> [Requisition].[RequisitionStatusTypeId]
    AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
	AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
	AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate

	ORDER BY

		CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
		END,

		CASE @oldestFirst WHEN 0 THEN [Requisition].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Requisition].[CreateDate] END ASC


) AS [Req]

LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%')
ORDER BY
	[ConditionalStatusSorting] 

OPTION (RECOMPILE)

END