﻿namespace RequisitionServices.DomainModel.Locations
{
    public class Facility
    {
        public Facility() { }
        public Facility(Location location)
        {
            this.COID = location.Id;
            this.Name = location.Description;
            this.ParentCOID = location.ParentId;
            this.ParentName = location.ParentDescription;
            this.IsDefault = location.IsDefault;
            this.CompanyCode = !string.IsNullOrWhiteSpace(location.CompanyCode) ? location.CompanyCode : "H";
        }

        public string GPOID { get; set; }
        public string COID { get; set; }
        public string Name { get; set; }
        public string ParentCOID { get; set; }
        public string ParentName { get; set; }
        public bool Active { get; set; }
        public int Organization { get; set; }
        public Address Address { get; set; }
        public bool IsDefault { get; set; }
        public string CompanyCode { get; set; } //Code from SMART
        public string CompanyName { get; set; }
        public int CompanyId { get; set; } //Id from PASS
    }
}
