﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_INonRushApprovalWorkflow1&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_INonRushApprovalWorkflow1" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpsBinding_INonRushApprovalWorkflow&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="BasicHttpsBinding_INonRushApprovalWorkflow" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_INonRushApprovalWorkflow1&quot; contract=&quot;NonRushWorkflowSvc.INonRushApprovalWorkflow&quot; name=&quot;BasicHttpBinding_INonRushApprovalWorkflow1&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_INonRushApprovalWorkflow1&quot; contract=&quot;NonRushWorkflowSvc.INonRushApprovalWorkflow&quot; name=&quot;BasicHttpBinding_INonRushApprovalWorkflow1&quot; /&gt;" contractName="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpBinding_INonRushApprovalWorkflow1" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_INonRushApprovalWorkflow&quot; contract=&quot;NonRushWorkflowSvc.INonRushApprovalWorkflow&quot; name=&quot;BasicHttpsBinding_INonRushApprovalWorkflow&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_INonRushApprovalWorkflow&quot; contract=&quot;NonRushWorkflowSvc.INonRushApprovalWorkflow&quot; name=&quot;BasicHttpsBinding_INonRushApprovalWorkflow&quot; /&gt;" contractName="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpsBinding_INonRushApprovalWorkflow" />
  </endpoints>
</configurationSnapshot>