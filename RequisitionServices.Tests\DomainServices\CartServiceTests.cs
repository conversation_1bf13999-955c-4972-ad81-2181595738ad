﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using Microsoft.Web.Http;
using Moq;
using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Cart.Responses;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Configuration;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class CartServiceTests
    {
        static Mock<IItemService> _mockItemService;
        static Mock<IRequisitionService> _mockRequisitionService;
        static Mock<ICartRepository> _mockCartRepository;
        static Mock<IRequisitionRepository> _mockRequisitionRepository;
        static Mock<ISmartCOIDService> _mockSmartCoidService;

        static CartService _cartService;

        [ClassInitialize]
        public static void ClassInit(TestContext context)
        {
            _mockItemService = new Mock<IItemService>();
            _mockRequisitionService = new Mock<IRequisitionService>();
            _mockCartRepository = new Mock<ICartRepository>();
            _mockRequisitionRepository = new Mock<IRequisitionRepository>();
            _mockSmartCoidService = new Mock<ISmartCOIDService>();
        }

        [TestInitialize]
        public void Initialize()
        {
            _cartService = new CartService(_mockItemService.Object, _mockRequisitionService.Object, _mockCartRepository.Object, _mockRequisitionRepository.Object, _mockSmartCoidService.Object);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _cartService = null;
            _mockItemService.Reset();
            _mockRequisitionService.Reset();
            _mockCartRepository.Reset();
            _mockRequisitionRepository.Reset();
            _mockSmartCoidService.Reset();
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            _mockItemService = null;
            _mockRequisitionService = null;
            _mockCartRepository = null;
            _mockRequisitionRepository = null;
            _mockSmartCoidService = null;
        }

        [TestMethod]
        public void GetCart_When_Cart_Exists()
        {
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>())).Returns(new Cart());

            _cartService.GetCart(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Never);
        }

        [TestMethod]
        public void GetCart_Creates_Cart_When_Cart_Does_Not_Exists()
        {
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>())).Returns((Cart)null);
            _mockCartRepository.Setup(x => x.AddCart(It.IsAny<CartRequest>())).Returns(new Cart());

            _cartService.GetCart(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Once);
        }

        [TestMethod]
        public void GetAttributes_Returns_Null_If_Cart_Does_Not_Exist()
        {
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>())).Returns((Cart)null);

            var response = _cartService.GetAttributes(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            Assert.IsNull(response);
        }

        [TestMethod]
        public void GetAttributes_Returns_Correct_Count_And_Cart_Id()
        {
            var cart = new Cart
            {
                Id = 1,
                Items = new List<CartItem>
                    {
                        new CartItem(),
                        new CartItem(),
                        new CartItem()
                    }
            };
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(cart);

            var response = _cartService.GetAttributes(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            Assert.IsNotNull(response);
            Assert.AreEqual(response.CartId, cart.Id);
            Assert.AreEqual(response.Count, cart.Items.Count);
        }

        [TestMethod]
        public void GetAttributes_Returns_Null_Response()
        {
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns((Cart)null);

            var response = _cartService.GetAttributes(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            Assert.IsNull(response);
        }

        [TestMethod]
        public void CartItemExists_Returns_False_When_Cart_Is_Null()
        {
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns((Cart)null);

            var response = _cartService.CartItemExists(new CartItemExistsRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            Assert.IsFalse(response);
        }

        [TestMethod]
        public void CartItemExists_Returns_False_When_Item_Not_In_Cart()
        {
            var itemNumber = 1;
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(new Cart
                {
                    Items = new List<CartItem>
                    {
                        new CartItem{ ItemNumber = itemNumber++ }
                    }
                });

            var response = _cartService.CartItemExists(new CartItemExistsRequest { ItemNumber = itemNumber });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            Assert.IsFalse(response);
        }

        [TestMethod]
        public void CartItemExists_Returns_True_When_Item_In_Cart()
        {
            var itemNumber = 1;
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(new Cart
                {
                    Items = new List<CartItem>
                    {
                        new CartItem{ ItemNumber = itemNumber }
                    }
                });

            var response = _cartService.CartItemExists(new CartItemExistsRequest { ItemNumber = itemNumber });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            Assert.IsTrue(response);
        }

        [TestMethod]
        public void AddToCart_Creates_New_Cart()
        {
            const int itemNumber = 1;
            const string coid = "12345";
            const int dept = 100;
            const string parId = "F01";

            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns((Cart)null);
            _mockCartRepository.Setup(x => x.AddCart(It.IsAny<CartRequest>()))
                .Returns(new Cart
                {
                    ParId = parId,
                    Items = new List<CartItem>()
                });
            _mockCartRepository.Setup(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()))
                .Returns(new AddToCartResponse
                {
                    Status = AddToCartStatus.Added
                });
            _mockItemService.Setup(x => x.GetItemsWithDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<List<ItemParDTO>>(), It.IsAny<ApiVersion>()))
                .Returns(new List<ItemDetailsDTO>
                {
                    new ItemDetailsDTO
                    {
                        AvailableParItems = new List<ParItem>
                        {
                            new ParItem { ParId = parId }
                        }
                    }
                });
            _mockRequisitionRepository
                .Setup(x => x.GetLastOrderedDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<ParItem>>()))
                .Returns(new List<ParItemWithLastOrdered>
                {
                    new ParItemWithLastOrdered
                    {
                        ItemId = itemNumber
                    }
                });

            var response = _cartService.AddToCart(new CartAddItemRequest { ItemNumber = itemNumber, Coid = coid, DepartmentNumber = dept, ParId = parId });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()), Times.Once);
            Assert.IsNotNull(response);
            Assert.AreEqual(AddToCartStatus.Added, response.Status);
        }

        [TestMethod]
        public void AddToCart_Adds_New_Item()
        {
            const int itemNumber = 1;
            const string coid = "12345";
            const int dept = 100;
            const string parId = "F01";

            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartAddItemRequest>()))
                .Returns(new Cart
                {
                    ParId = parId,
                    Items = new List<CartItem>()
                });
            _mockCartRepository.Setup(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()))
                .Returns(new AddToCartResponse
                {
                    Status = AddToCartStatus.Added
                });
            _mockItemService.Setup(x => x.GetItemsWithDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<List<ItemParDTO>>(), It.IsAny<ApiVersion>()))
                .Returns(new List<ItemDetailsDTO>
                {
                    new ItemDetailsDTO
                    {
                        AvailableParItems = new List<ParItem>
                        {
                            new ParItem { ParId = parId }
                        }
                    }
                });
            _mockRequisitionRepository
                .Setup(x => x.GetLastOrderedDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<ParItem>>()))
                .Returns(new List<ParItemWithLastOrdered>
                {
                    new ParItemWithLastOrdered
                    {
                        ItemId = itemNumber
                    }
                });

            var response = _cartService.AddToCart(new CartAddItemRequest { ItemNumber = itemNumber, Coid = coid, DepartmentNumber = dept, ParId = parId });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartAddItemRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Never);
            _mockCartRepository.Verify(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()), Times.Once);
            Assert.IsNotNull(response);
            Assert.AreEqual(AddToCartStatus.Added, response.Status);
        }

        [TestMethod]
        public void AddToCart_Returns_Exists_Status()
        {
            var itemNumber = 1;
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(new Cart
                {
                    Items = new List<CartItem>
                    {
                        new CartItem{ ItemNumber = itemNumber }
                    }
                });

            var response = _cartService.AddToCart(new CartAddItemRequest { ItemNumber = itemNumber });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Never);
            _mockCartRepository.Verify(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()), Times.Never);
            Assert.IsNotNull(response);
            Assert.AreEqual(AddToCartStatus.Exists, response.Status);
        }

        [TestMethod]
        public void AddToCart_Returns_Full_Status()
        {
            const int itemNumber = 101;
            const string coid = "12345";
            const int dept = 100;
            const string parId = "F01";

            var cart = new Cart { ParId = parId, Items = new List<CartItem>() };
            for (var i = 1; i <= int.Parse(ConfigurationManager.AppSettings.Get("maxCartItemCount")); i++)
            {
                cart.Items.Add(new CartItem
                {
                    ItemNumber = i
                });
            }

            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(cart);
            _mockItemService.Setup(x => x.GetItemsWithDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<List<ItemParDTO>>(), It.IsAny<ApiVersion>()))
                .Returns(new List<ItemDetailsDTO>
                {
                    new ItemDetailsDTO
                    {
                        AvailableParItems = new List<ParItem>
                        {
                            new ParItem { ParId = parId }
                        }
                    }
                });
            _mockRequisitionRepository
                .Setup(x => x.GetLastOrderedDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<ParItem>>()))
                .Returns(new List<ParItemWithLastOrdered>
                {
                    new ParItemWithLastOrdered
                    {
                        ItemId = itemNumber
                    }
                });

            var response = _cartService.AddToCart(new CartAddItemRequest { ItemNumber = itemNumber, Coid = coid, DepartmentNumber = dept, ParId = parId });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Never);
            _mockCartRepository.Verify(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()), Times.Never);
            Assert.IsNotNull(response);
            Assert.AreEqual(AddToCartStatus.Full, response.Status);
        }

        [TestMethod]
        public void AddToCart_Returns_Almost_Full_Status()
        {
            const int itemNumber = 101;
            const string coid = "12345";
            const int dept = 100;
            const string parId = "F01";

            var cart = new Cart { ParId = parId, Items = new List<CartItem>() };
            for (var i = 1; i < int.Parse(ConfigurationManager.AppSettings.Get("maxCartItemCount")); i++)
            {
                cart.Items.Add(new CartItem
                {
                    ItemNumber = i
                });
            }

            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(cart);
            _mockItemService.Setup(x => x.GetItemsWithDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<List<ItemParDTO>>(), It.IsAny<ApiVersion>()))
                .Returns(new List<ItemDetailsDTO>
                {
                    new ItemDetailsDTO
                    {
                        AvailableParItems = new List<ParItem>
                        {
                            new ParItem { ParId = parId }
                        }
                    }
                });
            _mockRequisitionRepository
                .Setup(x => x.GetLastOrderedDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<ParItem>>()))
                .Returns(new List<ParItemWithLastOrdered>
                {
                    new ParItemWithLastOrdered
                    {
                        ItemId = itemNumber
                    }
                });

            var response = _cartService.AddToCart(new CartAddItemRequest
            {
                ItemNumber = itemNumber,
                Coid = coid,
                DepartmentNumber = dept,
                ParId = parId,
                Override = false
            });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Never);
            _mockCartRepository.Verify(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()), Times.Never);
            Assert.IsNotNull(response);
            Assert.AreEqual(AddToCartStatus.AlmostFull, response.Status);
        }

        [TestMethod]
        public void AddToCart_Allows_Override_To_Fill_Cart()
        {
            const int itemNumber = 101;
            const string coid = "12345";
            const int dept = 100;
            const string parId = "F01";

            var cart = new Cart { ParId = parId, Items = new List<CartItem>() };
            for (var i = 1; i < int.Parse(ConfigurationManager.AppSettings.Get("maxCartItemCount")); i++)
            {
                cart.Items.Add(new CartItem
                {
                    ItemNumber = i
                });
            }

            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(cart);
            _mockCartRepository.Setup(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()))
                .Returns(new AddToCartResponse
                {
                    Status = AddToCartStatus.Added
                });
            _mockItemService.Setup(x => x.GetItemsWithDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<List<ItemParDTO>>(), It.IsAny<ApiVersion>()))
                .Returns(new List<ItemDetailsDTO>
                {
                    new ItemDetailsDTO
                    {
                        AvailableParItems = new List<ParItem>
                        {
                            new ParItem { ParId = parId }
                        }
                    }
                });

            _mockRequisitionRepository
                .Setup(x => x.GetLastOrderedDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<ParItem>>()))
                .Returns(new List<ParItemWithLastOrdered>
                {
                    new ParItemWithLastOrdered
                    {
                        ItemId = itemNumber
                    }
                });

            var response = _cartService.AddToCart(new CartAddItemRequest 
                {
                    ItemNumber = itemNumber, 
                    Coid = coid, 
                    DepartmentNumber = dept, 
                    ParId = parId,
                    Override = true
                });

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockCartRepository.Verify(x => x.AddCart(It.IsAny<CartRequest>()), Times.Never);
            _mockCartRepository.Verify(x => x.AddToCart(It.IsAny<Cart>(), It.IsAny<CartItem>()), Times.Once);
            Assert.IsNotNull(response);
            Assert.AreEqual(AddToCartStatus.Added, response.Status);
        }

        [TestMethod]
        public void GetRequisition_Throws_Exception()
        {
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns((Cart)null);

            try
            {
                _cartService.GetRequisition(new CartRequest());
                Assert.IsTrue(false, "CartService.GetRequisition did not throw an Exception");
            }
            catch
            {
                Assert.IsTrue(true);
            }
        }

        [TestMethod]
        public void GetRequisition_Creates_New_Requisition()
        {
            GetRequisition_Setup();

            var response = _cartService.GetRequisition(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockSmartCoidService.Verify(x => x.GetCOID(It.IsAny<string>(), It.IsAny<string>()), Times.Once);

            Assert.IsNotNull(response);
            Assert.AreEqual(response.RequisitionId, 0);
        }

        [TestMethod]
        public void GetRequisition_Gets_Existing_Requisition()
        {
            var requisitionId = 1;
            GetRequisition_Setup(requisitionId);

            var response = _cartService.GetRequisition(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockRequisitionService.Verify(x => x.GetRequisition(It.IsAny<string>(), It.IsAny<int>()), Times.Once);

            Assert.IsNotNull(response);
            Assert.AreEqual(response.RequisitionId, requisitionId);
        }

        [TestMethod]
        public void GetRequisition_Creates_New_Requisition_When_Old_One_Is_Submitted()
        {
            var requisitionId = 1;
            GetRequisition_Setup(requisitionId, true);

            var response = _cartService.GetRequisition(new CartRequest());

            _mockCartRepository.Verify(x => x.GetCart(It.IsAny<CartRequest>()), Times.Once);
            _mockRequisitionService.Verify(x => x.GetRequisition(It.IsAny<string>(), It.IsAny<int>()), Times.Once);
            _mockSmartCoidService.Verify(x => x.GetCOID(It.IsAny<string>(), It.IsAny<string>()), Times.Once);

            Assert.IsNotNull(response);
            Assert.AreEqual(response.RequisitionId, 0);
        }

        private void GetRequisition_Setup(int? requisitionId = null, bool isSubmitted = false)
        {
            var itemNumber = 1;
            var parId = "F00";
            _mockCartRepository.Setup(x => x.GetCart(It.IsAny<CartRequest>()))
                .Returns(new Cart
                {
                    ParId = parId,
                    Items = new List<CartItem>
                        {
                            new CartItem{ ItemNumber = itemNumber }
                        },
                    RequisitionId = requisitionId
                });
            _mockSmartCoidService.Setup(x => x.GetCOID(It.IsAny<string>(), It.IsAny<string>()))
                .Returns(new Location());
            _mockItemService.Setup(x => x.GetItemsWithDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<List<ItemParDTO>>(), It.IsAny<ApiVersion>()))
                .Returns(new List<ItemDetailsDTO>
                {
                    new ItemDetailsDTO
                    {
                        Item = new Item { Id = itemNumber.ToString() },
                        AvailableParItems = new List<ParItem>
                        {
                            new ParItem
                            {
                                ItemId = itemNumber,
                                Item = new Item 
                                { 
                                    Id = itemNumber.ToString(),
                                    Vendor = new Vendor()
                                },
                                ParId = parId
                            }
                        }
                    }
                });
            _mockRequisitionRepository.Setup(x => x.GetLastOrderedDetails(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<List<ParItem>>()))
                .Returns(new List<ParItemWithLastOrdered>
                {
                    new ParItemWithLastOrdered
                    {
                        ItemId = itemNumber,
                        Item = new Item 
                        { 
                            Id = itemNumber.ToString(),
                            Vendor = new Vendor() 
                        },
                        ParId = parId
                    }
                });
            _mockRequisitionService.Setup(x => x.GetInFlightQuantity(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>(), It.IsAny<string>(), It.IsAny<string>()))
                .Returns(new InFlightQty());
            _mockRequisitionService.Setup(x => x.GetWithDetailsDTO(It.IsAny<Requisition>(), It.IsAny<string>()))
                    .Returns(new RequisitionWithDetailsDTO { RequisitionId = (isSubmitted || requisitionId == null) ? 0 : (int)requisitionId });
            if (requisitionId != null)
            {
                _mockRequisitionService.Setup(x => x.GetRequisition(It.IsAny<string>(), It.IsAny<int>()))
                    .Returns(new Requisition 
                    { 
                        RequisitionId = (int)requisitionId,
                        RequisitionStatusTypeId = isSubmitted ? (int)RequisitionStatusTypeEnum.Submitted : (int)RequisitionStatusTypeEnum.SubmissionError
                    });
            }
        }
    }
}
