﻿using RequisitionServices.Database;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.Repositories
{
    public class TypeRepository : AbstractRepository, ITypeRepository
    {
        public TypeRepository(EProcurementContext context) : base(context) { }
    
        public IEnumerable<DeliveryMethodType> GetDeliveryMethods()
        {
            return context.DeliveryMethodTypes.ToList();
        }

        public DeliveryMethodType GetDeliveryMethod(int deliveryMethodTypeId)
        {
            return context.DeliveryMethodTypes.Where(x => x.Id == deliveryMethodTypeId).FirstOrDefault();
        }

        public IEnumerable<WorkflowType> GetWorkflowTypes()
        {
            return context.WorkflowTypes.ToList();
        }
    }
}
