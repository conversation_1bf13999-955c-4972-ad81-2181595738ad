﻿using RequisitionServices.DomainModel.SystemNotifications;
using System.Collections.Generic;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface ISystemNotificationRepository
    {
        bool CheckSystemNotificationAuthorization(string userName);

        List<SystemNotificationAdminDTO> GetUsersWithAuthorization();

        List<SystemNotificationAdminDTO> SearchNewAdminUsingThreeFour(string searchUserName);

        List<SystemNotificationAdminDTO> SearchNewAdminUsingCOID(string searchString);

        List<SystemNotificationAdminDTO> UpdateAdminsWithAuthorization(List<SystemNotificationAdminDTO> systemNotificationAdmins);

        SystemNotification GetLatestNonExpiredSystemNotification();

        SystemNotification SaveNewSystemNotification(SystemNotification notification);

        void RemoveOldSystemNotifications();

    }
}
