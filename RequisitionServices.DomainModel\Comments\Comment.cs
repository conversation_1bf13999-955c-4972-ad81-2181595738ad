﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RequisitionServices.DomainModel.Constants;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.DomainModel.Comments
{
    public class Comment
    {
        public long Id { get; set; }
        
        public int RequisitionId { get; set; }
        [ForeignKey("RequisitionId")]
        public virtual Requisition Requisition { get; set; }
        
        public int UserId { get; set; }
        [ForeignKey("UserId")]
        public virtual User User { get; set; }

        [MaxLength(Values.RequisitionCommentMaxLength)]
        public string Text { get; set; }
        public DateTime CreatedUtc { get; set; }
        public DateTime LastUpdatedUtc { get; set; }
    }
}
