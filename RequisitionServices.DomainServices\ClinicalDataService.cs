﻿using RequisitionServices.DomainModel.Clinical;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices
{
    public class ClinicalDataService : IClinicalDataService
    {
        private ISmartPatientService smartPatientService;
        private ISmartDoctorService smartDoctorService;

        public ClinicalDataService(ISmartPatientService smartPatientSvc, ISmartDoctorService smartDoctorSvc)
        {
            smartPatientService = smartPatientSvc;
            smartDoctorService = smartDoctorSvc;
        }

        public IEnumerable<Provider> GetProviders(string userName, string COID)
        {
            return smartDoctorService.GetDoctors(userName, COID);
        }

        public IEnumerable<Patient> GetPatients(string userName, string COID)
        {
            return smartPatientService.GetPatients(userName, COID);
        }

        public Patient GetPatient(string userName, string COID, string patientId)
        {
            return smartPatientService.GetPatient(userName, COID, patientId);
        }

        public IEnumerable<Patient> SearchPatientsByName(string userName, string COID, string patientName)
        {
            return smartPatientService.SearchPatientsByName(userName, COID, patientName);
        }
    }
}
