USE [eProcurementQA]
GO
ALTER TABLE [dbo].[RequisitionVProBadgeLogs]
ALTER COLUMN [BadgeIn] INT;

EXEC sp_rename 'dbo.RequisitionVProBadgeLogs.BadgeIn', 'BadgeInStatusId', 'COLUMN';

CREATE TABLE RequisitionVProBadgeStatusTypes (
    id INT PRIMARY KEY IDENTITY(1,1),
    status VARCHAR(50),
    StatusId INT
);

INSERT INTO RequisitionVProBadgeStatusTypes (status, StatusId) VALUES ('No', 0);
INSERT INTO RequisitionVProBadgeStatusTypes (status, StatusId) VALUES ('Yes', 1);
INSERT INTO RequisitionVProBadgeStatusTypes (status, StatusId) VALUES ('Failed', 2);
GO