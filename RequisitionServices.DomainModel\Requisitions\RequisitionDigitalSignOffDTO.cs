﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Model for transferring DSO information to web applications
    /// </summary>
    public class RequisitionDigitalSignOffDTO
    {
        /// <summary>
        /// Primary Key for DigitalSignOffUsers
        /// </summary>
        public int Id { get; set; }
        /// <summary>
        /// FK for Requisitions
        /// </summary>
        public int RequisitionId { get; set; }
        /// <summary>
        /// FK for DigitalSignOffUsers
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// Flag for if a signature was captured during Digital signoff
        /// </summary>
        public bool SignatureCaptured { get; set; }
        /// <summary>
        /// Flag for if user input matches AD record during signing process
        /// </summary>
        public bool ADValidated { get; set; }
    }
}
