﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AutoMapper" version="10.1.1" targetFramework="net472" />
  <package id="Castle.Core" version="4.4.1" targetFramework="net472" />
  <package id="EntityFramework" version="6.1.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.Razor" version="3.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.7" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Versioning" version="4.0.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.2" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.2" targetFramework="net472" />
  <package id="Microsoft.Web.Infrastructure" version="*******" targetFramework="net472" />
  <package id="Moq" version="4.5.29" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="12.0.3" targetFramework="net472" />
</packages>