﻿using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;

namespace RequisitionServices.Controllers
{
    /// <summary>
    /// Handles the CRUD endpoints for the Digital Sign Off Process
    /// </summary>
    public class DigitalSignOffController : ApiController
    {
        readonly IDigitalSignOffService _digitalSignOffService;
        public DigitalSignOffController(IDigitalSignOffService digitalSignOffService)
        {
            _digitalSignOffService = digitalSignOffService;
        }

        [HttpGet]
        /// <summary>
        /// Get the record of RequisitionDigitalSignOffs table by RequisitionId
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        public async Task<RequisitionDigitalSignOff> GetDigitalSignOff(int requisitionId)
        {
            return await _digitalSignOffService.GetDigitalSignOff(requisitionId);
        }

        [HttpPost]
        /// <summary>
        /// Creates the new record in the RequisitionDigitalSignOffs table
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        public  async Task<RequisitionDigitalSignOff> CreateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff)
        {
            return await _digitalSignOffService.CreateRequisitionDigitalSignOff(requisitionDigitalSignoff);
        }

        [HttpPost]
        /// <summary>
        /// Deletes record of RequisitionDigitalSignOffs table by digital sign off Id
        /// </summary>
        /// <returns>DigitalSignOff</returns>
        public async Task DeleteRequisitionDigitalSignOff(int digitalSignOffId)
        {
           await _digitalSignOffService.DeleteRequisitionDigitalSignOff(digitalSignOffId);
        }

        [HttpPatch]
        /// <summary>
        /// Updates the record of RequisitionDigitalSignOffs table by digital sign off Id
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        public async Task UpdateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff, int digitalSignOffId)
        {
          await _digitalSignOffService.UpdateRequisitionDigitalSignOff(requisitionDigitalSignoff,digitalSignOffId);
        }

        [HttpGet]
        /// <summary>
        /// Get the first record of DigitalSignOffUsers table
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        public async Task <IEnumerable<DigitalSignOffUser>> GetAllDigitalSignOffUsers()
        {
            return await _digitalSignOffService.GetAllDigitalSignOffUsers();
        }

        [HttpPost]
        /// <summary>
        /// Adds new user based off data from AD
        /// </summary>
        /// <returns>CreateDigitalSignOffUser</returns>
        public async Task<DigitalSignOffUser> CreateDigitalSignOffUser(DigitalSignOffUser request)
        {
          return await _digitalSignOffService.CreateDigitalSignOffUser(request);
        }

        [HttpGet]
        /// <summary>
        /// Get user based off thier 3/4
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        public async Task<DigitalSignOffUser> GetSingleDigitalSignOffUser(string accountName)
        {
           return await _digitalSignOffService.GetSingleDigitalSignOffUser(accountName);
        }

        
        [HttpPatch]
        /// <summary>
        /// Updates record based off of ID thats passed after finding a discrepancy in AD
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        public async Task UpdateSingleDigitalSignOffUser(DigitalSignOffUser request)
        {
          await _digitalSignOffService.UpdateSingleDigitalSignOffUser(request);
        }
        /// <summary>
        /// Brings back the specific data for the Approvers section of procurement needed for DSO
        /// </summary>
        /// <returns>DigitalSignOffUser</returns>
        [HttpGet]
        public async Task<ApproverDigitalSignOffDTO> GetApproverDigitalSignOff(int requisitionId)
        {
            return await _digitalSignOffService.GetApproverDigitalSignOff(requisitionId);
        }
    }
}
