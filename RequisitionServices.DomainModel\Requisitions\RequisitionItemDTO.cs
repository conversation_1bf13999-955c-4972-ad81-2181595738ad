﻿using RequisitionServices.DomainModel.Items;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// An item on a requisition
    /// </summary>
    public class RequisitionItemDTO
    {
        public RequisitionItemDTO() { }
        public RequisitionItemDTO(RequisitionItem requisitionItem, bool IsVendor, bool fromDB)
        {
            if (requisitionItem != null)
            {
                this.Id = requisitionItem.Id;
                this.RequisitionId = requisitionItem.RequisitionId;
                this.ItemId = requisitionItem.ItemId;
                this.RequisitionerName = requisitionItem.RequisitionerName;
                this.ParIdentifier = requisitionItem.ParIdentifier;
                this.MainItemId = requisitionItem.MainItemId;
                this.RequisitionItemStatusTypeId = requisitionItem.RequisitionItemStatusTypeId;
                this.QuantityToOrder = requisitionItem.QuantityToOrder;
                this.QuantityFulfilled = requisitionItem.QuantityFulfilled;
                this.HasQuantityToOrderChanged = requisitionItem.HasQuantityToOrderChanged;
                this.PONumber = requisitionItem.PONumber;
                this.IsRushOrder = requisitionItem.IsRushOrder;
                this.ParentSystemId = requisitionItem.ParentSystemId;
                this.RequisitionScheduledDate = requisitionItem.RequisitionScheduledDate.HasValue ? requisitionItem.RequisitionScheduledDate.Value.LocalDateTime : (DateTime?)null;
                this.CreatedBy = requisitionItem.CreatedBy;
                this.CreateDate = requisitionItem.CreateDate;
                this.IsFileItem = requisitionItem.IsFileItem;
                this.FileItemHasChanged = requisitionItem.FileItemHasChanged;
                this.SmartItemNumber = requisitionItem.SmartItemNumber;
                this.StockIndicator = requisitionItem.StockIndicator;
                this.CatalogNumber = requisitionItem.CatalogNumber;
                this.HasDiscountChanged = requisitionItem.HasDiscountChanged;
                this.Discount = requisitionItem.Discount;
                this.IsWastePar = requisitionItem.IsWastePar;
                this.ParentRequisitionItemId = requisitionItem.ParentRequisitionItemId;
                this.ParentRequisitionItemNumber = requisitionItem.RequisitionItemAutoSubFlag;
                this.RejectionComments = requisitionItem.RejectionComments;
                this.IsPurged = requisitionItem.IsPurged;
                this.IsOnContract = requisitionItem.IsOnContract;
                this.LaborWarrantyMonths = requisitionItem.LaborWarrantyMonths;
                this.PartsWarrantyMonths = requisitionItem.PartsWarrantyMonths;

                LotSerialPairs = new List<LotSerialPair>();

                if (requisitionItem.ClinicalUseDetails != null && requisitionItem.ClinicalUseDetails.Any())
                {
                    var clincialDetails = requisitionItem.ClinicalUseDetails.First();
                    this.ClinicalUseId = clincialDetails.Id;
                    this.UpchargeCost = clincialDetails.UpchargeCost;
                    this.Provider = clincialDetails.Provider;
                    this.PatientId = clincialDetails.PatientId;
                    this.PatientName = clincialDetails.PatientName;
                    this.ProcedureDate = clincialDetails.ProcedureDate;
                    this.ProviderHasChanged = clincialDetails.ProviderHasChanged;
                    this.PatientIdHasChanged = clincialDetails.PatientIdHasChanged;
                    this.PatientNameHasChanged = clincialDetails.PatientNameHasChanged;
                    this.ProcedureDateHasChanged = clincialDetails.ProcedureDateHasChanged;

                    foreach (var clinicalDetail in requisitionItem.ClinicalUseDetails)
                    {
                        LotSerialPairs.Add(new LotSerialPair() { ClinicalUseDetailsId = clinicalDetail.Id, LotNumber = clinicalDetail.LotNumber, SerialNumber = clinicalDetail.SerialNumber });
                    }

                    if (!LotSerialPairs.Any())
                    {
                        LotSerialPairs.Add(new LotSerialPair() { ClinicalUseDetailsId = 0 });
                    }
                }

                if (requisitionItem.SPRDetail != null)
                {
                    this.SPRDetailDTO = new SPRDetailDTO(requisitionItem.SPRDetail);
                }

                HasConversionChanged = requisitionItem.HasConversionChanged;
                if (requisitionItem.VboHoldItemConversion != null)
                {
                    VboHoldItemConversionDto = new VboHoldItemConversionDto(requisitionItem.VboHoldItemConversion);
                }

                this.TrackerIndex = requisitionItem.TrackerIndex;

                this.Inventory = requisitionItem.Inventory;
                if (this.Inventory != null)
                {
                    this.Inventory.RequisitionItem = null;
                    this.Inventory.Requisition = null;
                }

                // The is to save PAR or On Contract data to DB
                // As well as populate these fields when making DTO from DB data
                if ((requisitionItem.ParIdentifier != "EPR" && requisitionItem.ParIdentifier != null)
                    || ((requisitionItem.IsOnContract ?? false || fromDB)
                    && IsVendor && requisitionItem.SPRDetail == null))
                {
                    this.ReOrder = requisitionItem.ReOrder;
                    this.PARLocation = requisitionItem.PARLocation;
                    this.ItemDescription = requisitionItem.ItemDescription;
                    this.VendorId = requisitionItem.VendorId;
                    this.VendorName = requisitionItem.VendorName;
                    this.GLAccount = requisitionItem.GeneralLedgerCode;
                    this.UOM = requisitionItem.UOMCode;
                    this.UnitCost = requisitionItem.UnitCost;
                    this.TotalCost = requisitionItem.TotalCost;
                    this.MinStock = requisitionItem.MinStock;
                    this.MaxStock = requisitionItem.MaxStock;
                }
            }
        }

        /// <summary>
        /// This is the primary key/identity for the item on a requisition, populate with 0 for new items.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// This is the tying field to the requisition's primary key/identity, populate with 0 for new requisitions.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Holds the first and last name of the individual who submitted the requisition.
        /// </summary>
        public string RequisitionerName { get; set; }

        /// <summary>
        /// The Id of the Item being added to the requisition.
        /// </summary>
        public string ItemId { get; set; }

        /// <summary>
        /// Full par identifier. For example "F01".
        /// </summary>
        public string ParIdentifier { get; set; }

        /// <summary>
        /// If the order type is capitated, populate this for all component items with the ItemId of the Main Item to which it ties to.
        /// </summary>
        public int? MainItemId { get; set; }

        /// <summary>
        /// The current item status. If not known, pass 1. Return value will contain the current item level status from the parent system. 21 = System Error.
        /// </summary>
        public int RequisitionItemStatusTypeId { get; set; }

        /// <summary>
        /// This is for return data usage. It will be populated with the Parent System (SMART) Requisition Id.
        /// </summary>
        public string ParentSystemId { get; set; }

        /// <summary>
        /// The quantity of the item to order.
        /// </summary>
        public int QuantityToOrder { get; set; }

        /// <summary>
        /// This is for return data usage. It will be populated with the quantity fulfilled when the order is fulfilled.
        /// </summary>
        public int? QuantityFulfilled { get; set; }

        public bool HasQuantityToOrderChanged { get; set; }

        /// <summary>
        /// This is for return data usage. It will be populated with the Parent System (SMART) Purchase Order Id.
        /// </summary>
        public int? PONumber { get; set; }

        /// <summary>
        /// True for Rush. False for normal.
        /// </summary>
        public bool IsRushOrder { get; set; }

        /// <summary>
        /// The user which created the requisition. This will be populated by the userName performing the parent action.
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// The date/time of the requisition creation. New Reqs (Id = 0), will get the current date/time autoset for this value
        /// </summary>
        public DateTime CreateDate { get; set; }


        /// <summary>
        /// Primary Key/Identity for the clinical data, if not already known from prior save, do not populate.
        /// </summary>
        public int? ClinicalUseId { get; set; }

        /// <summary>
        /// Lot number(s) to pass to parent system. Applies to Bill Only/Replace and Capitated requisitions only.
        /// </summary>
        public List<LotSerialPair> LotSerialPairs { get; set; }

        public string LotNumber { get; set; }

        public string SerialNumber { get; set; }

        /// <summary>
        /// Upcharge value to pass to parent system. Applies to Capitated requisitions only.
        /// </summary>
        public decimal? UpchargeCost { get; set; }

        /// <summary>
        /// Provider (physician) to pass to parent system. Applies to Bill Only/Replace and Capitated requisitions only.
        /// </summary>
        public string Provider { get; set; }

        /// <summary>
        /// Patient Id to pass to parent system. Applies to Bill Only/Replace and Capitated requisitions only.
        /// </summary>
        public string PatientId { get; set; }

        /// <summary>
        /// Patient Name to pass to parent system. Applies to Bill Only/Replace and Capitated requisitions only.
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// Procedure Date to pass to parent system. Applies to Bill Only/Replace and Capitated requisitions only.
        /// </summary>
        public DateTime? ProcedureDate { get; set; }

        /// <summary>
        /// Used for flagging if approver and changed Bill Information during req approval request process
        /// </summary>
        public bool ProviderHasChanged { get; set; }

        /// <summary>
        /// Used for flagging if approver and changed Bill Information during req approval request process
        /// </summary>
        public bool PatientIdHasChanged { get; set; }

        /// <summary>
        /// Used for flagging if approver and changed Bill Information during req approval request process
        /// </summary>
        public bool PatientNameHasChanged { get; set; }

        /// <summary>
        /// Used for flagging if approver and changed Bill Information during req approval request process
        /// </summary>
        public bool ProcedureDateHasChanged { get; set; }

        /// <summary>
        /// SPR Details. Leave NULL if not an SPR
        /// </summary>
        public SPRDetailDTO SPRDetailDTO { get; set; }

        public VboHoldItemConversionDto VboHoldItemConversionDto { get; set; }

        public bool? HasConversionChanged { get; set; }

        public DateTime? RequisitionScheduledDate { get; set; }

        public int? TrackerIndex { get; set; }

        public bool IsFileItem { get; set; }

        public bool FileItemHasChanged { get; set; }

        public int? SmartItemNumber { get; set; }

        public string ReOrder { get; set; }

        public string CatalogNumber { get; set; }

        public string PARLocation { get; set; }

        public string ItemDescription { get; set; }

        public int VendorId { get; set; }

        public string VendorName { get; set; }

        public string ParClass { get; set; }

        public string GLAccount { get; set; }

        public bool? StockIndicator { get; set; }

        public string UOM { get; set; }

        public decimal? UnitCost { get; set; }

        public decimal? TotalCost { get; set; }

        public int? MinStock { get; set; }

        public int? MaxStock { get; set; }

        public bool HasDiscountChanged { get; set; }

        public decimal? Discount { get; set; }

        public bool IsWastePar { get; set; }

        public string ParentRequisitionItemNumber { get; set; }

        public int? ParentRequisitionItemId { get; set; }

        public string RejectionComments { get; set; }

        public ItemInventory Inventory { get; set; }

        public bool IsPurged { get; set; }

        public bool? IsOnContract { get; set; }

        public byte? PartsWarrantyMonths { get; set; }

        public byte? LaborWarrantyMonths { get; set; }
    }
}
