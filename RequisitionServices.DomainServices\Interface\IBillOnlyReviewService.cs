﻿using RequisitionServices.DomainModel.BillOnlyReview;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices.Interface
{
    /// <summary>
    /// Represents the interface for the BillOnlyReviewService.
    /// </summary>
    public interface IBillOnlyReviewService
    {
        /// <summary>
        /// Retrieves a list of BillOnlyReviewDTO objects based on the provided BillOnlyReviewRequest.
        /// </summary>
        /// <param name="request">The BillOnlyReviewRequest object.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of BillOnlyReviewDTO objects.</returns>
        Task<PaginatedBORDTO> GetBillOnlyReviewRequisitions(BillOnlyReviewRequest request);

        /// <summary>
        /// Retrieves a list of BillOnlyReviewRequisitionWithDetailsDTO objects for BOR printing based on the provided BillOnlyReviewPrintRequest.
        /// </summary>
        /// <param name="request">The BillOnlyReviewPrintRequest object containing a list of RequisitionIds and the UserName.</param>
        /// <returns>An asynchronous task result containing a list of BillOnlyReviewRequisitionWithDetailsDTO requisition objects with item detail.</returns>
        Task<List<BillOnlyReviewRequisitionWithDetailsDTO>> GetRequisitionsDetailsForBORPrint(BillOnlyReviewPrintRequest request);
    }
}
