﻿using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.VPro;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Represents a requisition in the system.
    /// </summary>
    public class Requisition
    {
        public Requisition() { }

        /// <summary>
        /// Gets or sets the total requisition amount.
        /// </summary>
        public decimal? TotalReqAmount { get; set; } = 0; // TotalCost Project

        /// <summary>
        /// Initializes a new instance of the <see cref="Requisition"/> class using a <see cref="RequisitionDTO"/>.
        /// </summary>
        /// <param name="requisitionDTO">The requisition DTO.</param>
        public Requisition(RequisitionDTO requisitionDTO)
        {
            if (requisitionDTO != null)
            {
                this.RequisitionId = requisitionDTO.RequisitionId;
                this.RequisitionStatusTypeId = requisitionDTO.RequisitionStatusTypeId;
                this.RequisitionTypeId = requisitionDTO.RequisitionTypeId;
                this.LocationIdentifier = requisitionDTO.LocationIdentifier;
                this.ApprovalStep = requisitionDTO.ApprovalStep;
                this.ApprovedAmount = requisitionDTO.ApprovedAmount;
                this.IsCurrentApproverStep = requisitionDTO.IsCurrentApproverStep;
                this.Comments = requisitionDTO.Comments;
                this.WorkflowInstanceId = requisitionDTO.WorkflowInstanceId;
                this.CreateDate = requisitionDTO.CreateDate;
                this.CreatedBy = requisitionDTO.CreatedBy;
                this.DenyDelegateByApproverId = requisitionDTO.DenyDelegateByApproverId;
                this.RequisitionParClass = requisitionDTO.RequisitionParClass;
                this.IsMobile = requisitionDTO.IsMobile;
                this.IsVendor = requisitionDTO.IsVendor;
                this.RequisitionSubmissionTypeId = requisitionDTO.RequisitionSubmissionTypeId;
                this.BadgeLogId = requisitionDTO.BadgeLogId;
                this.VProBadgeLog = requisitionDTO.VProBadgeLog;

                //CWG TODO: Once we start actually getting SmartCountryCode from Smart, start
                //setting to some other "default" code so we know if CountryCode was null or not.
                this.CountryCode = requisitionDTO.CountryCode != null ? requisitionDTO.CountryCode : "U";

                if (requisitionDTO.RequisitionItems != null)
                {
                    this.RequisitionItems = new List<RequisitionItem>();
                    foreach (var item in requisitionDTO.RequisitionItems)
                    {
                        this.RequisitionItems.Add(new RequisitionItem(item, this.RequisitionTypeId, requisitionDTO.IsVendor));
                    }
                }
                // totalcost addition
                this.TotalReqAmount = requisitionDTO.TotalReqAmount;
            }
        }

        /// <summary>
        /// Gets or sets the requisition ID.
        /// </summary>
        public int RequisitionId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status type ID.
        /// </summary>
        public int RequisitionStatusTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition status type.
        /// </summary>
        [ForeignKey("RequisitionStatusTypeId")]
        public RequisitionStatusType RequisitionStatusType { get; set; }

        /// <summary>
        /// Gets or sets the requisition type ID.
        /// </summary>
        public int RequisitionTypeId { get; set; }

        /// <summary>
        /// Gets or sets the requisition type.
        /// </summary>
        [ForeignKey("RequisitionTypeId")]
        public RequisitionType RequisitionType { get; set; }

        /// <summary>
        /// Gets or sets the approval step.
        /// </summary>
        public int? ApprovalStep { get; set; }

        /// <summary>
        /// Gets or sets the location identifier.
        /// </summary>
        [Required]
        [StringLength(50)]
        public string LocationIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the comments.
        /// </summary>
        public string Comments { get; set; }

        /// <summary>
        /// Gets or sets the approved amount.
        /// </summary>
        public decimal ApprovedAmount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this is the current approver step.
        /// </summary>
        [NotMapped]
        public bool IsCurrentApproverStep { get; set; }

        /// <summary>
        /// Gets or sets the pending review count.
        /// </summary>
        [NotMapped]
        public int? PendingReviewCount { get; set; }

        /// <summary>
        /// Gets or sets the workflow instance ID.
        /// </summary>
        public Guid? WorkflowInstanceId { get; set; }

        /// <summary>
        /// Gets or sets the user who created the requisition.
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the creation date.
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Gets or sets the country code.
        /// </summary>
        public string CountryCode { get; set; }

        /// <summary>
        /// Gets or sets the requisition PAR class.
        /// </summary>
        public string RequisitionParClass { get; set; }

        private ICollection<RequisitionItem> _requisitionItems;

        /// <summary>
        /// Gets or sets the requisition items.
        /// </summary>
        public virtual ICollection<RequisitionItem> RequisitionItems
        {
            get { return _requisitionItems ?? (_requisitionItems = new Collection<RequisitionItem>()); }
            set { _requisitionItems = value; }
        }

        private ICollection<FileAttachment> _fileAttachments;

        /// <summary>
        /// Gets or sets the file attachments.
        /// </summary>
        public virtual ICollection<FileAttachment> FileAttachments
        {
            get { return _fileAttachments ?? (_fileAttachments = new Collection<FileAttachment>()); }
            set { _fileAttachments = value; }
        }

        private ICollection<RequisitionStatusHistory> _requisitionStatusHistories;

        /// <summary>
        /// Gets or sets the requisition status histories.
        /// </summary>
        public virtual ICollection<RequisitionStatusHistory> RequisitionStatusHistories
        {
            get { return _requisitionStatusHistories ?? (_requisitionStatusHistories = new Collection<RequisitionStatusHistory>()); }
            set { _requisitionStatusHistories = value; }
        }

        private ICollection<RequisitionDigitalSignOff> _requisitionDigitalSignOff;

        /// <summary>
        /// Gets or sets the requisition digital sign-offs.
        /// </summary>
        public virtual ICollection<RequisitionDigitalSignOff> RequisitionDigitalSignOff
        {
            get { return _requisitionDigitalSignOff ?? (_requisitionDigitalSignOff = new Collection<RequisitionDigitalSignOff>()); }
            set { _requisitionDigitalSignOff = value; }
        }

        /// <summary>
        /// Gets or sets a value indicating whether submission to Smart Legacy is blocked.
        /// </summary>
        [NotMapped]
        public bool IsSubmitToSmartLegacyBlocked { get; set; }

        /// <summary>
        /// Gets a value indicating whether the requisition is a rush order.
        /// </summary>
        [NotMapped]
        public bool IsRush
        {
            get
            {
                var retVal = false;
                if(this.RequisitionTypeId == (int)RequisitionTypeEnum.Rush)
                {
                    retVal = true;
                }

                return retVal;
            }
        }

        /// <summary>
        /// Gets the applicable workflow type.
        /// </summary>
        [NotMapped]
        public WorkflowTypeEnum ApplicableWorkflowType
        {
            get
            {
                var workflowType = WorkflowTypeEnum.NotApplicable;

                if (this.IsVendor)
                {
                    workflowType = WorkflowTypeEnum.Vendor;
                }
                else if (this.RequisitionTypeId == (int)RequisitionTypeEnum.Capital)
                {
                    workflowType = WorkflowTypeEnum.Capital;
                }
                else
                {
                    if (this.RequisitionTypeId == (int)RequisitionTypeEnum.Rush)
                    {
                        workflowType = WorkflowTypeEnum.Rush;
                    }
                    else
                    {
                        if (this.RequisitionItems != null && this.RequisitionItems.Where(x => x.SPRDetail != null).Any())
                        {
                            workflowType = WorkflowTypeEnum.SPR;
                        }
                    }

                    if (this.RequisitionTypeId == (int)RequisitionTypeEnum.PunchOut)
                    {
                        workflowType = WorkflowTypeEnum.PunchOut;
                    }
                }

                return workflowType;
                }
            }

        /// <summary>
        /// Gets or sets the ID of the approver who denied the requisition.
        /// </summary>
        [NotMapped]
        public int? DenyDelegateByApproverId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is mobile.
        /// </summary>
        public bool IsMobile { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the requisition is for a vendor.
        /// </summary>
        public bool IsVendor { get; set; }

        /// <summary>
        /// Gets or sets the requisition submission type ID.
        /// </summary>
        public int RequisitionSubmissionTypeId { get; set; }

        /// <summary>
        /// Gets a value indicating whether the requisition is editable by the requisitioner.
        /// </summary>
        [NotMapped]
        public bool IsEditableByRequisitioner
        {
            get
            {
                return RequisitionStatusTypeId == (int) RequisitionStatusTypeEnum.Draft
                       || RequisitionStatusTypeId == (int) RequisitionStatusTypeEnum.Denied
                       || RequisitionStatusTypeId == (int) RequisitionStatusTypeEnum.SubmissionError
                       || RequisitionStatusTypeId == (int) RequisitionStatusTypeEnum.Recalled;
            }
        }
        
        /// <summary>
        /// Gets or sets the VPRO badge in ID.
        /// </summary>
        public int? BadgeLogId { get; set; }

        /// <summary>
        /// Gets or sets the VPro badge log associated with the requisition.
        /// </summary>
        [ForeignKey("BadgeLogId")]
        public RequisitionVProBadgeLog VProBadgeLog { get; set; }
    }
}
