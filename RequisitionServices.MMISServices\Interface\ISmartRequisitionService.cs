﻿using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartRequisitionService
    {
        List<Requisition> GetSPRs(string userName, string coid, List<string> externalReqIds);

        List<Requisition> GetRequisitions(string userName, string coid, List<int> externalReqIds);
        
        Requisition SubmitRequisition(string requisitionerFullName, string userName, string coid, Requisition requisition);

        InFlightQty GetInFlightQuantity(string userName, string coid, int dept, string parClass, string itemId);
    }
}
