﻿using RequisitionServices.Database;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.Entity;
using System.Data.SqlClient;
using log4net;
using System.Reflection;
using System.Threading.Tasks;

namespace RequisitionServices.Repositories
{
    public class UserRepository : AbstractRepository, IUserRepository
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        public UserRepository(EProcurementContext context) : base(context) { }
        
        public User GetUser(int id)
        {
            return context.Users.Where(x => x.Id == id).FirstOrDefault();
        }

        public User GetUser(string accountName)
        {
            return context.Users.Where(x => x.AccountName.ToLower() == accountName.ToLower()).FirstOrDefault();
        }

        public User GetUserWithoutDomain(string accountName)
        {
            return context.Users.Where(x => x.AccountName.ToLower().Contains(accountName.ToLower())).FirstOrDefault();
        }

        public IEnumerable<User> GetUsers(IEnumerable<string> accountNames)
        {
            return context.Users.Where(x => accountNames.Any(y => y.ToLower() == x.AccountName.ToLower()));
        }

        public IEnumerable<User> GetUsersWithUserProfiles(IEnumerable<UserProfile> profiles)
        {
            return (from p in profiles
                    join u in context.Users
                        on p.DomainSlashUserName.ToLower() equals u.AccountName.ToLower()
                    select new User(u, p)
                    );
        }

        public IEnumerable<User> GetUsers(IEnumerable<int> ids)
        {
            return context.Users.Where(x => ids.Any(y => y == x.Id));
        }

        public User InsertUser(User user)
        {
            try
            {
                context.Users.Add(user);
                context.SaveChanges();
            }
            catch (System.Data.Entity.Infrastructure.DbUpdateException ex)
            {
                log.Error(String.Format("Method: {0}, Error adding new User into database", "UserRepository.InsertUser"), ex);
                return context.Users.Where(x => x.AccountName.ToLower() == user.AccountName.ToLower()).FirstOrDefault();
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error adding new User into database", "UserRepository.InsertUser"), ex);
                return null;
            }

            return user;
        }

        public void UpdateUser(User user, string comments, string userName)
        {
            //Get current user from DB
            var currentUser = this.GetUser(user.Id);

            //Set scalar values from incoming object
            context.Entry(currentUser).CurrentValues.SetValues(user);
            
            context.SaveChanges();
        }

        public Approver GetApprover(int id)
        {
            return context.Approvers.Where(x => x.Id == id).FirstOrDefault();
        }
        
        public Approver GetApproverByUserId(int userId, string COID)
        {
            return context.Approvers.Include(u => u.User).Where(x => x.UserId == userId && x.COID.ToLower() == COID.ToLower()).FirstOrDefault();
        }

        public Approver GetApproverByUserNameAndCOID(string accountName, string COID)
        {
            return context.Approvers.Where(x => x.User.AccountName.ToLower() == accountName.ToLower() && x.COID.ToLower() == COID.ToLower()).FirstOrDefault();
        }

        public async Task<IEnumerable<ActiveApproversDto>> GetActiveApproversAsync()
        {
            return  await (from user in context.Users
                    join approver in context.Approvers on user.Id equals approver.UserId
                    join userWorkflowStep in context.UserWorkflowSteps on approver.Id equals userWorkflowStep.ApproverId
                    where userWorkflowStep.DelegatedByUserId == null
                    select new ActiveApproversDto {
                        Id = user.Id,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        AccountName = user.AccountName,
                        Coid = userWorkflowStep.COID})
                .Distinct().ToListAsync();
        }

        public async Task<IEnumerable<ApproverWorkflowDto>> GetApproverWorkflowsAsync(int approverUserId)
        {
            return await (from userWorkflowSteps in context.UserWorkflowSteps
                join workflowTypes in context.WorkflowTypes on userWorkflowSteps.WorkflowTypeId equals workflowTypes.Id
                join users in context.Users on userWorkflowSteps.UserId equals users.Id
                join approvers in context.Approvers on userWorkflowSteps.ApproverId equals approvers.Id
                let approversUserId = approvers.UserId
                where approversUserId.Equals(approverUserId) && userWorkflowSteps.DelegatedByUserId == null
                select new ApproverWorkflowDto
                {
                    Id = userWorkflowSteps.Id, UserId = userWorkflowSteps.UserId, Step = userWorkflowSteps.Step,
                    IsFinalStep = userWorkflowSteps.IsFinalStep,
                    IsFinalRushStep = userWorkflowSteps.IsFinalRushStep,
                    Coid = userWorkflowSteps.COID,
                    Description = workflowTypes.Description,
                    FirstName = users.FirstName,
                    LastName = users.LastName,
                    AccountName = users.AccountName,
                    MaxApprovalAmount = approvers.MaxApprovalAmount,
                    CapitalMaxApprovalAmount = approvers.CapitalMaxApprovalAmount,
                    ApproverId = userWorkflowSteps.ApproverId,
                    WorkflowTypeId = userWorkflowSteps.WorkflowTypeId
                }).ToListAsync();
        }

        public async Task<ActiveApproversDto> GetUserAndDelegateAsync(int selectedApproverUserId)
        {
            return await (from user in context.Users
                    join approver in context.Approvers on user.Id equals approver.UserId
                    where user.Id == selectedApproverUserId
                    select new ActiveApproversDto
                    {
                        Id = user.Id,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        AccountName = user.AccountName,
                        Coid = approver.COID,
                        DelegatedUserId = context.Approvers.FirstOrDefault(x => x.Id == approver.Delegate).UserId
                    }).FirstOrDefaultAsync();
        }


        public Approver GetApproverByUserIdCOIDAndDelegateId(int? userId, string COID, int delegateId)
        {
            return context.Approvers.Where(x => x.UserId == userId && x.COID.ToLower() == COID.ToLower() && x.Delegate == delegateId).FirstOrDefault();
        }

        public IEnumerable<Approver> GetApproversByUserId(int userId)
        {
            return context.Approvers.Where(x => x.UserId == userId);
        }

        public IEnumerable<Approver> GetApprovers(IEnumerable<string> accountNames, string COID)
        {
            return context.Approvers.Where(x => accountNames.Any(y => y.ToLower() == x.User.AccountName.ToLower()) && x.COID.ToLower() == COID.ToLower());
        }

        public async Task<IEnumerable<Approver>> GetApproversAsync(IEnumerable<string> accountNames, string COID)
        {
            return await context.Approvers.Where(x => accountNames.Any(y => y == x.User.AccountName) && x.COID == COID).ToListAsync();
        }

        public IEnumerable<Approver> GetApproversWithUserProfilesInUser(IEnumerable<UserProfile> profiles, string COID)
        {
            return (from p in profiles
                    join u in context.Users
                        on p.DomainSlashUserName.ToLower() equals u.AccountName.ToLower()
                    join a in context.Approvers
                        on new { p1 = u.Id, p2 = COID } equals new { p1 = a.UserId, p2 = a.COID }
                    select new Approver()
                    {
                        Id = a.Id,
                        UserId = u.Id,
                        User = new User(u, p),
                        MaxApprovalAmount = a.MaxApprovalAmount,
                        CapitalMaxApprovalAmount = a.CapitalMaxApprovalAmount,
                        IsActive = a.IsActive,
                        Delegate = a.Delegate,
                        CreatedBy = a.CreatedBy,
                        CreateDate = a.CreateDate,
                        COID = a.COID,
                        IsCERReviewer = a.IsCERReviewer
                    }
                    );
        }

        public IEnumerable<Approver> GetApprovers(IEnumerable<int> ids)
        {
            return context.Approvers.Where(x => ids.Any(y => y == x.Id));
        }

        public Approver InsertApprover(Approver approver, string comments)
        {
            try
            {
                context.Approvers.Add(approver);
                context.SaveChanges();
            }
            catch (System.Data.Entity.Infrastructure.DbUpdateException ex)
            {
                log.Error(String.Format("Method: {0}, Error adding new Approver into database", "UserRepository.InsertApprover"), ex);
                return context.Approvers.Where(x => x.UserId == approver.User.Id
                                                && x.COID.ToLower() == approver.COID.ToLower())
                                                .FirstOrDefault();
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error adding new Approver into database", "UserRepository.InsertApprover"), ex);
                return null;
            }

            return approver;
        }

        public void UpdateApprover(Approver approver, string comments, string userName)
        {
            //Get current approver from DB
            var currentApprover = this.GetApprover(approver.Id);

            //Set scalar values from incoming object
            context.Entry(currentApprover).CurrentValues.SetValues(approver);

            context.SaveChanges();
        }

        public bool IsApproverByUserName(string userName)
        {
            return  context.Approvers
                        .Include(x => x.User)
                    .Any(x => x.User.AccountName.ToLower() == userName.ToLower());
            
        }
                
        public IEnumerable<UserReportAndEditDBInfo> GetUserReportAndEditDBInfos(IEnumerable<UserReportProfileDTO> userNames, string coid)
        {
            return (from n in userNames
                        join u in context.Users
                            on n.AccountName.ToLower() equals u.AccountName.ToLower()
                        join a in context.Approvers
                            on new { p1 = u.Id, p2 = coid, p3 = n.IsApprover } equals new { p1 = a.UserId, p2 = a.COID, p3 = true } into ap
                    from apps in ap.DefaultIfEmpty()
                        join wf in context.UserWorkflowSteps
                            on new { p1 = u.Id, p2 = coid, p3 = n.IsSPRRequisitioner } equals new { p1 = wf.UserId, p2 = wf.COID, p3 = true } into wfs
                    from wflow in wfs.DefaultIfEmpty()
                    group new { u, apps, wflow } by new { u.Id } into pg
                    select new UserReportAndEditDBInfo()
                    {
                        User = pg.First().u,
                        Approver = pg.First().apps,
                        UserSetupWorkflows = new UserSetupWorkflows()
                        {                            
                            HasStdWorkflow = pg.Any(x => x.wflow != null && x.wflow.WorkflowTypeId == (int)WorkflowTypeEnum.SPR),
                            HasRushWorkflow = pg.Any(r => r.wflow != null && r.wflow.WorkflowTypeId == (int)WorkflowTypeEnum.Rush),
                            HasCapitalWorkflow = pg.Any(c => c.wflow != null && c.wflow.WorkflowTypeId == (int)WorkflowTypeEnum.Capital),
                            HasPunchoutWorkflow = pg.Any(p => p.wflow != null && p.wflow.WorkflowTypeId == (int)WorkflowTypeEnum.PunchOut)
                        }
                    }).ToList();

        }

        public string GetFavoriteFacilityId(string userName)
        {
            return context.Personalizations.Where(x => x.UserName.ToLower() == userName.ToLower()
                                                            && x.DepartmentId == null
                                                            && x.ParId == null
                                                            && x.FacilityId != null) //just for good measure, not necessary
                                                .Select(y => y.FacilityId)
                                                .FirstOrDefault();
        }

        public PersonalizationDTO SetFavoriteFacility(PersonalizationDTO personalizationDTO)
        {
            var sql = @"MERGE INTO Personalizations
                        USING 
                        (
	                      SELECT @userName AS UserName
                        ) AS Entity
                        ON Personalizations.UserName = Entity.UserName
                        AND Personalizations.DepartmentId IS NULL
                        AND Personalizations.ParId IS NULL
                        WHEN MATCHED THEN 
	                        UPDATE 
	                        SET FacilityId = @COID
                        WHEN NOT MATCHED THEN
	                        INSERT (UserName, FacilityId, DepartmentId, ParId)
	                        VALUES (@userName, @COID, NULL, NULL)
                        OUTPUT inserted.*;";

            object[] parameters = {
                                    new SqlParameter("@userName", personalizationDTO.UserName),
                                    new SqlParameter("@COID", personalizationDTO.FacilityId)
                                  };

            return context.Database.SqlQuery<PersonalizationDTO>(sql, parameters).FirstOrDefault();
        }

        public void DeleteFavoriteFacility(string userName)
        {
            var favoriteFacilities = context.Personalizations.Where(x => x.UserName.ToLower() == userName.ToLower()
                                                                       && x.DepartmentId == null
                                                                       && x.ParId == null);

            if (favoriteFacilities != null && favoriteFacilities.Any())
            {
                context.Personalizations.RemoveRange(favoriteFacilities);
                context.SaveChanges();
            }
        }


        public int GetFavoriteDepartmentId(string userName, string COID)
        {
            int department = context.Personalizations.Where(x => x.UserName.ToLower() == userName.ToLower() 
                                                                && x.FacilityId == COID 
                                                                && x.DepartmentId != null 
                                                                && x.ParId == null)
                                                    .Select(y => y.DepartmentId)
                                                    .FirstOrDefault() ?? 0;
            return department;
        }

        public PersonalizationDTO SetFavoriteDepartment(PersonalizationDTO personalizationDTO)
        {
            var sql = @"MERGE INTO Personalizations
                        USING 
                        (
	                      SELECT @userName AS UserName,
                                 @COID AS FacilityId
                        ) AS Entity
                        ON Personalizations.UserName = Entity.UserName
                        AND Personalizations.FacilityId = Entity.FacilityId
                        AND Personalizations.DepartmentId IS NOT NULL
                        AND Personalizations.ParId IS NULL
                        WHEN MATCHED THEN 
	                        UPDATE 
	                        SET DepartmentId = @departmentId
                        WHEN NOT MATCHED THEN
	                        INSERT (UserName, FacilityId, DepartmentId, ParId)
	                        VALUES (@userName, @COID, @departmentId, NULL)
                        OUTPUT inserted.*;";
            
            object[] parameters = {
                                    new SqlParameter("@userName", personalizationDTO.UserName),
                                    new SqlParameter("@COID", personalizationDTO.FacilityId),
                                    new SqlParameter("@departmentId", personalizationDTO.DepartmentId)
                                  };

            return context.Database.SqlQuery<PersonalizationDTO>(sql, parameters).FirstOrDefault();
        }

        public void DeleteFavoriteDepartment(PersonalizationDTO personalizationDTO)
        {
            var favoriteDepartments = context.Personalizations.Where(x => x.UserName.ToLower() == personalizationDTO.UserName.ToLower()
                                                                       && x.FacilityId == personalizationDTO.FacilityId
                                                                       && x.DepartmentId != null
                                                                       && x.ParId == null);

            if (favoriteDepartments != null && favoriteDepartments.Any())
            {
                context.Personalizations.RemoveRange(favoriteDepartments);
                context.SaveChanges();
            }
        }

        public string GetFavoriteParId(string userName, string COID, int departmentId)
        {
            return context.Personalizations.Where(x => x.UserName.ToLower() == userName.ToLower() && x.FacilityId == COID && x.DepartmentId == departmentId && x.ParId != null).Select(y => y.ParId).FirstOrDefault();
        }

        public PersonalizationDTO SetFavoritePar(PersonalizationDTO personalizationDTO)
        {
            var sql = @"MERGE INTO Personalizations
                        USING 
                        (
	                      SELECT @userName AS UserName,
                                 @COID AS FacilityId,
                                 @departmentId AS DepartmentId
                        ) AS Entity
                        ON Personalizations.UserName = Entity.UserName
                        AND Personalizations.FacilityId = Entity.FacilityId
                        AND Personalizations.DepartmentId = Entity.DepartmentId
                        AND Personalizations.ParId IS NOT NULL
                        WHEN MATCHED THEN 
	                        UPDATE 
	                        SET ParId = @parId
                        WHEN NOT MATCHED THEN
	                        INSERT (UserName, FacilityId, DepartmentId, ParId)
	                        VALUES (@userName, @COID, @departmentId, @parId)
                        OUTPUT inserted.*;";

            object[] parameters = {
                                    new SqlParameter("@userName", personalizationDTO.UserName),
                                    new SqlParameter("@COID", personalizationDTO.FacilityId),
                                    new SqlParameter("@departmentId", personalizationDTO.DepartmentId),
                                    new SqlParameter("@parId", personalizationDTO.ParId)
                                  };

            return context.Database.SqlQuery<PersonalizationDTO>(sql, parameters).FirstOrDefault();
        }

        public void DeleteFavoritePar(PersonalizationDTO personalizationDTO)
        {
            var favoritePars = context.Personalizations.Where(x => x.UserName.ToLower() == personalizationDTO.UserName.ToLower()
                                                                       && x.FacilityId == personalizationDTO.FacilityId
                                                                       && x.DepartmentId == personalizationDTO.DepartmentId
                                                                       && x.ParId != null);

            if (favoritePars != null && favoritePars.Any())
            {
                context.Personalizations.RemoveRange(favoritePars);
                context.SaveChanges();
            }
        }

        public void UpdateBulkApproverJobTrackerStatus(BulkApproverJobStatusDTO bulkApproverJobStatusDto)
        {
            var bulkApproverJobTracker = GetBulkApproverJobTracker(bulkApproverJobStatusDto.BulkApproverId);

            bulkApproverJobTracker.Status = bulkApproverJobStatusDto.Status;
            bulkApproverJobTracker.EndDateTime = bulkApproverJobStatusDto.JobEndDate;
            bulkApproverJobTracker.ErrorMessage = bulkApproverJobStatusDto.ErrorMessage;

            context.Entry(bulkApproverJobTracker).CurrentValues.SetValues(bulkApproverJobTracker);
            context.SaveChanges();
        }

        public BulkApproverJobTracker GetBulkApproverJobTracker(Guid bulkApproverId)
        {
            return context.BulkApproverJobTrackers.Include(x => x.User).Where(x => x.Id == bulkApproverId).SingleOrDefault();
        }
    }
}
