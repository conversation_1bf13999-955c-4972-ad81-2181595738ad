﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Vendors;
using System;
using System.Linq;

namespace RequisitionServices.DomainModel.Cart.Models
{
    public class CartItemDetails
    {
        // requisition item properties
        public int QuantityInFlight { get; set; }
        public int? QuantityLastOrdered { get; set; }
        public DateTime? DateLastOrdered { get; set; }
        public string FactorDisplay { get; set; }

        // par item properties
        public int MinStock { get; set; }
        public int MaxStock { get; set; }
        public string IssueUom { get; set; }
        public string Location { get; set; }

        // item properties
        public string Description { get; set; }
        public string ManufacturerCatalogNumber { get; set; }
        public bool IsStock { get; set; }
        public bool IsActive { get; set; }
        public int QuantityInStock { get; set; }
        public Vendor Vendors { get; set; }
        public bool ManualOrderFlag { get; set; }
        public int ORUOMFactor { get; set; }
        public string ORUOM { get; set; }

        public CartItemDetails() { }

        public CartItemDetails(ParItemWithLastOrdered parItem, int quantityInFlight)
        {
            QuantityInFlight = quantityInFlight;
            QuantityLastOrdered = parItem.LastOrderedQuantity;
            DateLastOrdered = parItem.LastOrderedDate;
            QuantityInStock = parItem.Item.QuantityAvailable ?? 0;
            MinStock = parItem.MinStock == null ? 0 : (int)parItem.MinStock;
            MaxStock = parItem.MaxStock == null ? 0 : (int)parItem.MaxStock;
            IssueUom = parItem.IssueUOM;
            Location = parItem.Location;
            Description = parItem.Item.Description;
            ManufacturerCatalogNumber = parItem.Item.ManufacturerCatalogNumber;
            IsStock = parItem.Item.IsStock;
            IsActive = parItem.Item.IsActive;
            Vendors = parItem.Item.Vendor;
            ManualOrderFlag = parItem.ManualOrderFlag;
            ORUOMFactor = parItem.Item.ORUOMFactor;
            ORUOM = parItem.Item.ORUOM;

            if (parItem.IssueUOM.ToUpperInvariant() == parItem.Item.PUOM.ToUpperInvariant())
            {
                FactorDisplay = parItem.Item.Factor.ToString();
            }
            else if (parItem.IssueUOM.ToUpperInvariant() == parItem.Item.IUOM.ToUpperInvariant())
            {
                FactorDisplay = 1.ToString();
            }
            else
            {
                var alternateUOM = parItem.Item.AlternateUOMs.Where(x => x.UOM.ToUpperInvariant() == parItem.IssueUOM.ToUpperInvariant()).FirstOrDefault();
                FactorDisplay = alternateUOM != null ? alternateUOM.Factor.ToString() : "?"; // Just in case we're still missing the needed UOM/Factor.
            }
        }
    }
}
