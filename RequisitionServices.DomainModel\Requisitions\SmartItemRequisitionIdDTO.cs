﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class SmartItemRequisitionIdDTO
    {

        public string COID { get; set; }

        public List<ItemRequistionId> ItemRequistionIds { get; set; }

        public SmartItemRequisitionIdDTO()
        {
            ItemRequistionIds = new List<ItemRequistionId>();
        }
    }

    public class ItemRequistionId
    {
        public string ItemNumber { get; set; }
        public string ReorderNumber { get; set; }
        public string DepartmentId { get; set; }
        public int RequisitionId { get; set; }
        public string ParentSystemId { get; set; }
        public int PONumber { get; set; }
        public string STDItemNumber { get; set; }
    }
}
