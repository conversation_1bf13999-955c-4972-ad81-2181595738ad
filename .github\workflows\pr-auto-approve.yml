name: 'PR: Auto-Approve'
on:
  pull_request:
    branches: [ master, develop ]

jobs:
  pr-auto-approval-check:
    runs-on: ubuntu-latest
    steps:
    
    - name: 'User: Check Permissions'
      id: perm-check
      uses: scherermichael-oss/action-has-permission@master
      with:
        required-permission: write
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 'PR: Title Check'
      id: title-check
      continue-on-error: true
      if: steps.perm-check.outputs.has-permission
      uses: deepakputhraya/action-pr-title@master
      with:
        allowed_prefixes: 'Release,Master -> Develop'
        github_token: ${{ secrets.GITHUB_TOKEN }}
        
    - name: 'PR: Approve'
      if: steps.perm-check.outputs.has-permission && steps.title-check.outcome == 'success'
      uses: juliangruber/approve-pull-request-action@v1.0.1
      with:
        number: ${{ github.event.number }}
        github-token: ${{ secrets.GITHUB_TOKEN }}
        
