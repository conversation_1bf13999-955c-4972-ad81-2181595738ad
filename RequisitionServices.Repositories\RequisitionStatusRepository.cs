﻿using RequisitionServices.Database;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.Repositories
{
    public class RequisitionStatusRepository : AbstractRepository, IRequisitionStatusRepository
    {
        public RequisitionStatusRepository(EProcurementContext context) : base(context) { }
          
        public IEnumerable<RequisitionItemStatusHistory> GetItemStatusHistory(int requisitionId)
        {
            return context.RequisitionItemStatusHistories.Where(x => x.RequisitionItem.RequisitionId == requisitionId).ToList();
        }
          
        public IEnumerable<RequisitionStatusHistory> GetStatusHistory(int requisitionId)
        {
            return context.RequisitionStatusHistories.Where(x => x.RequisitionId == requisitionId).ToList();
        }

        public void AddItemStatusHistory(RequisitionItemStatusHistory requisitionItemStatusHistory)
        {
            context.RequisitionItemStatusHistories.Add(requisitionItemStatusHistory);
            context.SaveChanges();
        }

        public void AddItemStatusHistories(IEnumerable<RequisitionItemStatusHistory> requisitionItemStatusHistories)
        {
            context.RequisitionItemStatusHistories.AddRange(requisitionItemStatusHistories);
            context.SaveChanges();
        }

        public void AddStatusHistory(RequisitionStatusHistory requisitionStatusHistory)
        {
            context.RequisitionStatusHistories.Add(requisitionStatusHistory);
            context.SaveChanges();
        }
    }
}
