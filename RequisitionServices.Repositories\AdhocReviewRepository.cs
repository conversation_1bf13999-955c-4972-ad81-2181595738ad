﻿using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Database;

namespace RequisitionServices.Repositories
{
    public class AdhocReviewRepository : AbstractRepository, IAdhocReviewRepository
    {
        static readonly List<int> NotAllowedRequisitionStatuses = new List<int> { 1, 5, 6, 7, 12}; // draft, deleted, denied, submission error, and recalled
              
        public AdhocReviewRepository(EProcurementContext context) : base(context)
        {
        }

        private AdhocReview GetAdhocReview(int id)
        {
            return context.AdhocReviews.Where(x => x.Id == id).FirstOrDefault();
        }

        public string GetRequester(int id)
        {
            return context.AdhocReviews.Where(x => x.Id == id).FirstOrDefault().Requester;
        }

        public void InsertAdhocReview(AdhocReview adhocReview)
        {
            context.AdhocReviews.Add(adhocReview);
            context.SaveChanges();
        }

        public void UpdateAdhocReview(AdhocReview adhocReview)
        {
            var currentAdhocReview = this.GetAdhocReview(adhocReview.Id);            
            
            currentAdhocReview.ReviewDate = adhocReview.ReviewDate;
            currentAdhocReview.ReviewerComments = adhocReview.ReviewerComments;
            currentAdhocReview.Recommended = adhocReview.Recommended;
            currentAdhocReview.ReviewerRequisitionStatusHistory = adhocReview.ReviewerRequisitionStatusHistory;

            context.SaveChanges();
        }

        public IEnumerable<AdhocReview> GetRequisitionAdhocReviews(int requisitionId)
        {
            return context.AdhocReviews.Where(ar => ar.RequisitionId == requisitionId).ToList();
        }

        public bool IsAdhocReviewAllowed(int adhocReviewId, string reviewer, int reqId)
        {
            var query = from ar in context.AdhocReviews
                        join req in context.Requisitions on ar.RequisitionId equals req.RequisitionId
                        where ar.Id == adhocReviewId && ar.Recommended == null
                        && string.Compare(ar.Reviewer, reviewer, true) == 0
                        && ar.RequisitionId == reqId
                        && !NotAllowedRequisitionStatuses.Contains(req.RequisitionStatusTypeId)
                        select ar;

            return query.FirstOrDefault() != null;
        }
    }
}
