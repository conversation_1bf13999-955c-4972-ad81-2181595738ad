﻿using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.DigitalSignOff;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;

namespace RequisitionServices.Repositories
{
    public class DigitalSignOffRepository: IDigitalSignOffRepository
    {
        private readonly ILog log = LogManager.GetLogger(typeof(DigitalSignOffRepository));

        private readonly EProcurementContext _procurementContext;

        public DigitalSignOffRepository(EProcurementContext procurementContext)
        {
            _procurementContext = procurementContext;
        }

        public RequisitionDigitalSignOff GetDigitalSignOff(int requisitionId){
            try{
                return _procurementContext.RequisitionsDigitalSignoff.Select(x => x).Where(x=> x.RequisitionId == requisitionId && !x.IsDeleted).FirstOrDefault();
            } 
            catch (Exception ex)
                {
                log.Error(String.Format("Method: {0}, Unsuccessful call to get digital sign off record", "DigitalSignOffRepository.GetDigitalSignOff"), ex);
                return null;
            }
        }
        public IEnumerable<RequisitionDigitalSignOff> GetAllRequisitionDigitalSignOffsByRequisitionId(int requisitionId)
        {
            try
            {
                return _procurementContext.RequisitionsDigitalSignoff.Select( x => x).Where(x => x.RequisitionId == requisitionId);
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Unsuccessful call to get all digital sign off records", "DigitalSignOffRepository.GetAllRequisitionDigitalSignOffs"), ex);
                return null;
            }
        }

        public RequisitionDigitalSignOff CreateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoff)
        {
            try{
                requisitionDigitalSignoff.CreateDate = DateTime.Now;
                _procurementContext.RequisitionsDigitalSignoff.Add(requisitionDigitalSignoff);
                _procurementContext.SaveChanges();
                return requisitionDigitalSignoff;
            } 
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error saving values into database, UserId and/or RequisitionId cannot be null", "DigitalSignOffRepository.CreateRequisitionDigitalSignOff"), ex);
                return null;
            }
        }
        public void UpdateRequisitionDigitalSignOff(RequisitionDigitalSignOff requisitionDigitalSignoffs, int digitalSignOffId)
        {
            try{
            //Gets the original digital sign off values for the RequisitionDigitalSignOff table before trying the updated values are passed
                RequisitionDigitalSignOff OriginalDigitalSignOff= _procurementContext.RequisitionsDigitalSignoff.Select(x => x).Where(x=> x.Id == digitalSignOffId).FirstOrDefault();           
                if(OriginalDigitalSignOff!=null){
                //Check if one of the column values is different than what is being passed to update.
                    if (OriginalDigitalSignOff.RequisitionId!=requisitionDigitalSignoffs.RequisitionId)
                    {
                        OriginalDigitalSignOff.RequisitionId=requisitionDigitalSignoffs.RequisitionId;
                    } 
                    if (OriginalDigitalSignOff.UserId!=requisitionDigitalSignoffs.UserId)
                    {
                            OriginalDigitalSignOff.UserId=requisitionDigitalSignoffs.UserId;
                    } 
                    if (OriginalDigitalSignOff.SignatureCaptured!=requisitionDigitalSignoffs.SignatureCaptured)
                    {
                        OriginalDigitalSignOff.SignatureCaptured=requisitionDigitalSignoffs.SignatureCaptured;
                    }
                    if (OriginalDigitalSignOff.ADValidated!=requisitionDigitalSignoffs.ADValidated)
                    {
                        OriginalDigitalSignOff.ADValidated=requisitionDigitalSignoffs.ADValidated;
                    }
                    if (OriginalDigitalSignOff.IsDeleted!=requisitionDigitalSignoffs.IsDeleted)
                    {
                        OriginalDigitalSignOff.IsDeleted=requisitionDigitalSignoffs.IsDeleted;
                    }
                _procurementContext.SaveChanges();
                }
            } 
            catch (Exception ex) 
            {
                log.Error(String.Format("Method: {0}, Error adding values into database, UserId and/or RequisitionId cannot be null", "DigitalSignOffRepository.UpdateRequisitionDigitalSignOff"), ex);                
            }
        }  
        

        public void DeleteRequisitionDigitalSignOff(int digitalSignOffId)
        {
            try{
                RequisitionDigitalSignOff DigitalSignOffDeleted = _procurementContext.RequisitionsDigitalSignoff.Select(x => x).Where(x=> x.Id == digitalSignOffId).FirstOrDefault();
                DigitalSignOffDeleted.IsDeleted = true;
                _procurementContext.SaveChanges();    
            }
            catch (Exception ex) 
            {
                log.Error(String.Format("Method: {0}, Error updating IsDeleted in the record", "DigitalSignOffRepository.DeleteRequisitionDigitalSignOff"), ex);
            }
        }

        public void DeleteRequisitionDigitalSignOffOnRecall(int digitalSignOffId, int requisitionId)
        {
            try
            {
                var DSORequisitionSubmissionTypeId = 0;
                RequisitionDigitalSignOff DigitalSignOffDeleted = _procurementContext.RequisitionsDigitalSignoff.Select(x => x).Where(x => x.Id == digitalSignOffId).FirstOrDefault();
                DigitalSignOffDeleted.IsDeleted = true;
                Requisition RequisitionToUpdate = _procurementContext.Requisitions.Select(x => x).Where(y => y.RequisitionId == requisitionId).FirstOrDefault();
                RequisitionToUpdate.RequisitionSubmissionTypeId = DSORequisitionSubmissionTypeId;
                _procurementContext.SaveChanges();
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Error updating IsDeleted in the record", "DigitalSignOffRepository.DeleteRequisitionDigitalSignOffOnRecall"), ex);
            }
        }
        public IEnumerable<DigitalSignOffUser> GetAllDigitalSignOffUsers()
        {
            try{
                return _procurementContext.DigitalSignoffUsers.Select(x => x);
            } 
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Unsuccessful call to get digital sign off users", "DigitalSignOffRepository.GetAllDigitalSignOffUsers"), ex);
                return null;
            }
        }

          public DigitalSignOffUser CreateDigitalSignOffUser(DigitalSignOffUser request)
        {
            try
            {
                request.LastUpdatedUTC = DateTime.Now;
                _procurementContext.DigitalSignoffUsers.Add(request);
                _procurementContext.SaveChanges();
                return request;
            }
            catch (Exception ex)
            {
                log.Error(String.Format("Method: {0}, Unsuccessful call to add digital sign off user", "DigitalSignOffRepository.CreateDigitalSignOffUser"), ex);
                return null;
            }
        }

        public DigitalSignOffUser GetSingleDigitalSignOffUser(string accountName)
        {
            try{
                return _procurementContext.DigitalSignoffUsers.Where(x => x.AccountName == accountName).Select(x => x).First();
            } 
            catch (Exception ex)
                {
                log.Error(String.Format("Method: {0}, Unsuccessful call to get a single user record", "DigitalSignOffRepository.GetSingleDigitalSignOffUser"), ex);
                return null;
            }
        }

        public void UpdateSingleDigitalSignOffUser(DigitalSignOffUser request)
        {
            try{

                // Get single record based off 3/4 ID
                var singleUserRecord = this.GetSingleDigitalSignOffUser(request.AccountName);

                // Set users ID to whats in the DB so it wont be changed
                request.Id = singleUserRecord.Id;

                // Update Record from the request sent
                _procurementContext.Entry(singleUserRecord).CurrentValues.SetValues(request);
                _procurementContext.SaveChanges();
            } 
            catch (Exception ex)
                {
                log.Error(String.Format("Method: {0}, Unsuccessful call to update the record", "DigitalSignOffRepository.GetSingleDigitalSignOffUser"), ex);
            }
        }

        public  ApproverDigitalSignOffDTO GetApproverDigitalSignOff(int requisitionId)
        {
            return (from dso in _procurementContext.RequisitionsDigitalSignoff
                    join dsoUser in _procurementContext.DigitalSignoffUsers on dso.UserId equals dsoUser.Id
                    where dso.IsDeleted != true
                         && dso.RequisitionId == requisitionId
                    select new ApproverDigitalSignOffDTO()
                    {
                        Title = dsoUser.Title,
                        FirstName = dsoUser.FirstName,
                        LastName = dsoUser.LastName,
                        ADValidated = dso.ADValidated,
                        AccountName = dsoUser.AccountName,
                        CreateDate = dso.CreateDate
                    }).FirstOrDefault();
        }
    }
}
