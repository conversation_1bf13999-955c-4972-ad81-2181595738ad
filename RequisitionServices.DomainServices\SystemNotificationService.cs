﻿using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using RequisitionServices.DomainModel.SystemNotifications;

namespace RequisitionServices.DomainServices
{
    public class SystemNotificationService : ISystemNotificationService
    {
        private ISystemNotificationRepository systemNotificationRepository;

        public SystemNotificationService(ISystemNotificationRepository systemNotificationRepo)
        {
            this.systemNotificationRepository = systemNotificationRepo;
        }

        public bool CheckSystemNotificationAuthorization(string userName)
        {
            return systemNotificationRepository.CheckSystemNotificationAuthorization(userName);
        }

        public SystemNotifcationTabInfoDTO GetSystemNotificationTabInfo()
        {
            return new SystemNotifcationTabInfoDTO()
            {
                AdminsWithAuthorization = systemNotificationRepository.GetUsersWithAuthorization(),
                Message = systemNotificationRepository.GetLatestNonExpiredSystemNotification()
            };
        }

        public List<SystemNotificationAdminDTO> SearchNewAdminUsingThreeFour(string searchUserName)
        {
            return systemNotificationRepository.SearchNewAdminUsingThreeFour(searchUserName);
        }

        public List<SystemNotificationAdminDTO> SearchNewAdminUsingCOID(string searchString)
        {
            return systemNotificationRepository.SearchNewAdminUsingCOID(searchString);
        }

        public List<SystemNotificationAdminDTO> UpdateAdminsWithAuthorization(List<SystemNotificationAdminDTO> systemNotificationAdmins)
        {
            return systemNotificationRepository.UpdateAdminsWithAuthorization(systemNotificationAdmins);
        }

        public SystemNotification GetLatestNonExpiredSystemNotification()
        {
            return systemNotificationRepository.GetLatestNonExpiredSystemNotification();
        }

        public SystemNotification SaveNewSystemNotification(SystemNotification notification)
        {
            return systemNotificationRepository.SaveNewSystemNotification(notification);
        }

        public void RemoveOldSystemNotifications()
        {
            systemNotificationRepository.RemoveOldSystemNotifications();
        }
    }
}
