﻿using System;
using RequisitionServices.DomainModel.PurchaseOrders;

namespace RequisitionServices.MMISServices.DTO
{
    public class POHeaderDTO
    {
        public int ParentCOID { get; set; }

        public int COID { get; set; }

        public int PONumber { get; set; }

        public int Vendor { get; set; }

        public DateTime CreatedDate { get; set; }

        public DateTime InvoicedDate { get; set; }

        public DateTime ClosedDate { get; set; }

        public DateTime DateFormatted { get; set; }

        public DateTime DateTransmitted { get; set; }

        public DateTime ConfirmedDate { get; set; }

        public string POStatus { get; set; }

        public int InvoiceNumber { get; set; }

        public Boolean POStatFlag { get; set; }

        public string POType { get; set; }

        public string POMethod { get; set; }

        public string FaxStatus { get; set; }

        public DateTime FaxDate { get; set; }

        public string EDIStatus { get; set; }

        public string EmailStatus { get; set; }

        public DateTime EmailDate { get; set; }

        public decimal Discount { get; set; }

        public int TotalLineItems { get; set; }

        public decimal TotalDollarAmount { get; set; }

        public decimal TotalCreditAmount { get; set; }

        public string BillName { get; set; }

        public string BillAddress1 { get; set; }

        public string BillAddress2 { get; set; }

        public string BillCity { get; set; }

        public string BillState { get; set; }

        public string BillZip { get; set; }

        public string BillCountry { get; set; }

        public string ShipName { get; set; }

        public string ShipAddress { get; set; }

        public string ShipCity { get; set; }

        public string ShipCounty { get; set; }

        public string ShipState { get; set; }

        public string ShipZip { get; set; }

        public string SpecialInstruction1 { get; set; }

        public string SpecialInstruction2 { get; set; }

        public string FacilityShipToCode { get; set; }

        public string BuyerID { get; set; }

        public string PatientName { get; set; }

        public string Comment1 { get; set; }

        public string Comment2 { get; set; }

        public string Comment3 { get; set; }

        public decimal TotalTax { get; set; }

        public int Department { get; set; }

        public bool OutstandingFlag { get; set; }

        public bool BackorderFlag { get; set; }

        public bool CreditFlag { get; set; }

        public bool ReturnFlag { get; set; }

        public decimal TotalCreditExpected { get; set; }

        public POInvoice POInvoice { get; set; }

        public POHeader MapToPOHeader()
        {
            return new POHeader()
            {
                ParentCOID = this.ParentCOID,
                COID = this.COID,
                BuyerID = this.BuyerID,
                ClosedDate = this.ClosedDate,
                Comment1 = this.Comment1,
                Comment2 = this.Comment2,
                Comment3 = this.Comment3,
                ConfirmedDate = this.ConfirmedDate,
                CreatedDate = this.CreatedDate,
                DateFormatted = this.DateFormatted,
                DateTransmitted = this.DateTransmitted,
                Discount = this.Discount,
                EDIStatus = this.EDIStatus,
                EmailDate = this.EmailDate,
                EmailStatus = this.EmailStatus,
                FacilityShipToCode = this.FacilityShipToCode,
                FaxDate = this.FaxDate,
                FaxStatus = this.FaxStatus,
                InvoicedDate = this.InvoicedDate,
                InvoiceNumber = this.InvoiceNumber,
                PatientName = this.PatientName,
                POMethod = this.POMethod,
                PONumber = this.PONumber,
                POStatFlag = this.POStatFlag,
                POStatus = this.POStatus,
                POType = this.POType,
                SpecialInstruction1 = this.SpecialInstruction1,
                SpecialInstruction2 = this.SpecialInstruction2,
                TotalCreditAmount = this.TotalCreditAmount,
                TotalDollarAmount = this.TotalDollarAmount,
                TotalLineItems = this.TotalLineItems,
                TotalTax = this.TotalTax,
                Vendor = this.Vendor,
                Department = this.Department,
                OutstandingFlag = this.OutstandingFlag,
                BackorderFlag = this.BackorderFlag,
                CreditFlag = this.CreditFlag,
                ReturnFlag = this.ReturnFlag,
                BillAddress = new DomainModel.Locations.Address()
                {
                    AddressName = this.BillName,
                    Address1 = this.BillAddress1,
                    Address2 = this.BillAddress2,
                    City = this.BillCity,
                    State = this.BillState,
                    Zip = this.BillZip
                },
                ShipAddress = new DomainModel.Locations.Address()
                {
                    AddressName = this.ShipName,
                    Address1 = this.ShipAddress,
                    City = this.ShipCity,
                    State = this.ShipState,
                    Zip = this.ShipZip
                },
                TotalCreditExpected = this.TotalCreditExpected,
                POInvoice = this.POInvoice
            };
        }
    }
}