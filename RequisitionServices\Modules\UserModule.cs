﻿using System;
using System.Web;
using log4net;
using Smart.Core.Common;
using Smart.Core.Common.Utilities;

namespace RequisitionServices.Modules
{
    public class UserModule : IHttpModule
    {
        public void Init(HttpApplication context)
        {
            context.PostAuthenticateRequest += HandlePostAuthenticateRequest;
        }

        public void Dispose()
        {
        }

        static void HandlePostAuthenticateRequest(object sender, EventArgs e)
        {
            if (HttpContext.Current?.User?.Identity?.Name.TrimOrNull() != null)
            {
                LogicalThreadContext.Properties[Constants.Logging.User] = HttpContext.Current.User.Identity.Name;
            }
        }
    }
}