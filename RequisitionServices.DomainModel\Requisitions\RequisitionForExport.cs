﻿using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    [NotMapped]
    public class RequisitionForExport : Requisition
    {
        [NotMapped]
        public string CreatedByFullName { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the badge in exists.
        /// </summary>
        public int? BadgeInStatusId { get; set; }
    }
}
