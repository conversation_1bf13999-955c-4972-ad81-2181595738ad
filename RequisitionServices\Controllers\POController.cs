﻿using Microsoft.Web.Http;
using RequisitionServices.DomainModel.PurchaseOrders;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Utility;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Web.Http;

namespace RequisitionServices.Controllers
{

    [ApiVersion("1.0", Deprecated = true)]
    [ApiVersion("2.0")]
    public class POController : ApiController
    {
        private IPOService POService;

        public POController(IPOService POService)
        {
            this.POService = POService;
        }

        /// <summary>
        /// Retrieves all the details related to COID and PONumber
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="PONumber"></param>
        /// <param name="stockIndicator"></param>
        [HttpGet, MapToApiVersion("1.0"), MapToApiVersion("2.0")]
        [Route("PO/GetDetailsByPO")]
        [Route("v{version:apiVersion}/PO/GetDetailsByPO")]
        public PODetails GetDetailsByPO(string userName, string COID, string PONumber, string stockIndicator = "")
        {
            var versionNumber = Request.GetRequestedApiVersion();
            try
            {
                return POService.GetDetailsByPO(userName, COID, PONumber, stockIndicator, versionNumber);
            }
            catch (BadRequestException ex)
            {

                if (versionNumber.MajorVersion > 1)
                {
                    var response = new HttpResponseMessage(HttpStatusCode.BadRequest) {
                        ReasonPhrase = ex.Message
                    };
                    throw new HttpResponseException(response);
                }
                throw;
            }
            catch (Exception)
            {
                throw;
            }

        }

        /// <summary>
        /// Retrieves all Confirmation Details associated with an item in a PO
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="PONumber"></param>
        /// <param name="lineNumber"></param>
        [HttpGet]
        public List<POConfirmationDetail> GetPOConfirmationDetails(string userName, string COID, string PONumber, string lineNumber)
        {
            return POService.GetPOConfirmationDetails(userName, COID, PONumber, lineNumber);
        }

        /// <summary>
        /// Retrieves all the PO details related to Date Range and COID
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="COID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="department"></param>
        /// <returns></returns>
        [HttpGet]
        public List<POLists> getDetailsByDateRange(string userName, string COID, DateTime startDate, DateTime endDate, int department)
        {
            var returnList = POService.GetDetailsByDateRange(userName, COID, startDate, endDate, department);

            return returnList;
        }

        /// <summary>
        /// Gets PONumbers based on a Date Range
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="coid"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="poType"></param>
        /// <param name="department"></param>
        /// <param name="reorderNumber"></param>
        /// <returns>Returns POOption</returns>
        [HttpGet]
        public List<POOptions> GetPoByOptions(string userName, string coid, DateTime startDate, DateTime endDate, string poType, int department, string reorderNumber)
        {
            var returnList = POService.GetPoByOptions(userName, coid, startDate, endDate, poType, department, reorderNumber);

            return returnList;
        }

        /// <summary>
        /// Returns POs that have items on it that are for a project number
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="coid"></param>
        /// <param name="projectNumber"></param>
        /// <returns></returns>
        [HttpGet]
        public List<POLists> GetPoByProjectNumber(string userName, string coid, string projectNumber)
        {
            return POService.GetPoByProjectNumber(userName, coid, projectNumber);
        }

        [HttpGet]
        public List<POHistory> GetHistoryByPO(string userId, string coid, string poNumber)
        {
            var returnList = POService.GetHistoryByPO(userId, coid, poNumber);

            return returnList;
        }
    }
}