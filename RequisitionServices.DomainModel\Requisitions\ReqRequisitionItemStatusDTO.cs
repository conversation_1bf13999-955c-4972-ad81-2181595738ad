﻿using Newtonsoft.Json;
using Newtonsoft.Json.Schema;
using RequisitionServices.DomainModel.Enum;
using System;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class ReqRequisitionItemStatusDTO : BaseRequisitionItemStatusDTO
    {

        /// <summary>
        /// Par Identifier
        /// </summary>
        [JsonProperty("ParIdentifier", Required = Required.Always)]
        public string ParIdentifier { get; set; }


        /// <summary>
        /// Original Parent System Id
        /// </summary>
        [JsonProperty("OriginalParentSystemId", Required = Required.AllowNull)]
        public string OriginalParentSystemId { get; set; }

        /// <summary>
        /// ItemId
        /// </summary>
        [JsonProperty("ItemId", Required = Required.Always)]
        public string ItemId { get; set; }

        /// <summary>
        /// Quantity fulfilled
        /// </summary>
        [JsonProperty("QuantityFulfilled", Required = Required.Always)]
        public int QuantityFulfilled { get; set; }


        /// <summary>
        /// Requisition Scheduled Date
        /// </summary>
        [JsonProperty("RequisitionScheduledDate", Required = Required.AllowNull)]
        public DateTime? RequisitionScheduledDate { get; set; }

        /// <summary>
        /// Sequence
        /// </summary>
        [JsonProperty("Sequence", Required = Required.Always)]
        public int Sequence { get; set; }

        public override RequisitionItemStatusTypeEnum GetRequisitionItemStatusType()
        {
            RequisitionItemStatusTypeEnum itemStatusType = RequisitionItemStatusTypeEnum.Unknown;

            switch (this.RequisitionItemStatusTypeId.ToLower())
            {
                case "n":
                    itemStatusType = RequisitionItemStatusTypeEnum.NeedToPrint;
                    break;
                case "p":
                    itemStatusType = RequisitionItemStatusTypeEnum.BeingPulled;
                    break;
                case "a":
                    itemStatusType = RequisitionItemStatusTypeEnum.AddedToPrevious;
                    break;
                case "o":
                    itemStatusType = RequisitionItemStatusTypeEnum.RequisitionOpen;
                    break;
                case "i":
                    itemStatusType = RequisitionItemStatusTypeEnum.POInProcess;
                    break;
                case "c":
                    itemStatusType = RequisitionItemStatusTypeEnum.POCreated;
                    break;
                case "r":
                    itemStatusType = this.GetPartialFillRequisitionStatus(this.QuantityToOrder, this.QuantityFulfilled, RequisitionItemStatusTypeEnum.ItemReceived);
                    break;
                case "w":
                    itemStatusType = RequisitionItemStatusTypeEnum.NeedToSendToWH;
                    break;
                case "s":
                    itemStatusType = RequisitionItemStatusTypeEnum.SentToWH;
                    break;
                case "x":
                    itemStatusType = this.GetPartialFillRequisitionStatus(this.QuantityToOrder, this.QuantityFulfilled, RequisitionItemStatusTypeEnum.RequisitionFilled);
                    break;
                case "0":
                    itemStatusType = RequisitionItemStatusTypeEnum.Processing;
                    break;
                case "t":
                    itemStatusType = RequisitionItemStatusTypeEnum.Scheduled;
                    break;
                default:
                    itemStatusType = RequisitionItemStatusTypeEnum.Unknown;
                    break;
            }

            return itemStatusType;
        }
        public override void UpdateRequisitionItemStatus(RequisitionItem reqItem)
        {
            RequisitionItemStatusTypeEnum itemStatusType = this.GetRequisitionItemStatusType();
            reqItem.RequisitionItemStatusTypeId = (int)itemStatusType;
            if (this.RequisitionScheduledDate.HasValue)
            {
                reqItem.RequisitionScheduledDate = this.GetRequisitionScheduledDate();
            }

            if (!string.IsNullOrEmpty(this.OriginalParentSystemId) && int.Parse(this.OriginalParentSystemId) > 0)
            {
                reqItem.OriginalParentSystemId = this.OriginalParentSystemId;
            }

            SetupCommonItems(reqItem);
        }

        public override string GetRequisitionItemStatusComments()
        {
            var itemStatusType = this.GetRequisitionItemStatusType();
            string comments = null;

            switch (itemStatusType)
            {
                case RequisitionItemStatusTypeEnum.AddedToPrevious:
                    comments = "Added to previous req from " + this.OriginalParentSystemId;
                    break;
            }

            return comments;
        }

        public override void SetupCommonItems(RequisitionItem reqItem)
        {
            base.SetupCommonItems(reqItem);
            reqItem.QuantityFulfilled = this.QuantityFulfilled;
        }

        private RequisitionItemStatusTypeEnum GetPartialFillRequisitionStatus(int orderQuantity, int issuedQuantity, RequisitionItemStatusTypeEnum currentStatus)
        {
            if (issuedQuantity >= orderQuantity)
            {
                return currentStatus;
            }

            return RequisitionItemStatusTypeEnum.NoPartialFill;
        }

        public DateTimeOffset GetRequisitionScheduledDate()
        {
            return new DateTimeOffset(RequisitionScheduledDate.Value).ToOffset(TimeSpan.FromHours(UTCOffset));
        }

        public static JSchema GetReqSchema()
        {
            return GenerateSchemaForClass(typeof(ReqRequisitionItemStatusDTO));
        }
    }
}
