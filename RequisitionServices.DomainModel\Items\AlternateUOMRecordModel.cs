﻿using System;

namespace RequisitionServices.DomainModel.Items
{
    public class AlternateUOMRecordModel : IEquatable<AlternateUOMRecordModel>
    {
        public string AUOM { get; set; }
        public int Factor { get; set; }
        public decimal Price { get; set; }

        public bool Equals(AlternateUOMRecordModel other)
        {
            if (ReferenceEquals(other, null)) return false;

            if (ReferenceEquals(this, other)) return true;

            return AUOM.Equals(other.AUOM)
                   && Factor.Equals(other.Factor)
                   && Price.Equals(other.Price);
        }

        public override int GetHashCode()
        {
            int hashAUOM = null == AUOM ? 0 : AUOM.GetHashCode();
            int hashFactor = Factor.GetHashCode();
            int hashPrice = Price.GetHashCode();

            return hashAUOM ^ hashFactor ^ hashPrice;
        }
    }
}
