﻿using RequisitionServices.Database;
using RequisitionServices.Repositories.Interfaces;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Data.SqlClient;
using RequisitionServices.DomainModel.Enum;
using System;
using RequisitionServices.DomainModel.Comments;

namespace RequisitionServices.Repositories
{
    public class CommentRepository : AbstractRepository, ICommentRepository
    {
        public CommentRepository(EProcurementContext context) : base(context)
        {
        }

        public List<Comment> Get(int requisitionId)
        {
            var comments = context.Comments
                .Where(c => c.RequisitionId == requisitionId)
                .Include(c => c.User);

            return comments.ToList();
        }

        public Comment Add(Comment comment)
        {
            context.Comments.Add(comment);
            context.SaveChanges();

            return comment;
        }

        public List<CommentNotificationRequisitionRow> GetNotifications(string username, int rowOffset, int pageSize, RequisitionSortOrder sortOrder, string filterText)
        {
            var sqlParams = new List<SqlParameter>
            {
                new SqlParameter("@username", username) { DbType = DbType.String }
            };

            sqlParams.Add(GetFilterTextParameter(filterText));
            sqlParams.AddRange(GetPaginationParameters(rowOffset, pageSize));
            sqlParams.AddRange(GetRequisitionSortingParameters(sortOrder));

            return context.Database.SqlQuery<CommentNotificationRequisitionRow>("exec dbo.usp_GetUnreadComments @username, @rowOffset, @pageSize, @filterText, @reqTypeIdGroup, @statusSorting, @oldestFirst, @vboFirst", sqlParams.ToArray()).ToList();
        }

        public void AddNotifications(List<UnreadComment> unreadComments)
        {
            context.UnreadComments.AddRange(unreadComments);
            context.SaveChanges();
        }

        public void RemoveNotifications(int requisitionId, int userId)
        {
            context.UnreadComments.RemoveRange(context.UnreadComments.Where(x => x.RequisitionId == requisitionId && x.UserId == userId));
            context.SaveChanges();
        }

        private SqlParameter GetFilterTextParameter(string filterText)
        {
            var filterParam = filterText == null ? new SqlParameter("@filterText", DBNull.Value) : new SqlParameter("@filterText", filterText);
            filterParam.DbType = DbType.String;
            return filterParam;
        }

        private List<SqlParameter> GetPaginationParameters(int rowOffset, int pageSize)
        {
            return new List<SqlParameter>
            {
                new SqlParameter("@rowOffset", rowOffset) { DbType = DbType.Int32 },
                new SqlParameter("@pageSize", pageSize) { DbType = DbType.Int32 }
            };
        }

        private List<SqlParameter> GetRequisitionSortingParameters(RequisitionSortOrder sortOrder)
        {
            var parameters = new List<SqlParameter>
            {
                new SqlParameter("@statusSorting", sortOrder == RequisitionSortOrder.Status) { DbType = DbType.Boolean },   // 0 = status sorting OFF, 1 = status sorting ON
                new SqlParameter("@oldestFirst", sortOrder == RequisitionSortOrder.Oldest) { DbType = DbType.Boolean },
                new SqlParameter("@vboFirst", sortOrder == RequisitionSortOrder.VBO) { DbType = DbType.Boolean },
            };

            var table = new DataTable("@reqTypeIdGroup");
            table.Columns.Add("Id", typeof(int));
            switch (sortOrder)
            {
                case RequisitionSortOrder.Rush:
                    table.Rows.Add(RequisitionTypeEnum.Rush);
                    break;
                case RequisitionSortOrder.Capital:
                    table.Rows.Add(RequisitionTypeEnum.Capital);
                    break;
                case RequisitionSortOrder.BillOnlyBillReplace:
                    table.Rows.Add(RequisitionTypeEnum.BillOnly);
                    table.Rows.Add(RequisitionTypeEnum.BillAndReplace);
                    table.Rows.Add(RequisitionTypeEnum.CapitatedBillOnly);
                    table.Rows.Add(RequisitionTypeEnum.CapitatedBillAndReplace);
                    break;
                default:
                    table.Rows.Add(999); // by default this should not equal any existing req type ID
                    break;
            }
            parameters.Add(new SqlParameter("@reqTypeIdGroup", table) { SqlDbType = SqlDbType.Structured, TypeName = "dbo.IdTemplate" });

            return parameters;
        }
    }
}