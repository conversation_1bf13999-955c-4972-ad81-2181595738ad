﻿using RequisitionServices.DomainModel.Locations;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;

namespace RequisitionServices.MMISServices
{
    public class SmartLocationService : ISmartLocationService
    {
        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");
        private const string updateLocator = "Location/UpdateLocator/";
        
        public SmartLocationService()
        {

        }

        public Locator UpdateLocator(string userId, Locator locator)
        {
            if (String.IsNullOrWhiteSpace(userId))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userId.IndexOf('/') != -1)
            {
                userId = userId.Split('/')[1];
            }

            var updateLocatorResult = ApiUtility.ExecuteApiPostWithContentTo<Locator>(endpoint, updateLocator, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userId }
                                                                            }, locator);
            
            return updateLocatorResult;
        }
    }
}
