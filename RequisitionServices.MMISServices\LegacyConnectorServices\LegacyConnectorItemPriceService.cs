﻿using log4net;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.LegacyConnectorInterfaces;
using RequisitionServices.Utility.LegacyConnector;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.MMISServices.LegacyConnectorServices
{
    public class LegacyConnectorItemPriceService : ILegacyConnectorItemPriceService
    {

        readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        readonly string _endpoint = ConfigurationManager.AppSettings.Get("ItemPriceLegacyConnectorAPIUrl");
        private const string GetItembyVendorReorderMethod = "ItemContract/ItembyVendorReorder";

        /// <summary>
        /// Get Item Price  details for the vendor Id or re-order number
        /// </summary>
        /// <param name="username">User Id</param>
        /// <param name="COID"> Facility number</param>
        /// <param name="reOrderNumber">Item re-order||vendor part number</param>
        /// <param name="vendorId">Vendor Id</param>
        /// <returns></returns>
        public ItemPriceDetails GetItemPrice(string username, string COID, string reOrderNumber, string vendorId)
        {
            ItemPriceDetails itemPriceDetails = new ItemPriceDetails();
            LegacyConnectorTokenGenerator tokenGenerator = new LegacyConnectorTokenGenerator();

            try
            {
                this.CheckUserName(ref username);

                var authToken = tokenGenerator.GetLegacyConnectorClientAuthToken<dynamic>();

                Dictionary<string, string> queryParameters = new Dictionary<string, string>()
                                                                            {
                                                                                { "Guid", Guid.NewGuid().ToString() },
                                                                                { "userId", username },
                                                                                { "coid", COID },
                                                                                { "reorder", reOrderNumber },
                                                                                { "vendor", vendorId }
                                                                            };

                
                Dictionary<string, string> requestHeaders = new Dictionary<string, string>()
                                                                            {
                                                                                {"Authorization", "Bearer " + authToken.access_token }
                                                                            };
            
                var itemPriceDetailsResponse = LegacyConnectorUtility.ExecuteApiGetTo<ItemPriceDetails>(_endpoint, GetItembyVendorReorderMethod, queryParameters, requestHeaders);
                if(itemPriceDetailsResponse != null)
                {
                    if(itemPriceDetailsResponse.errors == null && itemPriceDetailsResponse.fault == null)
                    {
                        itemPriceDetails = itemPriceDetailsResponse;
                        itemPriceDetails.IsDataPresent = true;
                        itemPriceDetails.LegacyConnectorAPICallIsSuccess = true;

                    }
                    else
                    {
                        HandleLegacyConnectorErrors(itemPriceDetailsResponse.errors, itemPriceDetailsResponse.fault, itemPriceDetails);
                        itemPriceDetails.LegacyConnectorAPICallIsSuccess = false;
                    }
                }
                else 
                {
                    itemPriceDetails.IsDataPresent = false;
                    itemPriceDetails.LegacyConnectorAPICallIsSuccess = true;
                    itemPriceDetails.Warning = $"No data available for this Coid/Vendor/Reorder number - {COID}/{vendorId}/{reOrderNumber}";
                }
            }
            catch (Exception ex)
            {
                _log.Error("Exception calling Legacy connector", ex);
                itemPriceDetails.IsDataPresent = false;
                itemPriceDetails.LegacyConnectorAPICallIsSuccess = false;
                itemPriceDetails.Warning = $"Legacy Connector is unable to give response. Check Log for more details";
            }
            return itemPriceDetails;
        }

        /// <summary>
        /// Sets the Username
        /// <para>This method isolates the user name without the domain name and truncates for seven characters</para>
        /// <para>Truncating seven characters is only written and used for consuming legacy connector API</para>
        /// </summary>
        /// <param name="userName">User Id</param>
        /// <returns>User name without the domain</returns>
        private void CheckUserName(ref string userName)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }
            userName = userName.Length <= 7 ? userName : userName.Substring(0, 7);
        }

        /// <summary>
        /// Sets the warning message 
        /// </summary>
        /// <param name="errors">Bad request error responses</param>
        /// <param name="fault"> 401,404 error responses</param>
        /// <param name="itemPriceDetails">Item price detail object to be sent as response</param>
        /// <returns>The error|fault responses are assigned as warning message in itemPriceDetails response object</returns>
        private void HandleLegacyConnectorErrors(dynamic errors,dynamic fault, ItemPriceDetails itemPriceDetails)
        {
            StringBuilder legacyConnectorError = new StringBuilder();
            if (errors != null)
            {
                if (errors.coid != null && errors.coid.Count > 0)
                    legacyConnectorError.AppendLine("Coid - "+errors.coid[0]);

                if (errors.userId != null && errors.userId.Count > 0)
                    legacyConnectorError.AppendLine("userId - " + errors.userId[0]);

                if (errors.vendor != null && errors.vendor.Count > 0)
                    legacyConnectorError.AppendLine("vendor - " + errors.vendor[0]);

                if (errors.reorder != null && errors.reorder.Count > 0)
                    legacyConnectorError.AppendLine("reorder - " + errors.reorder[0]);

                itemPriceDetails.Warning = legacyConnectorError.ToString();
            }
            if(fault != null)
            {
                if (fault.faultstring != null)
                    legacyConnectorError.AppendLine("Legacy Error - " + fault.faultstring);
                itemPriceDetails.Warning = legacyConnectorError.ToString();
            }
        }
    }
}
