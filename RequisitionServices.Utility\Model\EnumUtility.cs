﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Reflection;

namespace RequisitionServices.Utility.Model
{
    public static class EnumUtility
    {
        public static string GetEnumDescription(Enum value)
        {
            FieldInfo fi = value.GetType().GetField(value.ToString());

            DescriptionAttribute[] attributes = (DescriptionAttribute[])fi.GetCustomAttributes(typeof(DescriptionAttribute), false);

            if (attributes != null && attributes.Length > 0)
            {
                return attributes[0].Description;
            }
            else
            {
                return value.ToString();
            }
        }

        public static IEnumerable<T> GetValues<T>()
        {
            return Enum.GetValues(typeof(T)).Cast<T>();
        }

        public static int ToInt(this Enum enumValue)
        {
            return (int)((object)enumValue);
        }

        public static IEnumerable<String> GetDescriptions<T>()
        {
            var values = Enum.GetValues(typeof(T)).OfType<Enum>();
            if (values == null)
            {
                return null;
            }

            var descriptions = new List<String>();
            foreach (var value in values)
            {
                descriptions.Add(GetEnumDescription(value));
            }

            return descriptions;
        }
    }
}
