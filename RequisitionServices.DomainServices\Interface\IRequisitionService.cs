﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using System;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IRequisitionService
    {
        Requisition SaveRequisition(Requisition requisition, string userName, bool isMQEnabled, bool isFromSMARTResponse);

        Requisition SaveRequisitionAsApprover(Requisition requisition, string userName, bool isMQEnabled, bool isFromSMARTResponse);

        Requisition SubmitRequisition(Requisition requisition, string userName, bool requesterIsApprover, long? cartId);

        Requisition GetRequisition(string userName, int requisitionId);

        RequisitionWithDetailsDTO GetRequisitionWithDetails(int requisitionId, string userName);

        RequisitionWithDetailsDTO GetRequisitionWithDetails(int requisitionId, string coid, string userName);

        RequisitionWithDetailsDTO GetRequisitionForVendorUser(int requisitionId, string coid, string username, List<int> vendorAffiliations);

        RequisitionWithDetailsDTO GetLegacyRequisition(int requisitionId, string coid, string countryCode, string userName);

        RequisitionListMultiResultsDTO GetMyRequisitionsResults(string username, RequisitionListRequestDto leftSide, RequisitionListRequestDto rightSide);

        ApprovalListMultiResultsDTO GetMyApprovalsResults(string username, RequisitionListRequestDto leftSide, RequisitionListRequestDto rightSide);

        ApprovalListResultsDTO GetUpcomingApprovalsForApprover(RequisitionListRequestDto request);

        List<Requisition> GetRequisitionsByDateRange(string COID, DateTime startDate, DateTime endDate, int departmentId, string userName);

        List<Requisition> GetRequisitionsWithItemStatusesByDateRange(string userName, string COID, DateTime startDate, DateTime endDate, int departmentId);

        RequisitionListResultsDTO GetRequisitionsByVendor(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetRequisitionsByVendorReportExport(RequisitionReportRequestDto request);

        RequisitionListResultsDTO GetRequisitionsForReport(RequisitionReportRequestDto request);

        RequisitionListResultsDTO GetVBORequisitionsForReport(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetRequisitionsForReportExport(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetVBORequisitionsForReportExport(RequisitionReportRequestDto request);

        RequisitionListResultsDTO GetRequisitionsForReportByItemNumber(RequisitionReportRequestDto request);

        RequisitionReportExportResultsDTO GetRequisitionsForReportByItemNumberExport(RequisitionReportRequestDto request);

        StatusUpdateDTO UpdateRequisitionStatus(RequisitionStatusHistory requisitionStatusHistory);

        bool SmartLegacySubmissionAvailabilityCheck(string userId, string coid);

        List<RequisitionStatusHistory> GetRequisitionHistory(int requisitionId);

        void AdvanceRequisition(RequisitionAdvanceDTO requisitionAdvanceDTO);

        bool RequestAdhocReview(AdhocReviewDTO adhocReviewDTO);

        bool ProvideAdhocReview(AdhocReviewDTO adhocReviewDTO);

        bool UpdatePONumber(RequisitionItem requisitionItemDB, RequisitionItem requisitionItemSmart);

        IEnumerable<AdhocReviewDTO> GetRequisitionAdhocReviews(int requisitionId);

        bool IsAdhocReviewAllowed(int adhocReviewId, string reviewerName, int reqId);

        bool CheckIfSaveable(Requisition requisitionObject);

        bool DeleteAttachment(FileNamesDTO files);

        UpdateRequisitionItemStatusResultDTO UpdateReqRequisitionItemStatus(ReqRequisitionItemStatusDTO reqItemStatusDTO);

        UpdateRequisitionItemStatusResultDTO UpdateSprRequisitionItemStatus(SprRequisitionItemStatusDTO sprItemStatusDTO);

        void UpdateRequisitionStatusToSubmittedIfNeeded(BaseRequisitionItemStatusDTO messageFromMQ);

        InFlightQty GetInFlightQuantity(string userName, string coid, int dept, string parClass, string itemId);

        LegacyRequisitionReportDTO GetLegacyRequisitionReport(LegacyRequisitionReportRequestDTO request);

        LastOrderDetailsDTO GetLastOrderDetails(string coid, string dept, string parClass, string itemId);

        SmartItemRequisitionIdDTO GetSmartItemRequisitionIdByPONumber(SmartItemRequisitionIdDTO smartItemRequisitionIdDTO);

        RequisitionWithDetailsDTO GetWithDetailsDTO(Requisition requisition, string userName);

        /// <summary>
        /// Retrieves a queue of purchasing requisitions for the purchasing report.
        /// </summary>
        /// <param name="reportParameters">The parameters for the purchasing requisition report.</param>
        /// <returns>A <see cref="PurchasingRequisitionReportResultsDTO"/> containing the list of requisitions and the total count.</returns>
        PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport(PurchasingRequisitionReportParameters reportParameters);

        /// <summary>
        /// GetAdvancedFiltersForPurchasingReport method is used to get the advanced filters value for the Purchasing
        /// </summary>
        /// <param name="filterList"></param>
        /// <returns>multiple lists of values</returns>
        RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList);

        /// <summary>
        /// Retrieves a list of requisitions based on the provided purchase order number and COID.
        /// </summary>
        /// <param name="poNumber">The purchase order number.</param>
        /// <param name="coid">The facility ID.</param>
        /// <returns>A list of <see cref="RequisitionDTO"/> objects that match the specified criteria.</returns>
        List<RequisitionDTO> GetRequisitionAndItemsByPONumber(int poNumber, string coid);

        /// <summary>
        /// Retrieves a list of requisitions based on the provided purchase order number and COID.
        /// </summary>
        /// <param name="poNumber">The purchase order number.</param>
        /// <param name="coid">The facility ID.</param>
        /// <returns>A list of <see cref="RequisitionDTO"/> objects that match the specified criteria.</returns>
        Requisition HydrateBillOnlyReviewRequisitionWithParentSystemDetails(string userName, Requisition requisition);
    }
}
