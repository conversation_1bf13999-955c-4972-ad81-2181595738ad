﻿using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainModel.Enum;

namespace RequisitionServices.DomainModel.Cart.Responses
{
    public class AddToCartResponse
    {
        public AddToCartStatus Status { get; set; }
        public int CartQuantity { get; set; }
        public CartItem ExistingItem { get; set; }
        public long CartId { get; set; }
        public int CartItemCount { get; set; }
    }
}
