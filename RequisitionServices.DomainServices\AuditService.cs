﻿using log4net;
using RequisitionServices.DomainModel.Audit;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Reflection;
using RequisitionServices.DomainModel.Enum;
using System.Text;
using System.Xml.Serialization;
using System.IO;
using System.Xml;
using System.Threading.Tasks;

namespace RequisitionServices.DomainServices
{
    public class AuditService : IAuditService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private IAuditRepository auditRepository;
        private IUserRepository userRepository;

        public AuditService(IAuditRepository auditRepo, IUserRepository userRepo)
        {
            this.auditRepository = auditRepo;
            this.userRepository = userRepo;
        }

        public void UpdateApproverChangeLogList(IEnumerable<Approver> newApproverInfo, int auditEventType, int userid)
        {
            try
            {
                var auditChangeLogData = createApproverChangeLogList(newApproverInfo, auditEventType, userid);

                //Updating to database is a void method. So nothing to await. Hence wrapping it as a task.run and awaiting for it.
                TaskCompletionSource<bool> WriteToTable = new TaskCompletionSource<bool>();
                Task.Run(async () =>
                {
                    auditRepository.UpdateAuditChangeLog(auditChangeLogData);
                    await WriteToTable.Task;
                });
            }
            catch (Exception ex)
            {
                log.Error(ex);
            }
}

        private IEnumerable<AuditEventsDTO> createApproverChangeLogList(IEnumerable<Approver> newApproverInfoList, int auditEventType, int userid)
        {
            StringBuilder CommentInformation = new StringBuilder();
            List<AuditEventsDTO> approverChangeLogList = new List<AuditEventsDTO>();
            int AdminUserId = 0, EditedUserId = 0;
            string delegateName = string.Empty;

            if ((auditEventType == (int)AuditEventTypeEnum.DelegateAdded) || (auditEventType == (int)AuditEventTypeEnum.DelegateDeleted))
            {
                var DelegateInfo = userRepository.GetUser(userid);
                if(DelegateInfo!=null)
                {
                    delegateName = DelegateInfo.FirstName + " " + DelegateInfo.LastName;
                }
            }

            #region Adding Comment Messages
            foreach (Approver newApproverInfo in newApproverInfoList)
            {
                if (newApproverInfo != null)
                {
                    switch (auditEventType)
                    {
                        case (int)AuditEventTypeEnum.StandardApprovalAmountChanged:
                            CommentInformation.Append("Standard Approval Amount was set to " + newApproverInfo.MaxApprovalAmount.ToString() +
                               " for COID " + newApproverInfo.COID.ToString());
                            AdminUserId = userid;
                            //Get the approver's Id
                            EditedUserId = newApproverInfo.UserId;
                            break;
                        case (int)AuditEventTypeEnum.CapitalApprovalAmountChanged:
                            CommentInformation.Append("Capital Approval Amount was set to " + newApproverInfo.CapitalMaxApprovalAmount.ToString() +
                                                    " for COID " + newApproverInfo.COID.ToString());
                            AdminUserId = userid;
                            //Get the approver's Id
                            EditedUserId = newApproverInfo.UserId;
                            break;
                        case (int)AuditEventTypeEnum.DelegateAdded:
                            CommentInformation.Append(delegateName + " was Added as a Delegate for COID " + newApproverInfo.COID.ToString());
                            AdminUserId = newApproverInfo.UserId;
                            //Action happening on same user
                            EditedUserId = newApproverInfo.UserId;
                            break;
                        case (int)AuditEventTypeEnum.DelegateDeleted:
                            CommentInformation.Append(delegateName + " was Removed as a Delegate for COID " + newApproverInfo.COID.ToString());
                            AdminUserId = newApproverInfo.UserId;
                            //Action happening on same user
                            EditedUserId = newApproverInfo.UserId;
                            break;
                    }

                    var auditChangeXML = createChangeXMLApprover(AdminUserId, EditedUserId, CommentInformation.ToString());
                    //Build the ChangeLog for sending to database.
                    AuditEventsDTO changeLog = new AuditEventsDTO();
                    changeLog.AdminUserID = AdminUserId;
                    changeLog.Entity = EntityTypes.entityTableNames["Approver"];
                    changeLog.EditedUserID = EditedUserId;
                    changeLog.COID = newApproverInfo.COID;
                    changeLog.AuditEventType = auditEventType;
                    changeLog.CreatedDateTime = DateTime.Now;
                    changeLog.Comments = CommentInformation.ToString();
                    changeLog.ChangeXML = this.Serialize(auditChangeXML);
                    approverChangeLogList.Add(changeLog);

                    //Clear the String builder information
                    CommentInformation.Clear();
                }
            }
            #endregion

            return approverChangeLogList;
        }
        
        private string Serialize<T>(T toSerialize)
        {
            //Remove XML declarations since we are only storing change information from column data
            XmlWriterSettings settings = new XmlWriterSettings();
            settings.OmitXmlDeclaration = true;
            settings.Indent = true;

            MemoryStream ms = new MemoryStream();
            XmlWriter writer = XmlWriter.Create(ms, settings);

            XmlSerializerNamespaces ns = new XmlSerializerNamespaces(new[] { XmlQualifiedName.Empty });

            XmlSerializer mySerializer = new XmlSerializer(typeof(T));

            mySerializer.Serialize(writer, toSerialize, ns);
            ms.Flush();
            ms.Seek(0, SeekOrigin.Begin);

            StreamReader sr = new StreamReader(ms);

            return sr.ReadToEnd().ToString();
        }

        private AuditEventsChange createChangeXMLApprover(int ActorId, int SubjectId, string change)
        {
            var actorUser = userRepository.GetUser(ActorId);
            var subjectUser = userRepository.GetUser(SubjectId);

            AuditEventsChange auditChangXML = new AuditEventsChange();
            auditChangXML.Actor = new Actor();
            auditChangXML.Subject = new Subject();

            auditChangXML.Actor.Name = actorUser.FirstName + " " + actorUser.LastName;
            auditChangXML.Subject.Name = subjectUser.FirstName + " " + subjectUser.LastName;

            auditChangXML.Subject.Change = change;

            auditChangXML.Actor.CreatedDateTime = DateTime.Now;
            auditChangXML.Subject.CreatedDateTime = DateTime.Now;

            return auditChangXML;
        }

    }
}
