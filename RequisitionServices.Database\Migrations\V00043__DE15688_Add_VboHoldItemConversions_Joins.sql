USE [eProcurementQA]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
***************************************************************************
Database    : eProcurement
Name        : usp_PendingApprovalsGet
Purpose     : Returns a paginated list of requisitions and ad hoc reviews for the MyApprovals page.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 11-14-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/14/2017      Script created
Peter Hurlburt		11/29/2017      Submitted for deployment 24
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		09/14/2020		Rewrote the query to optimize the sproc (stop timeout)
Peter Hurlburt		02/04/2022		Added vendor requisitions as possible
									results, using facility workflow steps
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		06/08/2022		Adding grouping to compensate for req item join
Peter Hurlburt		07/22/2022		Remove references to facility workflow step number
Colin Glasco		10/12/2022		Adding UnitCost from VboHoldItemConversion table
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_PendingApprovalsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@vboFirst bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @totalPendingActions bigint
DECLARE @pendingActions TABLE (
	RowOrder int,
	SortOrder int,
	RequisitionId int,
	RequisitionStatusTypeId int,
	RequisitionLocationIdentifier VARCHAR(50),
	RequisitionComments VARCHAR(255),
	RequisitionCreateDate DATETIME,
	RequisitionTypeId int,
	PendingReviewsExist bit,
	RequisitionerId VARCHAR(100),
	RequisitionerFirstName VARCHAR(255),
	RequisitionerLastName VARCHAR(255),
	ReviewId int,
	RequesterId VARCHAR(255),
	RequesterFirstName VARCHAR(255),
	RequesterLastName VARCHAR(255),
	RequesterComments VARCHAR(255),
	RequestCreateDate DATETIME,
	CountryCode VARCHAR(3),
	DateForSorting DATETIME,
	IsVendor BIT
)

INSERT INTO @pendingActions (
	RowOrder,
	SortOrder,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionCreateDate,
	RequisitionTypeId,
	PendingReviewsExist,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	ReviewId,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	RequestCreateDate,
	CountryCode,
	DateForSorting,
	IsVendor
)
(
	SELECT 
		ROW_NUMBER() OVER
		(
			ORDER BY SortOrder,
				CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
				CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC,
				CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC
		) AS RowOrder,
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionLocationIdentifier,
		RequisitionComments,
		RequisitionCreateDate,
		RequisitionTypeId,
		PendingReviewsExist,
		RequisitionerId,
		RequisitionerFirstName,
		RequisitionerLastName,
		ReviewId,
		RequesterId,
		RequesterFirstName,
		RequesterLastName,
		RequesterComments,
		RequestCreateDate,
		CountryCode,
		DateForSorting,
		IsVendor
	FROM 
	(
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 3
					ELSE CASE
						WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup) THEN 1
						ELSE 2
						END
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Approval' AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [UserWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON WorkflowStep.UserId = Requisitioner.Id
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor,
					(CASE RequisitionTypeId
						WHEN 5 THEN 2	--Capital
						WHEN 6 THEN 3	--Punchout
						WHEN 7 THEN 1	--Rush
						ELSE 0			--Standard
					END) AS [WorkflowTypeId]
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId = 2 --Pending Approval
				) AS Requisition
				ON Requisitioner.AccountName = Requisition.CreatedBy
				AND WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
				AND WorkflowStep.Step = Requisition.ApprovalStep
				AND WorkflowStep.WorkflowTypeId = Requisition.WorkflowTypeId
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 0
		)
		UNION
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN CASE
						WHEN Requisition.RequisitionStatusTypeId = 14 THEN 2
						ELSE 3
						END
					ELSE CASE
						WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup) THEN 1
						ELSE 2
						END
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				CASE Requisition.RequisitionStatusTypeId
					WHEN 2 THEN 'Pending Approval'
					ELSE 'On Hold'
					END AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [FacilityWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId IN (2, 14) --Pending Approval or On Hold
				) AS Requisition
				ON WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
			LEFT OUTER JOIN Users AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 1
		)
		UNION
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 1
					ELSE 2
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				CAST (0 AS BIT) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				Review.Id AS ReviewId,
				Requester.AccountName AS RequesterId,
				Requester.FirstName AS RequesterFirstName,
				Requester.LastName AS RequesterLastName,
				Review.RequesterComments AS RequesterComments,
				Review.CreateDate AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Review' AS StatusForFilter,
				Review.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [AdhocReviews] AS Review WITH (NOLOCK)
			INNER JOIN [Users] AS Requester WITH (NOLOCK)
				ON Review.Requester = Requester.AccountName
			INNER JOIN [Requisitions] AS Requisition WITH (NOLOCK)
				ON Review.RequisitionId = Requisition.RequisitionId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			WHERE Review.Reviewer = @userName
				AND Review.Recommended IS NULL
				AND Requisition.RequisitionStatusTypeId IN (0, 2, 3, 4, 8, 9, 10, 11, 14)
		)
	) AS PendingAction
	WHERE @filterText IS NULL OR
	(PendingAction.RequisitionId LIKE @filterText + '%'
	OR PendingAction.RequisitionerId LIKE '%' + @filterText + '%'
	OR PendingAction.RequisitionerFirstName LIKE @filterText + '%'
	OR PendingAction.RequisitionerLastName LIKE @filterText + '%'
	OR (PendingAction.RequisitionerFirstName + ' ' + PendingAction.RequisitionerLastName) LIKE @filterText + '%'
	OR PendingAction.RequesterId LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterFirstName LIKE @filterText + '%'
	OR PendingAction.RequesterLastName LIKE @filterText + '%'
	OR (PendingAction.RequesterFirstName + ' ' + PendingAction.RequesterLastName) LIKE @filterText + '%'
	OR PendingAction.RequisitionComments LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterComments LIKE '%' + @filterText + '%'
	OR PendingAction.StatusForFilter LIKE '%' + @filterText + '%'
	OR RequisitionLocationIdentifier LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND PendingAction.RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
	OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionLocationIdentifier,
		RequisitionComments,
		RequisitionCreateDate,
		RequisitionTypeId,
		PendingReviewsExist,
		RequisitionerId,
		RequisitionerFirstName,
		RequisitionerLastName,
		ReviewId,
		RequesterId,
		RequesterFirstName,
		RequesterLastName,
		RequesterComments,
		RequestCreateDate,
		CountryCode,
		DateForSorting,
		IsVendor
)

SELECT @totalPendingActions = COUNT(*) FROM @pendingActions

SELECT 
	PendingAction.RequisitionId AS RequisitionId,
	PendingAction.RequisitionStatusTypeId AS RequisitionStatusTypeId,
	PendingAction.RequisitionLocationIdentifier AS RequisitionLocationIdentifier,
	PendingAction.RequisitionComments AS RequisitionComments,
	PendingAction.RequisitionCreateDate AS RequisitionCreateDate,
	PendingAction.RequisitionTypeId AS RequisitionTypeId,
	PendingAction.PendingReviewsExist AS PendingReviewsExist,
	RequisitionItem.Id AS RequisitionItemId,
	RequisitionItem.ItemId AS RequisitionItemNumber,
	RequisitionItem.RequisitionItemStatusTypeId AS RequisitionItemStatusTypeId,
	RequisitionItem.QuantityToOrder AS RequisitionItemQuantityToOrder,
	RequisitionItem.ParentSystemId AS RequisitionItemParentSystemId,
	RequisitionItem.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
	RequisitionItem.ParentRequisitionItemId AS RequisitionItemParentItemId,
	RequisitionItem.ParIdentifier AS RequisitionItemParIdentifier,
	RequisitionItem.Discount AS Discount,
	RequisitionItem.VendorId AS VendorId,
	RequisitionItem.UnitCost AS UnitCost,
	SPRDetail.VendorId AS SprDetailsVendorId,
	SPRDetail.VendorName AS SprDetailsVendorName,
	SPRDetail.PartNumber AS SprDetailsPartNumber,
	SPRDetail.EstimatedPrice AS SprDetailsEstimatedPrice,
	Attachment.RequisitionItemId AS SprDetailsFileAttachment,
	PendingAction.RequisitionerId AS RequisitionerId,
	PendingAction.RequisitionerFirstName AS RequisitionerFirstName,
	PendingAction.RequisitionerLastName AS RequisitionerLastName,
	PendingAction.ReviewId AS ReviewId,
	PendingAction.RequesterId AS RequesterId,
	PendingAction.RequesterFirstName AS RequesterFirstName,
	PendingAction.RequesterLastName AS RequesterLastName,
	PendingAction.RequesterComments AS RequesterComments,
	PendingAction.RequestCreateDate AS RequestCreateDate,
	@totalPendingActions AS TotalReqCount,
	PendingAction.CountryCode AS CountryCode,
	PendingAction.IsVendor,
	VboHoldItemConversion.UnitCost AS VboHoldItemConversionUnitCost
FROM (
	SELECT * FROM @pendingActions
	ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS PendingAction
LEFT OUTER JOIN [RequisitionItems] AS RequisitionItem WITH (NOLOCK)
	ON PendingAction.RequisitionId = RequisitionItem.RequisitionId
LEFT OUTER JOIN [SPRDetails] AS SPRDetail WITH (NOLOCK)
	ON RequisitionItem.Id = SPRDetail.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM [FileAttachments] WITH (NOLOCK)) AS Attachment
	ON RequisitionItem.Id = Attachment.RequisitionItemId
LEFT OUTER JOIN [VboHoldItemConversions] AS VboHoldItemConversion WITH (NOLOCK)
	ON RequisitionItem.Id = VboHoldItemConversion.RequisitionItemId

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsGet
Purpose     : Returns a paginated list of requisitions for the MyRequisitions page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 10-26-2017
Usage       : Executed by our Requisition Services server
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/26/2017      Script created
Peter Hurlburt		10/27/2017      Submitted for deployment 21
Peter Hurlburt		11/01/2017		Added WITH (NOLOCK) statements
Peter Hurlburt		11/02/2017		Changing ALTER to CREATE, adding new script headers
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
									Submitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column 
Colin Glasco		08/18/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

SELECT
	[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
	[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
	[DistinctRequisitions].[Comments] AS [Comments],
	[DistinctRequisitions].[CreateDate] AS [CreateDate],
	[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
	[DistinctRequisitions].[IsMobile] AS [IsMobile],
	[DistinctRequisitions].[IsVendor] AS [IsVendor],
	[AllReqItems].[Id] AS [RequisitionItemId],
	[AllReqItems].[ItemId] AS [RequisitionItemNumber],
	[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
	[AllReqItems].[QuantityToOrder] AS [RequisitionItemQuantityToOrder],
	[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
	[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
	[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
	[AllReqItems].[Discount] AS [Discount],
	[AllReqItems].[VendorId] AS [VendorId],
	[AllReqItems].[UnitCost] AS [UnitCost],
	[AllReqItems].[ParIdentifier] AS [ParIdentifier],
	[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
	[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
	[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
	[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
	[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
	[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
FROM
(
	SELECT
		ROW_NUMBER() OVER (
			ORDER BY (
				CASE @statusSorting 
					WHEN 1 THEN 
						CASE [RequisitionStatusTypeId] 
							WHEN 7 THEN 1 
							WHEN 6 THEN 2
							WHEN 12 THEN 3
							WHEN 1 THEN 4
							WHEN 14 THEN 5
							WHEN 2 THEN 6
							WHEN 4 THEN 7
							ELSE 8
						END 
				END), [ReqTypeGroupingOrder], 
				CASE @oldestFirst 
					WHEN 0 THEN [Req].[CreateDate] END DESC,
				CASE @oldestFirst 
					WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
		[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
		[Req].[RequisitionId] AS [RequisitionId],
		[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Req].[LocationIdentifier] AS [LocationIdentifier],
		[Req].[Comments] AS [Comments],
		[Req].[CreateDate] AS [CreateDate],
		[Req].[RequisitionTypeId] AS [RequisitionTypeId],
		[Req].[IsMobile] AS [IsMobile],
		[Req].[IsVendor] AS [IsVendor],
		(CASE @statusSorting 
			WHEN 1 THEN 
				CASE [RequisitionStatusTypeId] 
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 14 THEN 5
					WHEN 2 THEN 6
					WHEN 4 THEN 7
					ELSE 8
				END 
		END) AS [ConditionalStatusSorting],
		dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
		+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
		- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
			CASE
				WHEN @mobileReqs = 1 
				THEN 
					CASE
						WHEN [Requisition].[IsMobile] = @mobileReqs 
						THEN 1
						ELSE 2 
					END
				ELSE 
					CASE
						WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup) 
						THEN 1
						ELSE 2 
					END
			END AS [ReqTypeGroupingOrder],
			[Requisition].[RequisitionId] AS [RequisitionId],
			[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[Requisition].[LocationIdentifier] AS [LocationIdentifier],
			[Requisition].[Comments] AS [Comments],
			[Requisition].[CreateDate] AS [CreateDate],
			[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
			[Requisition].[IsMobile] AS [IsMobile],
			[Requisition].[IsVendor] AS [IsVendor]
		FROM
			[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
			[Requisition].[CreatedBy] = @userName
			AND 5 <> [Requisition].[RequisitionStatusTypeId]
			AND 8 <> [Requisition].[RequisitionStatusTypeId]
	) AS [Req]
		LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
		LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
		LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	WHERE
		@filterText IS NULL OR
		([Req].[RequisitionId] LIKE '%' + @filterText + '%'
		OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
		OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
		OR [Req].[Comments] LIKE '%' + @filterText + '%'
		OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
		OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
		OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
		OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
		OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
		OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
		OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
		[Req].[ReqTypeGroupingOrder],
		[Req].[RequisitionId],
		[Req].[RequisitionStatusTypeId],
		[Req].[LocationIdentifier],
		[Req].[Comments],
		[Req].[CreateDate],
		[Req].[RequisitionTypeId],
		[Req].[IsMobile],
		[Req].[IsVendor]
	ORDER BY
		[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
		CASE WHEN @oldestFirst = 0 THEN [Req].[CreateDate] END DESC,
		CASE WHEN @oldestFirst = 1 THEN [Req].[CreateDate] END ASC
		OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]

	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
	LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [AllReqItems].[Id] = [VboHoldItemConversion].[RequisitionItemId]

ORDER BY 
	[DistinctRequisitions].rowNumber


OPTION (RECOMPILE)

END -- Procedure
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 10-30-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/30/2017		Script created
Peter Hurlburt		11/03/2017		Version 1.0 submitted for deployment 22
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
Peter Hurlburt		11/09/2017		Changed date filters to DateTime objects from Date
Peter Hurlburt		11/13/2017		Removed Deleted requisitions from result set
									Resubmitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Jonathan Moosekian	08/19/2020		Adding multi-department selection
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Change @departmentIds to use general IdTemplate Table Type
Julio Pozo			02/23/2022		Adding IsVendor column
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId]
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN @mobileReqs = 1
			THEN 
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
		1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]
		AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid  
		AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
		AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
		[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
		
	
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [AllReqItems].[Id] = [VboHoldItemConversion].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_VBORequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : Julio Pozo
Created     : 03-28-2022
Usage       : Vendor Requisitions Report
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_VBORequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY,
	@userIsVendor bit
AS

BEGIN

	DECLARE @minBackMonths INT = -13
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 14 THEN 5
					WHEN 2 THEN 6
					WHEN 4 THEN 7
					ELSE 8 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR R.LocationIdentifier LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (@userIsVendor = 0 OR
			(NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
				LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
					ON RI2.Id = SPR2.RequisitionItemId
				WHERE RI2.RequisitionId = R.RequisitionId AND ((
					SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
					OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		)))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
		AND R.CreateDate >= DATEADD(MONTH, @minBackMonths, GETDATE())
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount,
		[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON RI.Id = [VboHoldItemConversion].[RequisitionItemId]

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report item number search.
Used By     : SMART Procurement team
Author      : Cassie Martinez
Created     : 02-05-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Cassie Martinez		02/05/2018		Script created
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS

BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].IsMobile AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC) as rowNumber,
		
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		[Item].[ItemId],
		[Item].[ReOrder],
		[Item].[CatalogNumber],
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDetail ON SPRDetail.RequisitionItemId = [Item].Id
		WHERE
		[ItemId] = @searchText
		OR [ReOrder] = @searchText
		OR [CatalogNumber] = @searchText
		OR [SPRDetail].PartNumber = @searchText
	) AS [Item]
	LEFT OUTER JOIN (SELECT
		(CASE 
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] As [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
	) AS [Req] ON [Item].[RequisitionId] = [Req].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	(@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [Item].[ItemId] LIKE '%' + @filterText + '%'
	OR [Item].[ReOrder] LIKE '%' + @filterText + '%'
	OR [Item].[CatalogNumber] LIKE '%' + @filterText + '%'
	OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)))
	AND substring([Req].[LocationIdentifier],0,(CHARINDEX('_',[Req].[LocationIdentifier]))) = @coid
	AND 1 <> [Req].[RequisitionStatusTypeId]
	AND 5 <> [Req].[RequisitionStatusTypeId]
	AND 8 <> [Req].[RequisitionStatusTypeId]
	AND 12 <> [Req].[RequisitionStatusTypeId]
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
	
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [AllReqItems].[Id] = [VboHoldItemConversion].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsVendorReportGet
Purpose     : Returns a paginated list of requisitions for the Vendor Id or Vendor Name in Requisition Report.
Used By     : SMART Procurement team
Author      : Vani Vasanthan
Created     : 02-06-2018
Usage       : Executed by the Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsVendorReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 14 THEN 5
        WHEN 2 THEN 6
        WHEN 4 THEN 7
        ELSE 8
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		INNER JOIN [dbo].[RequisitionItems] AS [ReqItemGroup1] ON [Requisition].RequisitionId = [ReqItemGroup1].RequisitionId
	    LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDETAIL ON SPRDETAIL.RequisitionItemId = [ReqItemGroup1].Id
		WHERE
		1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]		
		AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
		AND (@VendorId IS NULL OR Cast([ReqItemGroup1].[VendorId] as VARCHAR(32)) = @VendorId OR Cast(SPRDETAIL.[VendorId] as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR [ReqItemGroup1].[VendorName] = @VendorName OR SPRDETAIL.[VendorName] = @VendorName)

	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [AllReqItems].[Id] = [VboHoldItemConversion].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Database	: eProcurement
Name		: usp_ApprovalHistoryGet
Purpose		: Returns a paginated list of requisition history items for the MyApprovals page.
Used By		: SMART Procurement team
Author		: Peter Hurlburt
Created		: 11-20-2017
Usage		: Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Peter Hurlburt		11/20/2017		Script created
Peter Hurlburt		11/28/2017		Submitted for deployment 24
Peter Hurlburt		12/15/2017		Removed an overly-verbose union statement
									Submitted for deployment 24
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		10/27/2021		Rewriting the sproc to optimize the query
									Remove restriction of only showing histories
									since most recent Requisition draft status
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Farhan Khan			07/14/2022		Added a condition to limit the approval history
									records for last 12 months
Peter Hurlburt		08/15/2022		Optimizing GROUP BY sections of the SQL, and
									removing 12 month limit
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_ApprovalHistoryGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@vboFirst bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @historyList TABLE
(
	RowOrder INT NOT NULL,
	HistoryItemId INT NOT NULL,
	HistoryItemCreateDate DATETIME NOT NULL,
	HistoryItemStatusTypeId INT NOT NULL,
	RequisitionId INT NOT NULL,
	IsVendor BIT
)
DECLARE @tempReqTypeIds TABLE (Id INT)
DECLARE @totalhistoryCount BIGINT

INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup

INSERT INTO @historyList
SELECT
	ROW_NUMBER() OVER
	(
		ORDER BY SortOrder,
			CASE @oldestFirst WHEN 1 THEN HistoryItemCreateDate END ASC,
			CASE @vboFirst WHEN 1 THEN IsVendor END DESC,
			CASE @oldestFirst WHEN 0 THEN HistoryItemCreateDate END DESC
	) AS RowOrder,
	HistoryItemId,
	HistoryItemCreateDate,
	HistoryItemStatusTypeId,
	RequisitionId,
	IsVendor
FROM
(
	SELECT
		CASE @statusSorting
			WHEN 1 THEN CASE requisitionHistory.RequisitionStatusTypeId
				WHEN 6 THEN 3
				WHEN 3 THEN 2
				ELSE review.Recommended END
			ELSE CASE
				WHEN requisition.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
				THEN 1
				ELSE 2
				END
			END AS SortOrder,
		CASE requisitionHistory.RequisitionStatusTypeId
			WHEN 10 THEN CASE review.Recommended
				WHEN 1 THEN 'Recommend Approve'
				ELSE 'Recommend Deny'
				END
			ELSE
				requisitionStatus.[Description]
			END AS StatusForFilter,
		requisitionHistory.Id AS HistoryItemId,
		requisitionHistory.CreateDate AS HistoryItemCreateDate,
		requisitionHistory.RequisitionStatusTypeId AS HistoryItemStatusTypeId,
		requisitionHistory.RequisitionId AS RequisitionId,
		requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
		requisition.LocationIdentifier AS RequisitionLocationIdentifier,
		requisition.Comments AS RequisitionComments,
		requisition.RequisitionTypeId AS RequisitionTypeId,
		requisition.CreatedBy AS RequisitionerId,
		reqItem.ParIdentifier AS RequisitionItemParIdentifier,
		requisitioner.FirstName AS RequisitionerFirstName,
		requisitioner.LastName AS RequisitionerLastName,
		reviewRequester.AccountName AS RequesterId,
		reviewRequester.FirstName AS RequesterFirstName,
		reviewRequester.LastName AS RequesterLastName,
		review.RequesterComments AS RequesterComments,
		review.Recommended AS ReviewerRecommended,
		review.ReviewerComments AS ReviewerComments,
		requisition.IsVendor
	FROM RequisitionStatusHistories requisitionHistory WITH (NOLOCK)
	INNER JOIN RequisitionStatusTypes requisitionStatus WITH (NOLOCK)
		ON requisitionHistory.RequisitionStatusTypeId = requisitionStatus.Id
	INNER JOIN Requisitions requisition WITH (NOLOCK)
		ON requisitionHistory.RequisitionId = requisition.RequisitionId
	INNER JOIN Users requisitioner WITH (NOLOCK)
		ON requisition.CreatedBy = requisitioner.AccountName
	LEFT OUTER JOIN AdhocReviews review WITH (NOLOCK)
		ON requisitionHistory.Id = review.ReviewerRequisitionStatusHistoryId
	LEFT OUTER JOIN Users reviewRequester WITH (NOLOCK)
		ON review.Requester = reviewRequester.AccountName
	LEFT OUTER JOIN RequisitionItems AS reqItem WITH (NOLOCK)
		ON Requisition.RequisitionId = reqItem.RequisitionId
	WHERE requisitionHistory.CreatedBy = @userName
	AND requisitionHistory.RequisitionStatusTypeId IN (3, 6, 10) --Approved, Denied, Review Provided
) AS HistoryRecord
WHERE @filterText IS NULL OR
	(HistoryRecord.[RequisitionId] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequisitionerId] LIKE '%' + @filterText + '%'
	OR (HistoryRecord.[RequisitionerFirstName] + ' ' + HistoryRecord.[RequisitionerLastName]) LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequesterId] LIKE '%' + @filterText + '%'
	OR (HistoryRecord.[RequesterFirstName] + ' ' + HistoryRecord.[RequesterLastName]) LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequisitionComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequesterComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[ReviewerComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[StatusForFilter] LIKE '%' + @filterText + '%'
	OR RequisitionLocationIdentifier LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
	OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
GROUP BY
	SortOrder,
	HistoryItemId,
	HistoryItemCreateDate,
	HistoryItemStatusTypeId,
	RequisitionId,
	IsVendor

SELECT @totalHistoryCount = COUNT(*) FROM @historyList

SELECT
	orderedHistory.HistoryItemId,
	orderedHistory.HistoryItemCreateDate,
	orderedHistory.HistoryItemStatusTypeId,
	orderedHistory.RequisitionId,
	orderedHistory.RequisitionStatusTypeId,
	orderedHistory.RequisitionLocationIdentifier,
	orderedHistory.RequisitionComments,
	orderedHistory.RequisitionTypeId,
	(CASE
		WHEN EXISTS (
			SELECT 1
			FROM AdhocReviews AS review WITH (NOLOCK)
			WHERE orderedHistory.RequisitionId = review.RequisitionId
			AND review.Recommended IS NULL
		)
		THEN CAST (1 AS BIT)
		ELSE CAST (0 AS BIT)
		END) AS PendingReviewsExist,
	item.Id AS RequisitionItemId,
	item.RequisitionItemStatusTypeId,
	item.QuantityToOrder AS RequisitionItemQuantityToOrder,
	item.ParentSystemId AS RequisitionItemParentSystemId,
	item.ParIdentifier AS RequisitionItemParIdentifier,
	SPR.VendorId AS SprDetailsVendorId,
	SPR.VendorName AS SprDetailsVendorName,
	SPR.PartNumber AS SprDetailsPartNumber,
	SPR.EstimatedPrice AS SprDetailsEstimatedPrice,
	ReqItemFileAttachments.RequisitionItemId AS SprDetailsFileAttachment,
	orderedHistory.RequisitionerId,
	orderedHistory.RequisitionerFirstName,
	orderedHistory.RequisitionerLastName,
	orderedHistory.RequesterId,
	orderedHistory.RequesterFirstName,
	orderedHistory.RequesterLastName,
	orderedHistory.RequesterComments,
	orderedHistory.ReviewerRecommended,
	orderedHistory.ReviewerComments,
	orderedHistory.IsVendor,
	@totalhistoryCount AS TotalReqCount,
	VboHoldItemConversion.UnitCost AS VboHoldItemConversionUnitCost
FROM
(
	SELECT
		filteredHistory.HistoryItemId,
		filteredHistory.HistoryItemCreateDate,
		filteredHistory.HistoryItemStatusTypeId,
		filteredHistory.RequisitionId,
		filteredHistory.IsVendor,
		requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
		requisition.LocationIdentifier AS RequisitionLocationIdentifier,
		requisition.Comments AS RequisitionComments,
		requisition.RequisitionTypeId AS RequisitionTypeId,
		requisition.CreatedBy AS RequisitionerId,
		requisitioner.FirstName AS RequisitionerFirstName,
		requisitioner.LastName AS RequisitionerLastName,
		reviewRequester.AccountName AS RequesterId,
		reviewRequester.FirstName AS RequesterFirstName,
		reviewRequester.LastName AS RequesterLastName,
		review.RequesterComments AS RequesterComments,
		review.Recommended AS ReviewerRecommended,
		review.ReviewerComments AS ReviewerComments
	FROM @historyList filteredHistory
	INNER JOIN Requisitions requisition WITH (NOLOCK)
		ON filteredHistory.RequisitionId = requisition.RequisitionId
	INNER JOIN Users requisitioner WITH (NOLOCK)
		ON requisition.CreatedBy = requisitioner.AccountName
	LEFT OUTER JOIN AdhocReviews review WITH (NOLOCK)
		ON filteredHistory.HistoryItemId = review.ReviewerRequisitionStatusHistoryId
	LEFT OUTER JOIN Users reviewRequester WITH (NOLOCK)
		ON review.Requester = reviewRequester.AccountName
	ORDER BY filteredHistory.RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS orderedHistory
LEFT OUTER JOIN RequisitionItems item WITH (NOLOCK)
	ON orderedHistory.RequisitionId = item.RequisitionId
LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
	ON item.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) ReqItemFileAttachments
	ON item.Id = ReqItemFileAttachments.RequisitionItemId
LEFT OUTER JOIN VboHoldItemConversions AS VboHoldItemConversion
	ON item.Id = VboHoldItemConversion.RequisitionItemId

END
GO

