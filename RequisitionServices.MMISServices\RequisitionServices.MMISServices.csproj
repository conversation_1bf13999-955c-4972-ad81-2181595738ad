﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{706343DC-AB56-4A18-A4FC-CB852C6CA64C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RequisitionServices.MMISServices</RootNamespace>
    <AssemblyName>RequisitionServices.MMISServices</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\QA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <OutputPath>bin\Production\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <OutputPath>bin\Sandbox\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <OutputPath>bin\QASandbox\</OutputPath>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net, Version=2.0.12.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.12\lib\net45\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.WebApi.Versioning, Version=4.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Versioning.4.0.0\lib\net45\Microsoft.AspNet.WebApi.Versioning.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=12.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.12.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Net.Http.Formatting, Version=5.2.7.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Http, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DTO\AlternateUOMRecord.cs" />
    <Compile Include="DTO\BillOnlyErrorModel.cs" />
    <Compile Include="DTO\BillOnlyRequisitionItemModel.cs" />
    <Compile Include="DTO\BillOnlyRequisitionItemResponseModel.cs" />
    <Compile Include="DTO\BillOnlyRequisitionModel.cs" />
    <Compile Include="DTO\BillOnlyRequisitionResponseModel.cs" />
    <Compile Include="DTO\CapitatedReqErrorModel.cs" />
    <Compile Include="DTO\CapitatedRequisitionItemModel.cs" />
    <Compile Include="DTO\CapitatedRequisitionItemResponseModel.cs" />
    <Compile Include="DTO\CapitatedRequisitionModel.cs" />
    <Compile Include="DTO\CapitatedRequisitionResponseModel.cs" />
    <Compile Include="DTO\ContractDetailsModel.cs" />
    <Compile Include="DTO\CostCodesModel.cs" />
    <Compile Include="DTO\CoidRecordModel.cs" />
    <Compile Include="DTO\CoidRecordsModel.cs" />
    <Compile Include="DTO\DepartmentRecordModel.cs" />
    <Compile Include="DTO\DepartmentRecordsModel.cs" />
    <Compile Include="DTO\DoctorRecordModel.cs" />
    <Compile Include="DTO\FStoreDeptDTO.cs" />
    <Compile Include="DTO\GetItemParDetailsRequest.cs" />
    <Compile Include="DTO\UOMDetail.cs" />
    <Compile Include="DTO\LegacyRequisitionItemModel.cs" />
    <Compile Include="DTO\LegacyRequisitionItemsModel.cs" />
    <Compile Include="DTO\LegacyRequisitionReportModel.cs" />
    <Compile Include="DTO\LegacyRequisitionReportRequestModel.cs" />
    <Compile Include="DTO\LocatorErrorsModel.cs" />
    <Compile Include="DTO\LocatorLineModel.cs" />
    <Compile Include="DTO\LocatorModel.cs" />
    <Compile Include="DTO\OrderTimesDTO.cs" />
    <Compile Include="DTO\GLAccountModel.cs" />
    <Compile Include="DTO\GLAccountsModel.cs" />
    <Compile Include="DTO\IINItemRecordDTO.cs" />
    <Compile Include="DTO\ItemParDTO.cs" />
    <Compile Include="DTO\ItemRecordModel.cs" />
    <Compile Include="DTO\ItemRecordsModel.cs" />
    <Compile Include="DTO\ParHeaderModel.cs" />
    <Compile Include="DTO\ParItemDetailsModel.cs" />
    <Compile Include="DTO\ParItemModel.cs" />
    <Compile Include="DTO\ParRecordsModel.cs" />
    <Compile Include="DTO\ParScheduleModel.cs" />
    <Compile Include="DTO\PatientRecordModel.cs" />
    <Compile Include="DTO\POConfirmationDetailDTO.cs" />
    <Compile Include="DTO\POHeaderDTO.cs" />
    <Compile Include="DTO\POHistoryDTO.cs" />
    <Compile Include="DTO\POLineItemDTO.cs" />
    <Compile Include="DTO\POListsDTO.cs" />
    <Compile Include="DTO\POOptionsDTO.cs" />
    <Compile Include="DTO\POVendorDTO.cs" />
    <Compile Include="DTO\PurchaseOrderDetail.cs" />
    <Compile Include="DTO\RequisitionDetailModel.cs" />
    <Compile Include="DTO\RequisitionHeaderModel.cs" />
    <Compile Include="DTO\RequisitionItemModel.cs" />
    <Compile Include="DTO\LegacyRequisitionRecordsModel.cs" />
    <Compile Include="DTO\LegacyRequisitionRecordModel.cs" />
    <Compile Include="DTO\RequisitionResponseModel.cs" />
    <Compile Include="DTO\RequisitionModel.cs" />
    <Compile Include="DTO\RequisitionRecordModel.cs" />
    <Compile Include="DTO\RequisitionRecordsModel.cs" />
    <Compile Include="DTO\ShipToCodeModel.cs" />
    <Compile Include="DTO\ShipToCodeRecordsModel.cs" />
    <Compile Include="DTO\SPRItemModel.cs" />
    <Compile Include="DTO\SPRResponseModel.cs" />
    <Compile Include="DTO\SPRStatusCollectionModel.cs" />
    <Compile Include="DTO\SPRStatusDetailModel.cs" />
    <Compile Include="DTO\SPRStatusHeaderModel.cs" />
    <Compile Include="DTO\SPRStatusModel.cs" />
    <Compile Include="DTO\VendorHeaderModel.cs" />
    <Compile Include="DTO\VendorHeadersModel.cs" />
    <Compile Include="DTO\VendorInformationModel.cs" />
    <Compile Include="DTO\VendorRecordModel.cs" />
    <Compile Include="Interface\ISmartContractService.cs" />
    <Compile Include="Interface\ISmartIINItemService.cs" />
    <Compile Include="Interface\ISmartItemInfoService.cs" />
    <Compile Include="Interface\ISmartItemParService.cs" />
    <Compile Include="Interface\ISmartLocationService.cs" />
    <Compile Include="Interface\ISmartParItemsService.cs" />
    <Compile Include="Interface\ISmartPatientService.cs" />
    <Compile Include="Interface\ISmartDoctorService.cs" />
    <Compile Include="Interface\ISmartDepartmentService.cs" />
    <Compile Include="Interface\ISmartPOService.cs" />
    <Compile Include="Interface\ISmartRequisitionInquiryService.cs" />
    <Compile Include="Interface\ISmartRequisitionService.cs" />
    <Compile Include="Interface\ISmartVendorService.cs" />
    <Compile Include="Interface\ISmartCOIDService.cs" />
    <Compile Include="Interface\ISmartParService.cs" />
    <Compile Include="Interface\ISmartItemService.cs" />
    <Compile Include="LegacyConnectorInterfaces\ILegacyConnectorItemPriceService.cs" />
    <Compile Include="LegacyConnectorServices\LegacyConnectorItemPriceService.cs" />
    <Compile Include="SmartContractService.cs" />
    <Compile Include="SmartDoctorService.cs" />
    <Compile Include="SmartIINItemService.cs" />
    <Compile Include="SmartItemInfoService.cs" />
    <Compile Include="SmartItemParService.cs" />
    <Compile Include="SmartLocationService.cs" />
    <Compile Include="SmartParItemsService.cs" />
    <Compile Include="SmartPatientService.cs" />
    <Compile Include="SmartDepartmentService.cs" />
    <Compile Include="SmartCOIDService.cs" />
    <Compile Include="SmartItemService.cs" />
    <Compile Include="SmartParService.cs" />
    <Compile Include="SmartPOService.cs" />
    <Compile Include="SmartRequisitionInquiryService.cs" />
    <Compile Include="SmartRequisitionService.cs" />
    <Compile Include="SmartVendorService.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utilities\BillReplaceTypeMapper.cs" />
    <Compile Include="Utilities\ExtensionMethods.cs" />
    <Compile Include="Utilities\SmartAPIReqMapper.cs" />
    <Compile Include="Utilities\SmartInputValidator.cs" />
    <Compile Include="Utilities\SmartItemUtilities\ISmartReqItemUtility.cs" />
    <Compile Include="Utilities\SmartItemUtilities\SmartReqItemUtility.cs" />
    <Compile Include="Utilities\SmartItemUtilities\SmartSPRItemUtility.cs" />
    <Compile Include="Utilities\SPRTypeMapper.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RequisitionServices.DomainModel\RequisitionServices.DomainModel.csproj">
      <Project>{6ebe285e-ac6c-4a2a-9aa6-0a906b7ba723}</Project>
      <Name>RequisitionServices.DomainModel</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Repositories\RequisitionServices.Repositories.csproj">
      <Project>{79f7e2ee-74bc-4854-b2d2-e9b5035ac1ee}</Project>
      <Name>RequisitionServices.Repositories</Name>
    </ProjectReference>
    <ProjectReference Include="..\RequisitionServices.Utility\RequisitionServices.Utility.csproj">
      <Project>{6a4f4b1c-f81d-45ff-8b83-b693a6dba39d}</Project>
      <Name>RequisitionServices.Utility</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>