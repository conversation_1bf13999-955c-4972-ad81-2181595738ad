﻿using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.MMISServices.DTO
{
    public class LocatorErrorsModel
    {
        public string ObjectId { get; set; }

        public string ErrorMsg { get; set; }

        public int ErrorCode { get; set; }

        public LocatorErrors MapToLocatorError()
        {
            return new LocatorErrors()
            {
                ObjectId = this.ObjectId,
                ErrorCode = this.ErrorCode,
                ErrorMsg = this.ErrorMsg
            };
        }
    }
}
