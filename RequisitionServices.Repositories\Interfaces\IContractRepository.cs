﻿using RequisitionServices.DomainModel.Contracts;

namespace RequisitionServices.Repositories.Interfaces
{
    public interface IContractRepository
    {
        ContractReportResults GetAllContracts(int rowOffset, int pageSize, string sortOrder, string filterText);
        ContractReportResults GetContractsById(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText);
        ContractReportResults GetContractsByVendor(int rowOffset, int pageSize, string sortOrder, string filterText, string searchText);
    }
}
