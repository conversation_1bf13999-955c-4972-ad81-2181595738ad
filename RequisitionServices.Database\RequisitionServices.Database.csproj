﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{75CFCF81-401A-4388-BE1F-ED579C161B41}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>RequisitionServices.Database</RootNamespace>
    <AssemblyName>RequisitionServices.Database</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SolutionDir Condition="$(SolutionDir) == '' Or $(SolutionDir) == '*Undefined*'">..\</SolutionDir>
    <RestorePackages>true</RestorePackages>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QA|AnyCPU'">
    <OutputPath>bin\QA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Production|AnyCPU'">
    <OutputPath>bin\Production\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Sandbox|AnyCPU'">
    <OutputPath>bin\Sandbox\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'QASandbox|AnyCPU'">
    <OutputPath>bin\QASandbox\</OutputPath>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'CI_CD|AnyCPU'">
    <OutputPath>bin\CI_CD\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKQA|AnyCPU'">
    <OutputPath>bin\UKQA\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'UKProduction|AnyCPU'">
    <OutputPath>bin\UKProduction\</OutputPath>
    <DebugType>pdbonly</DebugType>
    <DebugSymbols>true</DebugSymbols>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.2\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.1.2\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EProcurementContext.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ResilientExecutionStrategy.cs" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\RequisitionServices.DomainModel\RequisitionServices.DomainModel.csproj">
      <Project>{6ebe285e-ac6c-4a2a-9aa6-0a906b7ba723}</Project>
      <Name>RequisitionServices.DomainModel</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Migrations\V00001__Initial_Evolve.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00029__DE14565_VendorId_Filter_Fix.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00026__US84010_Sort_By_VBO.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00033__US87288_Add_DescriptionHasChanged_Column.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00034__DE14677_Sort_By_VBO_fix.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00032__DE14624_Vbo_Req_Reports_fix_2.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00031__DE14624_Vbo_Req_Reports_fix.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00030__US81912_Vbo_Req_Reports.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00027__DE14501_Vendor_Req_Comments.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00025__Resync_Evolve_Version_Number.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00002__MultiDepartment.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00003__New_Requisition_Sort_Options.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00004__Update_usp_PendingApprovalsGet.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00005__Delete_Bedroc_Punchout_Vendor.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00006__Purge_RequisitionItemStatusHistories.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00007__GetLastOrderedDetails_Improvements.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00008__DE13099_GetLastOrderedDetails_Duplicates.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00009__DE13247_GetLastOrderedDetails_Indexing.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00010__US67605_Add_New_RequisitionItemStatusType.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00011__US76951_Add_New_RequisitionItemStatusType.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00012__US78232_Requisition_Comments.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00013__US78819_Add_Dell_Punchout_Vendor.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00014__US78232_Requisition_Comment_Notifications.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00015__DE14123_update_usp_ApprovalHistoryGet.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00016__US80543_Add_FacilityWorkflowSteps_Vendor_WorkflowType.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00018__DE14373_Limit_Exported_Reports_Max_Rows.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00017__US83096_Rename_OR_Step.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00019__US80544_Vendor_Requisition_Workflows.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00024__DE14478_Add_IsVendor_Workflow_Lists.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00023__DE14467_Add_IsVendor_To_Requisition_Report.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00022__US82352_Idendify_Vendor_Requisitions.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00021__DE14427_Add_Max_Req_Count.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00020__DE14373_Remove_RowCount_Limit.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00028__US81913_Report_Search_By_VBO.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00035__US82562_Facility_Dept_Filtering.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00038__US92284_Limiting_Approval_History_Records.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00037__DE15063_Approvals_Row_Grouping.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00036__DE14989_Facility_Dept_Id_Filtering.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00045__US97058_Update_Edw_Req_Item_View.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00044__US96555_Update_Edw_Req_View.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00043__DE15688_Add_VboHoldItemConversions_Joins.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00042__US89419_Add_VboHoldItemConversions_Table.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00041__US89418_Adding_OnHold_Req_Status.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00040__DE15547_Optimize_Approval_History_Records.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00039__US83096_Remove_VBO_Step_Types.sql">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Migrations\V00046__US106920_Latest_CreateDate_edwItemInventories.sql" />
    <Content Include="Migrations\V00047__US99328_Update_Requisition_Comment_Notifications.sql" />
    <Content Include="Migrations\V00048__US107406_Create_Table_DigitalSignoffUsers.sql" />
    <Content Include="Migrations\V00049__US107406_Create_Table_RequisitionsDigitalSignoff.sql" />
    <Content Include="Migrations\V00051__US108088_DSO_Update_CreateUsers_3-8-24.sql" />
    <Content Include="Migrations\V00053__DE17776_ExcludeVbo_PendingApprovalsGetUpdate.sql" />
    <Content Include="Migrations\V00052__US108359_dsao_add_create_date.sql" />
    <Content Include="Migrations\V00050__US104851_DSO_Update_PendingApprovalsGet.sql" />
    <Content Include="Migrations\V00054__US109275_RequisitionsGet_DSOReqSubmissionTypeUpdate.sql" />
    <Content Include="Migrations\V00055__US109275_PendingApprovalsGet_DSOReqSubmissionTypeUpdate.sql" />
    <Content Include="Migrations\V00056__ US109226_DROP_InternalId_Column_DigitalSignoffUsers.sql" />
    <Content Include="Migrations\V00057__US108001_Create_Edw_DigitalSignOffUsers_View.sql" />
    <Content Include="Migrations\V00058__US108001_Create_Edw_RequisitionDigitalSignOff_View.sql" />
    <Content Include="Migrations\V00059__US108001_Update_Edw_Requisition_View_for_DSO.sql" />
    <Content Include="Migrations\V00060__US109512-PendingApprovalsGet-Update-Filter-By-PatientId.sql" />
    <Content Include="Migrations\V00061__US109512-ApprovalHistoryGet-Update-Filter-By-PatientId.sql" />
    <Content Include="Migrations\V00062__US110855-RequisitionsReportByItemNumberExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00063__US110855-RequisitionsReportExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00064__US110855-RequisitionsVendorReportExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00065__US110855-VBORequisitionsReportExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00066__US110855-VendorUserRequisitionsReportByItemNumberExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00067__US110855-VendorUserRequisitionsReportExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00068__US1108055-VendorUserRequisitionsVendorReportExportGet-updateForDSO.sql" />
    <Content Include="Migrations\V00070_US111845-CreateNewColumnForTotalCost.sql" />
    <Content Include="Migrations\V00075__US110706-RequisitionsReportByItemNumberGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00074__US110706-RequisitionsVendorReportGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00073__US110706-VBORequisitionsReportGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00072__US110706-VendorUserRequisitionsReportGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00071__US110706-VendorUserRequisitionsReportByItemNumberGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00070__US110706-VendorUserRequisitionsVendorReportGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00069__US110804-ApprovalHistoryGet-updateForDSO.sql" />
    <Content Include="Migrations\V00076__US110706-RequisitionsReportGet-addSubmissionType.sql" />
    <Content Include="Migrations\V00077__US111113_RequisitionsReportByItemNumberGet_updateForDSOFilter.sql" />
    <Content Include="Migrations\V00078__US111113_RequisitionsVendorReportGet_updateForDSOFilter.sql" />
    <Content Include="Migrations\V00079__US111113_VBORequisitionsReportGet_updateForDSOFilter.sql" />
    <Content Include="Migrations\V00080__US111113_VendorUserRequisitionsReportByItemNumberGet_updateForDSOFilter.sql" />
    <Content Include="Migrations\V00081__US111113_VendorUserRequisitionsReportGet_updateForDSOFilter.sql" />
    <Content Include="Migrations\V00082__US111113_VendorUserRequisitionsVendorReportGet_updateForDSOFilter.sql" />
    <Content Include="Migrations\V00083_TA128070_AddIsPOUColumn_POU_Carts.sql" />
    <Content Include="Migrations\V00083__US114041_userDefinedCoidList.sql" />
    <Content Include="Migrations\V00084_US111877_AddedPARTypeTable_PARTypeId_Column_CartItems.sql" />
    <Content Include="Migrations\V00084__US114041_usp_PurchasingRequisitionsGet.sql" />
    <Content Include="Migrations\V00085_US114590_Adding_LastUpdated_Col_to_edwRequisitionItems_View.sql" />
    <Content Include="Migrations\V00085__US115494_usp_PurchasingRequisitionsGet_Date_Range.sql" />
    <Content Include="Migrations\V00086__US114235_usp_PurchasingRequisitionsGetIncludeFIleAttachment.sql" />
    <Content Include="Migrations\V00087__US114235_usp_PurchasingRequisitionsGetIncludeFIleAttachment_V2.sql" />
    <Content Include="Migrations\V00088__US116649_Create_ViraItemStatus_Table.sql" />
    <Content Include="Migrations\V00089_CreateRFIDCart.sql" />
    <Content Include="Migrations\V00089__US114235_usp_PurchasingRequisitionsGetIncludeFIleAttachment_V3 .sql" />
    <Content Include="Migrations\V00090_CreateVProBadgeLogTable.sql" />
    <Content Include="Migrations\V00090__US117523_Create_UserAlertMessageTypesTable.sql" />
    <Content Include="Migrations\V00091__US117523_Create_UserAlertMessageTable.sql" />
    <Content Include="Migrations\V00092__US118905_CreateRFIDCart_Final.sql" />
    <Content Include="Migrations\V00093__US118905_Item_RFID_Carts_Table.sql" />
    <Content Include="Migrations\V00094__US118276_Reqs_Table_ADD_BadgeInId.sql" />
    <Content Include="Migrations\V00095__US118276_RequisitionVProBadgeLogs_Update.sql" />
    <Content Include="Migrations\V00096__US118276_edwRequisitions_View_Update.sql" />
    <Content Include="Migrations\V00097__US118276_RequisitionVProBadgeLogsView_Create_View.sql" />
    <Content Include="Migrations\V00098__US118722_Update_RequisitionsReportExportGet.sql" />
    <Content Include="Migrations\V00099__US118722_Update_VendorUserRequisitionsReportExportGet.sql" />
    <Content Include="Migrations\V00100__US118722_Update_RequisitionsReportByItemNumberExportGet.sql" />
    <Content Include="Migrations\V00101__US118722_Update_VendorUserRequisitionsReportByItemNumberExportGet.sql" />
    <Content Include="Migrations\V00102__US118722_Update_VBORequisitionsReportExportGet.sql" />
    <Content Include="Migrations\V00103__US118722_Update_RequisitionsVendorReportExportGet.sql" />
    <Content Include="Migrations\V00104__US118722_Update_VendorUserRequisitionsVendorReportExportGet.sql" />
    <Content Include="Migrations\V00105__US119173_Create_EmptyBinHistory_table_And_AddEmptyBin_ParType.sql" />
    <Content Include="Migrations\V00106__US119173_Save_EmptyBinDetail.sql" />
    <Content Include="Migrations\V00107__DE18589_ApprovalHistoryGet_Sort_Update.sql" />
    <Content Include="Migrations\V00108__US120659_ApprovalHistoryGet_Update_FilterByPO.sql" />
    <Content Include="Migrations\V00109__US121562_Update_ReqBadgeLogs_Create_ReqBadgeStatusTypes.sql" />
    <Content Include="Migrations\V00110__US121970_Update_RequisitionVProBadgeLogsView.sql" />
    <Content Include="Migrations\V00111__US122508_Clear_BadgeLogId_Delete_RequisitionVProBadgeLogs_Records.sql" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(SolutionDir)\.nuget\NuGet.targets" Condition="Exists('$(SolutionDir)\.nuget\NuGet.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Enable NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('$(SolutionDir)\.nuget\NuGet.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(SolutionDir)\.nuget\NuGet.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>