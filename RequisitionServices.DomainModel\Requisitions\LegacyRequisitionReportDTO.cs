﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    public class LegacyRequisitionReportDTO
    {
        public string COID { get; set; }

        public int PageNumber { get; set; }

        public IEnumerable<string> PageIndices { get; set; }

        public bool IsLastPage { get; set; }

        public string ErrorMessage { get; set; }

        public IEnumerable<Requisition> Requisitions { get; set; }

        public LegacyRequisitionReportDTO()
        {

        }

        public LegacyRequisitionReportDTO(LegacyRequisitionReportRequestDTO request)
        {
            COID = request.COID;
            PageNumber = request.PageNumber;
            PageIndices = request.PageIndices;
            IsLastPage = true;
            Requisitions = new List<Requisition>();
        }
    }
}
