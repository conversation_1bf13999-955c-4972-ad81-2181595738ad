﻿using System;

namespace RequisitionServices.DomainModel.Users
{
    public class PersonalizationDTO
    {
        private string _userName;
        public string FacilityId { get; set; }
        public int? DepartmentId { get; set; }
        public string ParId { get; set; }

        public string UserName
        {
            get
            {
                if (_userName != null &&  !string.IsNullOrWhiteSpace(_userName) && _userName.IndexOf('/') != -1)
                {
                    _userName =  _userName.Split('/')[1].ToLower();
                }
                return _userName;
            }

            set => _userName = value.ToLower();
        }
    }
}
