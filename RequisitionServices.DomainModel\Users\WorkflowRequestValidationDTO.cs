﻿using RequisitionServices.DomainModel.Enum;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Users
{
    public class WorkflowRequestValidationDTO
    {
        public List<UserWorkflowStep> UserWorkflowSteps { get; set; }
        public string UserName {get; set;}
        public WorkflowTypeEnum WorkflowType {get; set;}
        public int WorkflowTypeId { get; set; }
        public string COID {get; set;}
        public decimal? RequisitionTotal {get; set;}
    }
}
