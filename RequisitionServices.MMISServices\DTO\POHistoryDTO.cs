﻿using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class POHistoryDTO
    {
        public int POLineNumber { get; set; }

        public DateTime Date { get; set; }

        public string ReqType { get; set; }

        public int POItem { get; set; }

        public string UserId { get; set; }

        public string Action { get; set; }

        public string OldDescription { get; set; }

        public string NewDescription { get; set; }

        public string OldReorderNumber { get; set; }

        public string NewReorderNumber { get; set; }

        public int OldDepartmentNumber { get; set; }

        public int NewDepartmentNumber { get; set; }

        public long OldInventoryAccount { get; set; }

        public long NewInventoryAccount { get; set; }

        public string OldTaxableCode { get; set; }

        public string NewTaxableCode { get; set; }

        public string OldPUOM { get; set; }

        public string NewPUOM { get; set; }

        public string OldMRCode { get; set; }

        public string NewMRCode { get; set; }

        public string OldManufactureCode { get; set; }

        public string NewManufactureCode { get; set; }

        public int OldFactor { get; set; }

        public int NewFactor { get; set; }

        public decimal OldUnitCost { get; set; }

        public decimal NewUnitCost { get; set; }

        public string ItemMasterUpdate { get; set; }

        public DateTime PODate { get; set; }

        public int ControlNumberBegin { get; set; }

        public decimal OldDiscount { get; set; }

        public decimal NewDiscount { get; set; }

        public decimal OldCredit { get; set; }

        public decimal NewCredit { get; set; }

        public string ImageFlag { get; set; }

        public string ApproverId { get; set; }

        public int OldItemNumber { get; set; }

        public int NewItemNumber { get; set; }

        public string OldNDC { get; set; }

        public string NewNDC { get; set; }

        public int OldQuantityOrdered { get; set; }

        public int NewQuantityOrdered { get; set; }

        public string Justification { get; set; }
        
        public string OtherJustifText { get; set; }
    }
}
