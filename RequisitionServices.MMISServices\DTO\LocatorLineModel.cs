﻿using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.MMISServices.DTO
{
    public class LocatorLineModel
    {
        public int Item { get; set; }

        public string Location { get; set; }

        public string PreLocation { get; set; }

        public string Result { get; set; }

        public LocatorLine MapToLocatorLine()
        {
            return new LocatorLine()
            {
                Item = this.Item,
                Location = this.Location,
                PreLocation = this.PreLocation,
                Result = this.Result
            };
        }
    }
}
