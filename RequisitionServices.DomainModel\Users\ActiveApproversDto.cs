﻿namespace RequisitionServices.DomainModel.Users
{
    public class ActiveApproversDto
    {
        public string AccountName { get; set; }
        public string Coid { get; set; }
        public string FirstName { get; set; }
        public int Id { get; set; }
        public string LastName { get; set; }
        public int? DelegatedUserId { get; set; }
        public bool IsDelegate { get; set; }
    }
}