﻿using RequisitionServices.Database;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Vendors;

namespace RequisitionServices.Repositories
{
    public class VendorRepository : AbstractRepository, IVendorRepository
    {
        const string nonAffilCompanyCode = "N";
        public VendorRepository(EProcurementContext context) : base(context) { }

        public PunchOutVendor GetPunchOutVendor(int punchOutVendorId)
        {
            return context.PunchOutVendors.Where(x => x.PunchOutVendorId == punchOutVendorId).FirstOrDefault();
        }

        public IEnumerable<PunchOutVendor> GetPunchOutVendors(Facility facility)
        {
            var vendors = context.PunchOutVendors.Where( x=> x.CompanyId == facility.CompanyId).ToList();

            if(facility.CompanyCode.ToUpper() == nonAffilCompanyCode) //Getting the "default" punchout vendors: vendors for nonaffil companies.
            {
                var nonAffilVendors = context.PunchOutVendors.Where(x => x.CompanyId == -1).ToList();
                vendors.AddRange(nonAffilVendors.Where(x => !vendors.Select(y => y.VendorId).Contains(x.VendorId)));
            }

            return vendors;
        }

        public IEnumerable<PunchOutVendor> GetAllPunchOutVendors()
        {
            return context.PunchOutVendors.ToList();
        }
    }
}
