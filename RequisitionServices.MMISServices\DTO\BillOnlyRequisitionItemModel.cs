﻿using System.Collections.Generic;

namespace RequisitionServices.MMISServices.DTO
{
    public class BillOnlyRequisitionItemModel
    {
        public int Id { get; set; }
        public int RequisitionId { get; set; }
        public int Coid { get; set; }
        public int ItemNumber { get; set; }
        public int ItemQuantity { get; set; }
        public string Description { get; set; }
        public string ReOrderNumber { get; set; }
        public int VendorNumber { get; set; }
        public string UOM { get; set; }
        public long GLAccount { get; set; }        
        public List<string> LotNumbers { get; set; }
        public List<string> SerialNumbers { get; set; }        
        public decimal UnitCost { get; set; }
        public string CommentsJustification { get; set; }
        public string RequisitionerName { get; set; }

        internal decimal? Discount { get; set; }
    }
}
