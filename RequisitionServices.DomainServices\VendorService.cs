﻿using log4net;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.DomainModel.Locations;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using RequisitionServices.DomainModel.Vendors;
using RequisitionServices.MMISServices.Interface;

namespace RequisitionServices.DomainServices
{
    public class VendorService : IVendorService
    {
        private IVendorRepository vendorRepository;
        private ISmartVendorService smartVendorService;
        //private ISmartCOIDService smartCoidService;
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        public VendorService(IVendorRepository vendorRepo, ISmartVendorService smartVendorSvc)
        {
            vendorRepository = vendorRepo;
            smartVendorService = smartVendorSvc;
        }

        public IEnumerable<PunchOutVendor> GetPunchoutVendors(Facility facility)
        {
            var punchOutVendors = new List<PunchOutVendor>();

            var allPunchOutVendors = vendorRepository.GetPunchOutVendors(facility);
            if (allPunchOutVendors != null)
            {
                //Filter to only vendors that the COID has.
                foreach (var punchOutVendor in allPunchOutVendors.Where(x => x.VendorId != null))
                {
                    try
                    {
                        var vendor = smartVendorService.GetVendor("System", facility.COID, (int)punchOutVendor.VendorId);
                        if (vendor != null)
                        {
                            punchOutVendor.Vendor = vendor;
                            punchOutVendors.Add(punchOutVendor);
                        }
                    }
                    catch(Exception ex)
                    {
                        log.Error(string.Format("Error on GetPunchOutVendors on COID {0}", facility.COID), ex);
                    }
                }
            }
            
            return punchOutVendors;
        }

        public Vendor GetVendordByVendorId(string COID, int vendorId)
        {
            var vendor = new Vendor();

            var smartVendor = smartVendorService.GetVendor("System", COID, vendorId);
            if (smartVendor != null)
            {
                vendor.Id = smartVendor.Id;
                vendor.Name = smartVendor.Name;
            }

            return vendor;
        }

        public Vendor GetVendorInformationById(string COID, int vendorId)
        {
            var vendor = new Vendor();

            var smartVendor = smartVendorService.GetVendorInformationById("System", COID, vendorId);
            if (smartVendor != null)
            {
                vendor.Id = smartVendor.Id;
                vendor.Name = smartVendor.Name;
            }

            return vendor;
        }

        public VendorDetails GetVendorDetailsByVendorId(string COID, int vendorId)
        {
            var vendor = new VendorDetails();

            var smartVendorDetails = smartVendorService.GetVendorDetails("System", COID, vendorId);
            if (smartVendorDetails != null)
            {
                vendor = smartVendorDetails;
            }
            return vendor;
        }

        public List<VendorHeaderInfo> GetAllVendorsForCoid(string COID)
        {
            var vendors = new List<VendorHeaderInfo>();
            try
            {
                var smartVendors = smartVendorService.GetAllVendorHeaders("System", COID);

                if (smartVendors != null)
                {
                    vendors = smartVendors;
                }
            }
            catch(Exception ex)
            {
                log.Error(string.Format("Error on calling GetAllVendorsForCOID with COID {0}", COID), ex); 
            }
            return vendors;
        }

        public List<Vendor> GetAllVendorsForSpecificCoid(string COID)
        {
            var vendorInfo = new List<Vendor>();
            var smartVendors = smartVendorService.GetAllVendorHeaders("System", COID);

            vendorInfo = smartVendors.ConvertAll(x => new Vendor { Id = x.Id, Name = x.Name });

            return vendorInfo;
        }
    }
}
