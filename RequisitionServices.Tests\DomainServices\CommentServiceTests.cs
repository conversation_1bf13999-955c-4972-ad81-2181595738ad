﻿using System;
using System.Collections.Generic;
using AutoMapper;
using Castle.DynamicProxy.Generators.Emitters.SimpleAST;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using RequisitionServices.DomainModel.Comments;
using RequisitionServices.DomainModel.Constants;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Utility;

namespace RequisitionServices.Tests.DomainServices
{
    [TestClass]
    public class CommentServiceTests
    {
        static Mock<ICommentRepository> _commentRepository;
        static Mock<IUserService> _userService;
        static Mock<IRequisitionService> _requisitionService;
        static Mock<IMapper> _mapper;
        static Mock<IFacilityWorkflowService> _facilityWorkflowService;

        static ICommentService _commentService;

        readonly string testUsername = "abc/def1234";

        [ClassInitialize]
        public static void ClassInit(TestContext context)
        {
            _commentRepository = new Mock<ICommentRepository>();
            _userService = new Mock<IUserService>();
            _requisitionService = new Mock<IRequisitionService>();
            _mapper = new Mock<IMapper>();
            _facilityWorkflowService = new Mock<IFacilityWorkflowService>();
        }

        [TestInitialize]
        public void Initialize()
        {
            _commentService = new CommentService(_commentRepository.Object, _userService.Object, _requisitionService.Object, _mapper.Object, _facilityWorkflowService.Object);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _commentService = null;
            _commentRepository.Reset();
            _userService.Reset();
            _requisitionService.Reset();
            _mapper.Reset();
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            _commentService = null;
            _commentRepository = null;
            _userService = null;
            _requisitionService = null;
            _mapper = null;
        }

        #region Get Tests

        [TestMethod]
        public void Get_Should_Throw_BadRequestException_With_Invalid_RequisitionId()
        {
            TryGet(-1, testUsername);
        }

        [TestMethod]
        public void Get_Should_Throw_BadRequestException_With_Invalid_Username()
        {
            TryGet(1, null);
        }

        [TestMethod]
        public void Get_Should_Throw_BadRequestException_When_Requisition_Returns_As_Null()
        {
            SetupGetMocks(null, new User{AccountName = testUsername}, new List<Comment>(), new List<UserWorkflowStep>());

            TryGet(1, testUsername);
        }

        [TestMethod]
        public void Get_Should_Throw_BadRequestException_When_User_Returns_As_Null()
        {
            SetupGetMocks(new Requisition{RequisitionId = 1}, null, new List<Comment>(), new List<UserWorkflowStep>());

            TryGet(1, testUsername);
        }

        [TestMethod]
        public void Get_Should_Return_CanAddComment_True_For_Requisition_Creator()
        {
            SetupGetMocks(new Requisition { RequisitionId = 1, CreatedBy = testUsername}, new User{AccountName = testUsername}, new List<Comment>(), new List<UserWorkflowStep>());

            var response = _commentService.Get(1, testUsername);

            _commentRepository.Verify(x => x.Get(It.IsAny<int>()), Times.Once);
            Assert.IsTrue(response.CanAddComment);
        }

        [TestMethod]
        public void Get_Should_Return_CanAddComment_True_For_Approver_In_Workflow()
        {
            SetupGetMocks(
                new Requisition { RequisitionId = 1, CreatedBy = "blah", LocationIdentifier = "1_2", IsVendor = false }, 
                new User { AccountName = testUsername }, 
                new List<Comment>(), 
                new List<UserWorkflowStep>
                {
                    new UserWorkflowStep
                    {
                        Approver = new Approver { User = new User { AccountName = testUsername } }
                    }
                });

            var response = _commentService.Get(1, testUsername);

            _commentRepository.Verify(x => x.Get(It.IsAny<int>()), Times.Once);
            Assert.IsTrue(response.CanAddComment);
        }

        [TestMethod]
        public void Get_Should_Return_CanAddComment_True_For_Approver_In_Vendor_Workflow()
        {
            SetupGetMocks(
                new Requisition { RequisitionId = 1, CreatedBy = "blah", LocationIdentifier = "1_2", IsVendor = true },
                new User { AccountName = testUsername },
                new List<Comment>(),
                new FacilityWorkflowDTO
                {
                    Steps = new List<FacilityWorkflowStep> {
                        new FacilityWorkflowStep {
                            Approver = new Approver { User = new User { AccountName = testUsername } }
                        }
                    }
                });

            var response = _commentService.Get(1, testUsername);

            _commentRepository.Verify(x => x.Get(It.IsAny<int>()), Times.Once);
            Assert.IsTrue(response.CanAddComment);
        }

        #endregion

        #region Add Tests

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_When_Request_Is_Null()
        {
            TryAdd(null);
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_With_Invalid_Username()
        {
            TryAdd(new AddCommentDTO{RequisitionId = 1, Text = "abc", Username = null});
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_With_Null_Text()
        {
            TryAdd(new AddCommentDTO { RequisitionId = 1, Text = null, Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_With_Text_Longer_Than_Max()
        {
            var text = "";
            for (var i = 0; i <= Values.RequisitionCommentMaxLength; i++)
            {
                text = string.Concat(text, "i");
            }

            TryAdd(new AddCommentDTO { RequisitionId = 1, Text = text, Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_With_Invalid_RequisitionId()
        {
            TryAdd(new AddCommentDTO { RequisitionId = 0, Text = "abc", Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_When_Requisition_Returns_As_Null()
        {
            SetupAddMocks(null, new User{Id = 1, AccountName = testUsername }, new List<UserWorkflowStep>(), new Comment());

            TryAdd(new AddCommentDTO { RequisitionId = 1, Text = "abc", Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_When_User_Returns_As_Null()
        {
            SetupAddMocks(new Requisition{RequisitionId = 1}, null, new List<UserWorkflowStep>(), new Comment());

            TryAdd(new AddCommentDTO { RequisitionId = 1, Text = "abc", Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_When_Requisition_Returns_Invalid()
        {
            SetupAddMocks(new Requisition(), new User{Id = 1, AccountName = testUsername}, new List<UserWorkflowStep>(), new Comment());

            TryAdd(new AddCommentDTO { RequisitionId = 1, Text = "abc", Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Throw_BadRequestException_When_User_Returns_Invalid()
        {
            SetupAddMocks(new Requisition { RequisitionId = 1 }, new User{ AccountName = testUsername }, new List<UserWorkflowStep>(), new Comment());

            TryAdd(new AddCommentDTO { RequisitionId = 1, Text = "abc", Username = testUsername });
        }

        [TestMethod]
        public void Add_Should_Call_CommentRepository()
        {
            SetupAddMocks(new Requisition { RequisitionId = 1, LocationIdentifier = "1_2", CreatedBy = testUsername }, new User { Id = 1, AccountName = testUsername }, new List<UserWorkflowStep>(), new Comment());

            _commentService.Add(new AddCommentDTO { RequisitionId = 1, Text = "abc", Username = testUsername });

            _commentRepository.Verify(x => x.Add(It.IsAny<Comment>()), Times.Once);
        }

        [TestMethod]
        public void Add_Should_Not_Call_AddNotifications_If_Commenter_Is_Requisition_Owner_And_No_Approvers_In_Workflow()
        {
            SetupAddMocks(new Requisition { RequisitionId = 1, LocationIdentifier = "1_2", CreatedBy = testUsername }, new User { Id = 1, AccountName = testUsername }, new List<UserWorkflowStep>(), new Comment());

            _commentService.Add(new AddCommentDTO { RequisitionId = 1, Text = "abc", Username = testUsername });

            _commentRepository.Verify(x => x.Add(It.IsAny<Comment>()), Times.Once);
            _commentRepository.Verify(x => x.AddNotifications(It.IsAny<List<UnreadComment>>()), Times.Never);
        }

        #endregion

        #region GetNotifications Tests

        [TestMethod]
        public void GetNotifications_Should_Throw_BadRequestException_With_Invalid_Username()
        {
            try
            {
                _commentService.GetNotifications(null, 0, 0, RequisitionSortOrder.Oldest, null);
                Assert.IsTrue(false, "CommentService.Get did not throw an BadRequestException");
            }
            catch (BadRequestException e)
            {
                Assert.IsTrue(true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(false, $"CommentService.Get returned Exception of type {e.GetType()}");
            }
        }

        #endregion

        private void SetupGetMocks(Requisition requisition, User user, List<Comment> comments, IEnumerable<UserWorkflowStep> userWorkflowSteps)
        {
            _requisitionService.Setup(x => x.GetRequisition(It.IsAny<string>(), It.IsAny<int>()))
                .Returns(requisition);

            _userService.Setup(x => x.GetUserByAccountName(It.IsAny<string>()))
                .Returns(user);

            _commentRepository.Setup(x => x.Get(It.IsAny<int>()))
                .Returns(comments);

            _userService.Setup(x => x.GetUserWorkflowSteps(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(userWorkflowSteps);
        }

        private void SetupGetMocks(Requisition requisition, User user, List<Comment> comments, FacilityWorkflowDTO facilityWorkflow)
        {
            _requisitionService.Setup(x => x.GetRequisition(It.IsAny<string>(), It.IsAny<int>()))
                .Returns(requisition);

            _userService.Setup(x => x.GetUserByAccountName(It.IsAny<string>()))
                .Returns(user);

            _commentRepository.Setup(x => x.Get(It.IsAny<int>()))
                .Returns(comments);

            _facilityWorkflowService.Setup(x => x.Get(It.IsAny<string>(), It.IsAny<WorkflowTypeEnum>()))
                .Returns(facilityWorkflow);
        }

        private void TryGet(int requisitionId, string username)
        {
            try
            {
                _commentService.Get(requisitionId, username);
                Assert.IsTrue(false, "CommentService.Get did not throw an BadRequestException");
            }
            catch (BadRequestException e)
            {
                Assert.IsTrue(true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(false, $"CommentService.Get returned Exception of type {e.GetType()}");
            }
        }

        public void SetupAddMocks(Requisition requisition, User user, IEnumerable<UserWorkflowStep> workflowSteps, Comment comment)
        {
            _requisitionService.Setup(x => x.GetRequisition(It.IsAny<string>(), It.IsAny<int>()))
                .Returns(requisition);

            _userService.Setup(x => x.GetUserByAccountName(It.IsAny<string>()))
                .Returns(user);

            _userService.Setup(x => x.GetUserWorkflowSteps(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<int>()))
                .Returns(workflowSteps);

            _commentRepository.Setup(x => x.Add(It.IsAny<Comment>()))
                .Returns(comment);
        }

        private void TryAdd(AddCommentDTO request)
        {
            try
            {
                _commentService.Add(request);
                Assert.IsTrue(false, "CommentService.Add did not throw an BadRequestException");
            }
            catch (BadRequestException e)
            {
                Assert.IsTrue(true);
            }
            catch (Exception e)
            {
                Assert.IsTrue(false, $"CommentService.Add returned Exception of type {e.GetType()}");
            }
        }
    }
}
