﻿body {
    overflow:hidden;
}

.mainView-title {
    margin-top: 30px;
    margin-left: 3px;
    margin-right:8px;
    float:right;
}

.blade .modalTitle, .bladeSmall .modalTitle {
    margin-top: 35px;
    margin-left: 2px;
    margin-right:6px;
    float:right;
}

.summaryArea {
    margin-left:-10px;
    /*width:100%;*/
}

.widgetDateMonth {
    margin-bottom: -3px;
}

.widgetDateYear {
    margin-top: -2px;
}

.widgetBody .scrollbar-outer {
    max-height:625px;
}

.itemCardContainer .scrollbar-outer{
    max-height: 620px;
}

.modal-body .itemCardContainer .scrollbar-dynamic {
    max-height:520px;
}

.marginLeft4IE {
    margin-left:4px;
}

.btn:focus {
    color: #333;
}

.btn-group {
    display:inline;
}

.scrollbar-outer > .scroll-element div,
.scrollbar-dynamic > .scroll-element div {
    /*display:none;*/
}

.largeChart {
    width:90%;
    height:260px;
}

.textInDonut {
    left:28%;
}