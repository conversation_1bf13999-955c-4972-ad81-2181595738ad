﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.FacilityWorkflow;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.DomainServices.Interface
{
    public interface IFacilityWorkflowService
    {
        FacilityWorkflowDTO Get(string coid, WorkflowTypeEnum workflowType);
        void Save(SaveFacilityWorkflowDTO request);
        bool AssignDelegateForApprover(int delegateUserId, string username, User user, User delegateUser);
        void DeleteDelegatedSteps(int delegatedByUserId);
    }
}
