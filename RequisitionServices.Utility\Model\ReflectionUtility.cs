﻿using System;
using System.Collections;
using System.Linq.Expressions;
using System.Reflection;

namespace RequisitionServices.Utility.Model
{
	public static class ReflectionUtility
	{
		public static string GetPropertyName<T>(Expression<Func<T>> expression)
		{
			var body = (MemberExpression)expression.Body;
			return body.Member.Name;
		}

		public static string GetPropertyName<T, TReturn>(Expression<Func<T, TReturn>> expression)
		{
			var body = (MemberExpression)expression.Body;
			return body.Member.Name;
		}

		public static bool IsNonStringEnumerable(this PropertyInfo pi)
		{
			return pi != null && pi.PropertyType.IsNonStringEnumerable();
		}

		public static bool IsNonStringEnumerable(this object instance)
		{
			return instance != null && instance.GetType().IsNonStringEnumerable();
		}

		public static bool IsNonStringEnumerable(this Type type)
		{
			if (type == null || type == typeof(string))
				return false;
			return typeof(IEnumerable).IsAssignableFrom(type);
		}
	}
}
