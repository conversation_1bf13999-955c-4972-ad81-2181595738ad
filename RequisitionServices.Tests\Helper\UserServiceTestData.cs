﻿using System.Collections.Generic;
using RequisitionServices.DomainModel.Locations;

namespace RequisitionServices.Tests.Helper
{
    public class UserServiceTestData
    {
        public static string userId = "ABC1234";
        public static string coid = "09391";

        public static List<Department> ListDeptResponse()
        {
            List<Department> deptList = new List<Department>();
            Department dept = new Department();

            dept.COID = "09391";
            dept.Description = "ACUDOSE CABINET";
            dept.Id = 185;
            dept.IsActive = true;
            deptList.Add(dept);

            dept = new Department();
            dept.COID = "09391";
            dept.Description = "PHARMACY ACUDOSE 2";
            dept.Id = 186;
            dept.IsActive = true;
            deptList.Add(dept);

            dept = new Department();
            dept.COID = "09391";
            dept.Description = "TESTING PURPOSE";
            dept.Id = 187;
            dept.IsActive = true;
            deptList.Add(dept);

            return deptList;
        }
    }
}
