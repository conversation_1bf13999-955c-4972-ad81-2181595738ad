﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace RequisitionServices.DomainModel.DigitalSignOff
{
    /// <summary>
    /// Houses active directory information for the signer of 
    /// Digital Sign Offs for VBO
    /// </summary>
    public class DigitalSignOffUser
    {
        /// <summary>Primary Key for DigitalSignOffUsers</summary>
        public int Id { get; set; }
        /// <summary>
        /// Job Title for signer
        /// </summary>
        public string Title { get; set; }
        /// <summary>
        /// First name for signer
        /// </summary>
        public string FirstName { get; set; }
        /// <summary>
        /// Last name for signer
        /// </summary>
        public string LastName { get; set; }
        /// <summary>
        /// Last time the record was updated
        /// </summary>
        public DateTime? LastUpdatedUTC { get; set; }
        /// <summary>
        /// Check if user is listed inside VPRO
        /// </summary>
        public bool VerifiedProfessional { get; set; }
        /// <summary>
        /// User's 3/4 ID
        /// </summary>
        public string AccountName { get; set; }
    }
}
