﻿USE [eProcurementQA]
GO

IF TYPE_ID(N'Varchar50Template') IS NULL
	CREATE TYPE [dbo].[Varchar50Template] AS TABLE(
		[VarcharVal] varchar(50) NOT NULL
	)
GO

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsGet
Purpose     : Returns a paginated list of requisitions for the MyRequisitions page.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 10-26-2017
Usage       : Executed by our Requisition Services server
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/26/2017      Script created
Peter Hurlburt		10/27/2017      Submitted for deployment 21
Peter Hurlburt		11/01/2017		Added WITH (NOLOCK) statements
Peter Hurlburt		11/02/2017		Changing ALTER to CREATE, adding new script headers
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
									Submitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column 
Colin Glasco		08/18/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

SELECT
	[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
	[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
	[DistinctRequisitions].[Comments] AS [Comments],
	[DistinctRequisitions].[CreateDate] AS [CreateDate],
	[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
	[DistinctRequisitions].[IsMobile] AS [IsMobile],
	[DistinctRequisitions].[IsVendor] AS [IsVendor],
	[AllReqItems].[Id] AS [RequisitionItemId],
	[AllReqItems].[ItemId] AS [RequisitionItemNumber],
	[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
	[AllReqItems].[QuantityToOrder] AS [RequisitionItemQuantityToOrder],
	[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
	[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
	[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
	[AllReqItems].[Discount] AS [Discount],
	[AllReqItems].[VendorId] AS [VendorId],
	[AllReqItems].[UnitCost] AS [UnitCost],
	[AllReqItems].[ParIdentifier] AS [ParIdentifier],
	[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
	[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
	[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
	[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
	[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
		ROW_NUMBER() OVER (
			ORDER BY (
				CASE @statusSorting 
					WHEN 1 THEN 
						CASE [RequisitionStatusTypeId] 
							WHEN 7 THEN 1 
							WHEN 6 THEN 2
							WHEN 12 THEN 3
							WHEN 1 THEN 4
							WHEN 2 THEN 5
							WHEN 4 THEN 6
							ELSE 7
						END 
				END), [ReqTypeGroupingOrder], 
				CASE @oldestFirst 
					WHEN 0 THEN [Req].[CreateDate] END DESC,
				CASE @oldestFirst 
					WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
		[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
		[Req].[RequisitionId] AS [RequisitionId],
		[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Req].[LocationIdentifier] AS [LocationIdentifier],
		[Req].[Comments] AS [Comments],
		[Req].[CreateDate] AS [CreateDate],
		[Req].[RequisitionTypeId] AS [RequisitionTypeId],
		[Req].[IsMobile] AS [IsMobile],
		[Req].[IsVendor] AS [IsVendor],
		(CASE @statusSorting 
			WHEN 1 THEN 
				CASE [RequisitionStatusTypeId] 
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7
				END 
		END) AS [ConditionalStatusSorting],
		dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
		+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
		- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
			CASE
				WHEN @mobileReqs = 1 
				THEN 
					CASE
						WHEN [Requisition].[IsMobile] = @mobileReqs 
						THEN 1
						ELSE 2 
					END
				ELSE 
					CASE
						WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup) 
						THEN 1
						ELSE 2 
					END
			END AS [ReqTypeGroupingOrder],
			[Requisition].[RequisitionId] AS [RequisitionId],
			[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[Requisition].[LocationIdentifier] AS [LocationIdentifier],
			[Requisition].[Comments] AS [Comments],
			[Requisition].[CreateDate] AS [CreateDate],
			[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
			[Requisition].[IsMobile] AS [IsMobile],
			[Requisition].[IsVendor] AS [IsVendor]
		FROM
			[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
			[Requisition].[CreatedBy] = @userName
			AND 5 <> [Requisition].[RequisitionStatusTypeId]
			AND 8 <> [Requisition].[RequisitionStatusTypeId]
	) AS [Req]
		LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
		LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
		LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	WHERE
		@filterText IS NULL OR
		([Req].[RequisitionId] LIKE '%' + @filterText + '%'
		OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
		OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
		OR [Req].[Comments] LIKE '%' + @filterText + '%'
		OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
		OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
		OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
		OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
		OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
		OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
		OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
		[Req].[ReqTypeGroupingOrder],
		[Req].[RequisitionId],
		[Req].[RequisitionStatusTypeId],
		[Req].[LocationIdentifier],
		[Req].[Comments],
		[Req].[CreateDate],
		[Req].[RequisitionTypeId],
		[Req].[IsMobile],
		[Req].[IsVendor]
	ORDER BY
		[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
		CASE WHEN @oldestFirst = 0 THEN [Req].[CreateDate] END DESC,
		CASE WHEN @oldestFirst = 1 THEN [Req].[CreateDate] END ASC
		OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]

	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
	LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]

ORDER BY 
	[DistinctRequisitions].rowNumber


OPTION (RECOMPILE)

END -- Procedure
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_TemplatesGet
Purpose     : Returns a paginated list of the users templates.
Used By     : SMART Procurement team
Author      : Cassie Martinez
Created     : 11-28-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------		-----------------------
Cassie Martinez		11/28/2017		Script created
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_TemplatesGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS
                BEGIN

SELECT
	[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
	[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
	[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
	[DistinctRequisitions].[Comments] AS [Comments],
	[AllReqItems].[Id] AS [RequisitionItemId],
	[AllReqItems].[ParIdentifier] AS [ParIdentifier],
	[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
		ROW_NUMBER() OVER (ORDER BY [Req].[CreateDate] DESC) as rowNumber,
		[Req].[RequisitionId] AS [RequisitionId],
		[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Req].[LocationIdentifier] AS [LocationIdentifier],
		[Req].[RequisitionTypeId] AS [RequisitionTypeId],
		[Req].[Comments] AS [Comments],
		dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
		+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
		- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
			[ReqGroup].[RequisitionId] AS [RequisitionId],
			[ReqGroup].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[ReqGroup].[LocationIdentifier] AS [LocationIdentifier],
			[ReqGroup].[RequisitionTypeId] AS [RequisitionTypeId],
			[ReqGroup].[Comments] AS [Comments],
			[ReqGroup].[CreateDate] AS [CreateDate]
		FROM
			[dbo].[Requisitions] AS [ReqGroup] WITH (NOLOCK)
		WHERE
			[ReqGroup].[CreatedBy] = @userName
			AND [ReqGroup].[RequisitionStatusTypeId] = 8
	) AS [Req]
		LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	WHERE
		@filterText IS NULL OR
		([Req].[RequisitionId] LIKE '%' + @filterText + '%'
		OR [Req].[Comments] LIKE '%' + @filterText + '%'
		OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
		OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
		OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
		[Req].[RequisitionId],
		[Req].[RequisitionStatusTypeId],
		[Req].[LocationIdentifier],
		[Req].[RequisitionTypeId],
		[Req].[Comments],
		[Req].[CreateDate]
	ORDER BY
		[Req].[CreateDate] DESC
		OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_PendingApprovalsGet
Purpose     : Returns a paginated list of requisitions and ad hoc reviews for the MyApprovals page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 11-14-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/14/2017      Script created
Peter Hurlburt		11/29/2017      Submitted for deployment 24
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		09/14/2020		Rewrote the query to optimize the sproc (stop timeout)
Peter Hurlburt		02/04/2022		Added vendor requisitions as possible
									results, using facility workflow steps
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_PendingApprovalsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@vboFirst bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @totalPendingActions bigint
DECLARE @pendingActions TABLE (
	RowOrder int,
	SortOrder int,
	RequisitionId int,
	RequisitionStatusTypeId int,
	RequisitionLocationIdentifier VARCHAR(50),
	RequisitionComments VARCHAR(255),
	RequisitionCreateDate DATETIME,
	RequisitionTypeId int,
	PendingReviewsExist bit,
	RequisitionerId VARCHAR(100),
	RequisitionerFirstName VARCHAR(255),
	RequisitionerLastName VARCHAR(255),
	ReviewId int,
	RequesterId VARCHAR(255),
	RequesterFirstName VARCHAR(255),
	RequesterLastName VARCHAR(255),
	RequesterComments VARCHAR(255),
	RequestCreateDate DATETIME,
	CountryCode VARCHAR(3),
	DateForSorting DATETIME,
	IsVendor BIT
)

INSERT INTO @pendingActions (
	RowOrder,
	SortOrder,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionCreateDate,
	RequisitionTypeId,
	PendingReviewsExist,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	ReviewId,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	RequestCreateDate,
	CountryCode,
	DateForSorting,
	IsVendor
)
(
	SELECT 
		ROW_NUMBER() OVER
		(
			ORDER BY SortOrder,
				CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
				CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC,
				CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC
		) AS RowOrder,
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionLocationIdentifier,
		RequisitionComments,
		RequisitionCreateDate,
		RequisitionTypeId,
		PendingReviewsExist,
		RequisitionerId,
		RequisitionerFirstName,
		RequisitionerLastName,
		ReviewId,
		RequesterId,
		RequesterFirstName,
		RequesterLastName,
		RequesterComments,
		RequestCreateDate,
		CountryCode,
		DateForSorting,
		IsVendor
	FROM 
	(
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 2
					ELSE CASE
						WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup) THEN 1
						ELSE 2
						END
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Approval' AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [UserWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON WorkflowStep.UserId = Requisitioner.Id
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor,
					(CASE RequisitionTypeId
						WHEN 5 THEN 2	--Capital
						WHEN 6 THEN 3	--Punchout
						WHEN 7 THEN 1	--Rush
						ELSE 0			--Standard
					END) AS [WorkflowTypeId]
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId = 2 --Pending Approval
				) AS Requisition
				ON Requisitioner.AccountName = Requisition.CreatedBy
				AND WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
				AND WorkflowStep.Step = Requisition.ApprovalStep
				AND WorkflowStep.WorkflowTypeId = Requisition.WorkflowTypeId
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 0
		)
		UNION
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 2
					ELSE CASE
						WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup) THEN 1
						ELSE 2
						END
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Approval' AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [FacilityWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId = 2 --Pending Approval
				) AS Requisition
				ON WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
				AND WorkflowStep.Step = Requisition.ApprovalStep
			LEFT OUTER JOIN Users AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 1
		)
		UNION
		(
			SELECT
				CASE @statusSorting
					WHEN 1 THEN 1
					ELSE 2
					END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				CAST (0 AS BIT) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				ReqItem.ParIdentifier AS RequisitionItemParIdentifier,
				Review.Id AS ReviewId,
				Requester.AccountName AS RequesterId,
				Requester.FirstName AS RequesterFirstName,
				Requester.LastName AS RequesterLastName,
				Review.RequesterComments AS RequesterComments,
				Review.CreateDate AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Review' AS StatusForFilter,
				Review.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [AdhocReviews] AS Review WITH (NOLOCK)
			INNER JOIN [Users] AS Requester WITH (NOLOCK)
				ON Review.Requester = Requester.AccountName
			INNER JOIN [Requisitions] AS Requisition WITH (NOLOCK)
				ON Review.RequisitionId = Requisition.RequisitionId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN RequisitionItems AS ReqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = ReqItem.RequisitionId
			WHERE Review.Reviewer = @userName
				AND Review.Recommended IS NULL
				AND Requisition.RequisitionStatusTypeId IN (0, 2, 3, 4, 8, 9, 10, 11)
		)
	) AS PendingAction
	WHERE @filterText IS NULL OR
	(PendingAction.RequisitionId LIKE @filterText + '%'
	OR PendingAction.RequisitionerId LIKE '%' + @filterText + '%'
	OR PendingAction.RequisitionerFirstName LIKE @filterText + '%'
	OR PendingAction.RequisitionerLastName LIKE @filterText + '%'
	OR (PendingAction.RequisitionerFirstName + ' ' + PendingAction.RequisitionerLastName) LIKE @filterText + '%'
	OR PendingAction.RequesterId LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterFirstName LIKE @filterText + '%'
	OR PendingAction.RequesterLastName LIKE @filterText + '%'
	OR (PendingAction.RequesterFirstName + ' ' + PendingAction.RequesterLastName) LIKE @filterText + '%'
	OR PendingAction.RequisitionComments LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterComments LIKE '%' + @filterText + '%'
	OR PendingAction.StatusForFilter LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND PendingAction.RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
	OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
)

SELECT @totalPendingActions = COUNT(*) FROM @pendingActions

SELECT 
	PendingAction.RequisitionId AS RequisitionId,
	PendingAction.RequisitionStatusTypeId AS RequisitionStatusTypeId,
	PendingAction.RequisitionLocationIdentifier AS RequisitionLocationIdentifier,
	PendingAction.RequisitionComments AS RequisitionComments,
	PendingAction.RequisitionCreateDate AS RequisitionCreateDate,
	PendingAction.RequisitionTypeId AS RequisitionTypeId,
	PendingAction.PendingReviewsExist AS PendingReviewsExist,
	RequisitionItem.Id AS RequisitionItemId,
	RequisitionItem.ItemId AS RequisitionItemNumber,
	RequisitionItem.RequisitionItemStatusTypeId AS RequisitionItemStatusTypeId,
	RequisitionItem.QuantityToOrder AS RequisitionItemQuantityToOrder,
	RequisitionItem.ParentSystemId AS RequisitionItemParentSystemId,
	RequisitionItem.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
	RequisitionItem.ParentRequisitionItemId AS RequisitionItemParentItemId,
	RequisitionItem.ParIdentifier AS RequisitionItemParIdentifier,
	RequisitionItem.Discount AS Discount,
	RequisitionItem.VendorId AS VendorId,
	RequisitionItem.UnitCost AS UnitCost,
	SPRDetail.VendorId AS SprDetailsVendorId,
	SPRDetail.VendorName AS SprDetailsVendorName,
	SPRDetail.PartNumber AS SprDetailsPartNumber,
	SPRDetail.EstimatedPrice AS SprDetailsEstimatedPrice,
	Attachment.RequisitionItemId AS SprDetailsFileAttachment,
	PendingAction.RequisitionerId AS RequisitionerId,
	PendingAction.RequisitionerFirstName AS RequisitionerFirstName,
	PendingAction.RequisitionerLastName AS RequisitionerLastName,
	PendingAction.ReviewId AS ReviewId,
	PendingAction.RequesterId AS RequesterId,
	PendingAction.RequesterFirstName AS RequesterFirstName,
	PendingAction.RequesterLastName AS RequesterLastName,
	PendingAction.RequesterComments AS RequesterComments,
	PendingAction.RequestCreateDate AS RequestCreateDate,
	@totalPendingActions AS TotalReqCount,
	PendingAction.CountryCode AS CountryCode,
	PendingAction.IsVendor
FROM (
	SELECT * FROM @pendingActions
	ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS PendingAction
LEFT OUTER JOIN [RequisitionItems] AS RequisitionItem WITH (NOLOCK)
	ON PendingAction.RequisitionId = RequisitionItem.RequisitionId
LEFT OUTER JOIN [SPRDetails] AS SPRDetail WITH (NOLOCK)
	ON RequisitionItem.Id = SPRDetail.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM [FileAttachments] WITH (NOLOCK)) AS Attachment 
	ON RequisitionItem.Id = Attachment.RequisitionItemId

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_ApprovalHistoryGet
Purpose     : Returns a paginated list of requisition history items for the MyApprovals page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 11-20-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Peter Hurlburt		11/20/2017		Script created
Peter Hurlburt		11/28/2017		Submitted for deployment 24
Peter Hurlburt		12/15/2017		Removed an overly-verbose union statement
									Submitted for deployment 24
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Colin Glasco		10/27/2021		Rewriting the sproc to optimize the query
									Remove restriction of only showing histories
									since most recent Requisition draft status
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_ApprovalHistoryGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@vboFirst bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @historyList TABLE 
(
	RowOrder INT NOT NULL,
	SortOrder INT NOT NULL,
	HistoryItemId INT NOT NULL,
	HistoryItemCreateDate DATETIME NOT NULL,
	HistoryItemStatusTypeId INT NOT NULL,
	RequisitionId INT NOT NULL,
	RequisitionStatusTypeId INT NOT NULL,
	RequisitionLocationIdentifier VARCHAR(50) NOT NULL,
	RequisitionComments VARCHAR(255) NULL,
	RequisitionTypeId INT NOT NULL,
	RequisitionerId VARCHAR(100) NOT NULL,
	RequisitionerFirstName VARCHAR(255) NOT NULL,
	RequisitionerLastName VARCHAR(255) NOT NULL,
	RequesterId VARCHAR(255) NULL,
	RequesterFirstName VARCHAR(255) NULL,
	RequesterLastName VARCHAR(255) NULL,
	RequesterComments VARCHAR(255) NULL,
	ReviewerRecommended BIT NULL,
	ReviewerComments VARCHAR(255) NULL,
	IsVendor BIT
)
DECLARE @tempReqTypeIds TABLE (Id INT)
DECLARE @totalhistoryCount BIGINT

INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup

INSERT INTO @historyList
SELECT 
	ROW_NUMBER() OVER
	(
		ORDER BY SortOrder,
			CASE @oldestFirst WHEN 1 THEN [HistoryItemCreateDate] END ASC,
			CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
			CASE @oldestFirst WHEN 0 THEN [HistoryItemCreateDate] END DESC
	) AS RowOrder,
	SortOrder,
	HistoryItemId,
	HistoryItemCreateDate,
	HistoryItemStatusTypeId,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionTypeId,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	ReviewerRecommended,
	ReviewerComments,
	IsVendor
FROM 
(
	SELECT 
		CASE @statusSorting
			WHEN 1 THEN CASE requisitionHistory.RequisitionStatusTypeId
				WHEN 6 THEN 3
				WHEN 3 THEN 2
				ELSE review.Recommended END
			ELSE CASE
				WHEN requisition.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
				THEN 1
				ELSE 2
				END
			END AS SortOrder,
		CASE requisitionHistory.RequisitionStatusTypeId
			WHEN 10
			THEN
				CASE review.Recommended
				WHEN 1 THEN 'Recommend Approve'
				ELSE 'Recommend Deny'
				END
			ELSE	
				requisitionStatus.[Description] 
			END AS StatusForFilter,
		requisitionHistory.Id AS HistoryItemId,
		requisitionHistory.CreateDate AS HistoryItemCreateDate,
		requisitionHistory.RequisitionStatusTypeId AS historyItemStatusTypeId,
		requisitionHistory.RequisitionId AS RequisitionId,
		requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
		requisition.LocationIdentifier AS RequisitionLocationIdentifier,
		requisition.Comments AS RequisitionComments,
		requisition.RequisitionTypeId AS RequisitionTypeId,
		requisition.CreatedBy AS RequisitionerId,
		reqItem.ParIdentifier AS RequisitionItemParIdentifier,
		requisitioner.FirstName AS RequisitionerFirstName,
		requisitioner.LastName AS RequisitionerLastName,
		reviewRequester.AccountName AS RequesterId,
		reviewRequester.FirstName AS RequesterFirstName,
		reviewRequester.LastName AS RequesterLastName,
		review.RequesterComments AS RequesterComments,
		review.Recommended AS ReviewerRecommended,
		review.ReviewerComments AS ReviewerComments,
		requisition.IsVendor
	FROM RequisitionStatusHistories requisitionHistory WITH (NOLOCK)
	INNER JOIN RequisitionStatusTypes requisitionStatus WITH (NOLOCK)
		ON requisitionHistory.RequisitionStatusTypeId = requisitionStatus.Id
	INNER JOIN Requisitions requisition WITH (NOLOCK)
		ON requisitionHistory.RequisitionId = requisition.RequisitionId
	INNER JOIN Users requisitioner WITH (NOLOCK)
		ON requisition.CreatedBy = requisitioner.AccountName
	LEFT OUTER JOIN AdhocReviews review WITH (NOLOCK)
		ON requisitionHistory.Id = review.ReviewerRequisitionStatusHistoryId
	LEFT OUTER JOIN Users reviewRequester WITH (NOLOCK)
		ON review.Requester = reviewRequester.AccountName
	LEFT OUTER JOIN RequisitionItems AS reqItem WITH (NOLOCK)
		ON Requisition.RequisitionId = reqItem.RequisitionId
	WHERE requisitionHistory.CreatedBy = @userName
	AND requisitionHistory.RequisitionStatusTypeId IN (3, 6, 10) --Approved, Denied, Review Provided
) AS HistoryRecord
WHERE @filterText IS NULL OR
	(HistoryRecord.[RequisitionId] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequisitionerId] LIKE '%' + @filterText + '%'
	OR (HistoryRecord.[RequisitionerFirstName] + ' ' + HistoryRecord.[RequisitionerLastName]) LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequesterId] LIKE '%' + @filterText + '%'
	OR (HistoryRecord.[RequesterFirstName] + ' ' + HistoryRecord.[RequesterLastName]) LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequisitionComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[RequesterComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[ReviewerComments] LIKE '%' + @filterText + '%'
	OR HistoryRecord.[StatusForFilter] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
	OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
GROUP BY
	SortOrder,
	HistoryItemId,
	HistoryItemCreateDate,
	HistoryItemStatusTypeId,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionTypeId,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	ReviewerRecommended,
	ReviewerComments,
	IsVendor

SELECT @totalHistoryCount = COUNT(*) FROM @historyList

SELECT 
	historyList.HistoryItemId,
	historyList.HistoryItemCreateDate,
	historyList.HistoryItemStatusTypeId,
	historyList.RequisitionId,
	historyList.RequisitionStatusTypeId,
	historyList.RequisitionLocationIdentifier,
	historyList.RequisitionComments,
	historyList.RequisitionTypeId,
	(CASE
		WHEN EXISTS (
			SELECT 1
			FROM AdhocReviews AS review WITH (NOLOCK)
			WHERE historyList.RequisitionId = review.RequisitionId
			AND review.Recommended IS NULL
		)
		THEN CAST (1 AS BIT)
		ELSE CAST (0 AS BIT) 
		END) AS PendingReviewsExist,
	item.Id AS RequisitionItemId,
	item.RequisitionItemStatusTypeId,
	item.QuantityToOrder AS RequisitionItemQuantityToOrder,
	item.ParentSystemId AS RequisitionItemParentSystemId,
	item.ParIdentifier AS RequisitionItemParIdentifier,
	SPR.VendorId AS SprDetailsVendorId,
	SPR.VendorName AS SprDetailsVendorName,
	SPR.PartNumber AS SprDetailsPartNumber,
	SPR.EstimatedPrice AS SprDetailsEstimatedPrice,
	ReqItemFileAttachments.RequisitionItemId AS SprDetailsFileAttachment,
	historyList.RequisitionerId,
	historyList.RequisitionerFirstName,
	historyList.RequisitionerLastName,
	historyList.RequesterId,
	historyList.RequesterFirstName,
	historyList.RequesterLastName,
	historyList.RequesterComments,
	historyList.ReviewerRecommended,
	historyList.ReviewerComments,
	historyList.IsVendor,
	@totalhistoryCount AS TotalReqCount
FROM
(
	SELECT * FROM @historyList 
	ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS historyList
LEFT OUTER JOIN RequisitionItems item WITH (NOLOCK)
	ON historyList.RequisitionId = item.RequisitionId
LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
	ON item.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) ReqItemFileAttachments
	ON item.Id = ReqItemFileAttachments.RequisitionItemId
ORDER BY historyList.RowOrder, item.Id

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_UpcomingApprovalsGet
Purpose     : Returns a paginated list of requisitions and ad hoc reviews for the MyApprovals page.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 12-14-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		12/14/2017      Script created
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR
Peter Hurlburt		02/04/2022		Added vendor requisitions as possible
									results, using facility workflow steps
Julio Pozo			03/03/2022		Return IsVendor flag
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_UpcomingApprovalsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@oldestFirst bit,
	@vboFirst bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

DECLARE @totalPendingActions bigint
DECLARE @pendingActions TABLE (
	RowOrder int,
	SortOrder int,
	RequisitionId int,
	RequisitionStatusTypeId int,
	RequisitionLocationIdentifier VARCHAR(50),
	RequisitionComments VARCHAR(255),
	RequisitionCreateDate DATETIME,
	RequisitionTypeId int,
	PendingReviewsExist bit,
	RequisitionerId VARCHAR(100),
	RequisitionerFirstName VARCHAR(255),
	RequisitionerLastName VARCHAR(255),
	ReviewId int,
	RequesterId VARCHAR(255),
	RequesterFirstName VARCHAR(255),
	RequesterLastName VARCHAR(255),
	RequesterComments VARCHAR(255),
	RequestCreateDate DATETIME,
	CountryCode VARCHAR(3),
	DateForSorting DATETIME,
	IsVendor BIT
)

INSERT INTO @pendingActions (
	RowOrder,
	SortOrder,
	RequisitionId,
	RequisitionStatusTypeId,
	RequisitionLocationIdentifier,
	RequisitionComments,
	RequisitionCreateDate,
	RequisitionTypeId,
	PendingReviewsExist,
	RequisitionerId,
	RequisitionerFirstName,
	RequisitionerLastName,
	ReviewId,
	RequesterId,
	RequesterFirstName,
	RequesterLastName,
	RequesterComments,
	RequestCreateDate,
	CountryCode,
	DateForSorting,
	IsVendor
)
(
	SELECT
		ROW_NUMBER() OVER
		(
			ORDER BY SortOrder,
				CASE @oldestFirst WHEN 1 THEN [DateForSorting] END ASC,
				CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
				CASE @oldestFirst WHEN 0 THEN [DateForSorting] END DESC
		) AS RowOrder,
		SortOrder,
		RequisitionId,
		RequisitionStatusTypeId,
		RequisitionLocationIdentifier,
		RequisitionComments,
		RequisitionCreateDate,
		RequisitionTypeId,
		PendingReviewsExist,
		RequisitionerId,
		RequisitionerFirstName,
		RequisitionerLastName,
		ReviewId,
		RequesterId,
		RequesterFirstName,
		RequesterLastName,
		RequesterComments,
		RequestCreateDate,
		CountryCode,
		DateForSorting,
		IsVendor
	FROM
	(
		(
			SELECT
				CASE 
					WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2
				END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				reqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Approval' AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [UserWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN [Users] AS Requisitioner WITH (NOLOCK)
				ON WorkflowStep.UserId = Requisitioner.Id
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor,
					(CASE RequisitionTypeId
						WHEN 5 THEN 2	--Capital
						WHEN 6 THEN 3	--Punchout
						WHEN 7 THEN 1	--Rush
						ELSE 0			--Standard
					END) AS [WorkflowTypeId]
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId = 2 --Pending Approval
				) AS Requisition
				ON Requisitioner.AccountName = Requisition.CreatedBy
				AND WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
				AND WorkflowStep.Step > Requisition.ApprovalStep
				AND WorkflowStep.WorkflowTypeId = Requisition.WorkflowTypeId
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			LEFT OUTER JOIN RequisitionItems AS reqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = reqItem.RequisitionId
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 0
		)
		UNION
		(
			SELECT
				CASE 
					WHEN Requisition.RequisitionTypeId IN (SELECT Id FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2
				END AS SortOrder,
				Requisition.RequisitionId AS RequisitionId,
				Requisition.RequisitionStatusTypeId AS RequisitionStatusTypeId,
				Requisition.LocationIdentifier AS RequisitionLocationIdentifier,
				Requisition.Comments AS RequisitionComments,
				Requisition.CreateDate AS RequisitionCreateDate,
				Requisition.RequisitionTypeId AS RequisitionTypeId,
				(CASE WHEN RequisitionReview.Id IS NULL THEN 0 ELSE 1 END) AS PendingReviewsExist,
				Requisitioner.AccountName AS RequisitionerId,
				Requisitioner.FirstName AS RequisitionerFirstName,
				Requisitioner.LastName AS RequisitionerLastName,
				reqItem.ParIdentifier AS RequisitionItemParIdentifier,
				NULL AS ReviewId,
				NULL AS RequesterId,
				NULL AS RequesterFirstName,
				NULL AS RequesterLastName,
				NULL AS RequesterComments,
				NULL AS RequestCreateDate,
				Requisition.CountryCode AS CountryCode,
				'Pending Approval' AS StatusForFilter,
				Requisition.CreateDate AS DateForSorting,
				Requisition.IsVendor
			FROM [Users] AS CurrentUser WITH (NOLOCK)
			INNER JOIN [Approvers] AS Approver WITH (NOLOCK)
				ON CurrentUser.Id = Approver.UserId
			INNER JOIN [FacilityWorkflowSteps] WorkflowStep WITH (NOLOCK)
				ON Approver.Id = WorkflowStep.ApproverId
			INNER JOIN (
				SELECT 
					RequisitionId,
					LocationIdentifier,
					CreatedBy,
					CreateDate,
					ApprovalStep,
					RequisitionTypeId,
					RequisitionStatusTypeId,
					Comments,
					CountryCode,
					IsVendor
				FROM [Requisitions] WITH (NOLOCK)
				WHERE RequisitionStatusTypeId = 2 --Pending Approval
				) AS Requisition
				ON WorkflowStep.COID = SUBSTRING(Requisition.LocationIdentifier, 1, CHARINDEX('_', Requisition.LocationIdentifier)-1)
				AND WorkflowStep.Step > Requisition.ApprovalStep
			LEFT OUTER JOIN Users AS Requisitioner WITH (NOLOCK)
				ON Requisition.CreatedBy = Requisitioner.AccountName
			LEFT OUTER JOIN AdhocReviews AS RequisitionReview WITH (NOLOCK)
				ON Requisition.RequisitionId = RequisitionReview.RequisitionId
				AND RequisitionReview.Recommended IS NULL
			LEFT OUTER JOIN RequisitionItems AS reqItem WITH (NOLOCK)
				ON Requisition.RequisitionId = reqItem.RequisitionId
			WHERE CurrentUser.AccountName = @userName
				AND Requisition.IsVendor = 1
		)
	) AS PendingAction
	WHERE @filterText IS NULL OR
	(PendingAction.RequisitionId LIKE @filterText + '%'
	OR PendingAction.RequisitionerId LIKE '%' + @filterText + '%'
	OR PendingAction.RequisitionerFirstName LIKE @filterText + '%'
	OR PendingAction.RequisitionerLastName LIKE @filterText + '%'
	OR (PendingAction.RequisitionerFirstName + ' ' + PendingAction.RequisitionerLastName) LIKE @filterText + '%'
	OR PendingAction.RequesterId LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterFirstName LIKE @filterText + '%'
	OR PendingAction.RequesterLastName LIKE @filterText + '%'
	OR (PendingAction.RequesterFirstName + ' ' + PendingAction.RequesterLastName) LIKE @filterText + '%'
	OR PendingAction.RequisitionComments LIKE '%' + @filterText + '%'
	OR PendingAction.RequesterComments LIKE '%' + @filterText + '%'
	OR PendingAction.StatusForFilter LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND RequisitionItemParIdentifier LIKE '%' + @filterText + '%')
	OR SUBSTRING(RequisitionLocationIdentifier, 0, CHARINDEX('_', RequisitionLocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR RequisitionLocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
)

SELECT @totalPendingActions = COUNT(*) FROM @pendingActions

SELECT 
	PendingAction.RequisitionId AS RequisitionId,
	PendingAction.RequisitionStatusTypeId AS RequisitionStatusTypeId,
	PendingAction.RequisitionLocationIdentifier AS RequisitionLocationIdentifier,
	PendingAction.RequisitionComments AS RequisitionComments,
	PendingAction.RequisitionCreateDate AS RequisitionCreateDate,
	PendingAction.RequisitionTypeId AS RequisitionTypeId,
	PendingAction.PendingReviewsExist AS PendingReviewsExist,
	RequisitionItem.Id AS RequisitionItemId,
	RequisitionItem.ItemId AS RequisitionItemNumber,
	RequisitionItem.RequisitionItemStatusTypeId AS RequisitionItemStatusTypeId,
	RequisitionItem.QuantityToOrder AS RequisitionItemQuantityToOrder,
	RequisitionItem.ParentSystemId AS RequisitionItemParentSystemId,
	RequisitionItem.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
	RequisitionItem.ParentRequisitionItemId AS RequisitionItemParentItemId,
	RequisitionItem.ParIdentifier AS RequisitionItemParIdentifier,
	RequisitionItem.Discount AS Discount,
	RequisitionItem.VendorId AS VendorId,
	RequisitionItem.UnitCost AS UnitCost,
	SPRDetail.VendorId AS SprDetailsVendorId,
	SPRDetail.VendorName AS SprDetailsVendorName,
	SPRDetail.PartNumber AS SprDetailsPartNumber,
	SPRDetail.EstimatedPrice AS SprDetailsEstimatedPrice,
	Attachment.RequisitionItemId AS SprDetailsFileAttachment,
	PendingAction.RequisitionerId AS RequisitionerId,
	PendingAction.RequisitionerFirstName AS RequisitionerFirstName,
	PendingAction.RequisitionerLastName AS RequisitionerLastName,
	PendingAction.ReviewId AS ReviewId,
	PendingAction.RequesterId AS RequesterId,
	PendingAction.RequesterFirstName AS RequesterFirstName,
	PendingAction.RequesterLastName AS RequesterLastName,
	PendingAction.RequesterComments AS RequesterComments,
	PendingAction.RequestCreateDate AS RequestCreateDate,
	@totalPendingActions AS TotalReqCount,
	PendingAction.CountryCode AS CountryCode,
	PendingAction.IsVendor
FROM (
	SELECT * FROM @pendingActions
	ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS PendingAction
LEFT OUTER JOIN [RequisitionItems] AS RequisitionItem WITH (NOLOCK)
	ON PendingAction.RequisitionId = RequisitionItem.RequisitionId
LEFT OUTER JOIN [SPRDetails] AS SPRDetail WITH (NOLOCK)
	ON RequisitionItem.Id = SPRDetail.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM [FileAttachments] WITH (NOLOCK)) AS Attachment 
	ON RequisitionItem.Id = Attachment.RequisitionItemId

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 10-30-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/30/2017		Script created
Peter Hurlburt		11/03/2017		Version 1.0 submitted for deployment 22
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
Peter Hurlburt		11/09/2017		Changed date filters to DateTime objects from Date
Peter Hurlburt		11/13/2017		Removed Deleted requisitions from result set
									Resubmitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Jonathan Moosekian	08/19/2020		Adding multi-department selection
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Change @departmentIds to use general IdTemplate Table Type
Julio Pozo			02/23/2022		Adding IsVendor column
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
                BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId]
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN @mobileReqs = 1
			THEN 
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
		1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]
        AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid  
		AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
		AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate
	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
		[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
		
	
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

                END
GO



/*
***************************************************************************
Author		: Colin Glasco
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by date range
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_VendorUserRequisitionsReportGet] 
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_VBORequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : Julio Pozo
Created     : 03-28-2022
Usage       : Vendor Requisitions Report
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_VBORequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY,
	@userIsVendor bit
AS

BEGIN

	DECLARE @minBackMonths INT = -13
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (@userIsVendor = 0 OR
			(NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
				LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
					ON RI2.Id = SPR2.RequisitionItemId
				WHERE RI2.RequisitionId = R.RequisitionId AND ((
					SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
					OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		)))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
		AND R.CreateDate >= DATEADD(MONTH, @minBackMonths, GETDATE())
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsVendorReportGet
Purpose     : Returns a paginated list of requisitions for the Vendor Id or Vendor Name in Requisition Report.
Used By     : SMART Procurement team
Author      : Vani Vasanthan
Created     : 02-06-2018
Usage       : Executed by the Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date			Reason for modification
---------------		-----------		-----------------------
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsVendorReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].[IsMobile] AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC) as rowNumber,
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		(CASE
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] AS [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		INNER JOIN [dbo].[RequisitionItems] AS [ReqItemGroup1] ON [Requisition].RequisitionId = [ReqItemGroup1].RequisitionId
	    LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDETAIL ON SPRDETAIL.RequisitionItemId = [ReqItemGroup1].Id
		WHERE
		1 <> [Requisition].[RequisitionStatusTypeId]
		AND 5 <> [Requisition].[RequisitionStatusTypeId]
		AND 8 <> [Requisition].[RequisitionStatusTypeId]
		AND 12 <> [Requisition].[RequisitionStatusTypeId]		
		AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
		AND (@VendorId IS NULL OR Cast([ReqItemGroup1].[VendorId] as VARCHAR(32)) = @VendorId OR Cast(SPRDETAIL.[VendorId] as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR [ReqItemGroup1].[VendorName] = @VendorName OR SPRDETAIL.[VendorName] = @VendorName)

	) AS [Req]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Author		: Colin Glasco
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by Vendor
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_VendorUserRequisitionsVendorReportGet] 
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (@VendorId IS NULL OR Cast(RI.VendorId as VARCHAR(32)) = @VendorId OR Cast(SPR.VendorId as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR RI.VendorName = @VendorName OR SPR.VendorName = @VendorName)
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report item number search.
Used By     : SMART Procurement team
Author      : Cassie Martinez
Created     : 02-05-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Cassie Martinez		02/05/2018		Script created
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS

BEGIN

SELECT
[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
[DistinctRequisitions].[Comments] AS [Comments],
[DistinctRequisitions].[CreateDate] AS [CreateDate],
[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
[DistinctRequisitions].[FirstName] AS [FirstName],
[DistinctRequisitions].[LastName] AS [LastName],
[DistinctRequisitions].IsMobile AS [IsMobile],
[DistinctRequisitions].[IsVendor] AS [IsVendor],
[AllReqItems].[Id] AS [RequisitionItemId],
[AllReqItems].[ItemId] AS [RequisitionItemNumber],
[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
[AllReqItems].[PONumber] AS [RequisitionItemPONumber],
[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
[AllReqItems].[ParIdentifier] AS [RequisitionItemParIdentifier],
[AllReqItems].[Discount] AS [Discount],
[AllReqItems].[VendorId] AS [VendorId],
[AllReqItems].[UnitCost] AS [UnitCost],
[AllReqItems].[QuantityToOrder] AS [QuantityToOrder],
[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
[DistinctRequisitions].[CreatedBy] AS [CreatedById],
[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount]
FROM
(
	SELECT
	ROW_NUMBER() OVER (ORDER BY (CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END), [ReqTypeGroupingOrder], 
		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC) as rowNumber,
		
	[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
	[Req].[RequisitionId] AS [RequisitionId],
	[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier] AS [LocationIdentifier],
	[Req].[Comments] AS [Comments],
	[Req].[CreatedBy] AS [CreatedBy],
	[Req].[CreateDate] AS [CreateDate],
	[Req].[RequisitionTypeId] AS [RequisitionTypeId],
	[Req].[IsMobile] AS [IsMobile],
	[Req].[IsVendor] AS [IsVendor],
	[User].[FirstName] AS [FirstName],
	[User].[LastName] AS [LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
        WHEN 7 THEN 1 
        WHEN 6 THEN 2
        WHEN 12 THEN 3
        WHEN 1 THEN 4
        WHEN 2 THEN 5
        WHEN 4 THEN 6
        ELSE 7
        END END) AS [ConditionalStatusSorting],
	dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
	+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
	- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
		[Item].[ItemId],
		[Item].[ReOrder],
		[Item].[CatalogNumber],
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS SPRDetail ON SPRDetail.RequisitionItemId = [Item].Id
		WHERE
		[ItemId] = @searchText
		OR [ReOrder] = @searchText
		OR [CatalogNumber] = @searchText
		OR [SPRDetail].PartNumber = @searchText
	) AS [Item]
	LEFT OUTER JOIN (SELECT
		(CASE 
			WHEN @mobileReqs = 1
			THEN
				CASE
					WHEN [Requisition].[IsMobile] = @mobileReqs
					THEN 1
					ELSE 2
				END
			ELSE
				CASE
					WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
					THEN 1
					ELSE 2 
				END
		END) AS [ReqTypeGroupingOrder],
		[Requisition].[RequisitionId] AS [RequisitionId],
		[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Requisition].[LocationIdentifier] AS [LocationIdentifier],
		[Requisition].[Comments] AS [Comments],
		[Requisition].[CreatedBy] AS [CreatedBy],
		[Requisition].[CreateDate] AS [CreateDate],
		[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
		[Requisition].[IsMobile] As [IsMobile],
		[Requisition].[IsVendor] AS [IsVendor]
		FROM
		[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
	) AS [Req] ON [Item].[RequisitionId] = [Req].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
	WHERE
	(@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [Item].[ItemId] LIKE '%' + @filterText + '%'
	OR [Item].[ReOrder] LIKE '%' + @filterText + '%'
	OR [Item].[CatalogNumber] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)))
	AND substring([Req].[LocationIdentifier],0,(CHARINDEX('_',[Req].[LocationIdentifier]))) = @coid
	AND 1 <> [Req].[RequisitionStatusTypeId]
	AND 5 <> [Req].[RequisitionStatusTypeId]
	AND 8 <> [Req].[RequisitionStatusTypeId]
	AND 12 <> [Req].[RequisitionStatusTypeId]
	GROUP BY
	[Req].[ReqTypeGroupingOrder],
	[Req].[RequisitionId],
	[Req].[RequisitionStatusTypeId],
	[Req].[LocationIdentifier],
	[Req].[Comments],
	[Req].[CreatedBy],
	[Req].[CreateDate],
	[Req].[RequisitionTypeId],
	[Req].[IsMobile],
	[Req].[IsVendor],
	[User].[FirstName],
	[User].[LastName]
	ORDER BY
	[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
	CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
	CASE @oldestFirst WHEN 1 THEN [Req].[CreateDate] END ASC,
	CASE @oldestFirst WHEN 0 THEN [Req].[CreateDate] END DESC
	
	OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
ORDER BY [DistinctRequisitions].rowNumber
OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Author		: Colin Glasco
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by ItemNumber
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_VendorUserRequisitionsReportByItemNumberGet] 
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filter != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (ItemId = @searchText
			OR ReOrder = @searchText
			OR CatalogNumber = @searchText
			OR SPR.PartNumber = @searchText)
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportExportGet
Purpose     : Returns a non-paginated list of requisitions for the Requisition Report export button.
Used By     : SMART Procurement team
Author      : Peter Hurlburt
Created     : 11-02-2017
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		11/02/2017		Script created
Peter Hurlburt		11/03/2017		Version 1.0 submitted for deployment 22
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
Peter Hurlburt		11/09/2017		Changed date filters to DateTime objects from Date
Peter Hurlburt		11/13/2017		Removed Deleted requisitions from result set
									Resubmitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Jonathan Moosekian	08/13/2020		Adding IsMobile column
Jonathan Moosekian	08/19/2020		Adding multi-department selection
Colin Glasco		08/20/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Change @departmentIds to use general IdTemplate Table Type
Aditya Joshi		10/17/2020		Added [RequisitionItemStatusTypeId] to get the item status in export 
Julio Pozo			02/16/2022		Added parameter to limit max number of requisitions returned
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@maxExportedRequisitionCount int = 1000,
	@vboFirst bit
AS
BEGIN

SELECT  
	[Req].[RequisitionId]				AS [RequisitionId],
	[Req].[CreatedBy]					AS [RequisitionerId],
	[Req].[RequisitionTypeId]			AS [RequisitionTypeId],
	[Req].[CreateDate]					AS [RequisitionCreateDate],
	[Req].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Req].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
	[Req].[IsMobile]					AS [RequisitionIsMobile],
	[User].[FirstName]					AS [RequisitionerFirstName],
	[User].[LastName]					AS [RequisitionerLastName],
	[ReqItem].[Id]						AS [RequisitionItemId],
	[ReqItem].[ItemId]					AS [RequisitionItemNumber],
	[ReqItem].[UOMCode]					AS [RequisitionItemUomCode],
	[ReqItem].[PONumber]				AS [RequisitionItemPONumber],
	[ReqItem].[UnitCost]				AS [RequisitionItemUnitCost],
	[ReqItem].[VendorId]				AS [RequisitionItemVendorId],
	[ReqItem].[VendorName]				AS [RequisitionItemVendorName],
	[ReqItem].[ItemDescription]			AS [RequisitionItemDescription],
	[ReqItem].[ParIdentifier]			AS [RequisitionItemParIdentifier],
	[ReqItem].[ReOrder]					AS [RequisitionItemReorderNumber],
	[ReqItem].[ParentSystemId]			AS [RequisitionItemParentSystemId],
	[ReqItem].[OriginalParentSystemId]	AS [RequisitionItemOriginalParentSystemId],
	[ReqItem].[QuantityToOrder]			AS [RequisitionItemQuantityOrdered],
	[ReqItem].[SmartItemNumber]			AS [RequisitionItemSmartItemNumber],
	[ReqItem].[GeneralLedgerCode]		AS [RequisitionItemGeneralLedgerCode],
	[ReqItem].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
	[ReqItem].[Discount]				AS [Discount],
	[ReqItem].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
	[SprDetails].[UOMCode]				AS [SprDetailsUomCode],
	[SprDetails].[VendorId]				AS [SprDetailsVendorId],
	[SprDetails].[VendorName]			AS [SprDetailsVendorName],
	[SprDetails].[PartNumber]			AS [SprDetailsPartNumber],
	[SprDetails].[ItemDescription]		AS [SprDetailsDescription],
	[SprDetails].[TradeInValue]			AS [SprDetailsTradeInValue],
	[SprDetails].[BudgetNumber]			AS [SprDetailsBudgetNumber],
	[SprDetails].[GeneralLedgerCode]	AS [SprDetailsGeneralLedgerCode],
	[SprDetails].[EstimatedPrice]		AS [SprDetailsEstimatedUnitPrice],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
FROM
(
	SELECT TOP (@maxExportedRequisitionCount)
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
	END)										AS [ReqTypeGroupingOrder],
	[Requisition].[RequisitionId]				AS [RequisitionId],
	[Requisition].[RequisitionStatusTypeId]		AS [RequisitionStatusTypeId],
	[Requisition].[LocationIdentifier]			AS [LocationIdentifier],
	[Requisition].[IsMobile]					AS [IsMobile],
	[Requisition].[Comments]					AS [Comments],
	[Requisition].[CreatedBy]					AS [CreatedBy],
	[Requisition].[CreateDate]					AS [CreateDate],
	[Requisition].[RequisitionTypeId]			AS [RequisitionTypeId]
	FROM [dbo].[Requisitions]					AS [Requisition] WITH (NOLOCK)
	WHERE
	1 <> [Requisition].[RequisitionStatusTypeId]
	AND 5 <> [Requisition].[RequisitionStatusTypeId]
	AND 8 <> [Requisition].[RequisitionStatusTypeId]
	AND 12 <> [Requisition].[RequisitionStatusTypeId]
    AND substring([Requisition].[LocationIdentifier],0,(CHARINDEX('_',[Requisition].[LocationIdentifier]))) = @coid
	AND ((SELECT COUNT(*) FROM @departmentIds) = 0 OR (substring([Requisition].[LocationIdentifier],(CHARINDEX('_',[Requisition].[LocationIdentifier])+1),LEN([Requisition].[LocationIdentifier])) IN (SELECT DISTINCT Id FROM @departmentIds)))
	AND [Requisition].[CreateDate] BETWEEN @startDate AND @endDate

	ORDER BY

		CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [Requisition].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END				
		END,

		CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
		CASE @oldestFirst WHEN 1 THEN [Requisition].[CreateDate] END ASC,
		CASE @oldestFirst WHEN 0 THEN [Requisition].[CreateDate] END DESC
		

) AS [Req]

LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [Req].[CreatedBy] = [User].[AccountName]
WHERE
	@filterText IS NULL OR
	([Req].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [Req].[Comments] LIKE '%' + @filterText + '%'
	OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
ORDER BY
	[ConditionalStatusSorting] 

OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportGet
Purpose     : Returns list of VBO requisitions for the Requisition Report export.
Used By     : SMART Procurement team
Author      : Julio Pozo
Created     : 03-29-2022
Usage       : Vendor Requisitions Report Export
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_VBORequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@maxExportedRequisitionCount int = 1000,
	@vendorAffiliations [dbo].[IdTemplate] READONLY,
	@userIsVendor bit
	
AS
BEGIN

	DECLARE @minBackMonths INT = -13
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (@userIsVendor = 0 OR
			(NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
				LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
					ON RI2.Id = SPR2.RequisitionItemId
				WHERE RI2.RequisitionId = R.RequisitionId AND ((
					SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
					OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		)))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
		AND R.CreateDate >= DATEADD(MONTH, @minBackMonths, GETDATE())
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT TOP (@maxExportedRequisitionCount)
		*
		FROM @requisitionList
		ORDER BY RowOrder
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId

END
GO



/*
***************************************************************************
Author		: Colin Glasco
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by date range export
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_VendorUserRequisitionsReportExportGet]
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@maxExportedRequistiionCount int = 1000,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT TOP (@maxExportedRequistiionCount)
		*
		FROM @requisitionList
		ORDER BY RowOrder
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	
END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsReportByItemNumberExportGet
Purpose     : Returns a non-paginated list of requisitions for the Requisition Report export button on the item number search.
Used By     : SMART Procurement team
Author      : Cassie Martinez
Created     : 02-07-2018
Usage       : Executed by our Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Cassie Martinez		02/07/2018		Script created
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsReportByItemNumberExportGet]
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
                BEGIN

SELECT
[PopulatedReq].[RequisitionId]				AS [RequisitionId],
[PopulatedReq].[CreatedBy]					AS [RequisitionerId],
[PopulatedReq].[RequisitionTypeId]			AS [RequisitionTypeId],
[PopulatedReq].[CreateDate]					AS [RequisitionCreateDate],
[PopulatedReq].[RequisitionStatusTypeId]	AS [RequisitionStatusTypeId],
[PopulatedReq].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[PopulatedReq].[FirstName]					AS [RequisitionerFirstName],
[PopulatedReq].[LastName]					AS [RequisitionerLastName],
[PopulatedReq].[IsMobile]					AS [RequisitionIsMobile],
[PopulatedReq].[IsVendor]					AS [RequisitionIsVendor],
[ReqItem].[Id]								AS [RequisitionItemId],
[ReqItem].[ItemId]							AS [RequisitionItemNumber],
[ReqItem].[UOMCode]							AS [RequisitionItemUomCode],
[ReqItem].[PONumber]						AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]						AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]						AS [RequisitionItemVendorId],
[ReqItem].[VendorName]						AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]					AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]					AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]							AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]					AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]			AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]					AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]					AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]				AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId]			AS [RequisitionItemParentItemId],
[ReqItem].[Discount]						AS [Discount],
[SprDetails].[UOMCode]						AS [SprDetailsUomCode],
[SprDetails].[VendorId]						AS [SprDetailsVendorId],
[SprDetails].[VendorName]					AS [SprDetailsVendorName],
[SprDetails].[PartNumber]					AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]				AS [SprDetailsDescription],
[SprDetails].[TradeInValue]					AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]					AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]			AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]				AS [SprDetailsEstimatedUnitPrice]
FROM
(
	SELECT DISTINCT
	(CASE 
		WHEN @mobileReqs = 1
		THEN 
			CASE
				WHEN [MatchingReq].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [MatchingReq].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2 
			END
	END) AS [ReqTypeGroupingOrder],
	[MatchingReq].[RequisitionId],
	[MatchingReq].[CreatedBy],
	[MatchingReq].[RequisitionTypeId],
	[MatchingReq].[CreateDate],
	[MatchingReq].[RequisitionStatusTypeId],
	[MatchingReq].[LocationIdentifier],
	[MatchingReq].[IsMobile],
	[MatchingReq].[IsVendor],
	[User].[FirstName],
	[User].[LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId] 
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
	FROM
	(
		SELECT DISTINCT
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] ON [SprDetail].[RequisitionItemId] = [Item].[Id]
		WHERE
		[Item].[ItemId] = @searchText
		OR [Item].[ReOrder] = @searchText
		OR [Item].[CatalogNumber] = @searchText
		OR [SprDetail].[PartNumber] = @searchText
	) AS [MatchingItem]
	LEFT OUTER JOIN [dbo].[Requisitions] AS [MatchingReq] WITH (NOLOCK) ON [MatchingItem].[RequisitionId] = [MatchingReq].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [MatchingReq].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [MatchingReqItem] WITH (NOLOCK) ON [MatchingReq].[RequisitionId] = [MatchingReqItem].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [MatchingReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [MatchingReq].[CreatedBy] = [User].[AccountName]
	WHERE
	substring([MatchingReq].[LocationIdentifier],0,(CHARINDEX('_',[MatchingReq].[LocationIdentifier]))) = @coid
	AND (@filterText IS NULL OR
	([MatchingReq].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
	OR [MatchingReq].[Comments] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[PONumber] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ItemId] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[ReOrder] LIKE '%' + @filterText + '%'
	OR [MatchingReqItem].[CatalogNumber] LIKE '%' + @filterText + '%')
	OR (@filterText != 'EPR' AND [MatchingReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([MatchingReq].[LocationIdentifier], 0, CHARINDEX('_', [MatchingReq].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [MatchingReq].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	AND 1 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 5 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 8 <> [MatchingReq].[RequisitionStatusTypeId]
	AND 12 <> [MatchingReq].[RequisitionStatusTypeId]
) AS [PopulatedReq]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [PopulatedReq].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
ORDER BY
[ConditionalStatusSorting], [PopulatedReq].[ReqTypeGroupingOrder], 
CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
CASE @oldestFirst WHEN 1 THEN [PopulatedReq].[CreateDate] END ASC,
CASE @oldestFirst WHEN 0 THEN [PopulatedReq].[CreateDate] END DESC

OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Author		: Colin Glasco
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by Item number export
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_VendorUserRequisitionsReportByItemNumberExportGet]
	@coid varchar(140),
	@searchText varchar(140),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (ItemId = @searchText
			OR ReOrder = @searchText
			OR CatalogNumber = @searchText
			OR SPR.PartNumber = @searchText)
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT
		*
		FROM @requisitionList
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
	ORDER BY requisitionPage.RowOrder
	
END
GO



/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsVendorReportExportGet
Purpose     : Returns a non-paginated list of requisitions for the Vendor Search Requisition Report export button.
Used By     : SMART Procurement team
Author      : Vani Vasanthan
Created     : 02-06-2018
Usage       : Executed by the Requisition Services server

***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		03/30/2017		Added more columns for exported report
									Submitted for deployment 7
Vani Vasanthan		05/07/2018		LocationIdentifier accounts for UK COIDs
Peter Hurlburt		05/07/2018		Expanded export to include req item details
									Submitted for deployment 9
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Colin Glasco		08/24/2020		Adding new Sort types: Oldest, BO/BR, Mobile
									Add IsMobile column
Julio Pozo			03/09/2022		Allow to order by IsVendor flag
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsVendorReportExportGet]
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vboFirst bit
AS
BEGIN

SELECT
[PopulatedReq].[RequisitionId]				AS [RequisitionId],
[PopulatedReq].[CreatedBy]					AS [RequisitionerId],
[PopulatedReq].[RequisitionTypeId]			AS [RequisitionTypeId],
[PopulatedReq].[CreateDate]					AS [RequisitionCreateDate],
[PopulatedReq].[RequisitionStatusTypeId]	AS [RequisitionStatusTypeId],
[PopulatedReq].[LocationIdentifier]			AS [RequisitionLocationIdentifier],
[PopulatedReq].[FirstName]					AS [RequisitionerFirstName],
[PopulatedReq].[LastName]					AS [RequisitionerLastName],
[PopulatedReq].[IsMobile]					AS [RequisitionIsMobile],
[PopulatedReq].[IsVendor]					AS [RequisitionIsVendor],
[ReqItem].[Id]								AS [RequisitionItemId],
[ReqItem].[ItemId]							AS [RequisitionItemNumber],
[ReqItem].[UOMCode]							AS [RequisitionItemUomCode],
[ReqItem].[PONumber]						AS [RequisitionItemPONumber],
[ReqItem].[UnitCost]						AS [RequisitionItemUnitCost],
[ReqItem].[VendorId]						AS [RequisitionItemVendorId],
[ReqItem].[VendorName]						AS [RequisitionItemVendorName],
[ReqItem].[ItemDescription]					AS [RequisitionItemDescription],
[ReqItem].[ParIdentifier]					AS [RequisitionItemParIdentifier],
[ReqItem].[ReOrder]							AS [RequisitionItemReorderNumber],
[ReqItem].[ParentSystemId]					AS [RequisitionItemParentSystemId],
[ReqItem].[OriginalParentSystemId]			AS [RequisitionItemOriginalParentSystemId],
[ReqItem].[QuantityToOrder]					AS [RequisitionItemQuantityOrdered],
[ReqItem].[SmartItemNumber]					AS [RequisitionItemSmartItemNumber],
[ReqItem].[GeneralLedgerCode]				AS [RequisitionItemGeneralLedgerCode],
[ReqItem].[ParentRequisitionItemId]			AS [RequisitionItemParentItemId],
[ReqItem].[Discount]						AS [Discount],
[SprDetails].[UOMCode]						AS [SprDetailsUomCode],
[SprDetails].[VendorId]						AS [SprDetailsVendorId],
[SprDetails].[VendorName]					AS [SprDetailsVendorName],
[SprDetails].[PartNumber]					AS [SprDetailsPartNumber],
[SprDetails].[ItemDescription]				AS [SprDetailsDescription],
[SprDetails].[TradeInValue]					AS [SprDetailsTradeInValue],
[SprDetails].[BudgetNumber]					AS [SprDetailsBudgetNumber],
[SprDetails].[GeneralLedgerCode]			AS [SprDetailsGeneralLedgerCode],
[SprDetails].[EstimatedPrice]				AS [SprDetailsEstimatedUnitPrice]
FROM
(
	SELECT DISTINCT
	(CASE
		WHEN @mobileReqs = 1
		THEN
			CASE
				WHEN [MatchingVendorReq].[IsMobile] = @mobileReqs
				THEN 1
				ELSE 2
			END
		ELSE
			CASE
				WHEN [MatchingVendorReq].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup)
				THEN 1
				ELSE 2
			END
	END) AS [ReqTypeGroupingOrder],
	[MatchingVendorReq].[RequisitionId],
	[MatchingVendorReq].[CreatedBy],
	[MatchingVendorReq].[RequisitionTypeId],
	[MatchingVendorReq].[CreateDate],
	[MatchingVendorReq].[RequisitionStatusTypeId],
	[MatchingVendorReq].[LocationIdentifier],
	[MatchingVendorReq].[IsMobile],
	[MatchingVendorReq].[IsVendor],
	[User].[FirstName],
	[User].[LastName],
	(CASE @statusSorting WHEN 1 THEN CASE [RequisitionStatusTypeId]
		WHEN 7 THEN 1 
		WHEN 6 THEN 2
		WHEN 12 THEN 3
		WHEN 1 THEN 4
		WHEN 2 THEN 5
		WHEN 4 THEN 6
		ELSE 7
		END END) AS [ConditionalStatusSorting]
	FROM
	(
		SELECT DISTINCT
		[Item].[RequisitionId]
		FROM
		[dbo].[RequisitionItems] AS [Item] WITH (NOLOCK)
		LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] ON [SprDetail].[RequisitionItemId] = [Item].[Id]
		WHERE
		(@VendorId IS NULL OR Cast([Item].[VendorId] as VARCHAR(32)) = @VendorId OR Cast([SprDetail].[VendorId] as VARCHAR(32)) = @VendorId)
		AND (@VendorName IS NULL OR [Item].[VendorName] = @VendorName OR [SprDetail].[VendorName] = @VendorName)
	) AS [MatchingVendorItem]
	LEFT OUTER JOIN [dbo].[Requisitions] AS [MatchingVendorReq] ON [MatchingVendorItem].[RequisitionId] = [MatchingVendorReq].[RequisitionId]
	LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionStatusTypeId] = [ReqStatus].[Id]
	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [MatchingVendorReqItems] WITH (NOLOCK) ON [MatchingVendorReq].[RequisitionId] = [MatchingVendorReqItems].[RequisitionId]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetail] WITH (NOLOCK) ON [MatchingVendorReqItems].[Id] = [SprDetail].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [MatchingVendorReqItems].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
	LEFT OUTER JOIN (SELECT DISTINCT [AccountName], [FirstName], [LastName] FROM [dbo].[Users] WITH (NOLOCK)) AS [User] ON [MatchingVendorReq].[CreatedBy] = [User].[AccountName]
	WHERE
	substring([MatchingVendorReq].[LocationIdentifier],0,(CHARINDEX('_',[MatchingVendorReq].[LocationIdentifier]))) = @coid
	AND (@filterText IS NULL OR
	([MatchingVendorReq].[RequisitionId] LIKE '%' + @filterText + '%'
	OR ([User].[FirstName] + ' ' + [User].[LastName]) LIKE '%' + @filterText + '%'
	OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
	OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%')
	OR [MatchingVendorReq].[Comments] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[PONumber] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[ParentSystemId] LIKE '%' + @filterText + '%'
	OR [MatchingVendorReqItems].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
	OR (@filterText != 'EPR' AND [MatchingVendorReqItems].[ParIdentifier] LIKE '%' + @filterText + '%')
	OR SUBSTRING([MatchingVendorReq].[LocationIdentifier], 0, CHARINDEX('_', [MatchingVendorReq].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
	OR [MatchingVendorReq].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	AND 1 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 5 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 8 <> [MatchingVendorReq].[RequisitionStatusTypeId]
	AND 12 <> [MatchingVendorReq].[RequisitionStatusTypeId]
) AS [PopulatedReq]
LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [PopulatedReq].[RequisitionId] = [ReqItem].[RequisitionId]
LEFT OUTER JOIN [dbo].[SPRDetails] AS [SprDetails] WITH (NOLOCK) ON [ReqItem].[Id] = [SprDetails].[RequisitionItemId]
ORDER BY
[ConditionalStatusSorting], [PopulatedReq].[ReqTypeGroupingOrder], 
CASE @vboFirst WHEN 1 THEN [IsVendor] END DESC,
CASE @oldestFirst WHEN 1 THEN [PopulatedReq].[CreateDate] END ASC,
CASE @oldestFirst WHEN 0 THEN [PopulatedReq].[CreateDate] END DESC
OPTION (RECOMPILE)

END
GO



/*
***************************************************************************
Author		: Colin Glasco
Created		: 2022-03-08
Purpose		: Vendor BO/BR Requisition Report by Vendor export
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)

***************************************************************************
*/

ALTER   PROCEDURE [dbo].[usp_VendorUserRequisitionsVendorReportExportGet]
	@coid varchar(140),
	@VendorId varchar(32),
	@VendorName varchar(32),
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY
AS
BEGIN
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 2 THEN 5
					WHEN 4 THEN 6
					ELSE 7 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR RI.ParIdentifier LIKE @filter
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
			LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
				ON RI2.Id = SPR2.RequisitionItemId
			WHERE RI2.RequisitionId = R.RequisitionId AND ((
				SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
				OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid
		AND (@VendorId IS NULL OR Cast(RI.VendorId as VARCHAR(32)) = @VendorId OR Cast(SPR.VendorId as VARCHAR(32)) = @VendorId)
	    AND (@VendorName IS NULL OR RI.VendorName = @VendorName OR SPR.VendorName = @VendorName)
	) AS R

	SELECT 
		R.RequisitionId,
		R.CreatedBy AS RequisitionerId,
		R.RequisitionTypeId,
		R.CreateDate AS RequisitionCreateDate,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier AS RequisitionLocationIdentifier,
		R.IsMobile AS RequisitionIsMobile,
		R.IsVendor AS RequisitionIsVendor,
		U.FirstName AS RequisitionerFirstName,
		U.LastName AS RequisitionerLastName,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.UOMCode AS RequisitionItemUomCode,
		RI.PONumber AS RequisitionItemPONumber,
		RI.UnitCost AS RequisitionItemUnitCost,
		RI.VendorId AS RequisitionItemVendorId,
		RI.VendorName AS RequisitionItemVendorName,
		RI.ItemDescription AS RequisitionItemDescription,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.ReOrder AS RequisitionItemReorderNumber,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.QuantityToOrder AS RequisitionItemQuantityOrdered,
		RI.SmartItemNumber AS RequisitionItemSmartItemNumber,
		RI.GeneralLedgerCode AS RequisitionItemGeneralLedgerCode,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.Discount,
		RI.RequisitionItemStatusTypeId,
		SPR.UOMCode AS SprDetailsUomCode,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		SPR.ItemDescription AS SprDetailsDescription,
		SPR.TradeInValue AS SprDetailsTradeInValue,
		SPR.BudgetNumber AS SprDetailsBudgetNumber,
		SPR.GeneralLedgerCode AS SprDetailsGeneralLedgerCode,
		SPR.EstimatedPrice AS SprDetailsEstimatedUnitPrice
	FROM
	(
		SELECT
		*
		FROM @requisitionList
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId	
	ORDER BY requisitionPage.RowOrder
	
END
GO


