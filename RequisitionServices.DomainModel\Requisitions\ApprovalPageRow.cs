﻿using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace RequisitionServices.DomainModel.Requisitions
{
    [NotMapped]
    public class ApprovalPageRow
    {
        public int RequisitionId { get; set; }
        
        public int RequisitionStatusTypeId { get; set; }

        public string RequisitionLocationIdentifier { get; set; }

        public string RequisitionComments { get; set; }

        public DateTime RequisitionCreateDate { get; set; }

        public int RequisitionTypeId { get; set; }

        public bool PendingReviewsExist { get; set; }

        public int? RequisitionItemId { get; set; }

        public string RequisitionItemNumber { get; set; }

        public int? RequisitionItemStatusTypeId { get; set; }

        public int RequisitionItemQuantityToOrder { get; set; }

        public string RequisitionItemParentSystemId { get; set; }

        public string RequisitionItemOriginalParentSystemId { get; set; }

        public int? RequisitionItemParentItemId { get; set; }

        public string RequisitionItemParIdentifier { get; set; }

        public decimal? Discount { get; set; }

        public int? VendorId { get; set; }

        public decimal? UnitCost { get; set; }

        public int? SprDetailsVendorId { get; set; }

        public string SprDetailsVendorName { get; set; }

        public string SprDetailsPartNumber { get; set; }

        public decimal? SprDetailsEstimatedPrice { get; set; }

        public int? SprDetailsFileAttachment { get; set; }

        public string RequisitionerId { get; set; }

        public string RequisitionerFirstName { get; set; }

        public string RequisitionerLastName { get; set; }

        public int? ReviewId { get; set; }

        public string RequesterId { get; set; }

        public string RequesterFirstName { get; set; }

        public string RequesterLastName { get; set; }

        public string RequesterComments { get; set; }

        public DateTime? RequestCreateDate { get; set; }

        public long TotalReqCount { get; set; }

        public string CountryCode { get; set; }

        public bool IsVendor { get; set; }

        public decimal? VboHoldItemConversionUnitCost { get; set; }

        public int RequisitionSubmissionTypeId { get; set; }
    }
}
