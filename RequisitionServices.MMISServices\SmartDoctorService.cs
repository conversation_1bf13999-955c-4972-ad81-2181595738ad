﻿using RequisitionServices.DomainModel.Clinical;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;

namespace RequisitionServices.MMISServices
{
    public class SmartDoctorService : ISmartDoctorService
    {
        private const string getDoctorsMethod = "Doctors/GetAllDoctors/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IEnumerable<Provider> GetDoctors(string userName, string coid)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            int intCOID;
            if (!Int32.TryParse(coid, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", coid));
            }

            var doctorRecordModels = ApiUtility.ExecuteApiGetTo<IEnumerable<DoctorRecordModel>>(endpoint, getDoctorsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });

            var providers = new List<Provider>();
            if (doctorRecordModels != null)
            {
                foreach (var doctorRecordModel in doctorRecordModels)
                {
                    providers.Add(doctorRecordModel.MapToProvider());
                }
            }

            return providers;
        }
    }
}
