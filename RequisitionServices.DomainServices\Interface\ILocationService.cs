﻿using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.DomainServices.Interface
{
    public interface ILocationService
    {
        //IEnumerable<GLAccount> GetAllGLAccounts(string userName, string coid, int? costCode);
        IEnumerable<GLAccount> GetAllGLAccounts(string userName, string coid, string accountStringPartial);

        IEnumerable<int> GetAllCostCodes(string userName, string coid);

        GLAccount GetGLAccount(string userName, string coid, long accountNumber);

        IEnumerable<Address> GetAllAddresses(string userName, string coid);

        Address GetAddress(string userName, string coid, int shipNumber);

        IEnumerable<FacilityNotification> SaveFacilityNotifications(IEnumerable<FacilityNotification> facilityNotifications, string userName, string coid);

        IEnumerable<FacilityNotification> GetFacilityNotifications(string userName, string coid);

        Locator UpdateLocator(string userId, Locator locator);
    }
}
