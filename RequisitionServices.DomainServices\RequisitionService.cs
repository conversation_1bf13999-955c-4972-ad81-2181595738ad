﻿using log4net;
using Newtonsoft.Json;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.DomainModel.Users;
using RequisitionServices.DomainModel.Email;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.MMISServices.Utilities.SmartItemUtilities;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Utility.Domain;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;
using System.Text;
using RequisitionServices.DomainModel.Vendors;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using RequisitionServices.DomainModel.Constants;

namespace RequisitionServices.DomainServices
{
    public class RequisitionService : IRequisitionService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private string eproEndpoint = ConfigurationManager.AppSettings.Get("EProcurementUrl");

        private const string UserName = "MMIS";
        private const string parentSystemSubmissionErrorId = "0";

        private IRequisitionRepository requisitionRepository;
        private IRequisitionStatusRepository requisitionStatusRepository;
        private IAdhocReviewRepository adhocReviewRepository;
        private IUserService userService;
        private ISmartRequisitionService smartRequisitionService;
        private IWorkflowService workflowService;
        private IWorkflowRepository workflowRepository;
        private ITypeRepository typeRepository;
        private IEmailService emailService;
        private IConfigurationRepository configurationRepository;
        private IItemService itemService;
        private IParService parService;
        private IVendorService vendorSvc;
        private IConfigurationService configurationService;
        private ISmartIINItemService smartIINItemService;
        private ICensorService censorService;
        private ISmartRequisitionInquiryService smartRequisitionInquiryService;
        private ICartRepository cartRepository;
        private IFacilityWorkflowService _facilityWorkflowService;
        private IDigitalSignOffRepository digitalSignOffRepository;
        private IVProService vproService;


        public RequisitionService(IRequisitionRepository requisitionRepo, IRequisitionStatusRepository requisitionStatusRepo, IAdhocReviewRepository adhocReviewRepo, IWorkflowRepository workflowRepo, IUserService userSvc, ISmartRequisitionService smartRequisitionSvc, IWorkflowService workflowSvc, ITypeRepository typeRepo, IEmailService emailSvc, IConfigurationRepository configurationRepo, IItemService itemSvc, IParService parSvc, IVendorService vendSvc, IConfigurationService configSvc, ISmartIINItemService smartIINItemSvc, ICensorService censorSvc, ISmartRequisitionInquiryService requisitionInquirySvc, ICartRepository cartRepo, IFacilityWorkflowService facilityWorkflowService, IDigitalSignOffRepository digitalSignOffsRepository, IVProService vproService)
        {
            this.requisitionRepository = requisitionRepo;
            this.requisitionStatusRepository = requisitionStatusRepo;
            this.adhocReviewRepository = adhocReviewRepo;
            this.workflowRepository = workflowRepo;
            this.userService = userSvc;
            this.smartRequisitionService = smartRequisitionSvc;
            this.workflowService = workflowSvc;
            this.typeRepository = typeRepo;
            this.emailService = emailSvc;
            this.configurationRepository = configurationRepo;
            this.itemService = itemSvc;
            this.parService = parSvc;
            this.vendorSvc = vendSvc;
            this.configurationService = configSvc;
            this.smartIINItemService = smartIINItemSvc;
            this.censorService = censorSvc;
            this.smartRequisitionInquiryService = requisitionInquirySvc;
            this.cartRepository = cartRepo;
            this._facilityWorkflowService = facilityWorkflowService;
            this.digitalSignOffRepository = digitalSignOffsRepository;
            this.vproService = vproService;
        }

        private List<Requisition> UseRequisitionsStatusFromIIB(List<Requisition> requisitions)
        {
            var parentItems = new List<Tuple<string, string, string, RequisitionItem>>();
            requisitions.ForEach(req =>
            {
                foreach (var reqItem in req.RequisitionItems.Where(x => x.RequisitionItemStatusTypeId == (int)RequisitionItemStatusTypeEnum.Processing && x.RequisitionScheduledDate.HasValue))
                {
                    reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Scheduled; // this is an interpreted status
                }
            });
            return requisitions;
        }

        //Gets status updates for items that have been submitted to SMART using smart API
        private List<Requisition> UseRequisitionStatusFromSMARTAPI(string userName, List<Requisition> requisitions)
        {
            if (requisitions != null && requisitions.Any())
            {
                /* We can look up multiple requisitions in the same SMART call if they are for the same COID.
                 * That is why we are grouping the requisitions by COID.
                */
                var requisitionGroupsByCOID = requisitions.GroupBy(x => LocationMapper.GetCOID(x.LocationIdentifier));
                foreach (var coidGroup in requisitionGroupsByCOID)
                {
                    var requisitionList = coidGroup.AsEnumerable();
                    if (requisitionList.FirstOrDefault() == null)
                    {
                        continue; //go to next group if this one is empty/null
                    }
                    else
                    {
                        var coid = LocationMapper.GetCOID(requisitionList.First().LocationIdentifier);
                        var allSmartReqIds = requisitionList.SelectMany // get a list of all distinct ParentSystemIds in this group of requisitions
                                (x => x.RequisitionItems
                                    .Where(y => y.ParentSystemId != null)
                                    .Select(z => z.ParentSystemId)
                                ).Distinct();
                        var reqSmartReqIds = new List<string>(); //items with a numeric ParentSystemId must get their statuses from the smart API requisition Service
                        var sprSmartReqIds = new List<string>(); //items with an alphanumeric ParentSystemId must get their statuses from the smart API SPR Service
                        foreach (var reqId in allSmartReqIds)
                        {
                            int intId;
                            if (Int32.TryParse(reqId, out intId))
                            {
                                reqSmartReqIds.Add(reqId);
                            }
                            else
                            {
                                sprSmartReqIds.Add(reqId);
                            }
                        }
                        try
                        {
                            if (sprSmartReqIds.Any())
                            {
                                this.UpdateRequisitionItemsFromSMART(ref requisitionList, sprSmartReqIds, coid, userName, new SmartSPRItemUtility());
                            }

                            if (reqSmartReqIds.Any())
                            {
                                this.UpdateRequisitionItemsFromSMART(ref requisitionList, reqSmartReqIds, coid, userName, new SmartReqItemUtility());
                            }
                        }
                        catch (Exception ex)
                        {
                            log.Fatal("Error trying to load up requisition details", ex);
                            foreach (var reqItem in requisitionList.SelectMany(x => x.RequisitionItems))
                            {
                                reqItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Unknown;
                            }
                        }
                    }
                }
            }

            return requisitions;
        }

        // Uses the passed-in itemUtility to make smart API call to get status updates and updates the RequisitionItems.
        protected void UpdateRequisitionItemsFromSMART(ref IEnumerable<Requisition> requisitions, List<string> reqIds, string coid, string userName, ISmartItemUtility itemUtility)
        {
            var submittedReqItems = requisitions.SelectMany(x => x.RequisitionItems.Where(y => y.ParentSystemId != null));
            foreach (var reqid in reqIds)
            {
                foreach (var submittedReqItem in submittedReqItems)
                {
                    if (submittedReqItem.ParentSystemId == reqid)
                    {
                        submittedReqItem.IsPurged = true;
                    }
                }
            }
            List<Requisition> smartStatusInfos = null;
            try
            {
                smartStatusInfos = itemUtility.GetRequisitionsFromSmart(userName, coid, reqIds, ref smartRequisitionService);
            }
            catch (Exception ex)
            {
                log.Error(ex.Message);
            }
            if (smartStatusInfos != null)
            {
                foreach (var smartStatusInfo in smartStatusInfos)
                {
                    if (smartStatusInfo.RequisitionItems != null)
                    {
                        foreach (var smartItem in smartStatusInfo.RequisitionItems)
                        {
                            //change ReqItemStatus default setting to Processing instead of Unknown
                            if (smartItem.RequisitionItemStatusTypeId == (int)RequisitionItemStatusTypeEnum.Unknown)
                            {
                                smartItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Processing;
                            }

                            var matchesSmartItem = itemUtility.MatchRequisitionItemsToSmartItem(ref submittedReqItems, smartItem);
                            if (matchesSmartItem != null)
                            {
                                foreach (var submittedItem in submittedReqItems)
                                {
                                    foreach (var matcheItem in matchesSmartItem)
                                    {
                                        if (submittedItem.Id == matcheItem.originalRequisitionItem.Id)
                                        {
                                            submittedItem.IsPurged = false;
                                        }
                                    }
                                }
                            }
                            var matchesDiscountSmartItem = itemUtility.MatchDiscountRequisitionItemsToSmartItem(ref submittedReqItems, smartItem);
                            if (matchesDiscountSmartItem != null)
                            {
                                foreach (var submittedItem in submittedReqItems)
                                {
                                    foreach (var matcheDiscountItem in matchesDiscountSmartItem)
                                    {
                                        if (submittedItem.Id == matcheDiscountItem.originalRequisitionItem.Id)
                                        {
                                            submittedItem.IsPurged = false;
                                        }
                                    }
                                }
                            }

                            foreach (var smartItemMatch in matchesSmartItem)
                            {
                                switch (smartItem.RequisitionItemAutoSubFlag == null ? "" : smartItem.RequisitionItemAutoSubFlag.ToUpper())
                                {
                                    case "S":
                                        if (smartItemMatch.substitutedRequisitionItem == null)
                                        {
                                            var newSubReqItem = this.CreateNewSubReqItem(smartItemMatch, smartItem);
                                            var tuple = requisitionRepository.AddNewSubstituteRequisitionItemtoRequisition(smartItemMatch.originalRequisitionItem, newSubReqItem, userName);
                                            smartItemMatch.substitutedRequisitionItem = tuple.Item1;
                                            smartItemMatch.originalRequisitionItem.QuantityToOrder = tuple.Item2;
                                            var requisition = requisitions.FirstOrDefault(x => x.RequisitionId == smartItemMatch.substitutedRequisitionItem.RequisitionId);
                                            if (requisition != null)
                                            {
                                                if (!requisition.RequisitionItems.Any(x => x.Id == smartItemMatch.substitutedRequisitionItem.Id))
                                                {
                                                    //Check to make sure that the item is not already in the requisition, which it shouldn't be.
                                                    //If it's not, add the sub item into requisition so it will be passed up to UI with rest of req.                                               
                                                    requisition.RequisitionItems.Add(smartItemMatch.substitutedRequisitionItem);
                                                }
                                            }
                                        }
                                        else
                                        {
                                            var subItem = smartItemMatch.substitutedRequisitionItem;
                                            itemUtility.UpdateRequisitionItemInfoFromSmartItem(ref subItem, smartItem);
                                            if (smartItem.PONumber != null && smartItem.PONumber != 0)
                                            {
                                                if (UpdatePONumber(smartItemMatch.substitutedRequisitionItem, smartItem))
                                                {
                                                    Task.Run(() => log.Debug("Requisition Item Id: " + smartItem.Id + " PO Number updated to DB: " + smartItem.PONumber));
                                                }
                                            }
                                        }
                                        break;
                                    default:
                                        var updateItem = smartItemMatch.originalRequisitionItem;
                                        itemUtility.UpdateRequisitionItemInfoFromSmartItem(ref updateItem, smartItem);
                                        if (smartItem.PONumber != null && smartItem.PONumber != 0)
                                        {
                                            if (UpdatePONumber(smartItemMatch.originalRequisitionItem, smartItem))
                                            {
                                                Task.Run(() => log.Debug("Requisition Item Id: " + smartItem.Id + " PO Number updated to DB: " + smartItem.PONumber));
                                            }
                                        }
                                        break;
                                }
                            }
                            matchesDiscountSmartItem.Select(x => x.originalRequisitionItem).ToList().ForEach(smartDiscountItemMatch =>
                            {
                                itemUtility.UpdateDiscountRequisitionItemInfoFromSmartItem(ref smartDiscountItemMatch, smartItem);
                                if (smartItem.PONumber != null && smartItem.PONumber != 0)
                                {
                                    if (UpdatePONumber(smartDiscountItemMatch, smartItem))
                                    {
                                        Task.Run(() => log.Debug("Requisition Item Id: " + smartItem.Id + " PO Number updated to DB: " + smartItem.PONumber));
                                    }
                                }
                            });
                        }
                    }
                }
            }
        }

        public bool UpdatePONumber(RequisitionItem requisitionItemDB, RequisitionItem requisitionItemSmart)
        {
            if (requisitionItemDB.PONumber == null)
            {
                requisitionItemDB.PONumber = requisitionItemSmart.PONumber;
                return (requisitionRepository.UpdateRequisitionItemPONumber(requisitionItemDB) > 0);
            }
            return default(bool);
        }

        private List<RequisitionForExport> HydrateRequisitionsWithParentSystemDetails(string userName, List<RequisitionForExport> requisitions)
        {
            List<Requisition> requisitionsOnly = requisitions.Cast<Requisition>().ToList();

            List<Requisition> hydratedRequisitions = HydrateRequisitionsWithParentSystemDetails(userName, requisitionsOnly);

            List<RequisitionForExport> hydratedRequisitionsForExport = hydratedRequisitions.Cast<RequisitionForExport>().ToList();

            hydratedRequisitionsForExport.ForEach(x => x.CreatedByFullName = requisitions.FirstOrDefault(y => y.RequisitionId == x.RequisitionId).CreatedByFullName);

            return hydratedRequisitionsForExport;
        }

        private List<Requisition> HydrateRequisitionsWithParentSystemDetails(string userName, List<Requisition> requisitions)
        {
            var requisitionsForStatusFromIIB = requisitions.Where(req => configurationService.UseStatusFromIIB(LocationMapper.GetCOID(req.LocationIdentifier))).ToList();
            var requisitionsForStatusFromSMARTAPI = requisitions.Where(req => configurationService.UseStatusFromIIB(LocationMapper.GetCOID(req.LocationIdentifier)) == false).ToList();
            var results = new List<Requisition>();

            requisitionsForStatusFromIIB = UseRequisitionsStatusFromIIB(requisitionsForStatusFromIIB);
            requisitionsForStatusFromSMARTAPI = UseRequisitionStatusFromSMARTAPI(userName, requisitionsForStatusFromSMARTAPI);

            results.AddRange(requisitionsForStatusFromIIB);
            results.AddRange(requisitionsForStatusFromSMARTAPI);

            this.UpdateMissingVendors(ref results, userName);
            return results;
        }

        private void UpdateMissingVendors(ref List<Requisition> results, string userName)
        {
            // TODO: has the vendor call slowed?
            foreach (var coid in results.Select(x => LocationMapper.GetCOID(x.LocationIdentifier)).Distinct())
            {
                //Only get vendors that need to be looked up
                var reqItems = results.Where(x => LocationMapper.GetCOID(x.LocationIdentifier) == coid).SelectMany(y => y.RequisitionItems).Where(z => z.SPRDetail != null && (z.SPRDetail.Vendor == null || string.IsNullOrEmpty(z.SPRDetail.Vendor.Name) || z.SPRDetail.Vendor.Name == Names.VendorNotFound));
                if (reqItems.Any())
                {
                    var vendors = vendorSvc.GetAllVendorsForCoid(coid);
                    var sprDetails = new List<SPRDetail>();

                    foreach (var reqItem in reqItems)
                    {
                        var vendor = vendors.FirstOrDefault(v => v.Id == reqItem.SPRDetail.VendorId);
                        if (vendor == null)
                        {
                            log.Info(string.Format("Vendor not found for Coid: {0}, VendorId: {1}", coid, reqItem.SPRDetail.VendorId));
                            reqItem.SPRDetail.Vendor = new Vendor { Id = reqItem.SPRDetail.VendorId, Name = Names.VendorNotFound, InvalidVendorFlag = true };
                        }
                        else
                        {
                            if (reqItem.StockIndicator == false)
                            {
                                var checkVendor = vendorSvc.GetVendorInformationById(coid, reqItem.SPRDetail.VendorId);
                                if (checkVendor.Name == null)
                                {
                                    log.Info(string.Format("Vendor not found for Coid: {0}, VendorId: {1}", coid, reqItem.SPRDetail.VendorId));
                                    reqItem.SPRDetail.Vendor = new Vendor { Id = reqItem.SPRDetail.VendorId, Name = Names.VendorNotFound, InvalidVendorFlag = true };
                                }
                                else
                                    reqItem.SPRDetail.Vendor = new Vendor { Id = vendor.Id, Name = vendor.Name };
                            }
                            else
                                reqItem.SPRDetail.Vendor = new Vendor { Id = vendor.Id, Name = vendor.Name };
                        }

                        sprDetails.Add(reqItem.SPRDetail);
                    }

                    requisitionRepository.UpdateSprDetails(sprDetails, userName);

                    vendors = null;
                    sprDetails = null;
                }
            }
        }

        private Requisition HydrateRequisitionWithParentSystemDetails(string userName, Requisition requisition)
        {
            return this.HydrateRequisitionsWithParentSystemDetails(userName, new List<Requisition>() { requisition }).FirstOrDefault();
        }

        public Requisition HydrateBillOnlyReviewRequisitionWithParentSystemDetails(string userName, Requisition requisition)
        {
            return this.HydrateRequisitionsWithParentSystemDetails(userName, new List<Requisition>() { requisition }).FirstOrDefault();
        }

        public RequisitionWithDetailsDTO GetRequisitionWithDetails(int requisitionId, string userName)
        {
            var requisition = this.GetRequisition(userName, requisitionId);

            return GetWithDetailsDTO(requisition, userName);
        }

        public RequisitionWithDetailsDTO GetRequisitionWithDetails(int requisitionId, string coid, string userName)
        {
            var requisition = this.GetRequisition(userName, requisitionId, coid);
            return GetWithDetailsDTO(requisition, userName);
        }

        public RequisitionWithDetailsDTO GetRequisitionForVendorUser(int requisitionId, string coid, string username, List<int> vendorAffiliations)
        {
            var requisition = string.IsNullOrEmpty(coid) ? this.GetRequisition(username, requisitionId) : this.GetRequisition(username, requisitionId, coid);
            return isRequisitionAvailableForVendorUser(requisition, vendorAffiliations, username) ? GetWithDetailsDTO(requisition, username) : null;
        }

        public RequisitionWithDetailsDTO GetLegacyRequisition(int requisitionId, string coid, string countryCode, string userName)
        {
            var requisition = this.smartRequisitionInquiryService.GetRequisitionById(userName, requisitionId, coid, countryCode);
            return (requisition != null && requisition.LocationIdentifier != null) ? LegacyRequisitionToRequisitionWithDetailsDTO(requisition, userName) : null;
        }

        public RequisitionWithDetailsDTO LegacyRequisitionToRequisitionWithDetailsDTO(Requisition requisition, string userName)
        {
            RequisitionWithDetailsDTO dto = new RequisitionWithDetailsDTO();
            if (requisition != null)
            {
                var items = new List<Item>();
                var parItems = new List<ParItem>();
                var Req_Items = requisition.RequisitionItems.ToList();

                foreach (var reqItem in Req_Items)
                {
                    var parItem = ParItem.ConvertLegacyRequisitionItemToParItem(reqItem, (RequisitionTypeEnum)requisition.RequisitionTypeId);
                    if (parItem == null)
                    {
                        items.Add(Item.ConvertLegacyRequisitionItemToItem(reqItem));
                    }
                    else
                    {
                        parItems.Add(parItem);
                        items.Add(parItem.Item);
                    }
                }

                dto = new RequisitionWithDetailsDTO(requisition, items, parItems);
            }
            else
            {
                dto = new RequisitionWithDetailsDTO();
            }

            if (dto.RequisitionItems != null && dto.RequisitionItems.Any())
            {
                foreach (var reqItem in dto.RequisitionItems.Where(x => string.IsNullOrEmpty(x.Item.Vendor.Name)))
                {
                    var vendor = vendorSvc.GetVendordByVendorId(LocationMapper.GetCOID(requisition.LocationIdentifier), reqItem.Item.Vendor.Id);
                    reqItem.Item.Vendor.Name = vendor.Name;
                }
            }

            return dto;
        }

        public RequisitionWithDetailsDTO GetWithDetailsDTO(Requisition requisition, string userName)
        {
            if (requisition != null)
            {
                var items = new List<Item>();
                var parItems = new List<ParItem>();

                var reqItems = requisition.RequisitionItems.ToList();

                var reqParItems = reqItems.Where(x => x.ParIdentifier != null && x.ParIdentifier != "EPR").ToList();
                var reqSprItems = reqItems.Where(x => x.ParIdentifier == "EPR" || x.ParIdentifier == null).ToList();

                if (requisition.RequisitionItems != null && requisition.RequisitionItems.Any() && requisition.RequisitionTypeId != (int)RequisitionTypeEnum.Capital)
                {
                    var parInfoMissinginDb = reqParItems.Any(y => y.ReOrder == null || y.CatalogNumber == null);
                    var sprInfoMissinginDb = reqSprItems.Any(y => y.SPRDetail == null || (y.SmartItemNumber != null && y.CatalogNumber == null));

                    if ((parInfoMissinginDb && sprInfoMissinginDb) || requisition.IsEditableByRequisitioner)
                    {
                        (items, parItems) = GetItemDetailsForRequisitionItems(requisition, reqItems, userName);

                        if (sprInfoMissinginDb)
                        {
                            InsertCatalogNumberForSPRItems(ref reqSprItems, items);
                        }
                    }
                    else if (parInfoMissinginDb || sprInfoMissinginDb)
                    {
                        if (sprInfoMissinginDb)
                        {
                            (items, parItems) = MapMissingPresentItems(reqSprItems, reqParItems, requisition, userName);
                            InsertCatalogNumberForSPRItems(ref reqItems, items);
                        }
                        if (parInfoMissinginDb)
                        {
                            (items, parItems) = MapMissingPresentItems(reqParItems, reqSprItems, requisition, userName);
                        }
                    }
                    else
                    {
                        (items, parItems) = MapMissingPresentItems(reqParItems, reqSprItems, requisition, userName);
                        if (requisition.IsVendor && requisition.RequisitionItems.Any(x => x.VboHoldItemConversion != null))
                        {
                            var (convertedItems, convertedParItems) = GetItemDetailsForVboConvertedItems(requisition, userName);
                            items.AddRange(convertedItems);
                            parItems.AddRange(convertedParItems);
                        }
                    }
                }

                var result = new RequisitionWithDetailsDTO(requisition, items, parItems);

                if (result.IsVendor && result.RequisitionItems.Any(x => x.vboHoldItemConversionDto?.UnitCost != x.vboHoldItemConversionDto?.ItemDetails?.ParPrice))
                {
                    var priceOutOfDateItems = result.RequisitionItems.Where(x => x.vboHoldItemConversionDto != null && x.vboHoldItemConversionDto.ItemDetails != null && x.vboHoldItemConversionDto.UnitCost != x.vboHoldItemConversionDto.ItemDetails.ParPrice).ToList();

                    requisitionRepository.UpdatePriceForVboHoldItemConversions(priceOutOfDateItems.Select(x => x.vboHoldItemConversionDto).ToList());
                }

                if (requisition.BadgeLogId != null)
                {
                    result.VProBadgeLog = this.vproService.GetBadgeLogById((int)requisition.BadgeLogId);
                }

                return result;
            }

            return null;
        }

        private Tuple<List<Item>, List<ParItem>> GetItemDetailsForRequisitionItems(Requisition requisition, List<RequisitionItem> reqItems, string username)
        {
            var coid = LocationMapper.GetCOID(requisition.LocationIdentifier);
            var departmentId = LocationMapper.GetDepartmentId(requisition.LocationIdentifier);
            var itemPars = reqItems.Where(x => x.ItemId.IsValidItemIdForSMART()).Select(x => new ItemParDTO { ItemId = x.ItemId, ParId = string.IsNullOrEmpty(x.ParIdentifier) ? string.Empty : departmentId + x.ParIdentifier }).ToList();
            if (requisition.IsVendor)
            {
                itemPars = itemPars.Union(requisition.RequisitionItems.Where(x => x.VboHoldItemConversion != null).Select(y => new ItemParDTO { ItemId = y.VboHoldItemConversion.SmartItemNumber.ToString(), ParId = requisition.RequisitionParClass })).ToList();
            }

            return GetItemDetailsFromLegacy(username, requisition, coid, departmentId, itemPars);
        }

        private Tuple<List<Item>, List<ParItem>> GetItemDetailsForVboConvertedItems(Requisition requisition, string username)
        {
            var coid = LocationMapper.GetCOID(requisition.LocationIdentifier);
            var departmentId = LocationMapper.GetDepartmentId(requisition.LocationIdentifier);
            var itemPars = requisition.RequisitionItems.Where(x => x.VboHoldItemConversion != null).Select(y => new ItemParDTO { ItemId = y.VboHoldItemConversion.SmartItemNumber.ToString(), ParId = requisition.RequisitionParClass }).ToList();

            return GetItemDetailsFromLegacy(username, requisition, coid, departmentId, itemPars);
        }

        private Tuple<List<Item>, List<ParItem>> GetItemDetailsFromLegacy(string username, Requisition requisition, string coid, string departmentId, List<ItemParDTO> itemPars)
        {
            var getAvailablePars = requisition.RequisitionTypeId == (int)RequisitionTypeEnum.Standard || requisition.RequisitionTypeId == (int)RequisitionTypeEnum.Rush || requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillOnly || requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillAndReplace;
            var itemDetails = itemService.GetItemsWithDetails(username, coid, Convert.ToInt16(departmentId), itemPars/*, getAvailablePars*/).ToList();

            //Get PAR items
            if (itemDetails != null)
            {
                var parItemsToAdd = new List<ParItem>();
                if (getAvailablePars)
                {
                    foreach (var detail in itemDetails)
                    {
                        if (detail.AvailableParItems != null && detail.AvailableParItems.Any())
                        {
                            if (requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillOnly || requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillAndReplace)
                            {
                                parItemsToAdd.AddRange(detail.AvailableParItems.Where(x => x.ParType == (int)ParType.BillOnly));
                            }
                            else
                            {
                                parItemsToAdd.AddRange(detail.AvailableParItems);
                            }
                        }
                    }
                }
                else
                {
                    if (requisition.RequisitionItems.Any(x => !String.IsNullOrWhiteSpace(x.ParIdentifier)))
                    {
                        var parIdToGet = requisition.RequisitionItems.First(x => !String.IsNullOrWhiteSpace(x.ParIdentifier)).ParIdentifier;
                        parItemsToAdd = parService.GetParItems(username, coid, Convert.ToInt16(departmentId), parIdToGet).ToList();
                    }
                }

                return new Tuple<List<Item>, List<ParItem>>(itemDetails.Select(x => x.Item).ToList(), parItemsToAdd);
            }

            return null;
        }

        private Tuple<List<Item>, List<ParItem>> ReqItemsTuple(Requisition requisition, List<RequisitionItem> requisitionItemDetails)
        {
            var parOnly = requisitionItemDetails.Where(x => x.ParIdentifier != null && x.ParIdentifier != "EPR").ToList();
            var SPROnly = requisitionItemDetails.Where(x => x.ParIdentifier == "EPR" || x.ParIdentifier == null).ToList();

            //Building Items List for PAR Items
            var itemsPAR = parOnly.ConvertAll(x => new Item
            {
                Id = x.ItemId,
                ReorderNumber = x.ReOrder,
                ManufacturerCatalogNumber = x.CatalogNumber,
                Description = x.ItemDescription,
                IsStock = Convert.ToBoolean(x.StockIndicator),
                Vendor = new Vendor { Id = Convert.ToInt32(x.VendorId), Name = x.VendorName },
                UOM = x.UOMCode,
                Price = x.UnitCost,
                ParId = x.ParIdentifier,
                IsCapitated =
               ((x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillOnly || x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace) && (x.MainItemId == null))
                           ? true
                           : default(bool),
                GLSubCode = (x.GeneralLedgerCode != null) ? Convert.ToInt32(x.GeneralLedgerCode) : default(int)
            });

            //Building Items List for SPR Items
            var itemsSPR = SPROnly.ConvertAll(x => new Item
            {
                Id = x.ItemId,
                ParId = x.ParIdentifier,
                ManufacturerCatalogNumber = (x.IsFileItem == true && x.CatalogNumber != null) ? x.CatalogNumber : null,
                Description = (x.SPRDetail != null) ? x.SPRDetail.ItemDescription : default(string),
                IsStock = Convert.ToBoolean(x.StockIndicator),
                Vendor = new Vendor
                {
                    Id = (x.SPRDetail != null) ?
                            ((x.SPRDetail.Vendor != null) ? x.SPRDetail.Vendor.Id : default(int))
                           : default(int),
                    Name = (x.SPRDetail != null) ?
                            ((x.SPRDetail.Vendor != null) ? x.SPRDetail.Vendor.Name : default(string))
                           : default(string)
                },
                UOM = (x.SPRDetail != null) ? x.SPRDetail.UOMCode : default(string),
                Price = (x.SPRDetail != null) ? x.SPRDetail.EstimatedPrice : default(decimal),
                IsCapitated =
                ((x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillOnly || x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace) && (x.MainItemId == null))
                            ? true
                            : default(bool),
                GLSubCode = (x.SPRDetail != null) ?
                                (x.SPRDetail.GeneralLedgerCode != null) ? Convert.ToInt32(x.SPRDetail.GeneralLedgerCode) :
                                default(int)
                            : default(int)
            });

            //Combining Items List
            var items = itemsPAR.Concat(itemsSPR).ToList();

            //Building PAR List
            var parItems = parOnly.ConvertAll(x => new ParItem
            {
                ItemId = Convert.ToInt32(x.ItemId),
                ParId = x.ParIdentifier,
                Location = x.PARLocation,
                GLAccount = Convert.ToInt64(x.GeneralLedgerCode),
                MinStock = x.MinStock,
                MaxStock = x.MaxStock,
                IssueUOM = x.UOMCode,
                ParPrice = x.UnitCost == null ? 0 : (decimal)x.UnitCost,
                ParType =
                (x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillOnly || x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace)
                            ? (int)ParType.Capitated :
                (x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillOnly || x.Requisition.RequisitionTypeId == (int)RequisitionTypeEnum.BillAndReplace)
                            ? (int)ParType.BillOnly
                            : default(int),
                //Merging Item List within PAR List
                Item = itemsPAR.Where(y => y.Id == x.ItemId.ToString() && y.ParId == x.ParIdentifier).FirstOrDefault() as Item
            });

            if (requisition.RequisitionTypeId != (int)RequisitionTypeEnum.PunchOut)
            {
                items.ForEach(x =>
                {
                    if (x.Id.IsValidItemIdForSMART())
                    {
                        x.IsValidItem = true;
                    }
                });
            }
            return new Tuple<List<Item>, List<ParItem>>(items, parItems);
        }

        private Tuple<List<Item>, List<ParItem>> MapMissingPresentItems(List<RequisitionItem> missingItems, List<RequisitionItem> presentItems, Requisition requisition, string userName)
        {
            var items = new List<Item>();
            var parItems = new List<ParItem>();

            var newReqItemInfo = GetItemDetailsForRequisitionItems(requisition, missingItems, userName);
            var spritems_missing = newReqItemInfo.Item1;
            var parItems_missing = newReqItemInfo.Item2;

            var RequisitionItemTupleInfo = ReqItemsTuple(requisition, presentItems);
            var spritems_present = RequisitionItemTupleInfo.Item1;
            var parItems_present = RequisitionItemTupleInfo.Item2;

            items = spritems_missing.Concat(spritems_present).ToList();
            parItems = parItems_missing.Concat(parItems_present).ToList();
            parItems.AddRange(ConstructMissingParItemForSubItem(missingItems, parItems, items));

            return new Tuple<List<Item>, List<ParItem>>(items, parItems);
        }

        private List<ParItem> ConstructMissingParItemForSubItem(List<RequisitionItem> missingItems, List<ParItem> parItems, List<Item> items)
        {

            var returnList = new List<ParItem>();
            //For subItems that do not have ParItem objects for the correct/any ParClass
            var subItemsWithNoParItem = missingItems.Any(s => s.ParentRequisitionItemId != null)
                        ? missingItems.Where(x => x.ParentRequisitionItemId != null
                                                && !parItems.Any(y => x.ParIdentifier.ToLower() == y.ParId.ToLower()
                                                                    && x.ItemId.ToLower() == y.ItemId.ToString().ToLower()
                                                )
                        )
                        : new List<RequisitionItem>();
            if (subItemsWithNoParItem.Any())
            {
                foreach (var subItem in subItemsWithNoParItem)
                {
                    var subSMARTItem = items.FirstOrDefault(x => x.Id.ToLower() == subItem.ItemId.ToLower());
                    var mainReqItem = missingItems.Where(x => x.Id == subItem.ParentRequisitionItemId).FirstOrDefault();
                    var mainParItem = parItems.Where(x => x.ParId.ToLower() == subItem.ParIdentifier.ToLower()
                                                        && x.ItemId.ToString().ToLower() == mainReqItem.ItemId.ToLower()).FirstOrDefault();
                    if (subSMARTItem != null && mainReqItem != null && mainParItem != null)
                    {
                        int itemId;
                        if (Int32.TryParse(subItem.ItemId, out itemId))
                        {
                            returnList.Add(new ParItem()
                            {
                                ParId = mainParItem.ParId,
                                ItemId = itemId,
                                MinStock = 0,
                                MaxStock = 0,
                                IssueUOM = subSMARTItem.IUOM,
                                Item = subSMARTItem,
                                ParDescription = mainParItem.ParDescription,
                                ParType = mainParItem.ParType,
                                GLAccount = mainParItem.GLAccount,
                                IGLAccount = mainParItem.IGLAccount,
                                Location = mainParItem.Location,
                                ParPrice = subSMARTItem.CostIUOM
                            });
                        }
                    }
                }
            }
            return returnList;
        }

        public List<RequisitionItem> UpdateRequisitionItemWithPARItemInfo(List<RequisitionItem> reqItems, List<ParItem> parItems)
        {
            reqItems.ForEach(x =>
            {
                parItems.ForEach(y =>
                {
                    if ((x.ItemId == Convert.ToString(y.ItemId)) && (x.ParIdentifier == y.ParId))
                    {
                        x.GeneralLedgerCode = Convert.ToString(y.GLAccount);
                        x.PARLocation = y.Location;
                        x.MinStock = y.MinStock;
                        x.MaxStock = y.MaxStock;
                        x.UOMCode = y.IssueUOM;

                        x.ReOrder = y.Item.ReorderNumber;
                        x.CatalogNumber = y.Item.ManufacturerCatalogNumber;
                        x.ItemDescription = y.Item.Description;
                        x.StockIndicator = y.Item.IsStock;
                        x.VendorId = y.Item.Vendor.Id;
                        x.VendorName = y.Item.Vendor.Name;
                        x.UnitCost = y.ParPrice;
                        x.TotalCost = Convert.ToDecimal(y.Item.Price * x.QuantityToOrder);
                    }
                });
            });
            return reqItems;
        }

        public Requisition GetRequisition(string userName, int requisitionId)
        {
            var requisition = requisitionRepository.GetRequisition(requisitionId);

            //Get further details for submitted reqs
            if (requisition != null && requisition.RequisitionItems != null)
            {
                requisition = this.HydrateRequisitionWithParentSystemDetails(userName, requisition);
                requisition.RequisitionItems = requisition.RequisitionItems.OrderBy(x => x.ParentRequisitionItemId == null ? x.Id : x.ParentRequisitionItemId).ThenBy(y => y.CreateDate).ToList();
            }

            return requisition;
        }

        Requisition GetRequisition(string userName, int requisitionId, string coid)
        {
            var requisition = requisitionRepository.GetRequisition(requisitionId, coid);

            //Get further details for submitted reqs
            if (requisition != null && requisition.RequisitionItems != null)
            {
                requisition = this.HydrateRequisitionWithParentSystemDetails(userName, requisition);
                requisition.RequisitionItems = requisition.RequisitionItems.OrderBy(x => x.ParentRequisitionItemId == null ? x.Id : x.ParentRequisitionItemId).ThenBy(y => y.CreateDate).ToList();
            }

            return requisition;
        }

        public List<Requisition> GetRequisitionsByDateRange(string COID, DateTime startDate, DateTime endDate, int departmentId, string userName)
        {
            var requisitions = requisitionRepository.GetRequisitionsWithDetailsByDateRange(COID, departmentId, startDate, endDate);

            return requisitions;
        }

        public List<Requisition> GetRequisitionsWithItemStatusesByDateRange(string userName, string COID, DateTime startDate, DateTime endDate, int departmentId)
        {
            var requisitionsByDateRange = requisitionRepository.GetRequisitionsWithDetailsByDateRange(COID, departmentId, startDate, endDate);

            //Remove drafts, templates, and recalled requisitions
            var requisitions = requisitionsByDateRange.Where(x => (x.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Draft && x.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Template && x.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Recalled));

            //Get further details for submitted reqs
            var returnRequisitions = new List<Requisition>();
            if (requisitions != null)
            {
                returnRequisitions = this.HydrateRequisitionsWithParentSystemDetails(userName, requisitions.ToList());
            }

            return returnRequisitions;
        }

        public RequisitionListResultsDTO GetRequisitionsByVendor(RequisitionReportRequestDto request)
        {
            RequisitionReportResults dbResults = requisitionRepository.GetRequisitionsForReportByVendor(
                request.RowOffset, request.PageSize, request.SortOrder, request.Coid,
                request.SearchText, request.FilterText, request.VendorAffiliations,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter);

            var hydratedRequisitions = HydrateRequisitionsWithParentSystemDetails(request.Username, dbResults.Requisitions);

            var requisitionDtos = hydratedRequisitions.Select(req => new RequisitionDTO(req)).ToList();

            return new RequisitionListResultsDTO(requisitionDtos, dbResults.TotalCount);
        }

        public RequisitionReportExportResultsDTO GetRequisitionsByVendorReportExport(RequisitionReportRequestDto request)
        {
            RequisitionReportExportResults dbResults = requisitionRepository.GetRequisitionsForReportByVendorExport(
                request.SortOrder, request.Coid,
                request.SearchText, request.FilterText,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter, request.VendorAffiliations);

            return HydrateRequisitionsWithDetails(dbResults, request);
        }

        public RequisitionListResultsDTO GetRequisitionsForReport(RequisitionReportRequestDto request)
        {
            RequisitionReportResults dbResults = requisitionRepository.GetRequisitionsForReport(
                request.RowOffset, request.PageSize, request.SortOrder, request.Coid,
                request.StartDate, request.EndDate, request.FilterText, request.DepartmentIds,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter, request.VendorAffiliations);

            var hydratedRequisitions = HydrateRequisitionsWithParentSystemDetails(request.Username, dbResults.Requisitions);

            var requisitionDtos = hydratedRequisitions.Select(req => new RequisitionDTO(req)).ToList();

            return new RequisitionListResultsDTO(requisitionDtos, dbResults.TotalCount);
        }

        public RequisitionListResultsDTO GetVBORequisitionsForReport(RequisitionReportRequestDto request)
        {
            RequisitionReportResults dbResults = requisitionRepository.GetVBORequisitionsForReport(
                request.RowOffset, request.PageSize, request.SortOrder, request.Coid,
                request.StartDate, request.EndDate, request.FilterText, request.DepartmentIds,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter, request.VendorAffiliations);

            var hydratedRequisitions = HydrateRequisitionsWithParentSystemDetails(request.Username, dbResults.Requisitions);

            var requisitionDtos = hydratedRequisitions.Select(req => new RequisitionDTO(req)).ToList();

            return new RequisitionListResultsDTO(requisitionDtos, dbResults.TotalCount);
        }

        public RequisitionReportExportResultsDTO GetRequisitionsForReportExport(RequisitionReportRequestDto request)
        {
            RequisitionReportExportResults dbResults = requisitionRepository.GetRequisitionsForReportExport(
                request.SortOrder, request.Coid,
                request.StartDate, request.EndDate, request.FilterText, request.DepartmentIds,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter, request.VendorAffiliations);

            return HydrateRequisitionsWithDetails(dbResults, request);
        }

        public RequisitionReportExportResultsDTO GetVBORequisitionsForReportExport(RequisitionReportRequestDto request)
        {
            RequisitionReportExportResults dbResults = requisitionRepository.GetVBORequisitionsForReportExport(
                request.SortOrder, request.Coid,
                request.StartDate, request.EndDate, request.FilterText, request.DepartmentIds,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter, request.VendorAffiliations);

            return HydrateRequisitionsWithDetails(dbResults, request);
        }

        public RequisitionListResultsDTO GetRequisitionsForReportByItemNumber(RequisitionReportRequestDto request)
        {
            RequisitionReportResults dbResults = requisitionRepository.GetRequisitionsForReportByItemNumber(
                request.RowOffset, request.PageSize, request.SortOrder, request.Coid,
                request.SearchText, request.FilterText, request.VendorAffiliations,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter);

            var hydratedRequisitions = HydrateRequisitionsWithParentSystemDetails(request.Username, dbResults.Requisitions);

            var requisitionDtos = hydratedRequisitions.Select(req => new RequisitionDTO(req)).ToList();

            return new RequisitionListResultsDTO(requisitionDtos, dbResults.TotalCount);
        }

        public RequisitionReportExportResultsDTO GetRequisitionsForReportByItemNumberExport(RequisitionReportRequestDto request)
        {
            RequisitionReportExportResults dbResults = requisitionRepository.GetRequisitionsForReportByItemNumberExport(
                request.SortOrder, request.Coid,
                request.SearchText, request.FilterText, request.VendorAffiliations,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter);

            return HydrateRequisitionsWithDetails(dbResults, request);
        }

        public RequisitionListMultiResultsDTO GetMyRequisitionsResults(string username, RequisitionListRequestDto leftSideRequest, RequisitionListRequestDto rightSideRequest)
        {
            RequisitionListResultsDTO leftSideResults = null;
            RequisitionListResultsDTO rightSideResults = null;

            if (leftSideRequest != null)
            {
                RequisitionReportResults leftSideDbResults = requisitionRepository.GetRequisitionsForUser(
                    username,
                    leftSideRequest.RowOffset, leftSideRequest.PageSize,
                    leftSideRequest.FilterText, leftSideRequest.SortOrder,
                    leftSideRequest.FacilitiesMatchedOnFilter, leftSideRequest.DepartmentsMatchedOnFilter);

                var hydratedRequisitions = this.HydrateRequisitionsWithParentSystemDetails(username, leftSideDbResults.Requisitions);
                var requisitionDTOs = hydratedRequisitions.Select(req => new RequisitionDTO(req)).ToList();
                leftSideResults = new RequisitionListResultsDTO(requisitionDTOs, leftSideDbResults.TotalCount);
            }

            if (rightSideRequest != null)
            {
                RequisitionReportResults rightSideDbResults = requisitionRepository.GetTemplatesforAUser(
                    username,
                    rightSideRequest.RowOffset, rightSideRequest.PageSize,
                    rightSideRequest.FilterText,
                    rightSideRequest.FacilitiesMatchedOnFilter, rightSideRequest.DepartmentsMatchedOnFilter);

                var requisitionDTOs = rightSideDbResults.Requisitions.Select(req => new RequisitionDTO(req)).ToList();
                rightSideResults = new RequisitionListResultsDTO(requisitionDTOs, rightSideDbResults.TotalCount);
            }

            return new RequisitionListMultiResultsDTO(leftSideResults, rightSideResults);
        }

        public ApprovalListMultiResultsDTO GetMyApprovalsResults(string username, RequisitionListRequestDto leftSideRequest, RequisitionListRequestDto rightSideRequest)
        {
            ApprovalListResultsDTO leftSideResults = null;
            ApprovalListResultsDTO rightSideResults = null;

            if (leftSideRequest != null)
            {
                ApprovalPageResults leftSideDbResults = requisitionRepository.GetPendingApprovalsForApprover(
                    username, leftSideRequest.IsExcludeVboChecked,
                    leftSideRequest.RowOffset, leftSideRequest.PageSize,
                    leftSideRequest.FilterText, leftSideRequest.SortOrder,
                    leftSideRequest.FacilitiesMatchedOnFilter, leftSideRequest.DepartmentsMatchedOnFilter);

                var approvalDTOs = leftSideDbResults.Approvals.Select(approval => new ApprovalDTO(approval.Requisition, approval.AdhocReview, approval.PendingReviewsExist, true)).ToList();

                leftSideResults = new ApprovalListResultsDTO(approvalDTOs, leftSideDbResults.TotalCount);
            }

            if (rightSideRequest != null)
            {
                ApprovalPageResults rightSideDbResults = requisitionRepository.GetApprovalHistoryForApprover(
                    username,
                    rightSideRequest.RowOffset, rightSideRequest.PageSize,
                    rightSideRequest.FilterText, rightSideRequest.SortOrder,
                    rightSideRequest.FacilitiesMatchedOnFilter, rightSideRequest.DepartmentsMatchedOnFilter);

                var approvalDTOs = rightSideDbResults.Approvals.Select(approval => new ApprovalDTO(approval.Requisition, approval.AdhocReview, approval.HistoryItem, approval.PendingReviewsExist)).ToList();
                rightSideResults = new ApprovalListResultsDTO(approvalDTOs, rightSideDbResults.TotalCount);
            }

            return new ApprovalListMultiResultsDTO(leftSideResults, rightSideResults);
        }

        public ApprovalListResultsDTO GetUpcomingApprovalsForApprover(RequisitionListRequestDto request)
        {
            ApprovalPageResults dbResults = requisitionRepository.GetUpcomingApprovalsForApprover(
                request.Username,
                request.RowOffset, request.PageSize,
                request.FilterText, request.SortOrder,
                request.FacilitiesMatchedOnFilter, request.DepartmentsMatchedOnFilter);

            var approvalDTOs = dbResults.Approvals.Select(approval => new ApprovalDTO(approval.Requisition, approval.AdhocReview, approval.PendingReviewsExist)).ToList();

            return new ApprovalListResultsDTO(approvalDTOs, dbResults.TotalCount);
        }


        public List<RequisitionStatusHistory> GetRequisitionHistory(int requisitionId)
        {
            return requisitionStatusRepository.GetStatusHistory(requisitionId).ToList();
        }

        public StatusUpdateDTO UpdateRequisitionStatus(RequisitionStatusHistory requisitionStatusHistory)
        {
            if (requisitionStatusHistory == null)
            {
                throw new ArgumentNullException("requisitionStatusHistory");
            }

            if (String.IsNullOrWhiteSpace(requisitionStatusHistory.CreatedBy))
            {
                throw new ArgumentNullException("requisitionStatusHistory.CreatedBy");
            }

            var requisition = requisitionRepository.GetRequisition(requisitionStatusHistory.RequisitionId);
            if (requisition == null)
            {
                throw new ArgumentException(String.Format("No requisition found with specified Requisition Id ({0})", requisitionStatusHistory.RequisitionId));
            }

            var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
            if (String.IsNullOrWhiteSpace(COID))
            {
                throw new ArgumentNullException("Bad data in the Requisition's LocationIdentifier");
            }

            if (requisitionStatusHistory.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Approved)
            {
                //Get Approver
                var approver = userService.GetApproverByUserNameAndCOID(requisitionStatusHistory.CreatedBy, COID);
                if (approver == null)
                {
                    throw new UnauthorizedAccessException("User attempting to update requisition is not an approver");
                }

                //Fetch the WF step and Delegator's Approver Id for audit and writing to req status histories table.
                var isFinalRushStep = false;
                var presentWorkflowStep = GetCurrentUserWorkflowStep(requisition, approver.Id, COID);
                if (presentWorkflowStep != null)
                {
                    requisitionStatusHistory.DelegatedByApproverId = presentWorkflowStep.DelegatedByUserId != null ? userService.GetApproverByUserIdCOIDAndDelegateId(presentWorkflowStep.DelegatedByUserId, COID, presentWorkflowStep.ApproverId).Id : (int?)null;
                    isFinalRushStep = presentWorkflowStep.IsFinalRushStep;
                }

                //Add amount and step and IsCERReviewer
                var approverAmount = requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital ? approver.CapitalMaxApprovalAmount : approver.MaxApprovalAmount;
                requisitionStatusHistory.ApprovedAmount = isFinalRushStep ? 0 : approverAmount;
                requisitionStatusHistory.ApprovalStep = requisition.ApprovalStep;
                requisitionStatusHistory.IsCERReviewer = approver.IsCERReviewer;

                //Go to workflow
                workflowService.ApproveRequistion(requisition, approver.Id, requisition.ApprovalStep ?? 1);

                //Write history record
                requisitionStatusRepository.AddStatusHistory(requisitionStatusHistory);

                //Update requisition
                if (!isFinalRushStep && requisition.ApprovedAmount < approverAmount)
                {
                    requisition.ApprovedAmount = approverAmount;
                    requisitionRepository.UpdateRequisition(requisition, requisitionStatusHistory.CreatedBy, false, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);
                }
            }
            else
            {
                if (requisitionStatusHistory.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Denied)
                {
                    //Get Approver
                    var approver = userService.GetApproverByUserNameAndCOID(requisitionStatusHistory.CreatedBy, COID);
                    if (approver == null)
                    {
                        throw new UnauthorizedAccessException("User attempting to update requisition is not an approver");
                    }

                    //Go to workflow
                    workflowService.DenyRequisition(requisition, approver.Id);

                    //Fetch the WF step and Delegator's Approver Id for audit and writing to req status histories table.
                    var presentWorkflowStep = GetCurrentUserWorkflowStep(requisition, approver.Id, COID);
                    requisition.DenyDelegateByApproverId = (presentWorkflowStep != null && presentWorkflowStep.DelegatedByUserId != null) ? userService.GetApproverByUserIdCOIDAndDelegateId(presentWorkflowStep.DelegatedByUserId, COID, presentWorkflowStep.ApproverId).Id : (int?)null;

                    var profile = userService.GetProfile(requisition.CreatedBy);
                    if (profile != null)
                    {
                        var emailRequest = new EmailRequest()
                        {
                            EmailType = EmailType.RequisitionDenied,
                            Emails = new string[1] { profile.Email },
                            ViewLink = this.GetRequisitionUrl(requisition.RequisitionId),
                            SenderName = profile.FullName,
                            Requisition = requisition,
                            Comments = requisitionStatusHistory.Comments,
                            ActionDate = requisition.CreateDate
                        };

                        emailService.SendEmail(emailRequest);
                    }
                }

                if (requisitionStatusHistory.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Recalled)
                {
                    //Get User Id
                    var requisitionerUser = userService.GetUserByAccountName(requisition.CreatedBy.ToString());
                    workflowService.DenyRequisition(requisition, requisitionerUser.Id);
                    RequisitionDigitalSignOffSoftDelete(requisitionStatusHistory);
                }

                //Save new status
                requisition.RequisitionStatusTypeId = requisitionStatusHistory.RequisitionStatusTypeId;
                requisition.Comments = requisitionStatusHistory.Comments;
                requisitionRepository.UpdateRequisition(requisition, requisitionStatusHistory.CreatedBy, true, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);
            }

            return new StatusUpdateDTO() { IsStatusUpdated = true };
        }

        public bool SmartLegacySubmissionAvailabilityCheck(string userId, string coid)
        {
            try
            {
                smartRequisitionService.GetSPRs(userId, coid, new List<string>() { "000" });

                return true;
            }
            catch (Exception ex)
            {
                log.Warn(String.Format("SMART Legacy server failed to respond when user {0} attempted to submit a requisition at COID {1}. Full error message: {2}", userId, coid, ex.Message));

                return false;
            }
        }

        private UserWorkflowStep GetCurrentUserWorkflowStep(Requisition requisition, int userId, string COID)
        {

            var presentWorkflowStep = new UserWorkflowStep();

            if (requisition.ApprovalStep.HasValue)
            {
                var presentUserId = userService.GetUserByAccountName(requisition.CreatedBy.ToString());
                if (presentUserId != null)
                {
                    presentWorkflowStep = workflowRepository.GetWorkflowStepForStatusHistory(userId, requisition.ApprovalStep.Value, (int)requisition.ApplicableWorkflowType, COID, presentUserId.Id);
                }
            }

            return presentWorkflowStep;
        }

        private string SerializeAndCleanRequisition(Requisition requisition)
        {
            string serializedReq = JsonConvert.SerializeObject(requisition, Formatting.Indented);
            Requisition deserializedReqClone = JsonConvert.DeserializeObject<Requisition>(serializedReq);
            Requisition censoredReqClone = censorService.CensorPrivateRequisitionData("System", deserializedReqClone);
            return JsonConvert.SerializeObject(censoredReqClone, Formatting.Indented);
        }

        private Requisition SubmitRequisitionToSmart(Requisition requisition, string userName, List<string> serializedReqsForLogging, long? cartId)
        {
            //Populate delivery method objects so SMART can get Description
            if (requisition != null && requisition.RequisitionItems != null && requisition.RequisitionItems.Where(x => x.SPRDetail != null).Any())
            {
                var deliveryMethods = typeRepository.GetDeliveryMethods();

                foreach (var reqItem in requisition.RequisitionItems.Where(x => x.SPRDetail != null))
                {
                    reqItem.SPRDetail.DeliveryMethodType = deliveryMethods.Where(x => x.Id == reqItem.SPRDetail.DeliveryMethodTypeId).FirstOrDefault();
                }
            }

            //Submit to smart
            try
            {
                //Change for Mobile: Removing zero quantity items right before submission to SMART.
                //We are storing QOH in DB for inventory now and so we want to be able to save items with zero quantity in DB
                var savedRequisition = requisition;
                requisition.RequisitionItems = this.RemoveZeroQuantityLineItems(requisition.RequisitionItems);

                if (requisition.RequisitionItems.Any())
                {
                    if (requisition.IsVendor && requisition.RequisitionItems.Any(x => x.VboHoldItemConversion != null))
                    {
                        var missingConvertedItemInfo = requisition.RequisitionItems.Where(x => x.VboHoldItemConversion != null && x.VboHoldItemConversion.ItemDetails == null).ToList();
                        if (missingConvertedItemInfo.Any())
                        {
                            var itemDetails = itemService.GetItemsWithDetails(userName,
                                LocationMapper.GetCOID(requisition.LocationIdentifier),
                                int.Parse(LocationMapper.GetDepartmentId(requisition.LocationIdentifier)),
                                missingConvertedItemInfo.Select(x => new ItemParDTO { ItemId = x.VboHoldItemConversion.SmartItemNumber.ToString(), ParId = requisition.RequisitionParClass }).ToList());

                            var parItems = itemDetails.SelectMany(itemDetailsDTO => itemDetailsDTO.AvailableParItems);

                            foreach (var reqItem in missingConvertedItemInfo)
                            {
                                reqItem.VboHoldItemConversion.ItemDetails = parItems.FirstOrDefault(x => x.ItemId == reqItem.VboHoldItemConversion.SmartItemNumber && string.Equals(x.ParId, requisition.RequisitionParClass, StringComparison.InvariantCultureIgnoreCase));
                            }
                        }
                    }

                    var requisitionerFullName = userService.GetUserFullName(userName);
                    var result = smartRequisitionService.SubmitRequisition(requisitionerFullName, userName, LocationMapper.GetCOID(requisition.LocationIdentifier), requisition);
                    var isMQEnabled = configurationService.UseStatusFromIIB(LocationMapper.GetCOID(result.LocationIdentifier));

                    bool isOneRecordSubmitted = false;
                    if (result.RequisitionItems != null)
                    {
                        foreach (var resultItem in result.RequisitionItems)
                        {
                            if (resultItem.ParentSystemId == parentSystemSubmissionErrorId)
                            {
                                resultItem.ParentSystemId = null;
                            }

                            if (resultItem.ParentSystemId != null)
                            {
                                isOneRecordSubmitted = true;
                                if (!isMQEnabled)
                                {
                                    resultItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.Processing;
                                }
                            }
                            else
                            {
                                resultItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.SystemError;
                            }
                        }
                    }

                    //Update statuses to reflect pass-off to MMIS if at least one item made it
                    if (isOneRecordSubmitted)
                    {
                        result.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.Submitted;
                        if (cartId != null)
                        {
                            cartRepository.Delete((long)cartId);
                        }                       
                    }
                    else
                    {
                        result.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.SubmissionError;
                        if (cartId != null) cartRepository.CorrelateCartAndRequisition((long)cartId, requisition.RequisitionId);
                    }

                    // The following ID change induces the workflow error. WARNING: THIS BREAKS THE REQUISITION!!!
                    if ((ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl").Contains("qa") || ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl").Contains("sbx")) && requisition.Comments == "This requisition is bugged don't use it!")
                    {
                        result.RequisitionItems.FirstOrDefault().Id = 55000000;
                    }

                    #region Logging for Unknown Workflow Issue
                    if (serializedReqsForLogging != null)
                    {
                        serializedReqsForLogging.Add(SerializeAndCleanRequisition(requisition));
                    }
                    #endregion

                    //Populate new values to database and return updated result
                    savedRequisition = this.SaveRequisition(result, userName, isMQEnabled, true);

                    try
                    {
                        //If a rush, prepare an email for the CSC
                        if (requisition.RequisitionTypeId == (int)RequisitionTypeEnum.Rush)
                        {
                            bool isAmountMet = false;
                            bool isApprovedBySomeone = false;
                            if (savedRequisition.RequisitionStatusHistories != null)
                            {
                                isApprovedBySomeone = savedRequisition.RequisitionStatusHistories.Where(x => x.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Approved).Any();
                            }
                            if (requisition.ApprovedAmount >= this.GetRequisitionTotal(requisition) && isApprovedBySomeone)
                            {
                                isAmountMet = true;
                            }

                            var summaryComments = requisition.Comments;
                            var profile = userService.GetProfile(requisition.CreatedBy);
                            var vendors = userService.GetVendorsForEmailTemplate(requisition);
                            var requisitionItems = GetRequisitionWithDetails(requisition.RequisitionId, userName).RequisitionItems;
                            var (rushStockItems, rushNonStockItems, hasStockAndNonStockRushItems) = emailService.GetItemsForRushEmailTemplate(requisitionItems, true);
                            var emails = this.GetCSCEmails(requisition);
                            var sprItem = requisitionItems.Where(x => x.SPRDetailDTO != null && x.IsRushOrder).FirstOrDefault();
                            var nonStockDeliveryMethod = sprItem != null ? sprItem.SPRDetailDTO.DeliveryMethodTypeDescription : "N/A";
                            var stockDeliveryMethod = "N/A";

                            if (profile != null)
                            {
                                emails.Add(profile.Email);
                            }


                            //Send email that req is submitted
                            var emailRequest = new EmailRequest()
                            {
                                UserName = userName,
                                EmailType = EmailType.RequisitionSubmittedRush,
                                Emails = emails.ToArray(),
                                ViewLink = this.GetRequisitionUrl(requisition.RequisitionId),
                                SenderName = userService.GetUserFullName(requisition.CreatedBy),
                                IsApprovalAmountMet = isAmountMet,
                                Requisition = requisition,
                                RushStockItems = rushStockItems,
                                RushNonStockItems = rushNonStockItems,
                                Comments = summaryComments,
                                ActionDate = requisition.CreateDate,
                                HasStockAndNonStockRushItems = hasStockAndNonStockRushItems,
                                StockDeliveryMethod = stockDeliveryMethod,
                                NonStockDeliveryMethod = nonStockDeliveryMethod
                            };

                            emailService.SendEmail(emailRequest);

                        }
                        else
                        {
                            var profile = userService.GetProfile(requisition.CreatedBy);
                            if (profile != null && requisition.RequisitionItems.Any(ri => ri.SPRDetail != null))
                            {
                                var emailRequest = new EmailRequest()
                                {
                                    EmailType = EmailType.RequisitionSubmitted,
                                    Emails = new string[1] { profile.Email },
                                    ViewLink = this.GetRequisitionUrl(requisition.RequisitionId),
                                    SenderName = profile.FullName,
                                    Requisition = requisition,
                                    ActionDate = DateTime.Now
                                };

                                emailService.SendEmail(emailRequest);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        log.Error(ex);

                        return savedRequisition;
                    }
                }
                else
                {
                    requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.Submitted;
                    savedRequisition = this.SaveRequisition(requisition, userName, false, true);
                    if (cartId != null)
                    {
                        cartRepository.Delete((long)cartId);
                    }
                }

                return savedRequisition;
            }
            catch (Exception ex)
            {
                DateTime exceptionTimestamp = DateTime.Now;

                #region Logging for Unknown Workflow Issue
                if (ex.Message == "The property 'Id' is part of the object's key information and cannot be modified. ")
                {
                    if (serializedReqsForLogging != null)
                    {
                        log.Info("Workflow error! Serialized requisitions used in this method:");
                        for (int i = 0; i < serializedReqsForLogging.Count; i++)
                        {
                            log.Info(serializedReqsForLogging[i]);
                        }
                    }

                    var userProfile = userService.GetProfile(requisition.CreatedBy);
                    if (userProfile != null)
                    {
                        //Send notification of workflow error to team
                        string[] adminEmailsForAlert = ConfigurationManager.AppSettings.Get("AdminsForEmailAlerts").Split(',');

                        var emailRequest = new EmailRequest()
                        {
                            EmailType = EmailType.WorkflowIssueTeamAlert,
                            Emails = adminEmailsForAlert,
                            ViewLink = this.GetRequisitionUrl(requisition.RequisitionId),
                            SenderName = userProfile.FullName,
                            ActionDate = exceptionTimestamp,
                            Requisition = requisition,
                            HighPriority = true
                        };

                        emailService.SendEmail(emailRequest);

                        //Send notification of workflow error to user
                        emailRequest.Emails = new string[1] { userProfile.Email };
                        emailRequest.EmailType = EmailType.WorkflowIssueUserAlert;
                        emailService.SendEmail(emailRequest);

                    }
                }
                #endregion

                log.Error(ex);

                requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.SubmissionError;

                foreach (var resultItem in requisition.RequisitionItems)
                {
                    resultItem.RequisitionItemStatusTypeId = (int)RequisitionItemStatusTypeEnum.SystemError;
                }

                this.SaveRequisition(requisition, userName, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);

                return requisition;
            }
        }

        public Requisition SubmitRequisition(Requisition requisition, string userName, bool requesterIsApprover, long? cartId)
        {
            log.Debug("Entering Method: SubmitRequisition");
            if (requisition.RequisitionItems != null)
            {
                //Remove invalid items
                requisition.RequisitionItems = requisition.RequisitionItems.Where(x => RequisitionItemStatusIsValid(x.RequisitionItemStatusTypeId)).ToList();

                //Combine duplicates (merging quantities)
                if (requisition.RequisitionTypeId != (int)RequisitionTypeEnum.CapitatedBillAndReplace && requisition.RequisitionTypeId != (int)RequisitionTypeEnum.CapitatedBillOnly)
                {
                    requisition.RequisitionItems = requisition.RequisitionItems.Where(x => x.SPRDetail == null && x.Discount == null).GroupBy(o => new { o.ItemId, o.ParIdentifier })
                                                        .Select(g => g.Skip(1).Aggregate(g.First(), (a, o) => { a.QuantityToOrder += o.QuantityToOrder; return a; }))
                                                        .Union(requisition.RequisitionItems.Where(x => x.Discount != null))
                                                        .Union(requisition.RequisitionItems.Where(x => x.SPRDetail != null)).ToList();
                }

                if (!requisition.IsMobile)
                {
                    requisition.RequisitionItems = this.RemoveZeroQuantityLineItems(requisition.RequisitionItems);
                }
            }
            //Check if requisitioner maxApprovalAmount is enough to approve the requisition
            Approver requisitioner = null;
            if (requesterIsApprover && requisition.ApplicableWorkflowType != WorkflowTypeEnum.NotApplicable)
            {
                var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
                if (String.IsNullOrWhiteSpace(COID))
                {
                    throw new ArgumentNullException("Bad data in the Requisition's LocationIdentifier");
                }
                requisitioner = userService.GetApproverByUserNameAndCOID(userName, COID);

                if ((requisition.ApplicableWorkflowType != WorkflowTypeEnum.Capital && this.userService.RequisitionerMaxApprovalAmountIsGreaterOrEqual(requisitioner, this.GetRequisitionTotal(requisition))) || (requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital && this.userService.RequisitionerCapitalMaxApprovalAmountIsGreaterOrEqual(requisitioner, this.GetRequisitionTotal(requisition))))
                {
                    //Find final step for workflow, if workflow needed
                    var userWorkflowSteps = workflowRepository.GetUserWorkflowSteps(userName, COID).Where(x => x.WorkflowTypeId == (int)requisition.ApplicableWorkflowType && !x.IsFinalRushStep).ToList();
                    var finalStep = 0;
                    if (userWorkflowSteps != null && userWorkflowSteps.Any())
                    {
                        finalStep = userWorkflowSteps.Where(x => x.IsFinalStep).Select(x => x.Step).FirstOrDefault();
                    }

                    if (finalStep == 0 || requisition.IsVendor)
                    {
                        requisition = this.InitiateWorkflow(requisition, userName);
                    }
                    else
                    {
                        if (requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital)
                        {
                            requisition = this.InitiateWorkflow(requisition, userName, finalStep, requisitioner.CapitalMaxApprovalAmount);
                        }
                        else
                        {
                            requisition = this.InitiateWorkflow(requisition, userName, finalStep, requisitioner.MaxApprovalAmount);
                        }

                        if (requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval)
                        {
                            var statusHistory = new RequisitionStatusHistory()
                            {
                                RequisitionId = requisition.RequisitionId,
                                RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.PendingApproval,
                                ApprovedAmount = requisitioner.MaxApprovalAmount,
                                IsCERReviewer = requisitioner.IsCERReviewer,
                                ApprovalStep = 0,
                                CreatedBy = userName,
                                CreateDate = DateTime.Now
                            };

                            if (requisition.ApplicableWorkflowType == WorkflowTypeEnum.Capital)
                            {
                                statusHistory.ApprovedAmount = requisitioner.CapitalMaxApprovalAmount;
                            }

                            requisitionStatusRepository.AddStatusHistory(statusHistory);
                        }
                    }
                }
                else
                {
                    requisition = this.InitiateWorkflow(requisition, userName);
                }
            }
            else
            {
                requisition = this.InitiateWorkflow(requisition, userName);
            }

            bool submitToMMIS = this.DetermineIfSendingToMMIS(requisition);

            //Ensure up to date save
            requisition.CreateDate = DateTime.Now;
            requisition = this.SaveRequisition(requisition, userName, configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);

            if (submitToMMIS)
            {
                int[] reqTypes = { (int)RequisitionTypeEnum.BillOnly, (int)RequisitionTypeEnum.BillAndReplace, (int)RequisitionTypeEnum.CapitatedBillAndReplace, (int)RequisitionTypeEnum.CapitatedBillOnly };

                if (!(requisition.CreatedBy == userName) && reqTypes.Contains(requisition.RequisitionTypeId))
                {
                    var clinicalDetails = requisition.RequisitionItems.Select(x => x.ClinicalUseDetails.ToList()).ToList();

                    int clinicalId = 0;
                    if (clinicalDetails != null)
                    {
                        clinicalId = clinicalDetails.First().First().Id;
                    }
                    var clinicalData = requisitionRepository.GetClinicalUseDetail(clinicalId);

                    foreach (var reqItem in requisition.RequisitionItems)
                    {
                        var clinicalDetail = reqItem.ClinicalUseDetails.FirstOrDefault();
                        clinicalDetail.PatientId = clinicalData.PatientId;
                        clinicalDetail.PatientName = clinicalData.PatientName;
                        clinicalDetail.Provider = clinicalData.Provider;
                    }
                }

                requisition = this.SubmitRequisitionToSmart(requisition, userName, null, cartId);
            }
            else
            {
                if (cartId > 0)
                {
                    cartRepository.CorrelateCartAndRequisition((long)cartId, requisition.RequisitionId);
                }

                var emailRequest = requisition.IsRush ?
                        GetPendingApprovalRushEmailRequest(requisition, userName) :
                        GetPendingApprovalEmailRequest(requisition, userName);
                emailService.SendEmail(emailRequest);
            }

            return requisition;
        }

        private bool RequisitionItemStatusIsValid(int requisitionItemStatusTypeId)
        {
            switch (requisitionItemStatusTypeId)
            {
                case (int)RequisitionItemStatusTypeEnum.Valid:
                case (int)RequisitionItemStatusTypeEnum.ItemNotOrdered:
                    return true;
                default:
                    return false;
            }
        }

        private Requisition InitiateWorkflow(Requisition requisition, string userName)
        {
            requisition.ApprovalStep = null;
            requisition.ApprovedAmount = 0;

            return workflowService.StartWorkflow(requisition, userName);
        }

        private Requisition InitiateWorkflow(Requisition requisition, string userName, int? approvalStep, decimal requisitionerApprovalAmount)
        {
            requisition.ApprovalStep = approvalStep;
            requisition.ApprovedAmount = requisitionerApprovalAmount;

            return workflowService.StartWorkflow(requisition, userName);
        }

        private bool DetermineIfSendingToMMIS(Requisition requisition)
        {
            if (requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Draft)
            {
                return true;
            }
            else
            {
                return false;
            }
        }

        public Requisition SaveRequisition(Requisition requisition, string userName, bool isMQEnabled, bool isFromSMARTResponse)
        {
            requisition = ValidateAndReadyRequisitionBeforeSave(requisition, userName);

            if (requisition.RequisitionId == 0)
            {
                //New requisition
                requisition.CreateDate = DateTime.Now;
                requisition.CreatedBy = userName;
                foreach (var reqItem in requisition.RequisitionItems)
                {
                    if (reqItem.SPRDetail != null && reqItem.SPRDetail.FileAttachments != null)
                    {
                        foreach (var file in reqItem.SPRDetail.FileAttachments)
                        {
                            file.CreateDate = DateTime.Now;
                            file.CreatedBy = userName;
                        }
                    }
                    if (reqItem.Inventory != null)
                    {
                        reqItem.Inventory.UserName = userName;
                        reqItem.Inventory.CreateDate = DateTime.UtcNow;
                        reqItem.Inventory.LastUpdatedUTC = DateTime.UtcNow;
                    }
                }

                requisitionRepository.InsertRequisition(requisition, userName);
            }
            else
            {
                //Update new file attachments
                foreach (var reqItem in requisition.RequisitionItems)
                {
                    if (reqItem.SPRDetail != null && reqItem.SPRDetail.FileAttachments != null)
                    {
                        foreach (var file in reqItem.SPRDetail.FileAttachments.Where(x => x.Id == 0))
                        {
                            file.CreateDate = DateTime.Now;
                            file.CreatedBy = userName;
                        }
                    }
                }

                //Update
                requisitionRepository.UpdateRequisition(requisition, userName, true, isMQEnabled, isFromSMARTResponse);
            }

            requisition = RepopulateTypeTables(requisition);

            return requisition;
        }

        public Requisition SaveRequisitionAsApprover(Requisition requisition, string userName, bool isMQEnabled, bool isFromSMARTResponse)
        {
            requisition = ValidateAndReadyRequisitionBeforeSave(requisition, userName);

            requisitionRepository.UpdateRequisitionAsApprover(requisition, userName, true, isMQEnabled, isFromSMARTResponse);

            requisition = RepopulateTypeTables(requisition);

            return requisition;
        }

        private decimal GetRequisitionTotal(Requisition requisition)
        {
            decimal total = 0;

            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    if (requisitionItem.SPRDetail != null)
                    {
                        if (requisitionItem.SPRDetail.EstimatedPrice != null)
                        {
                            total += ((decimal)requisitionItem.SPRDetail.EstimatedPrice * requisitionItem.QuantityToOrder);
                        }
                    }
                }
            }

            return total;
        }

        public void AdvanceRequisition(RequisitionAdvanceDTO requisitionAdvanceDTO)
        {
            #region Logging for Unknown Workflow Issue
            List<string> serializedReqsForLogging = new List<string>();
            serializedReqsForLogging.Add(JsonConvert.SerializeObject(requisitionAdvanceDTO, Formatting.Indented));
            #endregion

            if (requisitionAdvanceDTO == null)
            {
                log.Error("METHOD: AdvanceRequisition. ERROR: requisitionAdvanceDTO IS NULL");

                throw new InvalidOperationException("Advancement DTO is required. INVALID REQUEST");
            }

            var requisition = this.GetRequisition("System", requisitionAdvanceDTO.RequisitionId);
            if (requisition == null)
            {
                //TODO: Remove extra info before submitting artifacts for deployment
                throw new InvalidOperationException("Requisition" + requisitionAdvanceDTO.RequisitionId + " not found. New Step: " + requisitionAdvanceDTO.NewStep + " IsWorkflowComplete: " + requisitionAdvanceDTO.IsWorkflowApprovalComplete + ". INVALID REQUEST.");
            }

            bool isChange = ((requisition.ApprovalStep != requisitionAdvanceDTO.NewStep) || requisitionAdvanceDTO.IsWorkflowApprovalComplete);
            if (isChange)
            {
                var currentStep = requisition.ApprovalStep;
                requisition.ApprovalStep = requisitionAdvanceDTO.NewStep;

                #region Logging for Unknown Workflow Issue
                serializedReqsForLogging.Add(SerializeAndCleanRequisition(requisition));
                #endregion

                this.SaveRequisition(requisition, "System", configurationService.UseStatusFromIIB(LocationMapper.GetCOID(requisition.LocationIdentifier)), false);

                if (requisitionAdvanceDTO.IsAutoEscalation)
                {
                    var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
                    if (String.IsNullOrWhiteSpace(COID))
                    {
                        throw new ArgumentNullException("Bad data in the Requisition's LocationIdentifier");
                    }
                    var userWorkFlow = userService.GetUserWorkflowSteps(requisition.CreatedBy, COID, (int)requisition.ApplicableWorkflowType);
                    var currentApprovers = userWorkFlow.Where(x => x.Step == currentStep);
                    foreach (var currentApprover in currentApprovers)
                    {
                        var currentUserName = userService.GetUserFullName(currentApprover.Approver.User.AccountName);

                        //Write history record of escalation
                        requisitionStatusRepository.AddStatusHistory(new RequisitionStatusHistory()
                        {
                            RequisitionId = requisition.RequisitionId,
                            RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.PendingApproval,
                            Comments = String.Format("Requisition auto-advanced to next approval step ({0}).", requisitionAdvanceDTO.NewStep),
                            ApprovalStep = requisitionAdvanceDTO.NewStep,
                            CreatedBy = currentUserName,
                            CreateDate = DateTime.Now,
                            ApprovedAmount = currentApprover.Approver.MaxApprovalAmount,
                            IsCERReviewer = currentApprover.Approver.IsCERReviewer
                        });
                    }
                }

                if (requisitionAdvanceDTO.IsWorkflowApprovalComplete)
                {
                    this.SubmitRequisitionToSmart(requisition, requisition.CreatedBy, serializedReqsForLogging, null);
                }
                else
                {
                    //Send out emails to new (current) step approvers 

                    var emailRequest = requisition.IsRush ?
                        GetPendingApprovalRushEmailRequest(requisition, "System") :
                        GetPendingApprovalEmailRequest(requisition, "System");
                    emailService.SendEmail(emailRequest);
                }
            }
        }

        private EmailRequest GetPendingApprovalRushEmailRequest(Requisition requisition, string userName)
        {
            var requisitionItems = GetRequisitionWithDetails(requisition.RequisitionId, userName).RequisitionItems;
            var (rushStockItems, rushNonStockItems, hasStockAndNonStockRushItems) = emailService.GetItemsForRushEmailTemplate(requisitionItems, false);
            var sprItem = requisitionItems.Where(x => x.SPRDetailDTO != null && x.IsRushOrder).FirstOrDefault();
            var nonStockDeliveryMethod = sprItem != null ? sprItem.SPRDetailDTO.DeliveryMethodTypeDescription : "N/A";
            var stockDeliveryMethod = "N/A";

            return new EmailRequest()
            {
                UserName = UserName,
                EmailType = EmailType.PendingApprovalRush,
                Emails = this.GetApproverEmails(requisition),
                ViewLink = this.GetApprovalUrl(requisition.RequisitionId),
                SenderName = userService.GetUserFullName(requisition.CreatedBy),
                Requisition = requisition,
                RushStockItems = rushStockItems,
                RushNonStockItems = rushNonStockItems,
                Comments = requisition.Comments,
                ActionDate = requisition.CreateDate,
                HasStockAndNonStockRushItems = hasStockAndNonStockRushItems,
                StockDeliveryMethod = stockDeliveryMethod,
                NonStockDeliveryMethod = nonStockDeliveryMethod
            };
        }

        private EmailRequest GetPendingApprovalEmailRequest(Requisition requisition, string username)
        {
            return new EmailRequest()
            {
                UserName = username,
                EmailType = requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.OnHold ? EmailType.PendingApprovalOnHold : EmailType.PendingApproval,
                Emails = this.GetApproverEmails(requisition),
                ViewLink = this.GetApprovalUrl(requisition.RequisitionId),
                SenderName = userService.GetUserFullName(requisition.CreatedBy),
                Requisition = requisition,
                Vendors = userService.GetVendorsForEmailTemplate(requisition),
                Comments = requisition.Comments,
                ActionDate = requisition.CreateDate
            };
        }

        private string[] GetApproverEmails(Requisition requisition)
        {
            var emails = new List<String>();

            var COID = LocationMapper.GetCOID(requisition.LocationIdentifier);
            if (String.IsNullOrWhiteSpace(COID))
            {
                throw new ArgumentNullException("Bad data in the Requisition's LocationIdentifier");
            }
            if (requisition.IsVendor)
            {
                var facilityWorkflowSteps = _facilityWorkflowService.Get(COID, WorkflowTypeEnum.Vendor);
                if (facilityWorkflowSteps != null && requisition.ApprovalStep != null)
                {
                    foreach (var facilityWorkflowStep in facilityWorkflowSteps.Steps)
                    {
                        if (facilityWorkflowStep.Approver != null && facilityWorkflowStep.Approver.User != null)
                        {
                            AddApproverEmail(facilityWorkflowStep.Approver.User.AccountName, emails);
                        }
                    }
                }
            }
            else
            {
                var userWorkflowSteps = userService.GetUserWorkflowSteps(requisition.CreatedBy, COID, (int)requisition.ApplicableWorkflowType).ToList();
                if (userWorkflowSteps != null && requisition.ApprovalStep != null)
                {
                    foreach (var userWorkflowStep in userWorkflowSteps.Where(x => x.Step == requisition.ApprovalStep))
                    {
                        if (userWorkflowStep.Approver != null && userWorkflowStep.Approver.User != null)
                        {
                            AddApproverEmail(userWorkflowStep.Approver.User.AccountName, emails);
                        }
                    }
                }
            }

            return emails.Distinct().ToArray();
        }

        // Add email from Security
        private void AddApproverEmail(string approverAccountName, List<String> emails)
        {
            if (approverAccountName.IndexOf('/') != -1)
            {
                var approverUserProfile = userService.GetSecurityProfile(approverAccountName.Split('/')[0], approverAccountName.Split('/')[1]);
                if (approverUserProfile != null)
                {
                    emails.Add(approverUserProfile.Email);
                }
            }
        }

        private List<string> GetCSCEmails(Requisition requisition)
        {
            var emails = new List<String>();

            var facilityNotification = configurationRepository.GetFacilityNotifications(LocationMapper.GetCOID(requisition.LocationIdentifier)).Where(x => x.FacilityNotificationTypeId == (int)FacilityNotificationTypeEnum.SPRApprovedEmail).FirstOrDefault();
            if (facilityNotification != null && !String.IsNullOrWhiteSpace(facilityNotification.EmailAddresses))
            {
                facilityNotification.EmailAddresses = Regex.Replace(facilityNotification.EmailAddresses, "\\s+", string.Empty);
                emails = facilityNotification.EmailAddresses.Split(';').ToList();
            }

            return emails;
        }

        private string GetRequisitionUrl(int requisitionId)
        {
            return eproEndpoint + "MyRequisition/" + requisitionId.ToString();
        }

        private string GetApprovalUrl(int requisitionId)
        {
            return eproEndpoint + "MyApproval/" + requisitionId.ToString();
        }

        private string GetAdhocReviewUrl(int requisitionId, int adhocReviewId)
        {
            return eproEndpoint + "MyRequisition/" + requisitionId.ToString() + "?AdHocReview=" + adhocReviewId.ToString();
        }

        public bool RequestAdhocReview(AdhocReviewDTO adhocReviewDTO)
        {
            var requisition = this.GetRequisition(adhocReviewDTO.Requester, adhocReviewDTO.RequisitionId);
            if (requisition == null)
            {
                throw new ArgumentException("Requisition not found. INVALID REQUEST");
            }

            if (requisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Submitted && requisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.PendingApproval && requisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.OnHold)
            {
                return false;
            }

            var adHocReview = new AdhocReview(adhocReviewDTO);
            var requestReviewStatusHistory = new RequisitionStatusHistory
            {
                Comments = $"Reviewer: {adhocReviewDTO.ReviewerName}",
                RequisitionId = adHocReview.RequisitionId,
                CreatedBy = adHocReview.Requester,
                CreateDate = DateTime.Now,
                RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.AdhocReviewRequested
            };

            adHocReview.CreateDate = DateTime.Now;
            adHocReview.RequesterRequisitionStatusHistory = requestReviewStatusHistory;
            adhocReviewRepository.InsertAdhocReview(adHocReview);

            var profile = userService.GetProfile(adhocReviewDTO.Reviewer);
            var emailRequest = new EmailRequest()
            {
                EmailType = EmailType.RequisitionReviewRequest,
                Emails = new string[1] { profile.Email },
                ViewLink = this.GetAdhocReviewUrl(adhocReviewDTO.RequisitionId, adHocReview.Id),
                SenderName = adhocReviewDTO.RequesterName,
                ActionDate = DateTime.Now,
                Comments = adhocReviewDTO.RequesterComments,
                Requisition = requisition
            };
            emailService.SendEmail(emailRequest);
            return true;
        }

        public bool ProvideAdhocReview(AdhocReviewDTO adhocReviewDTO)
        {
            var adHocReview = new AdhocReview(adhocReviewDTO);
            var requesterProfile = userService.GetProfile(adhocReviewRepository.GetRequester(adHocReview.Id));
            var reviewerProfile = userService.GetProfile(adhocReviewDTO.Reviewer);
            var reviewProvidedStatusHistory = new RequisitionStatusHistory
            {
                Comments = string.Format("Requester: {0}", requesterProfile.FullName),
                RequisitionId = adHocReview.RequisitionId,
                CreatedBy = adHocReview.Reviewer,
                CreateDate = DateTime.Now,
                RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.AdhocReviewProvided
            };

            adHocReview.ReviewerRequisitionStatusHistory = reviewProvidedStatusHistory;
            adHocReview.ReviewDate = DateTime.Now;
            adhocReviewRepository.UpdateAdhocReview(adHocReview);

            var requisition = this.GetRequisition(adhocReviewDTO.Reviewer, adhocReviewDTO.RequisitionId);
            if (requisition == null)
            {
                throw new ArgumentException("Requisition not found. INVALID REQUEST");
            }

            var emailRequest = new EmailRequest()
            {
                EmailType = EmailType.RequisitionReviewed,
                Emails = new string[1] { requesterProfile.Email },
                ViewLink = this.GetApprovalUrl(requisition.RequisitionId),
                SenderName = reviewerProfile.FullName,
                Requisition = requisition,
                Comments = adhocReviewDTO.ReviewerComments,
                Recommendation = (bool)adhocReviewDTO.Recommended,
                ActionDate = DateTime.Now
            };

            emailService.SendEmail(emailRequest);
            return true;
        }

        public IEnumerable<AdhocReviewDTO> GetRequisitionAdhocReviews(int requisitionId)
        {
            var adHocReviews = adhocReviewRepository.GetRequisitionAdhocReviews(requisitionId);

            var adHocReviewsDTOs = adHocReviews.Select(x => new AdhocReviewDTO(x));

            return adHocReviewsDTOs;
        }

        public bool IsAdhocReviewAllowed(int adhocReviewId, string reviewerName, int reqId)
        {
            return adhocReviewRepository.IsAdhocReviewAllowed(adhocReviewId, reviewerName, reqId);
        }

        public bool CheckIfSaveable(Requisition requisitionObject)
        {
            var originalRequisition = requisitionRepository.GetRequisition(requisitionObject.RequisitionId);
            if (originalRequisition != null && requisitionObject.ApprovalStep == originalRequisition.ApprovalStep && (originalRequisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Submitted && originalRequisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.Denied && originalRequisition.RequisitionStatusTypeId != (int)RequisitionStatusTypeEnum.SubmissionError))
            {
                return true;
            }
            return false;
        }

        public bool DeleteAttachment(FileNamesDTO files)
        {
            return requisitionRepository.DeleteAttachment(files.FileNames);
        }

        public UpdateRequisitionItemStatusResultDTO UpdateReqRequisitionItemStatus(ReqRequisitionItemStatusDTO reqItemStatusDTO)
        {
            var updateStatusDTO = new UpdateRequisitionItemStatusResultDTO();

            IEnumerable<RequisitionItem> reqItems = requisitionRepository.GetReqRequisitionItems(reqItemStatusDTO.Id, reqItemStatusDTO.COID, reqItemStatusDTO.ParentSystemId, reqItemStatusDTO.ItemId, true);

            if (reqItems.Count() == 0)
            {
                updateStatusDTO.ErrorMessages.Add(string.Format("MQ Message Error: REQ item Not found for MessageId: {0}", reqItemStatusDTO.MessageId)); ;
                return updateStatusDTO;
            }

            if (IsMessageAlreadyProcessed(reqItemStatusDTO, reqItems.First().RequisitionItemStatusHistories, UserName)) return updateStatusDTO;

            // Update Item and add to status history
            UpdateRequisitionItemAndHistory(reqItems, reqItemStatusDTO, UserName);

            return updateStatusDTO;
        }

        public UpdateRequisitionItemStatusResultDTO UpdateSprRequisitionItemStatus(SprRequisitionItemStatusDTO sprItemStatusDTO)
        {
            var updateStatusDTO = new UpdateRequisitionItemStatusResultDTO();

            RequisitionItem sprItem = requisitionRepository.GetSprRequisitionItem(sprItemStatusDTO.Id, sprItemStatusDTO.COID, sprItemStatusDTO.ParentSystemId, sprItemStatusDTO.VendorId, sprItemStatusDTO.PartNumber, true);

            if (sprItem == null)
            {
                updateStatusDTO.ErrorMessages.Add(string.Format("MQ Message Error: SPR item not found for MessageId: {0}", sprItemStatusDTO.MessageId));
                return updateStatusDTO;
            }

            if (IsMessageAlreadyProcessed(sprItemStatusDTO, sprItem.RequisitionItemStatusHistories, UserName)) return updateStatusDTO;

            // Update Item and add to status history
            UpdateRequisitionItemAndHistory(new[] { sprItem }, sprItemStatusDTO, UserName);

            return updateStatusDTO;
        }

        public void UpdateRequisitionStatusToSubmittedIfNeeded(BaseRequisitionItemStatusDTO messageFromMQ)
        {
            // IMPORTANT: To be used for MQ Message updating ONLY!
            if (messageFromMQ.Id != null && messageFromMQ.Id != 0)
            {
                requisitionRepository.UpdateRequisitionStatusToSubmittedIfNeeded(messageFromMQ.MessageId, (int)messageFromMQ.Id, true);
            }
        }

        private void UpdateRequisitionItemAndHistory(IEnumerable<RequisitionItem> reqItems, BaseRequisitionItemStatusDTO itemStatusDTO, string userName)
        {
            if (!IsMMISMessageLatest(itemStatusDTO, reqItems.First().RequisitionItemStatusHistories) && !MustUpdateRequisitionItem(itemStatusDTO, reqItems.First()))
            {// just save to status history because it is not the latest
                this.AddRequisitionItemStatusHistories(reqItems, itemStatusDTO, userName);
                return;
            }
            else if (!IsMMISMessageLatest(itemStatusDTO, reqItems.First().RequisitionItemStatusHistories) && MustUpdateRequisitionItem(itemStatusDTO, reqItems.First()))
            {
                foreach (var reqItem in reqItems)
                {
                    itemStatusDTO.UpdateRequisitionNumber(reqItem);
                }
                UpdateRequisitionItemStatusHistory(reqItems, itemStatusDTO, userName);
                this.requisitionRepository.UpdateRequisitionItemAndAddHistory(reqItems);
                return;
            }

            foreach (var reqItem in reqItems)
            {
                itemStatusDTO.UpdateRequisitionItemStatus(reqItem);
            }

            UpdateRequisitionItemStatusHistory(reqItems, itemStatusDTO, userName);
            this.requisitionRepository.UpdateRequisitionItemAndAddHistory(reqItems);
        }

        private static bool MustUpdateRequisitionItem(BaseRequisitionItemStatusDTO itemStatusDTO, RequisitionItem reqItem)
        {
            return string.IsNullOrEmpty(reqItem.ParentSystemId) && !string.IsNullOrEmpty(itemStatusDTO.ParentSystemId);
        }

        private static bool IsMessageAlreadyProcessed(BaseRequisitionItemStatusDTO itemStatusDTO, IEnumerable<RequisitionItemStatusHistory> statusHistory, string userName)
        {
            return statusHistory.Any(x => x.CreatedBy == userName && !string.IsNullOrEmpty(x.MessageId) && string.Compare(x.MessageId, itemStatusDTO.MessageId, true) == 0);
        }

        private static bool IsMMISMessageLatest(BaseRequisitionItemStatusDTO itemStatusDTO, IEnumerable<RequisitionItemStatusHistory> statusHistory)
        {
            statusHistory = statusHistory.Where(x => !string.IsNullOrEmpty(x.MessageId)).OrderByDescending(x => x.CreateDate);
            if (statusHistory.Count() == 0) return true;
            RequisitionItemStatusHistory itemHistory = statusHistory.First();
            if (itemHistory.CreateDate < itemStatusDTO.GetCreateDate())
            {
                return true;
            }
            return false;
        }

        private static void UpdateRequisitionItemStatusHistory(IEnumerable<RequisitionItem> reqItems, BaseRequisitionItemStatusDTO itemStatusDTO, string userName)
        {
            foreach (var reqItem in reqItems)
            {
                RequisitionItemStatusHistory itemStatusHistory = GetRequisitionItemStatusHistory(reqItem, itemStatusDTO, userName);
                reqItem.RequisitionItemStatusHistories.Add(itemStatusHistory);
            }
        }

        private void AddRequisitionItemStatusHistories(IEnumerable<RequisitionItem> reqItems, BaseRequisitionItemStatusDTO itemStatusDTO, string userName)
        {
            var itemStatusHistories = new List<RequisitionItemStatusHistory>();
            foreach (var reqItem in reqItems)
            {
                itemStatusHistories.Add(GetRequisitionItemStatusHistory(reqItem, itemStatusDTO, userName));
            }
            requisitionStatusRepository.AddItemStatusHistories(itemStatusHistories);
        }

        private static RequisitionItemStatusHistory GetRequisitionItemStatusHistory(RequisitionItem reqItem, BaseRequisitionItemStatusDTO itemStatusDTO, string userName)
        {
            var itemStatusHistory = new RequisitionItemStatusHistory
            {
                RequisitionItemId = reqItem.Id,
                Comments = itemStatusDTO.GetRequisitionItemStatusComments(),
                CreateDate = itemStatusDTO.GetCreateDate(),
                CreatedBy = userName,
                RequisitionItemStatusTypeId = (int)itemStatusDTO.GetRequisitionItemStatusType(),
                MessageId = itemStatusDTO.MessageId
            };
            return itemStatusHistory;
        }

        protected void InsertCatalogNumberForSPRItems(ref List<RequisitionItem> reqItems, List<Item> items)
        {
            reqItems.ForEach(x =>
            {
                items.ForEach(y =>
                {
                    if ((x.ItemId == Convert.ToString(y.Id)))
                    {
                        x.CatalogNumber = y.ManufacturerCatalogNumber;
                    }
                });
            });

        }

        protected Requisition ValidateAndReadyRequisitionBeforeSave(Requisition requisition, string userName)
        {
            if (requisition == null)
            {
                throw new ArgumentException("Invalid requisition provided");
            }

            //Clear out non-ASCII chars
            if (!String.IsNullOrWhiteSpace(requisition.Comments))
            {
                requisition.Comments = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(requisition.Comments));
            }

            if (requisition.RequisitionItems != null)
            {
                //We now only remove zero quantity items when we submit, except for capitated.  All Capitated items are reloaded when opening
                //an editable requisition, so we don't want to save all of them to the database.
                if (requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillOnly || requisition.RequisitionTypeId == (int)RequisitionTypeEnum.CapitatedBillAndReplace)
                {
                    requisition.RequisitionItems = this.RemoveZeroQuantityLineItems(requisition.RequisitionItems);
                }

                var count = 0;
                foreach (var requisitionItem in requisition.RequisitionItems)
                {
                    if (requisitionItem.ClinicalUseDetails != null)
                    {
                        foreach (var clinicalUseDetail in requisitionItem.ClinicalUseDetails)
                        {
                            //Clear out non-ASCII chars
                            if (!String.IsNullOrWhiteSpace(clinicalUseDetail.LotNumber))
                            {
                                clinicalUseDetail.LotNumber = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(clinicalUseDetail.LotNumber));
                            }
                            if (!String.IsNullOrWhiteSpace(clinicalUseDetail.SerialNumber))
                            {
                                clinicalUseDetail.SerialNumber = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(clinicalUseDetail.SerialNumber));
                            }
                            //Clear out non-ASCII chars
                            if (!String.IsNullOrWhiteSpace(clinicalUseDetail.PatientId))
                            {
                                clinicalUseDetail.PatientId = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(clinicalUseDetail.PatientId));
                            }
                            if (!String.IsNullOrWhiteSpace(clinicalUseDetail.PatientName))
                            {
                                clinicalUseDetail.PatientName = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(clinicalUseDetail.PatientName));
                            }
                            if (!String.IsNullOrWhiteSpace(clinicalUseDetail.Provider))
                            {
                                clinicalUseDetail.Provider = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(clinicalUseDetail.Provider));
                            }
                        }
                    }

                    if (requisitionItem.SPRDetail != null)
                    {

                        if (!String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.ItemDescription))
                        {
                            requisitionItem.SPRDetail.ItemDescription = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(requisitionItem.SPRDetail.ItemDescription.ToUpper()));
                        }

                        if (!String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.PartNumber))
                        {
                            requisitionItem.SPRDetail.PartNumber = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(requisitionItem.SPRDetail.PartNumber));
                        }

                        if (!String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.AdditionalInformation))
                        {
                            requisitionItem.SPRDetail.AdditionalInformation = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(requisitionItem.SPRDetail.AdditionalInformation));
                        }

                        if (!String.IsNullOrWhiteSpace(requisitionItem.SPRDetail.BudgetNumber))
                        {
                            requisitionItem.SPRDetail.BudgetNumber = Encoding.ASCII.GetString(Encoding.ASCII.GetBytes(requisitionItem.SPRDetail.BudgetNumber));
                        }
                    }

                    //Add user/date to items
                    if (requisitionItem.Id == 0)
                    {
                        requisitionItem.CreateDate = DateTime.Now.AddSeconds(count);
                        count++;
                        requisitionItem.CreatedBy = userName;
                    }
                }

                // Mark SPR items as Hold status
                if (requisition.IsVendor && requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.PendingApproval)
                {
                    if (requisition.RequisitionItems.Any(x => x.RequisitionItemStatusTypeId == (int)RequisitionItemStatusTypeEnum.Hold))
                    {
                        requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.OnHold;
                    }
                }
                else if (requisition.IsVendor && requisition.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.OnHold)
                {
                    if (requisition.RequisitionItems.All(x => x.RequisitionItemStatusTypeId != (int)RequisitionItemStatusTypeEnum.Hold))
                    {
                        requisition.RequisitionStatusTypeId = (int)RequisitionStatusTypeEnum.PendingApproval;
                    }
                }
            }
            return requisition;
        }

        protected Requisition RepopulateTypeTables(Requisition requisition)
        {
            if (requisition.RequisitionItems != null)
            {
                foreach (var requisitionItem in requisition.RequisitionItems.Where(x => x.SPRDetail != null))
                {
                    requisitionItem.SPRDetail.DeliveryMethodType = typeRepository.GetDeliveryMethod(requisitionItem.SPRDetail.DeliveryMethodTypeId);
                }
            }
            return requisition;
        }

        private List<RequisitionItem> RemoveZeroQuantityLineItems(IEnumerable<RequisitionItem> reqItems)
        {
            var reqItemList = reqItems.ToList();
            reqItemList.RemoveAll(x => x.QuantityToOrder == 0);
            return reqItemList;
        }

        public InFlightQty GetInFlightQuantity(string userName, string coid, int dept, string parClass, string itemId)
        {
            return smartRequisitionService.GetInFlightQuantity(userName, coid, dept, parClass, itemId);
        }

        public LegacyRequisitionReportDTO GetLegacyRequisitionReport(LegacyRequisitionReportRequestDTO request)
        {
            return smartRequisitionInquiryService.GetRequisitionCollectionByCreateDate(request);
        }

        public LastOrderDetailsDTO GetLastOrderDetails(string coid, string dept, string parClass, string itemId)
        {
            return requisitionRepository.GetLastOrderDetails(coid, dept, parClass, itemId);
        }

        private RequisitionItem CreateNewSubReqItem(RequisitionItemWithSubItem smartItemMatch, RequisitionItem smartItem)
        {
            //make sure we have as much information as possible when creating the new subitem record.
            //Necessary because not all item information is retrieved from the db for Reports
            var originalItem = requisitionRepository.GetRequisitionItemById(smartItemMatch.originalRequisitionItem.Id);
            return new RequisitionItem()
            {
                RequisitionId = originalItem.RequisitionId,
                ItemId = smartItem.ItemId,
                RequisitionerName = smartItemMatch.originalRequisitionItem.RequisitionerName,
                ParIdentifier = smartItem.ParIdentifier,
                MainItemId = null,
                RequisitionItemStatusTypeId = smartItem.RequisitionItemStatusTypeId,
                QuantityToOrder = smartItem.QuantityToOrder,
                QuantityFulfilled = smartItem.QuantityFulfilled,
                PONumber = smartItem.PONumber,
                IsRushOrder = originalItem.IsRushOrder,
                ParentSystemId = smartItem.ParentSystemId,
                OriginalParentSystemId = smartItem.OriginalParentSystemId,
                CreatedBy = originalItem.CreatedBy,
                CreateDate = DateTime.Now,
                RequisitionScheduledDate = smartItem.RequisitionScheduledDate,
                IsFileItem = true,
                FileItemHasChanged = false,
                SmartItemNumber = null, //leave null, this is only for routing purposes and it's already been submitted
                StockIndicator = smartItem.StockIndicator, // not a clue
                ParentRequisitionItemId = originalItem.Id,
            };
        }

        public SmartItemRequisitionIdDTO GetSmartItemRequisitionIdByPONumber(SmartItemRequisitionIdDTO smartItemRequisitionIdDTO)
        {
            return requisitionRepository.GetSmartItemRequisitionIdByPONumber(smartItemRequisitionIdDTO);
        }

        private bool isRequisitionAvailableForVendorUser(Requisition requisition, List<int> vendorAffiliations, string username)
        {
            if (string.Equals(requisition?.CreatedBy, username, StringComparison.InvariantCultureIgnoreCase))
            {
                return true;
            }

            if (requisition == null || !requisition.IsVendor || requisition.RequisitionItems?.Count < 1 || vendorAffiliations?.Count < 1)
            {
                return false;
            }

            return requisition.RequisitionItems.Select(x => x.SPRDetail?.VendorId ?? x.VendorId).All(vendorAffiliations.Contains);
        }

        private void RequisitionDigitalSignOffSoftDelete(RequisitionStatusHistory requisitionStatusHistory)
        {
            List<RequisitionDigitalSignOff> associatedDigitalSignoffs = new List<RequisitionDigitalSignOff>();
            {
                associatedDigitalSignoffs.AddRange(digitalSignOffRepository.GetAllRequisitionDigitalSignOffsByRequisitionId(requisitionStatusHistory.RequisitionId));
            };
            foreach (RequisitionDigitalSignOff associatedDigitalSignoff in associatedDigitalSignoffs)
            {
                if (requisitionStatusHistory.RequisitionStatusTypeId == (int)RequisitionStatusTypeEnum.Recalled)
                {
                    digitalSignOffRepository.DeleteRequisitionDigitalSignOffOnRecall(associatedDigitalSignoff.Id, requisitionStatusHistory.RequisitionId);
                }
                else
                {
                    digitalSignOffRepository.DeleteRequisitionDigitalSignOff(associatedDigitalSignoff.Id);
                }
            }
        }

        private RequisitionReportExportResultsDTO HydrateRequisitionsWithDetails(RequisitionReportExportResults dbResults, RequisitionReportRequestDto request)
        {
            var hydratedRequisitions = HydrateRequisitionsWithParentSystemDetails(request.Username, dbResults.Requisitions);

            var dtos = hydratedRequisitions.Select(req => new RequisitionForExportDTO(req)).ToList();

            return new RequisitionReportExportResultsDTO(dtos);
        }

        public PurchasingRequisitionReportResultsDTO GetRequisitionsForPurchasingReport(PurchasingRequisitionReportParameters reportParameters)
        {
            var dbResults = requisitionRepository.GetRequisitionsForPurchasingReport(reportParameters);

            return dbResults;
        }

        /// <summary>
        /// GetAdvancedFiltersForPurchasingReport gets values for Purchasing Advanced Filters
        /// </summary>
        /// <param name="filterList"></param>
        /// <returns>multiple lists of values</returns>
        public RequisitionPurchasingAdvancedFiltersDto GetAdvancedFiltersForPurchasingReport(RequisitionPurchasingAdvancedFilterRequest filterList)
        {
            return requisitionRepository.GetAdvancedFiltersForPurchasingReport(filterList);
        }

        public List<RequisitionDTO> GetRequisitionAndItemsByPONumber(int poNumber, string coid)
        {
            return requisitionRepository.GetRequisitionAndItemsByPONumber(poNumber, coid);
        }
    }
}
