﻿using log4net;
using Newtonsoft.Json;
using RequisitionServices.DomainModel.Requisitions;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    /// <summary>
    /// Smart Requisition Service
    /// </summary>
    public class SmartRequisitionService : ISmartRequisitionService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        /// <summary>
        /// Private : smart Vendor Service
        /// </summary>
        private ISmartVendorService smartVendorService;

        /// <summary>
        /// Private : smart COID Service
        /// </summary>
        private ISmartCOIDService smartCOIDService;

        /// <summary>
        /// Private : smart Par Item Service
        /// </summary>
        private ISmartParItemsService smartParItemService;

        private IRequisitionRepository requisitionRepository;

        /// <summary>
        /// Private : get Reqs By ReqIds Method
        /// </summary>
        private const string getReqsByReqIdsMethod = "Requisitions/GetRequisitionStatusCollection/";

        /// <summary>
        /// Private : get Req By ReqId Method
        /// </summary>
        private const string getReqByReqIdMethod = "Requisitions/GetRequisitionStatusByRequisitionId/";

        /// <summary>
        /// Private : post Req Method
        /// </summary>
        private const string postReqMethod = "Requisitions/SubmitRequisition/";

        /// <summary>
        /// Private : post Bill Only Req Method
        /// </summary>
        private const string postBillOnlyReqMethod = "Requisitions/SubmitBillOnlyRequisition/";

        /// <summary>
        /// Private : post Capitated Method
        /// </summary>
        private const string postCapitatedMethod = "Requisitions/SubmitCapitatedRequisition/";

        /// <summary>
        /// Private : post SPRs By ReqId Method
        /// </summary>
        private const string postSPRsByReqIdMethod = "SPRs/GetSPRStatusCollection/";

        private const string getInFlightQty = "Requisitions/GetInFlightQuantity/";
        private const string parentSystemSubmissionErrorId = "0";

        /// <summary>
        /// Private : endpoint
        /// </summary>
        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        /// <summary>
        /// Smart Requisition Service
        /// </summary>
        /// <param name="smartVendorSvc"></param>
        /// <param name="smartCOIDSvc"></param>
        public SmartRequisitionService(ISmartVendorService smartVendorSvc, ISmartCOIDService smartCOIDSvc, ISmartParItemsService smartParItemSvc, IRequisitionRepository requisitionRepo)
        {
            smartVendorService = smartVendorSvc;
            smartCOIDService = smartCOIDSvc;
            smartParItemService = smartParItemSvc;
            requisitionRepository = requisitionRepo;
        }

        /// <summary>
        /// Get SPRs
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="coid"></param>
        /// <param name="externalReqIds"></param>
        /// <returns></returns>
        public List<Requisition> GetSPRs(string userName, string coid, List<string> externalReqIds)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            var sPRStatusCollectionModel = ApiUtility.ExecuteApiPostWithContentTo<SPRStatusCollectionModel>(endpoint, postSPRsByReqIdMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid.ToString() }
                                                                            }, externalReqIds);

            var requisitions = new List<Requisition>();
            if (sPRStatusCollectionModel != null && sPRStatusCollectionModel.RequisitionRecordModels != null)
            {
                foreach (var sPRStatusModel in sPRStatusCollectionModel.RequisitionRecordModels)
                {
                    requisitions.Add(sPRStatusModel.MapToRequisition());
                }
            }

            return requisitions;
        }

        /// <summary>
        /// Get Requisitions
        /// </summary>
        /// <param name="userName"></param>
        /// <param name="coid"></param>
        /// <param name="externalReqIds"></param>
        /// <returns></returns>
        public List<Requisition> GetRequisitions(string userName, string coid, List<int> externalReqIds)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }

            var requisitionRecordsModel = ApiUtility.ExecuteApiPostWithContentTo<RequisitionRecordsModel>(endpoint, getReqsByReqIdsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid.ToString() }
                                                                            }, externalReqIds);
            var requisitions = new List<Requisition>();
            if (requisitionRecordsModel != null && requisitionRecordsModel.RequisitionRecordModels != null)
            {
                foreach (var requisitionRecordModel in requisitionRecordsModel.RequisitionRecordModels)
                {
                    requisitions.Add(requisitionRecordModel.MapToRequisition());
                }
            }

            return requisitions;
        }

        /// <summary>
        /// Submit Requisition to SmartApi
        /// </summary>
        /// <param name="requisitionerFullName"></param>
        /// <param name="userName"></param>
        /// <param name="coid"></param>
        /// <param name="requisition"></param>
        /// <returns></returns>
        public Requisition SubmitRequisition(string requisitionerFullName, string userName, string coid, Requisition requisition)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }
            int intCOID;
            if (!Int32.TryParse(coid, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", coid));
            }

            //Get Vendor and shipping info for SMART, from SMART...yeah, I know...how DUMB is that)
            if (requisition != null && requisition.RequisitionItems != null)
            {
                var vendorIds = new List<int>();
                var shipToCodes = new List<int>();
                var isWasteParRequisition = requisition.RequisitionItems.Any(x => x.IsWastePar);
                foreach (var sprItem in requisition.RequisitionItems.Where(x => x.SPRDetail != null))
                {
                    if (!vendorIds.Contains(sprItem.SPRDetail.VendorId))
                    {
                        vendorIds.Add(sprItem.SPRDetail.VendorId);
                    }

                    if (sprItem.SPRDetail.ShipToAddressId != null && !vendorIds.Contains((int)sprItem.SPRDetail.ShipToAddressId))
                    {
                        shipToCodes.Add((int)sprItem.SPRDetail.ShipToAddressId);
                    }
                }

                if (vendorIds.Any())
                {
                    foreach (var vendorId in vendorIds)
                    {
                        var vendor = smartVendorService.GetVendor(userName, coid, vendorId);
                        if (vendor != null)
                        {
                            foreach (var sprItem in requisition.RequisitionItems.Where(x => x.SPRDetail != null && x.SPRDetail.VendorId == vendorId))
                            {
                                sprItem.SPRDetail.Vendor = vendor;
                            }
                        }
                    }
                }

                if (shipToCodes.Any() || isWasteParRequisition)
                {
                    var addresses = smartCOIDService.GetAddresses(userName, coid);
                    if (addresses != null)
                    {
                        foreach (var sprItem in requisition.RequisitionItems.Where(x => x.SPRDetail != null))
                        {
                            var address = addresses.Where(x => x.ExternalSystemId == sprItem.SPRDetail.ShipToAddressId).FirstOrDefault();
                            if (address != null)
                            {
                                sprItem.SPRDetail.ShipToAddress = address;
                            }
                        }
                        
                    }
                }
            }

            switch (requisition.RequisitionTypeId)
            {
                case 1:
                case 2:
                    return this.SubmitBillOnlyRequisition(requisition, coid, userName, requisitionerFullName);

                case 3:
                case 4:
                    return this.SubmitCapitatedRequisition(requisition, coid, userName, requisitionerFullName);

                default:
                    return this.SubmitNormalRequisition(requisition, coid, userName, requisitionerFullName);
            }
        }

        /// <summary>
        /// Submit Capitated Requisition to SmartApi
        /// </summary>
        /// <param name="requisition"></param>
        /// <param name="coid"></param>
        /// <param name="userName"></param>
        /// <param name="requisitionerFullName"></param>
        /// <returns></returns>
        private Requisition SubmitCapitatedRequisition(Requisition requisition, string coid, string userName, string requisitionerFullName)
        {
            var capitatedReqModels = new List<CapitatedRequisitionModel>();

            foreach (var capModel in capitatedReqModels)
                capModel.RequisitionItems.ForEach(ri => ri.RequisitionerName = requisitionerFullName);

            //Split up requisition by main item
            if (requisition != null && requisition.RequisitionItems != null)
            {
                //for each main item
                foreach (var requisitionItem in requisition.RequisitionItems.Where(x => x.MainItemId == null))
                {
                    var reqItemsToCap = requisition.RequisitionItems.Where(x => x.MainItemId == Int32.Parse(requisitionItem.ItemId) || x.ItemId == requisitionItem.ItemId);
                    capitatedReqModels.Add(new CapitatedRequisitionModel(requisition, reqItemsToCap));
                }
            }

            foreach (var capitatedReqModel in capitatedReqModels)
            {
                var capitatedRequisitionResponseModel = ApiUtility.ExecuteApiPostWithContentTo<CapitatedRequisitionResponseModel>(endpoint, postCapitatedMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                {"fullName", requisitionerFullName }
                                                                            }, capitatedReqModel);

                if (capitatedRequisitionResponseModel != null && capitatedRequisitionResponseModel.RequisitionItems != null)
                {
                    foreach (var requisitionResponseModel in capitatedRequisitionResponseModel.RequisitionItems)
                    {
                        if (requisitionResponseModel.Id > 0)
                        {
                            var foundReqItem = requisition.RequisitionItems.Where(x => (x.Id == requisitionResponseModel.Id
                                                                                        && (x.MainItemId == null || x.MainItemId == requisitionResponseModel.ItemNumber))
                                                                                    && x.ParIdentifier == requisitionResponseModel.ParClass).FirstOrDefault();
                            if (foundReqItem != null)
                            {
                                foundReqItem.ParentSystemId = requisitionResponseModel.SmartRequisitionId.ToString();
                                foundReqItem.PONumber = requisitionResponseModel.PurchaseOrderNumber;
                            }
                        }
                        else
                        {
                            var foundReqItem = requisition.RequisitionItems.Where(x => (x.ItemId == requisitionResponseModel.ComponentItemNumber.ToString()
                                                                                        && (x.MainItemId == null || x.MainItemId == requisitionResponseModel.ItemNumber))
                                                                                    && x.ParIdentifier == requisitionResponseModel.ParClass).FirstOrDefault();
                            if (foundReqItem != null)
                            {
                                foundReqItem.ParentSystemId = requisitionResponseModel.SmartRequisitionId.ToString();
                                foundReqItem.PONumber = requisitionResponseModel.PurchaseOrderNumber;
                            }
                        }
                    }
                }
            }

            return requisition;
        }

        /// <summary>
        /// Submit Bill Only Requisition to SmartApi
        /// </summary>
        /// <param name="requisition"></param>
        /// <param name="coid"></param>
        /// <param name="userName"></param>
        /// <param name="requisitionerFullName"></param>
        /// <returns></returns>
        private Requisition SubmitBillOnlyRequisition(Requisition requisition, string coid, string userName, string requisitionerFullName)
        {
            BillOnlyRequisitionModel billOnlyRequisitionModel;

            billOnlyRequisitionModel = SmartAPIReqMapper.MapToBillOnlyRequisitionModel(requisition, requisitionerFullName);
            var billOnlyRequisitionResponseModel = ApiUtility.ExecuteApiPostWithContentTo<BillOnlyRequisitionResponseModel>(endpoint, postBillOnlyReqMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            }, billOnlyRequisitionModel);

            if (billOnlyRequisitionResponseModel != null && billOnlyRequisitionResponseModel.RequisitionItems != null)
            {
                foreach (var requisitionResponseModel in billOnlyRequisitionResponseModel.RequisitionItems)
                {
                    if (requisitionResponseModel.Id > 0)
                    {
                        var foundReqItem = requisition.RequisitionItems.FirstOrDefault(x => x.Id == requisitionResponseModel.Id);
                        if (foundReqItem != null)
                        {
                            foundReqItem.ParentSystemId = requisitionResponseModel.SmartRequisitionId.ToString();
                            foundReqItem.PONumber = requisitionResponseModel.PurchaseOrderNumber;
                        }
                    }
                    else
                    {
                        var foundReqItem = requisition.RequisitionItems.Where(x => x.ItemId == requisitionResponseModel.ItemNumber.ToString() && x.ParIdentifier == requisitionResponseModel.ParClass).FirstOrDefault();
                        if (foundReqItem != null)
                        {
                            foundReqItem.ParentSystemId = requisitionResponseModel.SmartRequisitionId.ToString();
                            foundReqItem.PONumber = requisitionResponseModel.PurchaseOrderNumber;
                        }
                        else
                        {
                            //Try to match by reorder number (NOTE: Need to update Item number to new TEMP item number
                            var sprFoundReqItems = requisition.RequisitionItems.Where(x => x.SPRDetail != null && x.SPRDetail.PartNumber == requisitionResponseModel.ReOrderNumber).ToList();
                            if (sprFoundReqItems != null)
                            {
                                foreach (var sprFoundReqItem in sprFoundReqItems)
                                {
                                    sprFoundReqItem.ParentSystemId = requisitionResponseModel.SmartRequisitionId.ToString();
                                    sprFoundReqItem.PONumber = requisitionResponseModel.PurchaseOrderNumber;
                                    sprFoundReqItem.ItemId = requisitionResponseModel.ItemNumber != 0 ? requisitionResponseModel.ItemNumber.ToString() : null;
                                }
                            }
                        }
                    }
                }

                if (requisition.IsVendor)
                {
                    if (requisition.RequisitionItems.Count > billOnlyRequisitionModel.RequisitionItems.Count)
                    {
                        foreach (var billOnlyRequestItem in billOnlyRequisitionModel.RequisitionItems)
                        {
                            var reqItem = requisition.RequisitionItems.First(x => x.Id == billOnlyRequestItem.Id);
                            var duplicateItems = requisition.RequisitionItems.Where(x => x.DuplicateItemId == reqItem.Id).ToList();

                            // if item failed to submit, we assign it Submission Error status later
                            if (reqItem.ParentSystemId == parentSystemSubmissionErrorId || reqItem.ParentSystemId == null)
                            {
                                // and we do not delete the duplicate items
                                duplicateItems.ForEach(x => x.DuplicateItemId = null);
                            }
                            else // item submitted successfully
                            {
                                // assign the QOH and Discount accepted by Legacy
                                reqItem.QuantityToOrder = billOnlyRequestItem.ItemQuantity;
                                reqItem.Discount = billOnlyRequestItem.Discount;

                                if (reqItem.ClinicalUseDetails.Count < 10)
                                {
                                    foreach (var duplicateItem in duplicateItems)
                                    {
                                        if (duplicateItem.ClinicalUseDetails != null && duplicateItem.ClinicalUseDetails.Any())
                                        {
                                            foreach (var clinicalUseDetail in duplicateItem.ClinicalUseDetails)
                                            {
                                                if (!string.IsNullOrWhiteSpace(clinicalUseDetail.LotNumber) || !string.IsNullOrWhiteSpace(clinicalUseDetail.SerialNumber)) {
                                                    var emptyClinicalUseDetails = reqItem.ClinicalUseDetails.Where(x => string.IsNullOrWhiteSpace(x.LotNumber) && string.IsNullOrWhiteSpace(x.SerialNumber)).ToList();
                                                    if (emptyClinicalUseDetails.Any())
                                                    {
                                                        requisitionRepository.DeleteClinicalUseDetails(emptyClinicalUseDetails);
                                                    }

                                                    reqItem.ClinicalUseDetails.Add(clinicalUseDetail);

                                                    clinicalUseDetail.RequisitionItemId = reqItem.Id;
                                                    clinicalUseDetail.RequisitionItem = reqItem;
                                                }
                                                if (reqItem.ClinicalUseDetails.Count == 10) break;
                                            }
                                            duplicateItem.ClinicalUseDetails = duplicateItem.ClinicalUseDetails.Where(x => x.RequisitionItemId == duplicateItem.Id).ToList();
                                        }

                                        if (reqItem.ClinicalUseDetails.Count == 10) break;
                                    }
                                }
                                // and we remove duplicate items
                                requisitionRepository.DeleteRequisitionItems(duplicateItems);
                            }
                        }

                        requisition.RequisitionItems = requisition.RequisitionItems.Where(x => x.DuplicateItemId == null).ToList();
                    }

                    if (requisition.RequisitionItems.Any(x => x.VboHoldItemConversion != null))
                    {
                        foreach (var reqItem in requisition.RequisitionItems.Where(x => x.VboHoldItemConversion != null))
                        {
                            var itemDetails = reqItem.VboHoldItemConversion.ItemDetails;
                            if (itemDetails != null && itemDetails.Item != null)
                            {
                                reqItem.IsFileItem = true;
                                reqItem.FileItemHasChanged = false;
                                reqItem.ReOrder = itemDetails.Item.ReorderNumber.ToUpper();
                                reqItem.ParIdentifier = itemDetails.ParId;
                                reqItem.ItemId = itemDetails.Item.Id;
                                reqItem.PARLocation = itemDetails.Location.ToUpper();
                                reqItem.ItemDescription = itemDetails.Item.Description.ToUpper();
                                reqItem.VendorId = itemDetails.Item.Vendor.Id;
                                reqItem.VendorName = itemDetails.Item.Vendor.Name.ToUpper();
                                reqItem.GeneralLedgerCode = itemDetails.GLAccount.ToString();
                                reqItem.StockIndicator = itemDetails.Item.IsStock;
                                reqItem.UOMCode = itemDetails.IssueUOM.ToUpper();
                                reqItem.UnitCost = itemDetails.ParPrice;
                                reqItem.TotalCost = reqItem.QuantityToOrder * reqItem.UnitCost;
                                reqItem.MinStock = itemDetails.MinStock;
                                reqItem.MaxStock = itemDetails.MaxStock;
                                reqItem.CatalogNumber = itemDetails.Item.ManufacturerCatalogNumber;

                                reqItem.SPRDetail = null;
                            }

                            reqItem.VboHoldItemConversion = null;
                        }
                    }
                }
            }

            return requisition;
        }
        
        /// <summary>
        /// Submit Normal Requisition to SMART Legacy API
        /// </summary>
        /// <param name="requisition"></param>
        /// <param name="coid"></param>
        /// <param name="userName"></param>
        /// <param name="requisitionerFullName"></param>
        /// <returns></returns>
        private Requisition SubmitNormalRequisition(Requisition requisition, string coid, string userName, string requisitionerFullName)
        {
            var requisitionModel = new RequisitionModel(requisition);

            if (requisition.RequisitionItems.Any(ri => ri.SPRDetail != null || ri.Discount != null))
            {
                requisitionModel.RequisitionItems.ForEach(ri => ri.RequisitionerName = requisitionerFullName);
            }
            
            //Change Shipping information to SPR Item for Waste Par Item
            if(requisition.RequisitionItems.Any(x=>x.SPRDetail != null && x.Discount != null))
            {
                var sprReqItem = requisition.RequisitionItems.FirstOrDefault(x => x.SPRDetail != null);
                foreach (var item in requisitionModel.RequisitionItems.Where(x=>x.ReorderNumber != null && x.ShipToCode == 0))
                {
                    item.ShipToAddress = sprReqItem.SPRDetail.ShipToAddress.Address1;
                    item.ShipToCode = sprReqItem.SPRDetail.ShipToAddressId ?? 0;
                    item.ShipToFacilityName = sprReqItem.SPRDetail.ShipToAddress != null ? sprReqItem.SPRDetail.ShipToAddress.AddressName : null;
                    item.ShipToCity = sprReqItem.SPRDetail.ShipToAddress != null ? sprReqItem.SPRDetail.ShipToAddress.City : null;
                    item.ShipToState = sprReqItem.SPRDetail.ShipToAddress != null ? sprReqItem.SPRDetail.ShipToAddress.State : null;
                    item.ShipToZip = sprReqItem.SPRDetail.ShipToAddress != null ? sprReqItem.SPRDetail.ShipToAddress.Zip : null;
                    item.ShippingInstructions = sprReqItem.SPRDetail.DeliveryMethodTypeId != 0 && sprReqItem.SPRDetail.DeliveryMethodType != null ? sprReqItem.SPRDetail.DeliveryMethodType.Description : null;
                }
            }
            else if (requisition.RequisitionItems.Any(x => x.Discount != null))
            {
                var addresses = smartCOIDService.GetAddresses(userName, coid).FirstOrDefault();
                foreach (var item in requisitionModel.RequisitionItems.Where(x => x.ReorderNumber != null && x.ShipToCode == 0))
                {
                    item.ShipToAddress = addresses.Address1;
                    item.ShipToCode = addresses.ExternalSystemId ?? 0;
                    item.ShipToFacilityName = addresses.AddressName;
                    item.ShipToCity = addresses.City;
                    item.ShipToState = addresses.State;
                    item.ShipToZip = addresses.Zip;
                }
            }
            
            log.Info(string.Format("Requisition  input data for smart service for  request data: {0}", JsonConvert.SerializeObject(requisitionModel, Formatting.Indented)));
            var requisitionResponseModels = ApiUtility.ExecuteApiPostWithContentTo<List<RequisitionResponseModel>>(endpoint, postReqMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            }, requisitionModel);

            if (requisitionResponseModels != null)
            {
                foreach (var requisitionResponseModel in requisitionResponseModels)
                {
                    if (requisitionResponseModel.Id > 0)
                    {
                        if (requisitionResponseModel.ItemNumber != 0)
                        {
                            var foundReqItem = requisition.RequisitionItems.Where(x => x.Id == requisitionResponseModel.Id && x.ParIdentifier == requisitionResponseModel.ParClass).FirstOrDefault();
                            if (foundReqItem != null)
                            {
                                foundReqItem.ParentSystemId = requisitionResponseModel.RequisitionNumber.ToString();
                            }
                        }
                        else
                        {
                            //SPR tie
                            var foundReqItems = requisition.RequisitionItems.Where(x => (x.SPRDetail != null || x.Discount != null) && x.Id == requisitionResponseModel.Id).ToList
                                ();
                            if (foundReqItems != null)
                            {
                                foreach (var foundReqItem in foundReqItems)
                                {
                                    foundReqItem.ParentSystemId = requisitionResponseModel.SPRNumber;
                                }
                            }
                        }
                    }
                    else
                    {
                        if (requisitionResponseModel.ItemNumber != 0)
                        {
                            var foundReqItem = requisition.RequisitionItems.Where(x => x.ItemId == requisitionResponseModel.ItemNumber.ToString() && x.ParIdentifier == requisitionResponseModel.ParClass).FirstOrDefault();
                            if (foundReqItem != null)
                            {
                                foundReqItem.ParentSystemId = requisitionResponseModel.RequisitionNumber.ToString();
                            }
                        }
                        else
                        {
                            //SPR tie
                            var foundReqItems = requisition.RequisitionItems.Where(x => x.SPRDetail != null && x.SPRDetail.PartNumber == requisitionResponseModel.ReOrder).ToList
                                ();
                            if (foundReqItems != null)
                            {
                                foreach (var foundReqItem in foundReqItems)
                                {
                                    foundReqItem.ParentSystemId = requisitionResponseModel.SPRNumber;
                                }
                            }
                        }
                    }
                }
            }

            return requisition;
        }

        public InFlightQty GetInFlightQuantity(string userName, string coid, int dept, string parClass, string itemId)
        {
            return ApiUtility.ExecuteApiGetTo<InFlightQty>(endpoint, getInFlightQty, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "dept", dept.ToString() },
                                                                                { "parClass", parClass },
                                                                                { "itemId", itemId }
                                                                            });
        }
    }
}
