﻿using System;
using System.Web.Http;
using System.Web.Mvc;
using Microsoft.Practices.ObjectBuilder2;
using Microsoft.Practices.Unity;
using Unity.Mvc5;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.Database;
using RequisitionServices.Repositories;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices;
using RequisitionServices.MMISServices.LegacyConnectorInterfaces;
using RequisitionServices.MMISServices.LegacyConnectorServices;

namespace Services.Injection
{
    public static class Bootstrapper
    {
        public static void Start()
        {

        }

        public static IUnityContainer InitializeApi()
        {
            var container = BuildUnityContainer();

            GlobalConfiguration.Configuration.DependencyResolver = new Unity.WebApi.UnityDependencyResolver(container);

            DependencyResolver.SetResolver(new UnityDependencyResolver(container));

            return container;
        }

        public static IUnityContainer InitializeWeb()
        {
            var container = BuildUnityContainer();

            DependencyResolver.SetResolver(new UnityDependencyResolver(container));

            return container;
        }

        private static IUnityContainer BuildUnityContainer()
        {
            var container = new UnityContainer();

            container.RegisterInstance(AutoMapperConfiguration.Initialize().CreateMapper(), new ContainerControlledLifetimeManager());

            //Database
            container.RegisterType<EProcurementContext>(new PerResolveLifetimeManager());
            // used by Evolve db
            container.RegisterType<IConnectionFactory, ConnectionFactory>();

            //Repositories
            container.RegisterType<IRequisitionRepository, RequisitionRepository>();
            container.RegisterType<IRequisitionStatusRepository, RequisitionStatusRepository>();
            container.RegisterType<ITypeRepository, TypeRepository>();
            container.RegisterType<IWorkflowRepository, WorkflowRepository>();
            container.RegisterType<IUserRepository, UserRepository>();
            container.RegisterType<IConfigurationRepository, ConfigurationRepository>();
            container.RegisterType<IVendorRepository, VendorRepository>();
            container.RegisterType<IAdhocReviewRepository, AdhocReviewRepository>();
            container.RegisterType<IAuditRepository, AuditRepository>();
            container.RegisterType<ISystemNotificationRepository, SystemNotificationRepository>();
            container.RegisterType<IContractRepository, ContractRepository>();
            container.RegisterType<ICartRepository, CartRepository>();
            container.RegisterType<ICommentRepository, CommentRepository>();
            container.RegisterType<IFacilityWorkflowRepository, FacilityWorkflowRepository>();
            container.RegisterType<IDigitalSignOffRepository, DigitalSignOffRepository>();
            container.RegisterType<IBillOnlyReviewRepository, BillOnlyReviewRepository>();
            container.RegisterType<IViraRepository, ViraRepository>();
            container.RegisterType<IUserAlertMessageRepository, UserAlertMessageRepository>();
            container.RegisterType<IVProRepository, VProRepository>();


            //Services
            container.RegisterType<IRequisitionService, RequisitionService>();
            container.RegisterType<IUserService, UserService>();
            container.RegisterType<ILocationService, LocationService>();
            container.RegisterType<IParService, ParService>();
            container.RegisterType<IItemService, ItemService>();
            container.RegisterType<IItemInfoService, ItemInfoService>();
            container.RegisterType<IClinicalDataService, ClinicalDataService>();
            container.RegisterType<IWorkflowService, WorkflowService>();
            container.RegisterType<IVendorService, VendorService>();
            container.RegisterType<IEmailService, EmailService>();
            container.RegisterType<IPOService, POService>();
            container.RegisterType<ICOIDService, COIDService>();
            container.RegisterType<IConfigurationService, ConfigurationService>();
            container.RegisterType<IAuditService, AuditService>();
            container.RegisterType<IIINItemService, IINItemService>();
            container.RegisterType<ISystemNotificationService, SystemNotificationService>();
            container.RegisterType<ICensorService, CensorService>();
            container.RegisterType<IContractService, ContractService>();
            container.RegisterType<ICartService, CartService>();
            container.RegisterType<IFacilityWorkflowService, FacilityWorkflowService>();
            container.RegisterType<ICommentService, CommentService>();

            container.RegisterType<ISmartItemService, SmartItemService>();
            container.RegisterType<ISmartItemInfoService, SmartItemInfoService>();
            container.RegisterType<ISmartCOIDService, SmartCOIDService>();
            container.RegisterType<ISmartParService, SmartParService>();
            container.RegisterType<ISmartVendorService, SmartVendorService>();
            container.RegisterType<ISmartDepartmentService, SmartDepartmentService>();
            container.RegisterType<ISmartRequisitionService, SmartRequisitionService>();
            container.RegisterType<ISmartRequisitionInquiryService, SmartRequisitionInquiryService>();
            container.RegisterType<ISmartDoctorService, SmartDoctorService>();
            container.RegisterType<ISmartPatientService, SmartPatientService>();
            container.RegisterType<ISmartPOService, SmartPOService>();
            container.RegisterType<ISmartContractService, SmartContractService>();
            container.RegisterType<ISmartIINItemService, SmartIINItemService>();
            container.RegisterType<ISmartParItemsService, SmartParItemsService>();
            container.RegisterType<ISmartLocationService, SmartLocationService>();
            container.RegisterType<ISmartRequisitionInquiryService, SmartRequisitionInquiryService>();
            container.RegisterType<ISmartItemParService, SmartItemParService>();
            container.RegisterType<IDigitalSignOffService, DigitalSignOffService>();
            container.RegisterType<IBillOnlyReviewService, BillOnlyReviewService>();
            container.RegisterType<IViraService, ViraService>();
            container.RegisterType<IUserAlertMessageService, UserAlertMessageService>();
            container.RegisterType<IVProService, VProService>();
            container.RegisterType<ILegacyConnectorItemPriceService, LegacyConnectorItemPriceService>();

            container.AddNewExtension<LazyExtension>();

            return container;
        }
    }

    public class LazyExtension : UnityContainerExtension
    {
        protected override void Initialize()
        {
            Context.Policies.Set<IBuildPlanPolicy>(new LazyBuildPlanPolicy(), typeof(Lazy<>));
        }

        public class LazyBuildPlanPolicy : IBuildPlanPolicy
        {
            public void BuildUp(IBuilderContext context)
            {
                if (context.Existing != null)
                {
                    return;
                }

                var container = context.NewBuildUp<IUnityContainer>();
                var typeToBuild = context.BuildKey.Type.GetGenericArguments()[0];
                var nameToBuild = context.BuildKey.Name;
                var lazyType = typeof(Lazy<>).MakeGenericType(typeToBuild);

                var func = GetType()
                    .GetMethod("CreateResolver")
                    .MakeGenericMethod(typeToBuild)
                    .Invoke(this, new object[] { container, nameToBuild });

                context.Existing = Activator.CreateInstance(lazyType, func);

                DynamicMethodConstructorStrategy.SetPerBuildSingleton(context);
            }

            public Func<T> CreateResolver<T>(IUnityContainer currentContainer, string nameToBuild)
            {
                return () => currentContainer.Resolve<T>(nameToBuild);
            }
        }
    }
}