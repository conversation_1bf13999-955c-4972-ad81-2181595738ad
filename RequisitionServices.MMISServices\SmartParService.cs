﻿using log4net;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.Items;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.MMISServices.Utilities;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    public class SmartParService : ISmartParService
    {
        private ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private const string getAllParsMethod = "Pars/GetAllPars/";
        private const string getParsByItemMethod = "v2/PARItem/GetParsByItem/";
        private const string getGetParItemsByParIdMethod = "Pars/GetParItemsByParId/";
        private const string getGetParByIdMethod = "Pars/GetParById/";

        private readonly string _endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IEnumerable<ParItem> GetParItems(string username, string coid, int deptNumber, string parId)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref username);
                SmartInputValidator.CheckCoid(coid);

                var parItemRecords = ApiUtility.ExecuteApiGetTo<IEnumerable<ParItemModel>>(_endpoint, getGetParItemsByParIdMethod, new Dictionary<string, string>()
                {
                    { "userId", username },
                    { "coid", coid },
                    { "parDept", deptNumber.ToString() },
                    { "parClass", parId }
                });

                var parItems = new List<ParItem>();
                if (parItemRecords != null)
                {
                    foreach (var parItemRecord in parItemRecords)
                    {
                        parItems.Add(parItemRecord.MapToParItem());
                    }
                }

                return parItems;
            }
            catch (Exception ex)
            {
                Log.Error($"Error calling Smart API to retrieve Par Items (COID = {coid}, Dept = {deptNumber}, Par = {parId})", ex);
                return null;
            }
        }

        public IEnumerable<ParItem> GetParItemsByItem(string username, string coid, int deptNumber, string itemNumber, ApiVersion version = null)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref username);
                SmartInputValidator.CheckCoid(coid);
                SmartInputValidator.CheckItemId(itemNumber);

                var parItemRecords = ApiUtility.ExecuteApiGetTo<IEnumerable<ParItemModel>>(_endpoint, getParsByItemMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", username },
                                                                                { "coid", coid },
                                                                                { "itemNumber", itemNumber },
                                                                                { "deptNumber", deptNumber.ToString() }
                                                                            });
                var parItems = new List<ParItem>();
                if (parItemRecords != null)
                {
                    parItems.AddRange(parItemRecords.Select(parItemRecord => parItemRecord.MapToParItem()));
                }

                return parItems;
            }
            catch (Exception ex)
            {
                Log.Error($"Error on SmartParServie GetParItemsByItems with coid:{coid}, dept:{deptNumber}, itemNumber:{itemNumber}", ex);
                if (version?.MajorVersion > 1)
                {
                    throw;
                }
                return new List<ParItem>();
            }
        }

        public IEnumerable<Par> GetPars(string username, string coid, int? deptNumber = null)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref username);
                SmartInputValidator.CheckCoid(coid);

                ParRecordsModel parRecords;
                if (deptNumber != null)
                {
                    parRecords = ApiUtility.ExecuteApiGetTo<ParRecordsModel>(_endpoint, getAllParsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", username },
                                                                                { "coid", coid },
                                                                                { "deptNumber", deptNumber.ToString() }
                                                                            });
                }
                else
                {
                    parRecords = ApiUtility.ExecuteApiGetTo<ParRecordsModel>(_endpoint, getAllParsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", username },
                                                                                { "coid", coid }
                                                                            });
                }

                var pars = new List<Par>();
                if (parRecords?.ParHeaders != null)
                {
                    pars.AddRange(parRecords.ParHeaders.Select(parRecord => parRecord.MapToPar()));
                }

                return pars;
            }
            catch(Exception ex)
            {
                Log.Error($"Error calling Smart API to retrieve PARs (DepId = {deptNumber}, COID = {coid})", ex);
                return null;
            }
        }

        public Par GetParById(string username, string coid, int deptId, string parId)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref username);
                SmartInputValidator.CheckCoid(coid);

                var smartParClass = ApiUtility.ExecuteApiGetTo<ParHeaderModel>(_endpoint, getGetParByIdMethod, new Dictionary<string, string>()
                {
                    { "userId", username },
                    { "coid", coid },
                    { "parDept", deptId.ToString() },
                    { "parClass", parId }
                });
                return smartParClass?.MapToPar();
            }
            catch(Exception ex)
            {
                Log.Error($"Error calling GetParById for user {username}, COID {coid}, Department {deptId}, PAR {parId}.", ex);
                return null;
            }
        }
    }
}
