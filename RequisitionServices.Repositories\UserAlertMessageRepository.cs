﻿using log4net;
using RequisitionServices.Database;
using RequisitionServices.DomainModel.UserAlertMessage;
using RequisitionServices.Repositories.Interfaces;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Threading.Tasks;

namespace RequisitionServices.Repositories
{
    public class UserAlertMessageRepository : AbstractRepository, IUserAlertMessageRepository
    {
        private readonly ILog log = LogManager.GetLogger(typeof(UserAlertMessageRepository));
        public UserAlertMessageRepository(EProcurementContext context) : base(context) { }


        public async Task<List<UserAlertMessage>> GetAllMessages()
        {
            var currentDateTimeUtc = DateTime.UtcNow;
            return await context.UserAlertMessages
                .Include("UserAlertMessageType")
                .Where(x => x.IsActive)
                .OrderByDescending(x => x.UserAlertMessageTypeId)
                .ToListAsync();
        }

        public async Task<UserAlertMessage> GetMessageById(int messageId)
        {
            var message = await context.UserAlertMessages
                .Include("UserAlertMessageType")
                .FirstOrDefaultAsync(x => x.Id == messageId);
            if (message == null)
            {
                throw new KeyNotFoundException("Message not found.");
            }
            return message;
        }

        public async Task<UserAlertMessage> CreateMessage(UserAlertMessageRequest messageRequest)
        {
            var message = new UserAlertMessage
            {
                Message = messageRequest.Message,
                StartDate = messageRequest.StartDate,
                EndDate = messageRequest.EndDate,
                IsActive = messageRequest.IsActive,
                UserAlertMessageTypeId = messageRequest.UserAlertMessageTypeId
            };

            context.UserAlertMessages.Add(message);
            await context.SaveChangesAsync();

            return message;
        }

        public async Task DeleteMessage(int messageId)
        {
            var message = await context.UserAlertMessages.FindAsync(messageId);
            if (message == null)
            {
                throw new KeyNotFoundException("Message not found.");
            }
            context.UserAlertMessages.Remove(message);
            await context.SaveChangesAsync();
        }

        public async Task<UserAlertMessage> UpdateMessage(UserAlertMessageRequest messageRequest)
        {
            var existingMessage = await context.UserAlertMessages.FindAsync(messageRequest.Id);
            if (existingMessage == null)
            {
                throw new KeyNotFoundException("Message not found.");
            }
            else
            {
                existingMessage.Message = messageRequest.Message;
                existingMessage.StartDate = messageRequest.StartDate;
                existingMessage.EndDate = messageRequest.EndDate;
                existingMessage.IsActive = messageRequest.IsActive;
                existingMessage.UserAlertMessageTypeId = messageRequest.UserAlertMessageTypeId;
                await context.SaveChangesAsync();
            }                  
            return existingMessage;
        }
    }
}
