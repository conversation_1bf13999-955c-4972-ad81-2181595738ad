﻿using log4net;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;
using System.Collections.Generic;
using System.Reflection;

namespace RequisitionServices.DomainServices
{
    public class COIDService : ICOIDService
    {
        private readonly ILog log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private const string getUserCoidsMethod = "Coids/GetAllCoids/";

        private ISmartCOIDService smartCOIDService;

        public COIDService(ISmartCOIDService smartCOIDSvc)
        {
            smartCOIDService = smartCOIDSvc;
        }

        public List<int> GetAllCOIDs(string userName)
        {
            return smartCOIDService.GetAllCOIDs(userName);
        }
    }
}
