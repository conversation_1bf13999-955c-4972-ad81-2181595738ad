﻿using log4net;
using Newtonsoft.Json;
using Newtonsoft.Json.Schema;
using RequisitionServices.AppInsights;
using RequisitionServices.Filters;
using Services.Injection;
using System;
using System.Configuration;
using System.Reflection;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using System.Web.Routing;

namespace RequisitionServices
{
    public class WebApiApplication : HttpApplication
    {
        readonly ILog _log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        protected void Application_Start()
        {
            AreaRegistration.RegisterAllAreas();

            //Register models and services
            Bootstrapper.InitializeApi();

            GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);

            GlobalConfiguration.Configuration.Filters.Add(new UnhandledExceptionFilterAttribute());

            RouteConfig.RegisterRoutes(RouteTable.Routes);

            var licenseKey = ConfigurationManager.AppSettings["NewtonSoftJSchemaLicenseKey"];
            License.RegisterLicense(licenseKey);

            JsonConvert.DefaultSettings = () => new JsonSerializerSettings
            {
                Formatting = Formatting.Indented,
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore
            };

            EvolveDbConfig.Migrate();

            InitializeAppInsights._InitializeAppInsights();
        }

        protected void Application_Error(object sender, EventArgs e)
        {
            var exception = Server.GetLastError().GetBaseException();
            _log.Error("Unhandled application exception", exception);
        }
    }
}
