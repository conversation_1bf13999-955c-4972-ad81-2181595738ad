﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using RequisitionServices.Controllers;
using Moq;
using RequisitionServices.DomainServices.Interface;
using System.Linq;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.Tests.Controllers
{
    [TestClass]
    public class UserControllerTest
    {
        private static Mock<IUserService> mockUserService;
        private static UserController mockUserController;
        private const string COID = "09391";

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            mockUserService = new Mock<IUserService>();
            mockUserController = new UserController(mockUserService.Object);
        }

        [ClassCleanup]
        public static void ClassCleanup()
        {
            mockUserController = null;
            mockUserService = null;
        }

        [TestMethod]
        public void TestGetUserReportAndEditDBInfosInUserRepository()
        {
            var userName = "hcadev/PYA7593";
            var testdto = new UserReportInfoRequestDTO()
            {
                UserName = userName,
                COID = COID,
                Users = new List<UserReportProfileDTO>()
            };

            var result = mockUserController.RetrieveUserReportInfo(testdto);

            Assert.IsTrue(result != null && result.Count() == 0);

            //testdto.Users.ToList().Add(new UserReportProfileDTO()
            //{
            //    AccountName = "TestFake",
            //    IsApprover = false,
            //    FirstName = "Test",
            //    LastName = "Fake"
            //});
        }
    }
}
