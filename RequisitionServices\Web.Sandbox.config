<?xml version="1.0" encoding="utf-8"?>

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">

  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>

  <connectionStrings>
    <add name="EProcurementContext"
         providerName="System.Data.SqlClient"
         connectionString="Server=htnawddbsolp07.hcadev.corpaddev.net; Database=EProcurement; Integrated Security=True; MultipleActiveResultSets=True;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>

    <add name="WorkflowPersistanceStore"
         providerName="System.Data.SqlClient"
         connectionString="Server=htnawddbsolp07.hcadev.corpaddev.net;Initial Catalog=WorkflowPersistanceStore;Integrated Security=True;Asynchronous Processing=True;"
         xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
  </connectionStrings>

  <appSettings>
    <add key="HomeAPIUrl" value="http://dev-api.healthtrustpg.com/v1/api/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SmartServicesAPIUrl" value="https://sbx-api-smartservices.healthtrustpg.com/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="SecurityAPIUrl" value="http://dev-api.nsa.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SecurtiyAPIKey" value="9068249A-1CC4-4D8D-BB30-4A0FC9FC98CD" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

    <add key="ProfileAPIUrl" value="http://dev-api-profile.healthtrustpg.com/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="EProcurementUrl" value="https://sbx-smart.healthtrustpg.com/procurement/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="SwaggerRootUrl" value="https://sbx-api-requisitions.healthtrustpg.com" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="AppInsightsEnabled" value="False"  xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
    <add key="ikey" value="2afdb756-6064-4658-9fc5-0ea538dcc753" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>

	<!-- Legacy Connector -->
	<add key="ItemPriceLegacyConnectorAPIUrl" value="https://api-dev.app.hca.cloud/legacy-connector-services/legacy-connector-itemcontract-api/v1/" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenUrl" value="https://api-dev.app.hca.cloud/token" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenUsername" value="DdnBAgxbFry1w0ewRpHBDwMudSkQDI6QuU5MXkVDm8xlDPS9" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenPassword" value="kufW4TtpcA4GP7QTMNDjLAb8RLG4mPxZWVGjYqJVGG0XGMevCfoodPyvffa4vH9D" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
	<add key="LegacyConnectorTokenGrant_type" value="client_credentials" xdt:Transform="SetAttributes" xdt:Locator="Match(key)"/>
  </appSettings>

  <system.serviceModel>
    <bindings xdt:Transform="Replace">
      <basicHttpsBinding>
        <binding name="BasicHttpsBinding_IRushApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" />
        <binding name="BasicHttpsBinding_INonRushApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" />
        <binding name="BasicHttpsBinding_IVendorApprovalWorkflow" maxBufferSize="**********" maxBufferPoolSize="**********" maxReceivedMessageSize="**********" />
      </basicHttpsBinding>
    </bindings>
    <client xdt:Transform="Replace">
      <endpoint address="https://sbx-approvalworkflow.healthtrustpg.com/RushWorkflowService.xamlx"
          binding="basicHttpsBinding" bindingConfiguration="BasicHttpsBinding_IRushApprovalWorkflow"
          contract="RushWorkflowSvc.IRushApprovalWorkflow" name="BasicHttpsBinding_IRushApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
      <endpoint address="https://sbx-approvalworkflow.healthtrustpg.com/NonRushWorkflowService.xamlx"
          binding="basicHttpsBinding" bindingConfiguration="BasicHttpsBinding_INonRushApprovalWorkflow"
          contract="NonRushWorkflowSvc.INonRushApprovalWorkflow" name="BasicHttpsBinding_INonRushApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
        <endpoint address="https://sbx-approvalworkflow.healthtrustpg.com/VendorWorkflowService.xamlx"
            binding="basicHttpsBinding" bindingConfiguration="BasicHttpsBinding_IVendorApprovalWorkflow"
            contract="VendorWorkflowSvc.IVendorApprovalWorkflow" name="BasicHttpsBinding_IVendorApprovalWorkflow" xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </client>
  </system.serviceModel>

  <log4net xdt:Transform="Replace">
    <appender name="InfoFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="INFO"/>
      <param name="File" value="E:\Logs\requisitionServices\"/>
      <param name="MaxSizeRollBackups" value="-1"/>
      <param name="RollingStyle" value="Date"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyyMMdd'_Info.log'"/>
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="INFO"/>
        <levelMax value="WARN"/>
      </filter>
    </appender>
    <appender name="ErrorFileAppender" type="log4net.Appender.RollingFileAppender">
      <param name="Threshold" value="ERROR"/>
      <param name="File" value="E:\Logs\requisitionServices\"/>
      <param name="MaxSizeRollBackups" value="-1"/>
      <param name="RollingStyle" value="Date"/>
      <param name="StaticLogFileName" value="false"/>
      <param name="DatePattern" value="yyyyMMdd'_Error.log'"/>
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d [%-5p] %P{log4net:HostName} %c (%t) %P{X-Correlation-ID} %P{User} %m%n"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="ERROR"/>
        <levelMax value="FATAL"/>
      </filter>
    </appender>
    <root>
      <appender-ref ref="InfoFileAppender"/>
      <appender-ref ref="ErrorFileAppender"/>
    </root>
  </log4net>

</configuration>