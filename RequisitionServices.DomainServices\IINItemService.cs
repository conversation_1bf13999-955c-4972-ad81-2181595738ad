﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.MMISServices.Interface;

namespace RequisitionServices.DomainServices
{
    public class IINItemService : IIINItemService
    {
        private ISmartIINItemService smartIINItemService;

        public IINItemService(ISmartIINItemService smartIINItemSvc)
        {
            smartIINItemService = smartIINItemSvc;
        }

        public IINItemRecordModel GetIINItemById(string userId, string COID, string IINitemNumber)
        {
            return smartIINItemService.GetIINItemById(userId, COID, IINitemNumber);
        }
    }
}
