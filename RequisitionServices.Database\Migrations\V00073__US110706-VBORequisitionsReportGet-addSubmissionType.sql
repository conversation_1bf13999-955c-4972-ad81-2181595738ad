USE [eProcurementQA]
GO
/****** Object:  StoredProcedure [dbo].[usp_VBORequisitionsReportGet]    Script Date: 5/17/2024 1:07:13 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
***************************************************************************
Database    : eProcurement
Name        : usp_VBORequisitionsReportGet
Purpose     : Returns a paginated list of requisitions for the Requisition Report.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 03-28-2022
Usage       : Vendor Requisitions Report
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs

Eric Simmons		05-20-2024		Adding RequisitionSubmissionTypeId to create DSO tag
									and use for filtering for Approval History
***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_VBORequisitionsReportGet]
	@rowOffset int,
	@pageSize int,
	@coid varchar(140),
	@departmentIds [dbo].[IdTemplate] READONLY,
	@startDate DateTime,
	@endDate DateTime,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@vendorAffiliations [dbo].[IdTemplate] READONLY,
	@userIsVendor bit
AS

BEGIN

	DECLARE @minBackMonths INT = -13
	DECLARE @totalReqCount BIGINT
	DECLARE @filter VARCHAR(142) = NULL
	IF @filterText IS NOT NULL
		SET @filter = '%' + @filterText + '%'

	DECLARE @tempDepartmentIds TABLE (Id INT)
	DECLARE @tempReqTypeIds TABLE (Id INT)
	DECLARE @tempVendorAffiliations TABLE (Id INT)

	DECLARE @requisitionList TABLE 
	(
		RowOrder INT NOT NULL,
		Id INT NOT NULL
	)

	INSERT INTO @tempDepartmentIds SELECT Id FROM @departmentIds
	INSERT INTO @tempReqTypeIds SELECT Id FROM @reqTypeIdGroup
	INSERT INTO @tempVendorAffiliations SELECT Id FROM @vendorAffiliations

	INSERT INTO @requisitionList
	SELECT
		ROW_NUMBER() OVER(ORDER BY SortOrder DESC,
			CASE @oldestFirst WHEN 0 THEN R.CreateDate END DESC,
			CASE @oldestFirst WHEN 1 THEN R.CreateDate END ASC) AS RowOrder,
		R.RequisitionId
	FROM
	(
		SELECT DISTINCT
			CASE @statusSorting
				WHEN 1 THEN CASE R.RequisitionStatusTypeId
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 14 THEN 5
					WHEN 2 THEN 6
					WHEN 4 THEN 7
					ELSE 8 END
				ELSE (CASE @mobileReqs
					WHEN 1 THEN CASE R.IsMobile
						WHEN 1 THEN 1
						ELSE 2 END
					ELSE CASE
						WHEN R.RequisitionTypeId IN (SELECT Id FROM @tempReqTypeIds)
						THEN 1
						ELSE 2 END
					END)
				END AS SortOrder,
			R.RequisitionId,
			R.CreateDate
		FROM Requisitions R WITH (NOLOCK)
		INNER JOIN RequisitionStatusTypes RST WITH (NOLOCK)
			ON R.RequisitionStatusTypeId = RST.Id
		INNER JOIN Users U WITH (NOLOCK)
			ON R.CreatedBy = U.AccountName
		INNER JOIN RequisitionItems RI WITH (NOLOCK)
			ON R.RequisitionId = RI.RequisitionId
		INNER JOIN RequisitionItemStatusTypes RIST WITH (NOLOCK)
			ON RI.RequisitionItemStatusTypeId = RIST.Id
		LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
			ON RI.Id = SPR.RequisitionItemId
		WHERE (@filter IS NULL OR
		(
			R.RequisitionId LIKE @filter
			OR (U.FirstName + ' ' + U.LastName) LIKE @filter
			OR RST.[Description] LIKE @filter
			OR RIST.[Description] LIKE @filter
			OR R.Comments LIKE @filter
			OR RI.PONumber LIKE @filter
			OR RI.ParentSystemId LIKE @filter
			OR RI.OriginalParentSystemId LIKE @filter
			OR R.LocationIdentifier LIKE @filter
			OR (@filterText != 'EPR' AND RI.ParIdentifier LIKE @filter)
			OR SUBSTRING(R.LocationIdentifier, 0, CHARINDEX('_', R.LocationIdentifier)) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
			OR R.LocationIdentifier IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter)
		))
		AND R.IsVendor = 1
		AND (@userIsVendor = 0 OR
			(NOT EXISTS (SELECT * FROM RequisitionItems RI2 WITH (NOLOCK)
				LEFT OUTER JOIN SPRDetails SPR2 WITH (NOLOCK)
					ON RI2.Id = SPR2.RequisitionItemId
				WHERE RI2.RequisitionId = R.RequisitionId AND ((
					SPR2.VendorId IS NOT NULL AND SPR2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations))
					OR (RI2.VendorId <> 0 AND RI2.VendorId NOT IN (SELECT Id FROM @vendorAffiliations)))
		)))
		AND RST.Id NOT IN (1, 5, 8, 12) --Draft, Deleted, Template, Recalled
		AND substring(R.LocationIdentifier,0,(CHARINDEX('_',R.LocationIdentifier))) = @coid  
		AND ((SELECT COUNT(*) FROM @tempDepartmentIds) = 0 OR (substring(R.LocationIdentifier,(CHARINDEX('_',R.LocationIdentifier)+1),LEN(R.LocationIdentifier)) IN (SELECT DISTINCT Id FROM @tempDepartmentIds)))
		AND R.CreateDate BETWEEN @startDate AND @endDate
		AND R.CreateDate >= DATEADD(MONTH, @minBackMonths, GETDATE())
	) AS R

	SELECT @totalReqCount = COUNT(*) FROM @requisitionList

	SELECT 
		R.RequisitionId,
		R.RequisitionStatusTypeId,
		R.LocationIdentifier,
		R.Comments,
		R.CreateDate,
		R.RequisitionTypeId,
		U.FirstName,
		U.LastName,
		R.IsMobile,
		R.IsVendor,
		R.RequisitionSubmissionTypeId,
		RI.Id AS RequisitionItemId,
		RI.ItemId AS RequisitionItemNumber,
		RI.RequisitionItemStatusTypeId,
		RI.ParentSystemId AS RequisitionItemParentSystemId,
		RI.OriginalParentSystemId AS RequisitionItemOriginalParentSystemId,
		RI.PONumber AS RequisitionItemPONumber,
		RI.ParentRequisitionItemId AS RequisitionItemParentItemId,
		RI.ParIdentifier AS RequisitionItemParIdentifier,
		RI.Discount,
		RI.VendorId,
		RI.UnitCost,
		RI.QuantityToOrder,
		SPR.VendorId AS SprDetailsVendorId,
		SPR.VendorName AS SprDetailsVendorName,
		SPR.PartNumber AS SprDetailsPartNumber,
		F.RequisitionItemId AS SprDetailsFileAttachment,
		R.CreatedBy AS CreatedById,
		@totalReqCount AS TotalReqCount,
		[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
	FROM
	(
		SELECT * FROM @requisitionList
		ORDER BY RowOrder OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
	) AS requisitionPage
	INNER JOIN Requisitions R WITH (NOLOCK)
		ON requisitionPage.Id = R.RequisitionId
	INNER JOIN Users U WITH (NOLOCK)
		ON R.CreatedBy = U.AccountName
	LEFT OUTER JOIN RequisitionItems RI WITH (NOLOCK)
		ON R.RequisitionId = RI.RequisitionId
	LEFT OUTER JOIN SPRDetails SPR WITH (NOLOCK)
		ON RI.Id = SPR.RequisitionItemId
	LEFT OUTER JOIN [dbo].[RequisitionSubmissionTypes] AS [ReqSubmissionTypes] WITH (NOLOCK) 
			ON [R].[RequisitionSubmissionTypeId] = [ReqSubmissionTypes].[Id]
LEFT OUTER JOIN (SELECT DISTINCT RequisitionItemId FROM FileAttachments WITH (NOLOCK)) AS F ON RI.Id = F.RequisitionItemId
LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON RI.Id = [VboHoldItemConversion].[RequisitionItemId]

END