﻿using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Requisitions
{
    /// <summary>
    /// Represents a DTO for a queue of Purchasing Requisitions, including pagination information.
    /// </summary>
    public class PurchasingRequisitionQeueDTO
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PurchasingRequisitionQueDTO"/> class.
        /// </summary>
        /// <param name="requisitions">The list of purchasing requisitions in the queue.</param>
        /// <param name="totalCount">The total number of requisitions available.</param>
        public PurchasingRequisitionQeueDTO(List<PurchasingRequisitionDTO> requisitions, int totalCount)
        {
            this.Requisitions = requisitions;
            this.TotalCount = totalCount;
        }

        /// <summary>
        /// Gets or sets the total number of requisitions available.
        /// This property is used for pagination purposes.
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// Gets or sets the list of purchasing requisitions in the queue.
        /// </summary>
        public List<PurchasingRequisitionDTO> Requisitions { get; set; }
    }
}