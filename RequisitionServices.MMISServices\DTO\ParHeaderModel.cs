﻿using RequisitionServices.DomainModel.Items;
using RequisitionServices.DomainModel.Locations;
using System;

namespace RequisitionServices.MMISServices.DTO
{
    public class ParHeaderModel
    {
        public Par MapToPar()
        {
            return new Par()
            {
                Department = new Department() { Id = this.Department, Description = this.DepartmentDescription, COID = ParCoid.ToString() },
                ParId = this.Class + this.ParNumber,
                Description = this.ParDescription,
                ParType = this.ParType,
                ExternalInterface = this.ExternalInterface,
                ShipToCode = this.ShipTo
            };
        }

        public int ParCoid { get; set; }

        public int Department { get; set; }

        public string Class { get; set; }

        public string ParNumber { get; set; }

        public string ParDescription { get; set; }

        public string DepartmentDescription { get; set; }

        public int ShipTo { get; set; }

        public bool ExternalInterface { get; set; }

        public bool BillPatientFlag { get; set; }

        public bool ReconciliationFlag { get; set; }

        public bool BackOrderFlag { get; set; }

        public int DefaultAccount { get; set; }

        public int ParType { get; set; }

        public DateTime CountDate { get; set; }

        public ParScheduleModel ParSchedules { get; set; }
    }
}
