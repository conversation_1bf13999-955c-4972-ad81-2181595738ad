USE [eProcurementQA]
GO

/****** Object:  StoredProcedure [dbo].[usp_RequisitionsGet]    Script Date: 3/19/2024 5:00:32 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
***************************************************************************
Database    : eProcurement
Name        : usp_RequisitionsGet
Purpose     : Returns a paginated list of requisitions for the MyRequisitions page.
Used By     : SMART Procurement team
Author      : <PERSON>
Created     : 10-26-2017
Usage       : Executed by our Requisition Services server
***************************************************************************
Change History
***************************************************************************
Name				Date            Reason for modification
---------------		-----------     -----------------------
Peter Hurlburt		10/26/2017      Script created
Peter Hurlburt		10/27/2017      Submitted for deployment 21
Peter Hurlburt		11/01/2017		Added WITH (NOLOCK) statements
Peter Hurlburt		11/02/2017		Changing ALTER to CREATE, adding new script headers
Peter Hurlburt		11/06/2017		Modified DROP/CREATE to conditional CREATE/ALTER
									Submitted for deployment 22
Peter Hurlburt		12/15/2017      Removed an overly-verbose union statement
									Submitted for deployment 25
Colin Glasco		07/26/2018		Added missing fields needed from Items to 
									properly update item status from SMART
Colin Glasco		10/18/2018		Adding missing columns from RequisitionItems table
									needed for updating item status for Waste PARs
Monica Barbadillo	02/06/2019		Adding IsMobile column 
Colin Glasco		08/18/2020		Adding new Sort types: Oldest, BO/BR, Mobile
Julio Pozo			02/18/2022		Adding IsVendor column + Improving indentation
Peter Hurlburt		05/31/2022		Adding filtering over facility/department name
									(String comparisons are made in Epro Services,
									IDs are passed here for results filtering)
Peter Hurlburt		10/31/2022		Adding VboHoldItemConversion join for most sprocs
Jeremiah King		03/21/2024		Added RequisitionSubmissionTypeId and Description be 
									able to access from within the requisition object. 
									Added filtering on Description from 
									RequisitionSubmissionTypes table

***************************************************************************
*/

ALTER PROCEDURE [dbo].[usp_RequisitionsGet]
	@userName varchar(100),
	@rowOffset int,
	@pageSize int,
	@filterText varchar(140),
	@reqTypeIdGroup [dbo].[IdTemplate] READONLY,
	@statusSorting bit,
	@oldestFirst bit,
	@mobileReqs bit,
	@facilitiesMatchedOnFilter [dbo].[Varchar50Template] READONLY,
	@departmentsMatchedOnFilter [dbo].[Varchar50Template] READONLY
AS

BEGIN

SELECT
	[DistinctRequisitions].[RequisitionId] AS [RequisitionId],
	[DistinctRequisitions].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
	[DistinctRequisitions].[LocationIdentifier] AS [LocationIdentifier],
	[DistinctRequisitions].[Comments] AS [Comments],
	[DistinctRequisitions].[CreateDate] AS [CreateDate],
	[DistinctRequisitions].[RequisitionTypeId] AS [RequisitionTypeId],
	[DistinctRequisitions].[IsMobile] AS [IsMobile],
	[DistinctRequisitions].[IsVendor] AS [IsVendor],
	[DistinctRequisitions].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
	[AllReqItems].[Id] AS [RequisitionItemId],
	[AllReqItems].[ItemId] AS [RequisitionItemNumber],
	[AllReqItems].[RequisitionItemStatusTypeId] AS [RequisitionItemStatusTypeId],
	[AllReqItems].[QuantityToOrder] AS [RequisitionItemQuantityToOrder],
	[AllReqItems].[ParentSystemId] AS [RequisitionItemParentSystemId],
	[AllReqItems].[OriginalParentSystemId] AS [RequisitionItemOriginalParentSystemId],
	[AllReqItems].[ParentRequisitionItemId] AS [RequisitionItemParentItemId],
	[AllReqItems].[Discount] AS [Discount],
	[AllReqItems].[VendorId] AS [VendorId],
	[AllReqItems].[UnitCost] AS [UnitCost],
	[AllReqItems].[ParIdentifier] AS [ParIdentifier],
	[ReqItemSprDetails].[VendorId] AS [SprDetailsVendorId],
	[ReqItemSprDetails].[VendorName] AS [SprDetailsVendorName],
	[ReqItemSprDetails].[PartNumber] AS [SprDetailsPartNumber],
	[ReqItemFileAttachments].[RequisitionItemId] AS [SprDetailsFileAttachment],
	[DistinctRequisitions].[TotalReqCount] AS [TotalReqCount],
	[VboHoldItemConversion].[UnitCost] AS [VboHoldItemConversionUnitCost]
FROM
(
	SELECT
		ROW_NUMBER() OVER (
			ORDER BY (
				CASE @statusSorting 
					WHEN 1 THEN 
						CASE [RequisitionStatusTypeId] 
							WHEN 7 THEN 1 
							WHEN 6 THEN 2
							WHEN 12 THEN 3
							WHEN 1 THEN 4
							WHEN 14 THEN 5
							WHEN 2 THEN 6
							WHEN 4 THEN 7
							ELSE 8
						END 
				END), [ReqTypeGroupingOrder], 
				CASE @oldestFirst 
					WHEN 0 THEN [Req].[CreateDate] END DESC,
				CASE @oldestFirst 
					WHEN 1 THEN [Req].[CreateDate] END ASC
		) as rowNumber,
		[Req].[ReqTypeGroupingOrder] AS [ReqTypeGroupingOrder],
		[Req].[RequisitionId] AS [RequisitionId],
		[Req].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
		[Req].[LocationIdentifier] AS [LocationIdentifier],
		[Req].[Comments] AS [Comments],
		[Req].[CreateDate] AS [CreateDate],
		[Req].[RequisitionTypeId] AS [RequisitionTypeId],
		[Req].[IsMobile] AS [IsMobile],
		[Req].[IsVendor] AS [IsVendor],
		[Req].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId],
		(CASE @statusSorting 
			WHEN 1 THEN 
				CASE [RequisitionStatusTypeId] 
					WHEN 7 THEN 1 
					WHEN 6 THEN 2
					WHEN 12 THEN 3
					WHEN 1 THEN 4
					WHEN 14 THEN 5
					WHEN 2 THEN 6
					WHEN 4 THEN 7
					ELSE 8
				END 
		END) AS [ConditionalStatusSorting],
		dense_rank() OVER (ORDER BY [Req].[RequisitionId] ASC)
		+ dense_rank() OVER (ORDER BY [Req].[RequisitionId] DESC)
		- 1 AS [TotalReqCount]
	FROM
	(
		SELECT
			CASE
				WHEN @mobileReqs = 1 
				THEN 
					CASE
						WHEN [Requisition].[IsMobile] = @mobileReqs 
						THEN 1
						ELSE 2 
					END
				ELSE 
					CASE
						WHEN [Requisition].[RequisitionTypeId] IN (SELECT * FROM @reqTypeIdGroup) 
						THEN 1
						ELSE 2 
					END
			END AS [ReqTypeGroupingOrder],
			[Requisition].[RequisitionId] AS [RequisitionId],
			[Requisition].[RequisitionStatusTypeId] AS [RequisitionStatusTypeId],
			[Requisition].[LocationIdentifier] AS [LocationIdentifier],
			[Requisition].[Comments] AS [Comments],
			[Requisition].[CreateDate] AS [CreateDate],
			[Requisition].[RequisitionTypeId] AS [RequisitionTypeId],
			[Requisition].[IsMobile] AS [IsMobile],
			[Requisition].[IsVendor] AS [IsVendor],
			[Requisition].[RequisitionSubmissionTypeId] AS [RequisitionSubmissionTypeId]
		FROM
			[dbo].[Requisitions] AS [Requisition] WITH (NOLOCK)
		WHERE
			[Requisition].[CreatedBy] = @userName
			AND 5 <> [Requisition].[RequisitionStatusTypeId]
			AND 8 <> [Requisition].[RequisitionStatusTypeId]
	) AS [Req]
		LEFT OUTER JOIN [dbo].[RequisitionStatusTypes] AS [ReqStatus] WITH (NOLOCK) ON [Req].[RequisitionStatusTypeId] = [ReqStatus].[Id]
		LEFT OUTER JOIN [dbo].[RequisitionItems] AS [ReqItem] WITH (NOLOCK) ON [Req].[RequisitionId] = [ReqItem].[RequisitionId]
		LEFT OUTER JOIN [dbo].[RequisitionItemStatusTypes] AS [ReqItemStatus] WITH (NOLOCK) ON [ReqItem].[RequisitionItemStatusTypeId] = [ReqItemStatus].[Id]
		LEFT OUTER JOIN [dbo].[RequisitionSubmissionTypes] AS [ReqSubmissionType] WITH (NOLOCK) ON [Req].[RequisitionSubmissionTypeId] = [ReqSubmissionType].[Id]
	WHERE
		@filterText IS NULL OR
		([Req].[RequisitionId] LIKE '%' + @filterText + '%'
		OR [ReqStatus].[Description] LIKE '%' + @filterText + '%'
		OR [ReqSubmissionType].[Description] LIKE '%' + @filterText + '%'
		OR [ReqItemStatus].[Description] LIKE '%' + @filterText + '%'
		OR [Req].[Comments] LIKE '%' + @filterText + '%'
		OR [Req].[LocationIdentifier] LIKE '%' + @filterText + '%'
		OR [ReqItem].[PONumber] LIKE '%' + @filterText + '%'
		OR (@filterText != 'EPR' AND [ReqItem].[ParIdentifier] LIKE '%' + @filterText + '%')
		OR [ReqItem].[ParentSystemId] LIKE '%' + @filterText + '%'
		OR [ReqItem].[OriginalParentSystemId] LIKE '%' + @filterText + '%'
		OR SUBSTRING([Req].[LocationIdentifier], 0, CHARINDEX('_', [Req].[LocationIdentifier])) IN (SELECT [VarcharVal] FROM @facilitiesMatchedOnFilter)
		OR [Req].[LocationIdentifier] IN (SELECT [VarcharVal] FROM @departmentsMatchedOnFilter))
	GROUP BY
		[Req].[ReqTypeGroupingOrder],
		[Req].[RequisitionId],
		[Req].[RequisitionStatusTypeId],
		[Req].[LocationIdentifier],
		[Req].[Comments],
		[Req].[CreateDate],
		[Req].[RequisitionTypeId],
		[Req].[IsMobile],
		[Req].[IsVendor],
		[Req].[RequisitionSubmissionTypeId]
	ORDER BY
		[ConditionalStatusSorting], [ReqTypeGroupingOrder], 
		CASE WHEN @oldestFirst = 0 THEN [Req].[CreateDate] END DESC,
		CASE WHEN @oldestFirst = 1 THEN [Req].[CreateDate] END ASC
		OFFSET @rowOffset ROWS FETCH NEXT @pageSize ROWS ONLY
) AS [DistinctRequisitions]

	LEFT OUTER JOIN [dbo].[RequisitionItems] AS [AllReqItems] WITH (NOLOCK) ON [DistinctRequisitions].[RequisitionId] = [AllReqItems].[RequisitionId]
	LEFT OUTER JOIN [dbo].[SPRDetails] AS [ReqItemSprDetails] WITH (NOLOCK) ON [AllReqItems].[Id] = [ReqItemSprDetails].[RequisitionItemId]
	LEFT OUTER JOIN (SELECT DISTINCT [RequisitionItemId] FROM [dbo].[FileAttachments] WITH (NOLOCK)) AS [ReqItemFileAttachments] ON [AllReqItems].[Id] = [ReqItemFileAttachments].[RequisitionItemId]
	LEFT OUTER JOIN [dbo].[VboHoldItemConversions] AS [VboHoldItemConversion] WITH (NOLOCK) ON [AllReqItems].[Id] = [VboHoldItemConversion].[RequisitionItemId]

ORDER BY 
	[DistinctRequisitions].rowNumber


OPTION (RECOMPILE)

END -- Procedure
GO


