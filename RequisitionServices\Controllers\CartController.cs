﻿using RequisitionServices.DomainModel.Cart.Requests;
using RequisitionServices.DomainModel.Cart.Responses;
using RequisitionServices.DomainModel.Cart.Entities;
using RequisitionServices.DomainServices.Interface;
using System.Web.Http;
using RequisitionServices.DomainModel.Requisitions;
using System.Collections.Generic;
using System.Linq;
using RequisitionServices.DomainModel.Enum;
using RequisitionServices.Utility.Model;
using System;

namespace RequisitionServices.Controllers
{
    [RoutePrefix("cart")]
    public class CartController : ApiController
    {
        readonly ICartService _cartService;
        readonly ICensorService _censorService;

        public CartController(ICartService cartService, ICensorService censorService)
        {
            _cartService = cartService;
            _censorService = censorService;
        }

        [Route("")]
        [HttpGet]
        public Cart GetCart(string coid, int departmentNumber, string parId, long? cartId, string username)
        {
            var cart = _cartService.GetCart(new CartRequest
            {
                Coid = coid,
                DepartmentNumber = departmentNumber,
                ParId = parId,
                CartId = cartId,
                CartTypeId = CartType.Procurement.ToInt(),
                Username = username
            });
            if (cart?.Items?.Any() == true) cart.Items = RemoveCartReferenceFromCartItems(cart?.Items?.ToList());
            return cart;
        }

        [Route("poucart")]
        [HttpGet]
        public Cart GetPOUCart(string coid, string username)
        {
            var cart = _cartService.GetCart(new CartRequest
            {
                Coid = coid,
                Username = username,
                CartTypeId = CartType.POU.ToInt()
            });
            if (cart?.Items?.Any() == true) cart.Items = RemoveCartReferenceFromCartItems(cart?.Items?.ToList());
            return cart;
        }

        [Route("attributes")]
        [HttpGet]
        public CartAttributesResponse GetAttributes(string coid, int departmentNumber, string parId, long? cartId, string username)
        {
            return _cartService.GetAttributes(new CartRequest
            {
                Coid = coid,
                DepartmentNumber = departmentNumber,
                ParId = parId,
                CartId = cartId,
                Username = username,
                CartTypeId = CartType.Procurement.ToInt()
            });
        }

        [Route("pouattributes")]
        [HttpGet]
        public CartAttributesResponse GetPOUAttributes(string coid, long? cartId, string username) //, int departmentNumber, string parId
        {
            return _cartService.GetPOUAttributes(new CartRequest
            {
                Coid = coid,
                CartId = cartId,
                Username = username,
                CartTypeId = CartType.POU.ToInt()
            });
        }

        [Route("items/exists")]
        [HttpGet]
        public bool CartItemExists(int itemNumber, long cartId)
        {
            return _cartService.CartItemExists(new CartItemExistsRequest
            {
                ItemNumber = itemNumber,
                CartId = cartId
            });
        }

        [Route("items")]
        [HttpPost]
        public AddToCartResponse AddToCart(CartAddItemRequest request)
        {
            var response = _cartService.AddToCart(request);
            if (response.ExistingItem != null) response.ExistingItem = RemoveCartReferenceFromCartItems(new List<CartItem> { response.ExistingItem }).First();

            return response;
        }

        [Route("pouitems")]
        [HttpPost]
        public AddToCartResponse AddToPOUCart(CartAddItemRequest request)
        {
            var response = _cartService.AddToPOUCart(request);
            if (response.ExistingItem != null) response.ExistingItem = RemoveCartReferenceFromCartItems(new List<CartItem> { response.ExistingItem }).First();

            return response;
        }

        [Route("items")]
        [HttpPatch]
        public void UpdateCartItems(CartUpdateItemsRequest request)
        {
            _cartService.UpdateCartItems(request);
        }

        [Route("pouitems")]
        [HttpPatch]
        public void UpdateCartPOUItems(CartUpdateItemsRequest request)
        {
            _cartService.UpdateCartPOUItems(request);
        }

        [Route("items")]
        [HttpDelete]
        public int DeleteCartItem(long cartId, long cartItemId)
        {
            return _cartService.DeleteCartItem(new CartDeleteItemRequest
            {
                CartId = cartId,
                CartItemId = cartItemId
            });
        }

        [Route("pouitems")]
        [HttpDelete]
        public int DeletePOUCartItem(long cartId, long cartItemId)
        {
            return _cartService.DeletePOUCartItem(new CartDeleteItemRequest
            {
                CartId = cartId,
                CartItemId = cartItemId
            }); ;
        }

        [Route("requisition")]
        [HttpGet]
        public RequisitionWithDetailsDTO Get(long cartId, string username)
        {
            var requisition = _cartService.GetRequisition(new CartRequest
            {
                CartId = cartId,
                Username = username
            });

            return requisition != null ? _censorService.CensorPrivateRequisitionData(username, requisition) : requisition;
        }

        [Route("pourequisition")]
        [HttpGet]
        public RequisitionWithDetailsDTO Getpou(long cartId, string username)
        {
            var requisition = _cartService.GetRequisition(new CartRequest
            {
                CartId = cartId,
                Username = username,
                //IsPOU = true
                CartTypeId = CartType.POU.ToInt()
            });

            return requisition != null ? _censorService.CensorPrivateRequisitionData(username, requisition) : requisition;
        }

        private List<CartItem> RemoveCartReferenceFromCartItems(List<CartItem> items)
        {
            if (items != null)
            {
                foreach (var item in items)
                {
                    item.Cart = null;
                }
            }

            return items;
        }

        [Route("rfidcartattributes")]
        [HttpGet]
        public Item_RFID GetRfidCartAttributes(string coid, int departmentNumber, string parId, string username)
        {
            var rfidCart = _cartService.GetRfidCartAttributes(new CartRequest
            {
                Coid = coid,
                DepartmentNumber = departmentNumber,
                ParId = parId,
                Username = username
            });
            return rfidCart;
        }

        [Route("cartmerge")]
        [HttpGet]
        public Cart GetCartMerge(string coid, int departmentNumber, string parId, long? cartId, string username, int CartTypeId)
        {
            var cart = _cartService.GetCartMerge(new CartRequest
            {
                Coid = coid,
                DepartmentNumber = departmentNumber,
                ParId = parId,
                CartId = cartId,
                //CartTypeId = CartType.Procurement.ToInt(),
                CartTypeId = GetCartTypeById(CartTypeId).ToInt(),
                Username = username
            });
            if (cart?.Items?.Any() == true) cart.Items = RemoveCartReferenceFromCartItems(cart?.Items?.ToList());
            return cart;
        }

        [Route("attributesmerge")]
        [HttpGet]
        public CartAttributesResponse GetAttributesMerge(string coid, int departmentNumber, string parId, long? cartId, string username, int CartTypeId)
        {
            return _cartService.GetAttributesMerge(new CartRequest
            {
                Coid = coid,
                DepartmentNumber = departmentNumber,
                ParId = parId,
                CartId = cartId,
                Username = username,
                CartTypeId = GetCartTypeById(CartTypeId).ToInt()
            });
        }

        [Route("requisitionmerge")]
        [HttpGet]
        public RequisitionWithDetailsDTO GetMerge(long cartId, string username, int CartTypeId)
        {
            var requisition = _cartService.GetRequisition(new CartRequest
            {
                CartId = cartId,
                Username = username,
                CartTypeId = GetCartTypeById(CartTypeId).ToInt()
            });

            return requisition != null ? _censorService.CensorPrivateRequisitionData(username, requisition) : requisition;
        }

        [Route("itemsmerge")]
        [HttpPost]
        public AddToCartResponse AddToCartMerge(CartAddItemRequest request)
        {
            var response = _cartService.AddToCartMerge(request);
            if (response.ExistingItem != null) response.ExistingItem = RemoveCartReferenceFromCartItems(new List<CartItem> { response.ExistingItem }).First();
            return response;
        }

        [Route("itemsMerge")]
        [HttpPatch]
        public void UpdateCartItemsMerge(CartUpdateItemsRequest request)
        {
            _cartService.UpdateCartItemsMerge(request);
        }

        [Route("itemsMerge")]
        [HttpDelete]
        public int DeleteCartItemMerge(long cartId, long cartItemId)
        {
            return _cartService.DeleteCartItemMerge(new CartDeleteItemRequest
            {
                CartId = cartId,
                CartItemId = cartItemId
            });
        }

        private CartType GetCartTypeById(int CartTypeId)
        {
            switch (CartTypeId)
            {
                case 1:
                    return CartType.Procurement;
                case 2:
                    return CartType.POU;
                case 3:
                    return CartType.RFID;
                default:
                    throw new ArgumentOutOfRangeException(nameof(CartTypeId), "Invalid CartTypeId");
            }
        }

    }
}