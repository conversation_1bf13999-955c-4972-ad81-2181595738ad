﻿using log4net;
using RequisitionServices.DomainModel.Locations;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;
using RequisitionServices.MMISServices.Utilities;

namespace RequisitionServices.MMISServices
{
    public class SmartDepartmentService : ISmartDepartmentService
    {
        private ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        private const string getDepartmentMethod = "Departments/GetDepartmentById/";
        private const string getAllDepartmentsMethod = "Departments/GetAllDepartments/";
        private const string getAllDepartmentsForCacheMethod = "Departments/GetAllDepartmentsForCache/";

        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");

        public IEnumerable<Department> GetDepartments(string userName, string coid)
        {
            var departments = new List<Department>();
            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                var deptRecordsModel = ApiUtility.ExecuteApiGetTo<DepartmentRecordsModel>(endpoint, getAllDepartmentsMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });

                if (deptRecordsModel != null && deptRecordsModel.DepartmentRecords != null)
                {
                    foreach (var deptRecordModel in deptRecordsModel.DepartmentRecords)
                    {
                        departments.Add(deptRecordModel.MapToDepartment());
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(String.Format("Error calling Smart API to GetAllDepartments (COID = {0})", coid), ex);
            }
            return departments;
        }

        public IEnumerable<Department> GetDepartmentsForCache(string userName, string coid)
        {
            var departments = new List<Department>();
            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                var deptRecordsModel = ApiUtility.ExecuteApiGetTo<DepartmentRecordsModel>(endpoint, getAllDepartmentsForCacheMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid }
                                                                            });

                if (deptRecordsModel != null && deptRecordsModel.DepartmentRecords != null)
                {
                    foreach (var deptRecordModel in deptRecordsModel.DepartmentRecords)
                    {
                        departments.Add(deptRecordModel.MapToDepartment());
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Error(String.Format("Error calling Smart API to GetAllDepartments (COID = {0})", coid), ex);
            }
            return departments;
        }

        public Department GetDepartment(string userName, string coid, int deptNumber)
        {
            try
            {
                SmartInputValidator.CheckUserName(ref userName);
                SmartInputValidator.CheckCoid(coid);

                var deptRecordModel = ApiUtility.ExecuteApiGetTo<DepartmentRecordModel>(endpoint, getDepartmentMethod, new Dictionary<string, string>()
                                                                            {
                                                                                { "userId", userName },
                                                                                { "coid", coid },
                                                                                { "deptNumber", deptNumber.ToString() }
                                                                            });

                if (deptRecordModel != null)
                {
                    return deptRecordModel.MapToDepartment();
                }
            }
            catch(Exception ex)
            {
                Log.Error(String.Format("Error calling Smart API to retrieve Dept (DepId = {0}, COID = {1})", deptNumber.ToString(), coid), ex);
            }

            return null;
        }
    }
}
