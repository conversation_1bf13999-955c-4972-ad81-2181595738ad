﻿using RequisitionServices.DomainModel.Cart.Entities;
using System.Collections.Generic;

namespace RequisitionServices.DomainModel.Cart.Requests
{
    public class CartSubmitRequest
    {
        public string Username { get; set; }
        public long CartId { get; set; }
        /// <summary>
        /// Items that have been changed prior to submitting
        /// </summary>
        public List<CartItem> UpdatedItems { get; set; }
    }
}
