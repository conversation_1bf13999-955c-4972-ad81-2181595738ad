﻿using log4net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;

namespace RequisitionServices.Utility.WebAPI
{
    internal static class ApiFactory
    {
        private static HttpClient Client { get; set; }
        private static HttpClient SmartSecurityClient { get; set; }

        private static readonly ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod()?.DeclaringType);

        internal static HttpClient GetClient(string apiUrl, bool useDefaultCredentials)
        {
            if (Client != null) return Client;

            var handler = new WebRequestHandler
            {
                UseDefaultCredentials = true,
                UnsafeAuthenticatedConnectionSharing = true
            };
            
            Log.Debug($"Creating new HTTPClient for {apiUrl}");
            Client = new HttpClient(handler);
            Client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            return Client;
        }
        
        internal static HttpClient GetSmartSecurityClient(string apiUrl)
        {
            if (SmartSecurityClient != null) return SmartSecurityClient;
                
            Log.Debug($"Creating new Smart Security HttpClient for {apiUrl}");
            SmartSecurityClient = new HttpClient();
            SmartSecurityClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            return SmartSecurityClient;
        }
    }
}
