﻿using System;
using System.ComponentModel.DataAnnotations;

namespace RequisitionServices.DomainModel.UserAlertMessage
{
    /// <summary>
    /// Represents a request for a user alert message.
    /// </summary>
    public class UserAlertMessageRequest
    {
        public UserAlertMessageRequest() { }

        private int _alertTypeId = 0;

        /// <summary>
        /// Gets or sets the unique identifier for the user alert message request.
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Gets or sets the message content of the user alert.
        /// </summary>
        [Required]
        public string Message { get; set; }

        /// <summary>
        /// Gets or sets the start date of the user alert message.
        /// </summary>
        public DateTime? StartDate { get; set; }

        /// <summary>
        /// Gets or sets the end date of the user alert message.
        /// </summary>
        public DateTime? EndDate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user alert message is active.
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Gets or sets the type identifier for the user alert message.
        /// </summary>
        [Required]
        public int UserAlertMessageTypeId { 
            get { if (_alertTypeId == 0) {
                    throw new ArgumentException(message: "UserAlertMessageTypeId cannot be 0"); 
                } else { 
                    return _alertTypeId; 
                } 
            }
            set { if(value == 0)
                { 
                    throw new ArgumentException(message: "UserAlertMessageTypeId cannot be 0"); 
                } else { 
                    _alertTypeId = value; 
                }
            } 
        }
    }
}
