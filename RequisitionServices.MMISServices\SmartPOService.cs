﻿using log4net;
using Microsoft.Web.Http;
using RequisitionServices.DomainModel.PurchaseOrders;
using RequisitionServices.MMISServices.DTO;
using RequisitionServices.MMISServices.Interface;
using RequisitionServices.Repositories.Interfaces;
using RequisitionServices.Utility;
using RequisitionServices.Utility.WebAPI;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Reflection;

namespace RequisitionServices.MMISServices
{
    public class SmartPOService : ISmartPOService
    {
        private const string getDetailsByPO = "v2/POs/GetDetailsByPO/";
        private const string getPOConfirmationDetails = "POs/GetPOConfirmationDetails/";
        private const string getDetailsByDateRange = "POs/GetDetailsByDateRange/";
        private const string getPoByOptions = "POs/GetPoByOptions/";
        private const string getHistoryByPO = "POS/GetHistoryByPO/";
        private const string getPoHeadersByPoNumbers = "POs/GetPoHeaderInfoByPoNumbers/";
        private ILog Log = LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);
        private string endpoint = ConfigurationManager.AppSettings.Get("SmartServicesAPIUrl");
        private IRequisitionRepository requisitionRepository;

        public SmartPOService(IRequisitionRepository reqRepo)
        {
            requisitionRepository = reqRepo;
        }
        public PODetails GetDetailsByPO(string userName, string coid, string PONumber, string stockIndicator, ApiVersion version = null)
        {
            PODetails ret = new PODetails();

            this.CheckUserName(ref userName);
            this.CheckCoid(coid);

            int intPONumber;
            if (!Int32.TryParse(PONumber, out intPONumber))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for PONumber. PONumber = {0}", PONumber));
            }

            try
            {
                var PODTO = ApiUtility.ExecuteApiGetTo<PurchaseOrderDetail>(endpoint, getDetailsByPO, new Dictionary<string, string>()
            {
                { "userId", userName },
                { "coid", coid },
                { "poNumber", PONumber },
                { "stockIndicator", stockIndicator}
            }, null, null, null, version);

                if (PODTO != null && PODTO.POHeader != null)
                {
                    ret.POHeader = PODTO.POHeader.MapToPOHeader();
                    foreach (var poLineItemDTO in PODTO.POLineItem)
                    {
                        var lineItem = poLineItemDTO.MapToPOLineItem();
                        if (lineItem.RequisitionNumber != 0)
                        {
                            var ReqItem = requisitionRepository.GetReqRequisitionItem(lineItem.RequisitionItemId, ret.POHeader.COID.ToString(), lineItem.RequisitionNumber.ToString(), lineItem.ItemNumber.ToString());
                            if (ReqItem != null)
                            {
                                lineItem.RequisitionItemId = ReqItem.Id;
                                lineItem.RequisitionId = ReqItem.RequisitionId;
                                lineItem.FileAttachments = requisitionRepository.GetFileAttachments(ReqItem.Id);
                            }
                        }
                        else
                        {
                            var ReqItem = requisitionRepository.GetSprRequisitionItem(lineItem.RequisitionItemId, ret.POHeader.COID.ToString(), lineItem.ParentSystemId, ret.POHeader.Vendor, lineItem.ReorderNumber);
                            if (ReqItem != null)
                            {
                                lineItem.RequisitionItemId = ReqItem.Id;
                                lineItem.RequisitionId = ReqItem.RequisitionId;
                                lineItem.BudgetNumber = ReqItem.SPRDetail == null ? null : ReqItem.SPRDetail.BudgetNumber;
                                lineItem.FileAttachments = requisitionRepository.GetFileAttachments(ReqItem.Id);
                                lineItem.AcquisitionType = ReqItem.SPRDetail.AcquisitionType;
                                lineItem.EquipmentType = ReqItem.SPRDetail.EquipmentType;
                                lineItem.PartsWarrantyMonths = ReqItem.PartsWarrantyMonths;
                                lineItem.LaborWarrantyMonths = ReqItem.LaborWarrantyMonths;
                            }
                        }
                        ret.POLineItem.Add(lineItem);
                    }
                    ret.VendorHeader = PODTO.VendorHeader.MapToPOVendor();
                }

                return ret;
            }
            catch (BadRequestException ex)
            {
                Log.Error($"Error on SmartPOService GetDetailsByPO with coid:{coid}, PONumber:{PONumber}, stockIndicator:{stockIndicator}", ex);
                throw ex;
              
            }
            catch (Exception ex)
            {
                Log.Error($"Error on SmartPOService GetDetailsByPO with coid:{coid}, PONumber:{PONumber}, stockIndicator:{stockIndicator}", ex);
                return new PODetails();

            }

        }

        public IEnumerable<POConfirmationDetail> GetPOConfirmationDetails(string userName, string coid, string PONumber, string lineNumber)
        {
            List<POConfirmationDetail> ret = new List<POConfirmationDetail>();

            this.CheckUserName(ref userName);
            this.CheckCoid(coid);

            int intPONumber;
            if (!Int32.TryParse(PONumber, out intPONumber))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for PONumber. PONumber = {0}", PONumber));
            }

            int intLineNumber;
            if (!Int32.TryParse(lineNumber, out intLineNumber))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for PONumber. lineNumber = {0}", lineNumber));
            }

            var POConfirmationDTO = ApiUtility.ExecuteApiGetTo<List<POConfirmationDetailDTO>>(endpoint, getPOConfirmationDetails, new Dictionary<string, string>()
            {
                { "userId", userName },
                { "coid", coid },
                { "poNumber", PONumber },
                { "lineNumber", lineNumber }
            });

            foreach (var POConfirmationDetail in POConfirmationDTO)
            {
                ret.Add(POConfirmationDetail.MapToPOConfirmationDetail());
            }

            return ret;
        }

        public IEnumerable<POLists> GetDetailsByDateRange(string userName, string coid, DateTime startDate, DateTime endDate, int department)
        {
            List<POLists> rets = new List<POLists>();

            this.CheckUserName(ref userName);
            this.CheckCoid(coid);

            var PODTOs = ApiUtility.ExecuteApiGetTo<List<POListsDTO>>(endpoint, getDetailsByDateRange, new Dictionary<string, string>()
                {
                    { "userId", userName },
                    { "coid", coid },
                    { "startDate", startDate.ToString() },
                    { "endDate", endDate.ToString() },
                    { "department", department.ToString() }
                });

            foreach (var PODTO in PODTOs)
            {
                rets.Add(PODTO.MapToPOList());
            }

            return rets;
        }


        public IEnumerable<POOptions> GetPoByOptions(string userName, string coid, DateTime startDate, DateTime endDate, string poType, int department, string reorderNumber)
        {
            List<POOptions> rets = new List<POOptions>();

            this.CheckUserName(ref userName);
            this.CheckCoid(coid);

            var PODTOs = ApiUtility.ExecuteApiGetTo<List<POOptionsDTO>>(endpoint, getPoByOptions, new Dictionary<string, string>()
                {
                    { "userId", userName },
                    { "coid", coid },
                    { "startDate", startDate.ToString() },
                    { "endDate", endDate.ToString() },
                    { "poType", poType },
                    { "department", department.ToString() },
                    { "reorderNumber", reorderNumber }
                });

            foreach (var PODTO in PODTOs)
            {
                rets.Add(PODTO.MapToPOList());
            }

            return rets;
        }

        public IEnumerable<POLists> GetPoHeaderInfoByPoNumbers(string userName, string coid, IEnumerable<int> poNumbers)
        {
            var returnList = new List<POLists>();

            this.CheckUserName(ref userName);
            this.CheckCoid(coid);

            var PODTOs = ApiUtility.ExecuteApiPostWithContentTo<List<POListsDTO>>(endpoint, getPoHeadersByPoNumbers, new Dictionary<string, string>()
                                                            {
                                                                { "userId", userName },
                                                                { "coid", coid.ToString() }
                                                            }, poNumbers);

            foreach (var PODTO in PODTOs)
            {
                returnList.Add(PODTO.MapToPOList());
            }

            return returnList;

        }

        public IEnumerable<POHistory> GetHistoryByPO(string userName, string coid, string PONumber)
        {
            this.CheckUserName(ref userName);
            this.CheckCoid(coid);

            var rets = ApiUtility.ExecuteApiGetTo<List<POHistory>>(endpoint, getHistoryByPO, new Dictionary<string, string>()
                {
                    { "userId", userName },
                    { "coid", coid },
                    { "poNumber", PONumber}
                });

            return rets;
        }

        private void CheckUserName(ref string userName)
        {
            if (String.IsNullOrWhiteSpace(userName))
            {
                throw new InvalidOperationException("No username provided. This is a required property");
            }
            if (userName.IndexOf('/') != -1)
            {
                userName = userName.Split('/')[1];
            }
        }

        private void CheckCoid(string coid)
        {
            int intCOID;
            if (!Int32.TryParse(coid, out intCOID))
            {
                throw new ArgumentException(String.Format("Smart does not support non-numeric characters for COID. COID = {0}", coid));
            }
        }

    }
}
