﻿using RequisitionServices.DomainModel.Locations;
using System.Collections.Generic;

namespace RequisitionServices.MMISServices.Interface
{
    public interface ISmartCOIDService
    {
        Location GetCOID(string userName, string coid);
        IEnumerable<Location> GetUserCOIDs(string userName);
        List<int> GetAllCOIDs(string userName);
        IEnumerable<int> GetAllCostCodes(string userName, string coid);
        IEnumerable<GLAccount> GetAllGLAccounts(string userName, string coid, string accountStringPartial);
        GLAccount GetGLAccount(string userName, string coid, long accountNumber);
        Address GetAddress(string userName, string coid, int shipNumber);
        IEnumerable<Address> GetAddresses(string userName, string coid);
    }
}
