﻿namespace RequisitionServices.DomainModel.DigitalSignOff
{
    public class DigitalSignOffResponse
    {
        /// <summary>
        /// Returned ID from AD
        /// </summary>
        public string Id { get; set; }
        /// <summary>
        /// Returned 3/4 from AD
        /// </summary>
        public string Username { get; set; }
        /// <summary>
        /// Returned first name from AD
        /// </summary>
        public string FirstName { get; set; }
        /// <summary>
        /// Returned last name e from AD
        /// </summary>
        public string LastName { get; set; }
        /// <summary>
        /// Returned email from AD
        /// </summary>
        public string Email { get; set; }
        /// <summary>
        /// Returned job position from AD
        /// </summary>
        public string Type { get; set; }
        /// <summary>
        /// Returned if user is deactivated in AD
        /// </summary>
        public string IsDeactivated { get; set; }
        /// <summary>
        /// Returned domain that was called for AD
        /// </summary>
        public string domain { get; set; }
    }
}