﻿using System;
using System.Xml.Serialization;

namespace RequisitionServices.DomainModel.Audit
{
    public class AuditEventsDTO
    {
        public int Id { get; set; }

        public int AdminUserID { get; set; }

        public string Entity { get; set; }

        public int EditedUserID { get; set; }

        public string COID { get; set; }

        public int AuditEventType { get; set; }

        public DateTime CreatedDateTime { get; set; }

        public string Comments { get; set; }

        [XmlIgnore]
        public string ChangeXML { get; set; }
    }
}
