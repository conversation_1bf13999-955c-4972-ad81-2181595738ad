USE [eProcurementQA]
GO

/****** Object:  Table [dbo].[RequisitionsDigitalSignoff]    Script Date: 1/24/2024 8:53:28 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].RequisitionDigitalSignOffs(
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[RequisitionId] [int] NOT NULL,
	[UserId] [int] NOT NULL,
	[SignatureCaptured] [bit] NOT NULL,
	[ADValidated] [bit] NOT NULL,
 CONSTRAINT [PK_RequisitionDigitalSignOffs] PRIMARY KEY CLUSTERED 
(
	[Id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[RequisitionDigitalSignOffs]  WITH CHECK ADD  CONSTRAINT [FK_RequisitionDigitalSignOffs_DigitalSignoffUsers] FOREIGN KEY([UserId])
REFERENCES [dbo].[DigitalSignoffUsers] ([Id])
GO

ALTER TABLE [dbo].[RequisitionDigitalSignOffs] CHECK CONSTRAINT [FK_RequisitionDigitalSignOffs_DigitalSignoffUsers]
GO

ALTER TABLE [dbo].[RequisitionDigitalSignOffs]  WITH CHECK ADD  CONSTRAINT [FK_RequisitionDigitalSignOffs_Requisitions] FOREIGN KEY([RequisitionId])
REFERENCES [dbo].[Requisitions] ([RequisitionId])
GO

ALTER TABLE [dbo].[RequisitionDigitalSignOffs] CHECK CONSTRAINT [FK_RequisitionDigitalSignOffs_Requisitions]
GO


