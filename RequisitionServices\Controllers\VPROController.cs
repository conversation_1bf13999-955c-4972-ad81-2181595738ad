﻿using log4net;
using RequisitionServices.Repositories;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web.Http;
using RequisitionServices.DomainModel.VPro;
using RequisitionServices.DomainServices;
using RequisitionServices.DomainServices.Interface;
using RequisitionServices.DomainModel.Vira;
using Microsoft.Ajax.Utilities;
using System.Data.Entity.Infrastructure;
using System.Data.SqlClient;

namespace RequisitionServices.Controllers
{
    /// <summary>
    /// Handles HTTP requests for VPro badge operations.
    /// </summary>
    public class VProController : ApiController
    {
        private static readonly ILog log = LogManager.GetLogger(typeof(VProController));
        private readonly IVProService _vProService;

        public VProController(IVProService vProService)
        {
            _vProService = vProService;
        }

        [HttpGet]
        public List<RequisitionVProBadgeLog> GetallBadgeLogs()
        {
        return _vProService.GetallBadgeLogs();
     
        }

        [HttpGet]
        public IHttpActionResult GetVProBadgeLogById(int Id)
        {
            if (Id <= 0)
            {
                return BadRequest("Invalid Id.");
            }
            var badgeLog = _vProService.GetBadgeLogById(Id);
            if (badgeLog == null)
            {
                return NotFound();
            }
            return Ok(badgeLog);
        }

        [HttpPut]
        public IHttpActionResult UpdateBadgeLog(RequisitionVProBadgeLog badgeLog)
        {
            var existingBadgeLog = _vProService.GetBadgeLogById(badgeLog.Id);
            if (existingBadgeLog == null)
            {
                return NotFound();
            }
            var updatedBadgeLog = _vProService.UpdateBadgeLog(badgeLog);
            return Ok(updatedBadgeLog);
        }

        [HttpDelete]
        public IHttpActionResult DeleteVProBadgeLog(int Id)
        {
            var badgeLog = _vProService.GetBadgeLogById(Id);

            if (badgeLog == null)
            {
                return NotFound();
            }

            _vProService.DeleteVProBadgeLog(Id);
            return StatusCode(HttpStatusCode.NoContent);
        }

        public IHttpActionResult CreateVProBadgeLog(VProBadgeLogDTO request)
        {
            if (request == null)
            {
                return BadRequest("The Provided RequisitionID Must Be an Integer");
            }
            var response = new RequisitionVProBadgeLog
            {
                Id = request.Id,
                RequisitionId = request.RequisitionId,
                BadgeInStatusId = request.BadgeInStatusId
            };
            try
            {
                var createdBadgeLog = _vProService.CreateVProBadgeLog(response);
                return Ok(response);
            }
            catch (DbUpdateException ex) when (ex.InnerException?.InnerException is SqlException sqlEx &&
                                                sqlEx.Message.Contains("FOREIGN KEY constraint"))
            {
                // Return 400 Bad Request with a custom message for foreign key violation
                return BadRequest("The provided RequisitionID does not exist in the database.");
            }
        }
    }
}
