﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using RequisitionServices.DomainModel.Users;

namespace RequisitionServices.DomainModel.Audit
{
    public class AuditEvents
    {
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        [Required]
        public int AdminUserID { get; set; }
        [ForeignKey("AdminUserID")]
        public virtual User AdminUser { get; set; }

        [Required]
        [Column(TypeName = "nvarchar")]
        public string Entity { get; set; }

        [Required]
        public int EditedUserID { get; set; }
        [ForeignKey("EditedUserID")]
        public virtual User EditUser { get; set; }

        [Required]
        [Column(TypeName = "nvarchar")]
        [StringLength(10)]
        public string COID { get; set; }

        public int AuditEventType { get; set; }
        [ForeignKey("AuditEventType")]
        public virtual AuditEventTypes AuditId { get; set; }

        public DateTime CreatedDateTime { get; set; }

        [Column(TypeName = "nvarchar")]
        public string Comments { get; set; }

        [Column(TypeName = "nvarchar")]
        [StringLength(3000)]
        public string ChangeXML { get; set; }

    }
}
