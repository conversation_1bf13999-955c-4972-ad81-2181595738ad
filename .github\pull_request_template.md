# Pull Request Template

<details>
<summary>PR Checklist Please REVIEW First</summary>

- [ ] Named Branch `feature/US00000-short-summary` or `bugfix/DE00000-short-summary`
- [ ] Named PR `US/DE00000-short-summary-of-US-or-DE`
- [ ] Make sure small(Atomic) commits (within reason of course, some we can't control)
- [ ] Add appropriate label based on versioning
    - Major: Application Breaking Changes
    - Minor: Application Feature
    - Patch: Application Defect/Bug
    - NoVersion: Used for Release Branches and Small Tasks Like README Changes
- [ ] Review your changes going in on the `Files changed` tab
- [ ] Add a reviewer
- [ ] Fill out all information below

> *Note: Place an `x` in the box to make it as checked and remove the space in the box as well*

</details>

## Details of Pull Request

Fill out what your branch/pull request is doing. It should be focused only on the Rally task you are working.

## Related PRs In Other Projects

If applicable, leave this section here and come back to edit when you are finished creating all your PRs. Link all PRs here, including this one, and make sure to copy this to all applicable PRs related.

## Any Developer Specific Testing?

This is optional, but if there is anything special about testing locally, please mention it here

> *Note: Please leave checklist in the PR, if the check is not applicable to the PR, please leave it unchecked*
